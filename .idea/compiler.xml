<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="CompilerConfiguration">
    <annotationProcessing>
      <profile name="Maven default annotation processors profile" enabled="true">
        <sourceOutputDir name="target/generated-sources/annotations" />
        <sourceTestOutputDir name="target/generated-test-sources/test-annotations" />
        <outputRelativeToContentRoot value="true" />
        <module name="civ-v5" />
        <module name="rest-client" />
        <module name="rest-server" />
      </profile>
    </annotationProcessing>
  </component>
  <component name="JavacSettings">
    <option name="ADDITIONAL_OPTIONS_OVERRIDE">
      <module name="civ-v5" options="-parameters" />
      <module name="civv5-rest" options="-parameters" />
      <module name="rest-client" options="-parameters" />
      <module name="rest-server" options="-parameters" />
    </option>
  </component>
</project>