<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AugmentWebviewStateStore">
    <option name="stateMap">
      <map>
        <entry key="CHAT_STATE" value="{&quot;currentConversationId&quot;:&quot;320fa92b-2405-4420-b45a-e24f199d885b&quot;,&quot;conversations&quot;:{&quot;320fa92b-2405-4420-b45a-e24f199d885b&quot;:{&quot;id&quot;:&quot;320fa92b-2405-4420-b45a-e24f199d885b&quot;,&quot;createdAtIso&quot;:&quot;2025-07-28T08:55:05.071Z&quot;,&quot;lastInteractedAtIso&quot;:&quot;2025-07-28T09:17:43.241Z&quot;,&quot;chatHistory&quot;:[{&quot;status&quot;:&quot;success&quot;,&quot;request_id&quot;:&quot;762aa09c-9ad0-4af9-a838-7459bf142288&quot;,&quot;request_message&quot;:&quot;/Users/<USER>/Library/Java/JavaVirtualMachines/corretto-1.8.0_432/Contents/Home/bin/java -Dmaven.multiModuleProjectDirectory=/Users/<USER>/jhProject/civ-5.2/rest-client -Djansi.passthrough=true -Dmaven.home=/usr/local/apache-maven-3.6.3 -Dclassworlds.conf=/usr/local/apache-maven-3.6.3/bin/m2.conf -Dmaven.ext.class.path=/Applications/IntelliJ IDEA.app/Contents/plugins/maven/lib/maven-event-listener.jar -javaagent:/Applications/IntelliJ IDEA.app/Contents/lib/idea_rt.jar=61178 -Dfile.encoding=UTF-8 -classpath /usr/local/apache-maven-3.6.3/boot/plexus-classworlds.license:/usr/local/apache-maven-3.6.3/boot/plexus-classworlds-2.6.0.jar org.codehaus.classworlds.Launcher -Didea.version=2025.1.3 -s /usr/local/apache-maven-3.6.3/conf/settings-ptyfzc.xml -Dmaven.repo.local=/usr/local/apache-maven-3.6.3/repo install\n[INFO] Scanning for projects...\n[ERROR] [ERROR] Some problems were encountered while processing the POMs:\n[FATAL] Non-resolvable parent POM for com.goodwill.hdr:civv5-rest:5.0.1-SNAPHSHOT: Could not find artifact com.goodwill.hdr:hdr-common:pom:1.0.42-SNAPSHOT @ com.goodwill.hdr:civv5-rest:5.0.1-SNAPHSHOT, /usr/local/apache-maven-3.6.3/repo/com/goodwill/hdr/civv5-rest/5.0.1-SNAPHSHOT/civv5-rest-5.0.1-SNAPHSHOT.pom, line 11, column 13\n @ \n[ERROR] The build could not read 1 project -&gt; [Help 1]\n[ERROR]   \n[ERROR]   The project com.goodwill.hdr:rest-client:5.0.6 (/Users/<USER>/jhProject/civ-5.2/rest-client/pom.xml) has 1 error\n[ERROR]     Non-resolvable parent POM for com.goodwill.hdr:civv5-rest:5.0.1-SNAPHSHOT: Could not find artifact com.goodwill.hdr:hdr-common:pom:1.0.42-SNAPSHOT @ com.goodwill.hdr:civv5-rest:5.0.1-SNAPHSHOT, /usr/local/apache-maven-3.6.3/repo/com/goodwill/hdr/civv5-rest/5.0.1-SNAPHSHOT/civv5-rest-5.0.1-SNAPHSHOT.pom, line 11, column 13 -&gt; [Help 2]\n[ERROR] \n[ERROR] To see the full stack trace of the errors, re-run Maven with the -e switch.\n[ERROR] Re-run Maven using the -X switch to enable full debug logging.\n[ERROR] \n[ERROR] For more information about the errors and possible solutions, please read the following articles:\n[ERROR] [Help 1] http://cwiki.apache.org/confluence/display/MAVEN/ProjectBuildingException\n[ERROR] [Help 2] http://cwiki.apache.org/confluence/display/MAVEN/UnresolvableModelException\n\nProcess finished with exit code 1\n&quot;,&quot;rich_text_json_repr&quot;:{&quot;type&quot;:&quot;doc&quot;,&quot;content&quot;:[{&quot;type&quot;:&quot;paragraph&quot;,&quot;content&quot;:[{&quot;type&quot;:&quot;text&quot;,&quot;text&quot;:&quot;/Users/<USER>/Library/Java/JavaVirtualMachines/corretto-1.8.0_432/Contents/Home/bin/java -Dmaven.multiModuleProjectDirectory=/Users/<USER>/jhProject/civ-5.2/rest-client -Djansi.passthrough=true -Dmaven.home=/usr/local/apache-maven-3.6.3 -Dclassworlds.conf=/usr/local/apache-maven-3.6.3/bin/m2.conf -Dmaven.ext.class.path=/Applications/IntelliJ IDEA.app/Contents/plugins/maven/lib/maven-event-listener.jar -javaagent:/Applications/IntelliJ IDEA.app/Contents/lib/idea_rt.jar=61178 -Dfile.encoding=UTF-8 -classpath /usr/local/apache-maven-3.6.3/boot/plexus-classworlds.license:/usr/local/apache-maven-3.6.3/boot/plexus-classworlds-2.6.0.jar org.codehaus.classworlds.Launcher -Didea.version=2025.1.3 -s /usr/local/apache-maven-3.6.3/conf/settings-ptyfzc.xml -Dmaven.repo.local=/usr/local/apache-maven-3.6.3/repo install&quot;},{&quot;type&quot;:&quot;hardBreak&quot;},{&quot;type&quot;:&quot;text&quot;,&quot;text&quot;:&quot;[INFO] Scanning for projects...&quot;},{&quot;type&quot;:&quot;hardBreak&quot;},{&quot;type&quot;:&quot;text&quot;,&quot;text&quot;:&quot;[ERROR] [ERROR] Some problems were encountered while processing the POMs:&quot;},{&quot;type&quot;:&quot;hardBreak&quot;},{&quot;type&quot;:&quot;text&quot;,&quot;text&quot;:&quot;[FATAL] Non-resolvable parent POM for com.goodwill.hdr:civv5-rest:5.0.1-SNAPHSHOT: Could not find artifact com.goodwill.hdr:hdr-common:pom:1.0.42-SNAPSHOT @ com.goodwill.hdr:civv5-rest:5.0.1-SNAPHSHOT, /usr/local/apache-maven-3.6.3/repo/com/goodwill/hdr/civv5-rest/5.0.1-SNAPHSHOT/civv5-rest-5.0.1-SNAPHSHOT.pom, line 11, column 13&quot;},{&quot;type&quot;:&quot;hardBreak&quot;},{&quot;type&quot;:&quot;text&quot;,&quot;text&quot;:&quot; @ &quot;},{&quot;type&quot;:&quot;hardBreak&quot;},{&quot;type&quot;:&quot;text&quot;,&quot;text&quot;:&quot;[ERROR] The build could not read 1 project -&gt; [Help 1]&quot;},{&quot;type&quot;:&quot;hardBreak&quot;},{&quot;type&quot;:&quot;text&quot;,&quot;text&quot;:&quot;[ERROR]   &quot;},{&quot;type&quot;:&quot;hardBreak&quot;},{&quot;type&quot;:&quot;text&quot;,&quot;text&quot;:&quot;[ERROR]   The project com.goodwill.hdr:rest-client:5.0.6 (/Users/<USER>/jhProject/civ-5.2/rest-client/pom.xml) has 1 error&quot;},{&quot;type&quot;:&quot;hardBreak&quot;},{&quot;type&quot;:&quot;text&quot;,&quot;text&quot;:&quot;[ERROR]     Non-resolvable parent POM for com.goodwill.hdr:civv5-rest:5.0.1-SNAPHSHOT: Could not find artifact com.goodwill.hdr:hdr-common:pom:1.0.42-SNAPSHOT @ com.goodwill.hdr:civv5-rest:5.0.1-SNAPHSHOT, /usr/local/apache-maven-3.6.3/repo/com/goodwill/hdr/civv5-rest/5.0.1-SNAPHSHOT/civv5-rest-5.0.1-SNAPHSHOT.pom, line 11, column 13 -&gt; [Help 2]&quot;},{&quot;type&quot;:&quot;hardBreak&quot;},{&quot;type&quot;:&quot;text&quot;,&quot;text&quot;:&quot;[ERROR] &quot;},{&quot;type&quot;:&quot;hardBreak&quot;},{&quot;type&quot;:&quot;text&quot;,&quot;text&quot;:&quot;[ERROR] To see the full stack trace of the errors, re-run Maven with the -e switch.&quot;},{&quot;type&quot;:&quot;hardBreak&quot;},{&quot;type&quot;:&quot;text&quot;,&quot;text&quot;:&quot;[ERROR] Re-run Maven using the -X switch to enable full debug logging.&quot;},{&quot;type&quot;:&quot;hardBreak&quot;},{&quot;type&quot;:&quot;text&quot;,&quot;text&quot;:&quot;[ERROR] &quot;},{&quot;type&quot;:&quot;hardBreak&quot;},{&quot;type&quot;:&quot;text&quot;,&quot;text&quot;:&quot;[ERROR] For more information about the errors and possible solutions, please read the following articles:&quot;},{&quot;type&quot;:&quot;hardBreak&quot;},{&quot;type&quot;:&quot;text&quot;,&quot;text&quot;:&quot;[ERROR] [Help 1] http://cwiki.apache.org/confluence/display/MAVEN/ProjectBuildingException&quot;},{&quot;type&quot;:&quot;hardBreak&quot;},{&quot;type&quot;:&quot;text&quot;,&quot;text&quot;:&quot;[ERROR] [Help 2] http://cwiki.apache.org/confluence/display/MAVEN/UnresolvableModelException&quot;},{&quot;type&quot;:&quot;hardBreak&quot;},{&quot;type&quot;:&quot;hardBreak&quot;},{&quot;type&quot;:&quot;text&quot;,&quot;text&quot;:&quot;Process finished with exit code 1&quot;},{&quot;type&quot;:&quot;hardBreak&quot;}]}]},&quot;mentioned_items&quot;:[],&quot;structured_output_nodes&quot;:[{&quot;id&quot;:1,&quot;type&quot;:0,&quot;content&quot;:&quot;The Maven build is failing because it cannot resolve the parent POM. There are version mismatches in your project structure:\n\n**Issue**: The `rest-client/pom.xml` references parent version `5.0.1-SNAPHSHOT`, but the root `pom.xml` has version `5.0.2-SNAPHSHOT`.\n\n**Fix**: Update the parent version in `rest-client/pom.xml`:\n\n&lt;augment_code_snippet path=\&quot;rest-client/pom.xml\&quot; mode=\&quot;EDIT\&quot;&gt;\n```xml\n    &lt;parent&gt;\n        &lt;artifactId&gt;civv5-rest&lt;/artifactId&gt;\n        &lt;groupId&gt;com.goodwill.hdr&lt;/groupId&gt;\n        &lt;version&gt;5.0.2-SNAPHSHOT&lt;/version&gt;\n    &lt;/parent&gt;\n```\n&lt;/augment_code_snippet&gt;\n\n**Additional Issues to Fix**:\n\n1. **Typo in version**: \&quot;SNAPHSHOT\&quot; should be \&quot;SNAPSHOT\&quot; in all POMs\n2. **Parent dependency**: The root POM references `hdr-common:1.0.45-SNAPSHOT` but the error shows it's looking for `1.0.42-SNAPSHOT`\n\nAfter fixing the parent version, you may also need to:\n- Ensure the `hdr-common` artifact is available in your Maven repository\n- Run `mvn clean install` from the root directory first to build the parent POM\n&quot;},{&quot;id&quot;:2,&quot;type&quot;:2,&quot;content&quot;:&quot;&quot;},{&quot;id&quot;:3,&quot;type&quot;:3,&quot;content&quot;:&quot;pom.xml:0-838\npom.xml:1268-2108\npom.xml:2108-3044\npom.xml:3044-3747\npom.xml:3747-5338\npom.xml:5338-5385\nrest-client/pom.xml:0-879\nrest-client/pom.xml:879-1859\nrest-client/pom.xml:1859-2638\nciv-v5/src/main/resources/application.yml:0-117\nciv-v5/src/main/resources/application.yml:117-1071\nciv-v5/src/main/resources/application.yml:1071-1840\nciv-v5/pom.xml:0-745\nciv-v5/pom.xml:745-1803\nciv-v5/pom.xml:1803-2883\nciv-v5/pom.xml:2883-3845\nciv-v5/pom.xml:3845-4692\nrest-server/pom.xml:0-501\nrest-server/pom.xml:501-1491\nrest-server/pom.xml:1491-2650\nrest-server/pom.xml:2650-3700\nciv-v5/src/main/java/com/goodwill/Application.java:0-755\nciv-v5/src/main/resources/licenseVerifyParam.properties:0-518\nciv-v5/src/main/java/com/goodwill/hdr/civ/handle/GlobalExceptionHandler.java:333-976\nrest-server/src/main/resources/application.yml:0-281\nciv-v5/src/main/java/com/goodwill/hdr/civ/service/impl/VisitServiceImpl.java:783-1508\nciv-v5/src/main/java/com/goodwill/hdr/civ/service/impl/VisitServiceImpl.java:87788-88496\nrest-client/src/main/resources/application.yml:0-89\nciv-v5/src/main/java/com/goodwill/hdr/civ/config/Config.java:58553-59394\nciv-v5/src/main/java/com/goodwill/hdr/civ/config/Config.java:64727-65690\nciv-v5/src/main/java/com/goodwill/hdr/civ/service/impl/PatientListServiceImpl.java:1100-1834\nciv-v5/src/main/java/com/goodwill/hdr/civ/utils/Utils.java:30213-31094\nciv-v5/src/main/java/com/goodwill/hdr/civ/service/impl/PowerServiceImpl.java:112879-113586\nciv-v5/src/main/java/com/goodwill/hdr/civ/service/impl/DiagnoseServiceImpl.java:43-778\nciv-v5/src/main/java/com/goodwill/hdr/civ/service/impl/MedicalServiceImpl.java:810-1484&quot;}],&quot;seen_state&quot;:&quot;unseen&quot;,&quot;structured_request_nodes&quot;:[{&quot;id&quot;:0,&quot;type&quot;:0,&quot;text_node&quot;:{&quot;content&quot;:&quot;/Users/<USER>/Library/Java/JavaVirtualMachines/corretto-1.8.0_432/Contents/Home/bin/java -Dmaven.multiModuleProjectDirectory=/Users/<USER>/jhProject/civ-5.2/rest-client -Djansi.passthrough=true -Dmaven.home=/usr/local/apache-maven-3.6.3 -Dclassworlds.conf=/usr/local/apache-maven-3.6.3/bin/m2.conf -Dmaven.ext.class.path=/Applications/IntelliJ IDEA.app/Contents/plugins/maven/lib/maven-event-listener.jar -javaagent:/Applications/IntelliJ IDEA.app/Contents/lib/idea_rt.jar=61178 -Dfile.encoding=UTF-8 -classpath /usr/local/apache-maven-3.6.3/boot/plexus-classworlds.license:/usr/local/apache-maven-3.6.3/boot/plexus-classworlds-2.6.0.jar org.codehaus.classworlds.Launcher -Didea.version=2025.1.3 -s /usr/local/apache-maven-3.6.3/conf/settings-ptyfzc.xml -Dmaven.repo.local=/usr/local/apache-maven-3.6.3/repo install\n[INFO] Scanning for projects...\n[ERROR] [ERROR] Some problems were encountered while processing the POMs:\n[FATAL] Non-resolvable parent POM for com.goodwill.hdr:civv5-rest:5.0.1-SNAPHSHOT: Could not find artifact com.goodwill.hdr:hdr-common:pom:1.0.42-SNAPSHOT @ com.goodwill.hdr:civv5-rest:5.0.1-SNAPHSHOT, /usr/local/apache-maven-3.6.3/repo/com/goodwill/hdr/civv5-rest/5.0.1-SNAPHSHOT/civv5-rest-5.0.1-SNAPHSHOT.pom, line 11, column 13\n @ \n[ERROR] The build could not read 1 project -&gt; [Help 1]\n[ERROR]   \n[ERROR]   The project com.goodwill.hdr:rest-client:5.0.6 (/Users/<USER>/jhProject/civ-5.2/rest-client/pom.xml) has 1 error\n[ERROR]     Non-resolvable parent POM for com.goodwill.hdr:civv5-rest:5.0.1-SNAPHSHOT: Could not find artifact com.goodwill.hdr:hdr-common:pom:1.0.42-SNAPSHOT @ com.goodwill.hdr:civv5-rest:5.0.1-SNAPHSHOT, /usr/local/apache-maven-3.6.3/repo/com/goodwill/hdr/civv5-rest/5.0.1-SNAPHSHOT/civv5-rest-5.0.1-SNAPHSHOT.pom, line 11, column 13 -&gt; [Help 2]\n[ERROR] \n[ERROR] To see the full stack trace of the errors, re-run Maven with the -e switch.\n[ERROR] Re-run Maven using the -X switch to enable full debug logging.\n[ERROR] \n[ERROR] For more information about the errors and possible solutions, please read the following articles:\n[ERROR] [Help 1] http://cwiki.apache.org/confluence/display/MAVEN/ProjectBuildingException\n[ERROR] [Help 2] http://cwiki.apache.org/confluence/display/MAVEN/UnresolvableModelException\n\nProcess finished with exit code 1\n&quot;}},{&quot;id&quot;:1,&quot;type&quot;:4,&quot;ide_state_node&quot;:{&quot;workspaceFolders&quot;:[{&quot;repositoryRoot&quot;:&quot;/Users/<USER>/jhProject/civ-5.2&quot;,&quot;folderRoot&quot;:&quot;/Users/<USER>/jhProject/civ-5.2&quot;}],&quot;workspaceFoldersUnchanged&quot;:false}}],&quot;timestamp&quot;:&quot;2025-07-28T08:55:06.120Z&quot;,&quot;response_text&quot;:&quot;The Maven build is failing because it cannot resolve the parent POM. There are version mismatches in your project structure:\n\n**Issue**: The `rest-client/pom.xml` references parent version `5.0.1-SNAPHSHOT`, but the root `pom.xml` has version `5.0.2-SNAPHSHOT`.\n\n**Fix**: Update the parent version in `rest-client/pom.xml`:\n\n````xml path=rest-client/pom.xml mode=EDIT\n    &lt;parent&gt;\n        &lt;artifactId&gt;civv5-rest&lt;/artifactId&gt;\n        &lt;groupId&gt;com.goodwill.hdr&lt;/groupId&gt;\n        &lt;version&gt;5.0.2-SNAPHSHOT&lt;/version&gt;\n    &lt;/parent&gt;\n````\n\n**Additional Issues to Fix**:\n\n1. **Typo in version**: \&quot;SNAPHSHOT\&quot; should be \&quot;SNAPSHOT\&quot; in all POMs\n2. **Parent dependency**: The root POM references `hdr-common:1.0.45-SNAPSHOT` but the error shows it's looking for `1.0.42-SNAPSHOT`\n\nAfter fixing the parent version, you may also need to:\n- Ensure the `hdr-common` artifact is available in your Maven repository\n- Run `mvn clean install` from the root directory first to build the parent POM\n&quot;,&quot;workspace_file_chunks&quot;:[{&quot;char_start&quot;:&quot;0&quot;,&quot;char_end&quot;:&quot;838&quot;,&quot;blob_name&quot;:&quot;c653c2fb74fa2fb025f5f62dc150596404f53857e78e10b9dea6f011eb3995fd&quot;,&quot;file&quot;:{&quot;repoRoot&quot;:&quot;&quot;,&quot;pathName&quot;:&quot;pom.xml&quot;}},{&quot;char_start&quot;:&quot;1268&quot;,&quot;char_end&quot;:&quot;2108&quot;,&quot;blob_name&quot;:&quot;c653c2fb74fa2fb025f5f62dc150596404f53857e78e10b9dea6f011eb3995fd&quot;,&quot;file&quot;:{&quot;repoRoot&quot;:&quot;&quot;,&quot;pathName&quot;:&quot;pom.xml&quot;}},{&quot;char_start&quot;:&quot;2108&quot;,&quot;char_end&quot;:&quot;3044&quot;,&quot;blob_name&quot;:&quot;c653c2fb74fa2fb025f5f62dc150596404f53857e78e10b9dea6f011eb3995fd&quot;,&quot;file&quot;:{&quot;repoRoot&quot;:&quot;&quot;,&quot;pathName&quot;:&quot;pom.xml&quot;}},{&quot;char_start&quot;:&quot;3044&quot;,&quot;char_end&quot;:&quot;3747&quot;,&quot;blob_name&quot;:&quot;c653c2fb74fa2fb025f5f62dc150596404f53857e78e10b9dea6f011eb3995fd&quot;,&quot;file&quot;:{&quot;repoRoot&quot;:&quot;&quot;,&quot;pathName&quot;:&quot;pom.xml&quot;}},{&quot;char_start&quot;:&quot;3747&quot;,&quot;char_end&quot;:&quot;5338&quot;,&quot;blob_name&quot;:&quot;c653c2fb74fa2fb025f5f62dc150596404f53857e78e10b9dea6f011eb3995fd&quot;,&quot;file&quot;:{&quot;repoRoot&quot;:&quot;&quot;,&quot;pathName&quot;:&quot;pom.xml&quot;}},{&quot;char_start&quot;:&quot;5338&quot;,&quot;char_end&quot;:&quot;5385&quot;,&quot;blob_name&quot;:&quot;c653c2fb74fa2fb025f5f62dc150596404f53857e78e10b9dea6f011eb3995fd&quot;,&quot;file&quot;:{&quot;repoRoot&quot;:&quot;&quot;,&quot;pathName&quot;:&quot;pom.xml&quot;}},{&quot;char_start&quot;:&quot;0&quot;,&quot;char_end&quot;:&quot;879&quot;,&quot;blob_name&quot;:&quot;749ff3f1910b14e7003ba590834daf143ce7f59ee12d25daeadcc632288e3083&quot;,&quot;file&quot;:{&quot;repoRoot&quot;:&quot;&quot;,&quot;pathName&quot;:&quot;rest-client/pom.xml&quot;}},{&quot;char_start&quot;:&quot;879&quot;,&quot;char_end&quot;:&quot;1859&quot;,&quot;blob_name&quot;:&quot;749ff3f1910b14e7003ba590834daf143ce7f59ee12d25daeadcc632288e3083&quot;,&quot;file&quot;:{&quot;repoRoot&quot;:&quot;&quot;,&quot;pathName&quot;:&quot;rest-client/pom.xml&quot;}},{&quot;char_start&quot;:&quot;1859&quot;,&quot;char_end&quot;:&quot;2638&quot;,&quot;blob_name&quot;:&quot;749ff3f1910b14e7003ba590834daf143ce7f59ee12d25daeadcc632288e3083&quot;,&quot;file&quot;:{&quot;repoRoot&quot;:&quot;&quot;,&quot;pathName&quot;:&quot;rest-client/pom.xml&quot;}},{&quot;char_start&quot;:&quot;0&quot;,&quot;char_end&quot;:&quot;117&quot;,&quot;blob_name&quot;:&quot;5e44ef46a95f98109894d15e11f951e5fea8a9a8fd7be574d57457e40f736640&quot;,&quot;file&quot;:{&quot;repoRoot&quot;:&quot;&quot;,&quot;pathName&quot;:&quot;civ-v5/src/main/resources/application.yml&quot;}},{&quot;char_start&quot;:&quot;117&quot;,&quot;char_end&quot;:&quot;1071&quot;,&quot;blob_name&quot;:&quot;5e44ef46a95f98109894d15e11f951e5fea8a9a8fd7be574d57457e40f736640&quot;,&quot;file&quot;:{&quot;repoRoot&quot;:&quot;&quot;,&quot;pathName&quot;:&quot;civ-v5/src/main/resources/application.yml&quot;}},{&quot;char_start&quot;:&quot;1071&quot;,&quot;char_end&quot;:&quot;1840&quot;,&quot;blob_name&quot;:&quot;5e44ef46a95f98109894d15e11f951e5fea8a9a8fd7be574d57457e40f736640&quot;,&quot;file&quot;:{&quot;repoRoot&quot;:&quot;&quot;,&quot;pathName&quot;:&quot;civ-v5/src/main/resources/application.yml&quot;}},{&quot;char_start&quot;:&quot;0&quot;,&quot;char_end&quot;:&quot;745&quot;,&quot;blob_name&quot;:&quot;27d1fc2c490e48c075682d4d109bab6a555c090390573bee0733e9f11e207ab1&quot;,&quot;file&quot;:{&quot;repoRoot&quot;:&quot;&quot;,&quot;pathName&quot;:&quot;civ-v5/pom.xml&quot;}},{&quot;char_start&quot;:&quot;745&quot;,&quot;char_end&quot;:&quot;1803&quot;,&quot;blob_name&quot;:&quot;27d1fc2c490e48c075682d4d109bab6a555c090390573bee0733e9f11e207ab1&quot;,&quot;file&quot;:{&quot;repoRoot&quot;:&quot;&quot;,&quot;pathName&quot;:&quot;civ-v5/pom.xml&quot;}},{&quot;char_start&quot;:&quot;1803&quot;,&quot;char_end&quot;:&quot;2883&quot;,&quot;blob_name&quot;:&quot;27d1fc2c490e48c075682d4d109bab6a555c090390573bee0733e9f11e207ab1&quot;,&quot;file&quot;:{&quot;repoRoot&quot;:&quot;&quot;,&quot;pathName&quot;:&quot;civ-v5/pom.xml&quot;}},{&quot;char_start&quot;:&quot;2883&quot;,&quot;char_end&quot;:&quot;3845&quot;,&quot;blob_name&quot;:&quot;27d1fc2c490e48c075682d4d109bab6a555c090390573bee0733e9f11e207ab1&quot;,&quot;file&quot;:{&quot;repoRoot&quot;:&quot;&quot;,&quot;pathName&quot;:&quot;civ-v5/pom.xml&quot;}},{&quot;char_start&quot;:&quot;3845&quot;,&quot;char_end&quot;:&quot;4692&quot;,&quot;blob_name&quot;:&quot;27d1fc2c490e48c075682d4d109bab6a555c090390573bee0733e9f11e207ab1&quot;,&quot;file&quot;:{&quot;repoRoot&quot;:&quot;&quot;,&quot;pathName&quot;:&quot;civ-v5/pom.xml&quot;}},{&quot;char_start&quot;:&quot;0&quot;,&quot;char_end&quot;:&quot;501&quot;,&quot;blob_name&quot;:&quot;0603c58dee0b0ab8b286bea90fe660195b027de601cd9c911566bb21ce47fd2c&quot;,&quot;file&quot;:{&quot;repoRoot&quot;:&quot;&quot;,&quot;pathName&quot;:&quot;rest-server/pom.xml&quot;}},{&quot;char_start&quot;:&quot;501&quot;,&quot;char_end&quot;:&quot;1491&quot;,&quot;blob_name&quot;:&quot;0603c58dee0b0ab8b286bea90fe660195b027de601cd9c911566bb21ce47fd2c&quot;,&quot;file&quot;:{&quot;repoRoot&quot;:&quot;&quot;,&quot;pathName&quot;:&quot;rest-server/pom.xml&quot;}},{&quot;char_start&quot;:&quot;1491&quot;,&quot;char_end&quot;:&quot;2650&quot;,&quot;blob_name&quot;:&quot;0603c58dee0b0ab8b286bea90fe660195b027de601cd9c911566bb21ce47fd2c&quot;,&quot;file&quot;:{&quot;repoRoot&quot;:&quot;&quot;,&quot;pathName&quot;:&quot;rest-server/pom.xml&quot;}},{&quot;char_start&quot;:&quot;2650&quot;,&quot;char_end&quot;:&quot;3700&quot;,&quot;blob_name&quot;:&quot;0603c58dee0b0ab8b286bea90fe660195b027de601cd9c911566bb21ce47fd2c&quot;,&quot;file&quot;:{&quot;repoRoot&quot;:&quot;&quot;,&quot;pathName&quot;:&quot;rest-server/pom.xml&quot;}},{&quot;char_start&quot;:&quot;0&quot;,&quot;char_end&quot;:&quot;755&quot;,&quot;blob_name&quot;:&quot;0972d4b8961d462bf361fb93229bada6a4201aa59f01a66a8a3131e2837fd351&quot;,&quot;file&quot;:{&quot;repoRoot&quot;:&quot;&quot;,&quot;pathName&quot;:&quot;civ-v5/src/main/java/com/goodwill/Application.java&quot;}},{&quot;char_start&quot;:&quot;0&quot;,&quot;char_end&quot;:&quot;518&quot;,&quot;blob_name&quot;:&quot;c4a879f8dc65dcde5673200d83463e9372b5f4d53a48b877cb6c062ecbc35ed2&quot;,&quot;file&quot;:{&quot;repoRoot&quot;:&quot;&quot;,&quot;pathName&quot;:&quot;civ-v5/src/main/resources/licenseVerifyParam.properties&quot;}},{&quot;char_start&quot;:&quot;333&quot;,&quot;char_end&quot;:&quot;976&quot;,&quot;blob_name&quot;:&quot;14af312c3694b1ef45cbab744daacaaa841dfe6b3b9a4d5aa4a712baca04891f&quot;,&quot;file&quot;:{&quot;repoRoot&quot;:&quot;&quot;,&quot;pathName&quot;:&quot;civ-v5/src/main/java/com/goodwill/hdr/civ/handle/GlobalExceptionHandler.java&quot;}},{&quot;char_start&quot;:&quot;0&quot;,&quot;char_end&quot;:&quot;281&quot;,&quot;blob_name&quot;:&quot;015ac5ed7dffaf7b59a3de3f7ebe082aa91f6f2f4c72948cdbd1bedde32a1779&quot;,&quot;file&quot;:{&quot;repoRoot&quot;:&quot;&quot;,&quot;pathName&quot;:&quot;rest-server/src/main/resources/application.yml&quot;}},{&quot;char_start&quot;:&quot;783&quot;,&quot;char_end&quot;:&quot;1508&quot;,&quot;blob_name&quot;:&quot;add6508e1b6fce854cc49302a75ab43d791e40b188ba7309f6cfc45487f7454f&quot;,&quot;file&quot;:{&quot;repoRoot&quot;:&quot;&quot;,&quot;pathName&quot;:&quot;civ-v5/src/main/java/com/goodwill/hdr/civ/service/impl/VisitServiceImpl.java&quot;}},{&quot;char_start&quot;:&quot;87788&quot;,&quot;char_end&quot;:&quot;88496&quot;,&quot;blob_name&quot;:&quot;add6508e1b6fce854cc49302a75ab43d791e40b188ba7309f6cfc45487f7454f&quot;,&quot;file&quot;:{&quot;repoRoot&quot;:&quot;&quot;,&quot;pathName&quot;:&quot;civ-v5/src/main/java/com/goodwill/hdr/civ/service/impl/VisitServiceImpl.java&quot;}},{&quot;char_start&quot;:&quot;0&quot;,&quot;char_end&quot;:&quot;89&quot;,&quot;blob_name&quot;:&quot;43f3a34d89dc309a19d0b35dc45f36350cc37625f854e6592eda9c802a3fb2fa&quot;,&quot;file&quot;:{&quot;repoRoot&quot;:&quot;&quot;,&quot;pathName&quot;:&quot;rest-client/src/main/resources/application.yml&quot;}},{&quot;char_start&quot;:&quot;58553&quot;,&quot;char_end&quot;:&quot;59394&quot;,&quot;blob_name&quot;:&quot;6cf6d076cef5d63325522420213d430b8060d0ce0ca6bfcd3de017c62c5dbbe6&quot;,&quot;file&quot;:{&quot;repoRoot&quot;:&quot;&quot;,&quot;pathName&quot;:&quot;civ-v5/src/main/java/com/goodwill/hdr/civ/config/Config.java&quot;}},{&quot;char_start&quot;:&quot;64727&quot;,&quot;char_end&quot;:&quot;65690&quot;,&quot;blob_name&quot;:&quot;6cf6d076cef5d63325522420213d430b8060d0ce0ca6bfcd3de017c62c5dbbe6&quot;,&quot;file&quot;:{&quot;repoRoot&quot;:&quot;&quot;,&quot;pathName&quot;:&quot;civ-v5/src/main/java/com/goodwill/hdr/civ/config/Config.java&quot;}},{&quot;char_start&quot;:&quot;1100&quot;,&quot;char_end&quot;:&quot;1834&quot;,&quot;blob_name&quot;:&quot;48b995bdd53110a4946fdeb285af322ef8826fe667f2ac02122cafc1bda78605&quot;,&quot;file&quot;:{&quot;repoRoot&quot;:&quot;&quot;,&quot;pathName&quot;:&quot;civ-v5/src/main/java/com/goodwill/hdr/civ/service/impl/PatientListServiceImpl.java&quot;}},{&quot;char_start&quot;:&quot;30213&quot;,&quot;char_end&quot;:&quot;31094&quot;,&quot;blob_name&quot;:&quot;7eb24b678e473840ae561160c2cc928c4d92eb11c3e2e6b642daa85c274b227e&quot;,&quot;file&quot;:{&quot;repoRoot&quot;:&quot;&quot;,&quot;pathName&quot;:&quot;civ-v5/src/main/java/com/goodwill/hdr/civ/utils/Utils.java&quot;}},{&quot;char_start&quot;:&quot;112879&quot;,&quot;char_end&quot;:&quot;113586&quot;,&quot;blob_name&quot;:&quot;e0c4977a23e23b38550997e6a599dfb3f602f96adc89869473d122af54b7c293&quot;,&quot;file&quot;:{&quot;repoRoot&quot;:&quot;&quot;,&quot;pathName&quot;:&quot;civ-v5/src/main/java/com/goodwill/hdr/civ/service/impl/PowerServiceImpl.java&quot;}},{&quot;char_start&quot;:&quot;43&quot;,&quot;char_end&quot;:&quot;778&quot;,&quot;blob_name&quot;:&quot;a7413a2d9c0141f34566e35be76d2e10e2e14f2f6bb6cc71c811cfd23b089e3c&quot;,&quot;file&quot;:{&quot;repoRoot&quot;:&quot;&quot;,&quot;pathName&quot;:&quot;civ-v5/src/main/java/com/goodwill/hdr/civ/service/impl/DiagnoseServiceImpl.java&quot;}},{&quot;char_start&quot;:&quot;810&quot;,&quot;char_end&quot;:&quot;1484&quot;,&quot;blob_name&quot;:&quot;080406bf0030926cc66b5f9894ffc4ce57b811aaa535eb1583d87845497bc715&quot;,&quot;file&quot;:{&quot;repoRoot&quot;:&quot;&quot;,&quot;pathName&quot;:&quot;civ-v5/src/main/java/com/goodwill/hdr/civ/service/impl/MedicalServiceImpl.java&quot;}}]},{&quot;status&quot;:&quot;success&quot;,&quot;request_id&quot;:&quot;88cab523-764f-46b1-8f49-b2ae8e803c91&quot;,&quot;request_message&quot;:&quot;中文解释&quot;,&quot;rich_text_json_repr&quot;:{&quot;type&quot;:&quot;doc&quot;,&quot;content&quot;:[{&quot;type&quot;:&quot;paragraph&quot;,&quot;content&quot;:[{&quot;type&quot;:&quot;text&quot;,&quot;text&quot;:&quot;中文解释&quot;}]}]},&quot;mentioned_items&quot;:[],&quot;structured_output_nodes&quot;:[{&quot;id&quot;:1,&quot;type&quot;:0,&quot;content&quot;:&quot;Maven构建失败是因为无法解析父POM。你的项目结构中存在版本不匹配的问题：\n\n**问题**：`rest-client/pom.xml`引用的父版本是`5.0.1-SNAPHSHOT`，但根目录的`pom.xml`版本是`5.0.2-SNAPHSHOT`。\n\n**解决方案**：更新`rest-client/pom.xml`中的父版本：\n\n&lt;augment_code_snippet path=\&quot;rest-client/pom.xml\&quot; mode=\&quot;EDIT\&quot;&gt;\n```xml\n    &lt;parent&gt;\n        &lt;artifactId&gt;civv5-rest&lt;/artifactId&gt;\n        &lt;groupId&gt;com.goodwill.hdr&lt;/groupId&gt;\n        &lt;version&gt;5.0.2-SNAPHSHOT&lt;/version&gt;\n    &lt;/parent&gt;\n```\n&lt;/augment_code_snippet&gt;\n\n**其他需要修复的问题**：\n\n1. **版本拼写错误**：所有POM文件中的\&quot;SNAPHSHOT\&quot;应该改为\&quot;SNAPSHOT\&quot;\n2. **父依赖问题**：根POM引用的是`hdr-common:1.0.45-SNAPSHOT`，但错误显示它在寻找`1.0.42-SNAPSHOT`\n\n修复父版本后，你可能还需要：\n- 确保`hdr-common`构件在你的Maven仓库中可用\n- 先从根目录运行`mvn clean install`来构建父POM\n&quot;},{&quot;id&quot;:2,&quot;type&quot;:2,&quot;content&quot;:&quot;&quot;},{&quot;id&quot;:3,&quot;type&quot;:3,&quot;content&quot;:&quot;civ-v5/src/main/java/com/goodwill/hdr/civ/enums/commonModule/LianZhongDmrEnum.java:0-796\nciv-v5/src/main/resources/application.yml:0-117\nciv-v5/src/main/resources/application.yml:117-1071\nciv-v5/src/main/resources/application.yml:1071-1840\nciv-v5/src/main/java/com/goodwill/hdr/civ/service/impl/EncryptService.java:0-465\nciv-v5/src/main/java/com/goodwill/hdr/civ/service/impl/EncryptService.java:6749-7556\nciv-v5/src/main/java/com/goodwill/hdr/civ/enums/commonModule/SysCodeEnum.java:0-867\nrest-server/src/main/java/com/goodwill/hdr/rest/server/enums/JhdcpServerCode.java:0-599\nciv-v5/src/main/java/com/goodwill/Application.java:0-755\nrest-client/src/main/java/com/goodwill/hdr/rest/client/enums/JhdcpServerCode.java:0-780\nciv-v5/src/main/java/com/goodwill/hdr/civ/utils/Utils.java:14618-15357\nciv-v5/src/main/java/com/goodwill/hdr/civ/utils/Utils.java:29775-30213\nciv-v5/src/main/java/com/goodwill/hdr/civ/utils/EMRUtils.java:0-670\nciv-v5/src/main/java/com/goodwill/hdr/civ/service/impl/MedicalRecordServiceImpl.java:54726-55549\nciv-v5/src/main/resources/mapper/SicknessMapper.xml:0-691\nciv-v5/src/main/java/com/goodwill/hdr/civ/controller/DictHbaseAction.java:16038-16869\nciv-v5/src/main/resources/mapper/SysFiledUserMapper.xml:0-713\nciv-v5/src/main/resources/mapper/PageConfigMapper.xml:0-602\nciv-v5/src/main/java/com/goodwill/hdr/civ/utils/GenUtils.java:900-1689\nciv-v5/src/main/java/com/goodwill/hdr/civ/service/impl/VisitServiceImpl.java:87788-88496\nciv-v5/src/main/java/com/goodwill/hdr/civ/service/impl/VisitServiceImpl.java:90832-91458\nciv-v5/src/main/java/com/goodwill/hdr/civ/service/impl/CurrentViewServiceImpl.java:2613-3277\nciv-v5/src/main/resources/mapper/LogRecordMapper.xml:0-817\nciv-v5/src/main/java/com/goodwill/hdr/civ/service/impl/CommonURLServiceImpl.java:13475-14491\nciv-v5/src/main/java/com/goodwill/hdr/civ/controller/URLCommonAction.java:5410-6251\nciv-v5/src/main/java/com/goodwill/hdr/civ/service/impl/PatientListServiceImpl.java:1100-1834\nciv-v5/src/main/resources/mapper/SpecialtyDeptMapper.xml:0-666\nciv-v5/src/main/java/com/goodwill/hdr/civ/controller/SingleLoginAction.java:479-1179\nciv-v5/src/main/java/com/goodwill/hdr/civ/mapper/HdrMdPlatformDictItemMapper.java:0-732\nciv-v5/src/main/java/com/goodwill/hdr/civ/handle/GlobalExceptionHandler.java:333-976\nciv-v5/src/main/java/com/goodwill/hdr/civ/controller/OrderCloseLoopAction.java:466-1275&quot;}],&quot;seen_state&quot;:&quot;unseen&quot;,&quot;structured_request_nodes&quot;:[{&quot;id&quot;:0,&quot;type&quot;:0,&quot;text_node&quot;:{&quot;content&quot;:&quot;中文解释&quot;}},{&quot;id&quot;:1,&quot;type&quot;:4,&quot;ide_state_node&quot;:{&quot;workspaceFolders&quot;:[{&quot;repositoryRoot&quot;:&quot;/Users/<USER>/jhProject/civ-5.2&quot;,&quot;folderRoot&quot;:&quot;/Users/<USER>/jhProject/civ-5.2&quot;}],&quot;workspaceFoldersUnchanged&quot;:false}}],&quot;timestamp&quot;:&quot;2025-07-28T08:55:20.977Z&quot;,&quot;response_text&quot;:&quot;Maven构建失败是因为无法解析父POM。你的项目结构中存在版本不匹配的问题：\n\n**问题**：`rest-client/pom.xml`引用的父版本是`5.0.1-SNAPHSHOT`，但根目录的`pom.xml`版本是`5.0.2-SNAPHSHOT`。\n\n**解决方案**：更新`rest-client/pom.xml`中的父版本：\n\n````xml path=rest-client/pom.xml mode=EDIT\n    &lt;parent&gt;\n        &lt;artifactId&gt;civv5-rest&lt;/artifactId&gt;\n        &lt;groupId&gt;com.goodwill.hdr&lt;/groupId&gt;\n        &lt;version&gt;5.0.2-SNAPHSHOT&lt;/version&gt;\n    &lt;/parent&gt;\n````\n\n**其他需要修复的问题**：\n\n1. **版本拼写错误**：所有POM文件中的\&quot;SNAPHSHOT\&quot;应该改为\&quot;SNAPSHOT\&quot;\n2. **父依赖问题**：根POM引用的是`hdr-common:1.0.45-SNAPSHOT`，但错误显示它在寻找`1.0.42-SNAPSHOT`\n\n修复父版本后，你可能还需要：\n- 确保`hdr-common`构件在你的Maven仓库中可用\n- 先从根目录运行`mvn clean install`来构建父POM\n&quot;,&quot;workspace_file_chunks&quot;:[{&quot;char_start&quot;:&quot;0&quot;,&quot;char_end&quot;:&quot;796&quot;,&quot;blob_name&quot;:&quot;49e7d9b8a13c82b0b672b89a838d08642beb2a4052ae59f447a47ec144284b76&quot;,&quot;file&quot;:{&quot;repoRoot&quot;:&quot;&quot;,&quot;pathName&quot;:&quot;civ-v5/src/main/java/com/goodwill/hdr/civ/enums/commonModule/LianZhongDmrEnum.java&quot;}},{&quot;char_start&quot;:&quot;0&quot;,&quot;char_end&quot;:&quot;117&quot;,&quot;blob_name&quot;:&quot;5e44ef46a95f98109894d15e11f951e5fea8a9a8fd7be574d57457e40f736640&quot;,&quot;file&quot;:{&quot;repoRoot&quot;:&quot;&quot;,&quot;pathName&quot;:&quot;civ-v5/src/main/resources/application.yml&quot;}},{&quot;char_start&quot;:&quot;117&quot;,&quot;char_end&quot;:&quot;1071&quot;,&quot;blob_name&quot;:&quot;5e44ef46a95f98109894d15e11f951e5fea8a9a8fd7be574d57457e40f736640&quot;,&quot;file&quot;:{&quot;repoRoot&quot;:&quot;&quot;,&quot;pathName&quot;:&quot;civ-v5/src/main/resources/application.yml&quot;}},{&quot;char_start&quot;:&quot;1071&quot;,&quot;char_end&quot;:&quot;1840&quot;,&quot;blob_name&quot;:&quot;5e44ef46a95f98109894d15e11f951e5fea8a9a8fd7be574d57457e40f736640&quot;,&quot;file&quot;:{&quot;repoRoot&quot;:&quot;&quot;,&quot;pathName&quot;:&quot;civ-v5/src/main/resources/application.yml&quot;}},{&quot;char_start&quot;:&quot;0&quot;,&quot;char_end&quot;:&quot;465&quot;,&quot;blob_name&quot;:&quot;12d00df75059140c53a30bb36d558a25849510a91db3ff624c7e2cbb25ca765f&quot;,&quot;file&quot;:{&quot;repoRoot&quot;:&quot;&quot;,&quot;pathName&quot;:&quot;civ-v5/src/main/java/com/goodwill/hdr/civ/service/impl/EncryptService.java&quot;}},{&quot;char_start&quot;:&quot;6749&quot;,&quot;char_end&quot;:&quot;7556&quot;,&quot;blob_name&quot;:&quot;12d00df75059140c53a30bb36d558a25849510a91db3ff624c7e2cbb25ca765f&quot;,&quot;file&quot;:{&quot;repoRoot&quot;:&quot;&quot;,&quot;pathName&quot;:&quot;civ-v5/src/main/java/com/goodwill/hdr/civ/service/impl/EncryptService.java&quot;}},{&quot;char_start&quot;:&quot;0&quot;,&quot;char_end&quot;:&quot;867&quot;,&quot;blob_name&quot;:&quot;7b82295cc552d2215d7a79b2c6b6c33666c70be9b8f2d7a26c2186e86cf3260a&quot;,&quot;file&quot;:{&quot;repoRoot&quot;:&quot;&quot;,&quot;pathName&quot;:&quot;civ-v5/src/main/java/com/goodwill/hdr/civ/enums/commonModule/SysCodeEnum.java&quot;}},{&quot;char_start&quot;:&quot;0&quot;,&quot;char_end&quot;:&quot;599&quot;,&quot;blob_name&quot;:&quot;fe8fb788c67d8549a9ac0f7d6921d704a8434039a3b9a57fef97ed22b97b9d72&quot;,&quot;file&quot;:{&quot;repoRoot&quot;:&quot;&quot;,&quot;pathName&quot;:&quot;rest-server/src/main/java/com/goodwill/hdr/rest/server/enums/JhdcpServerCode.java&quot;}},{&quot;char_start&quot;:&quot;0&quot;,&quot;char_end&quot;:&quot;755&quot;,&quot;blob_name&quot;:&quot;0972d4b8961d462bf361fb93229bada6a4201aa59f01a66a8a3131e2837fd351&quot;,&quot;file&quot;:{&quot;repoRoot&quot;:&quot;&quot;,&quot;pathName&quot;:&quot;civ-v5/src/main/java/com/goodwill/Application.java&quot;}},{&quot;char_start&quot;:&quot;0&quot;,&quot;char_end&quot;:&quot;780&quot;,&quot;blob_name&quot;:&quot;5ffcf22660396d4d3ed604221198159302173959ea32ccb10d4d999c6fb0fbae&quot;,&quot;file&quot;:{&quot;repoRoot&quot;:&quot;&quot;,&quot;pathName&quot;:&quot;rest-client/src/main/java/com/goodwill/hdr/rest/client/enums/JhdcpServerCode.java&quot;}},{&quot;char_start&quot;:&quot;14618&quot;,&quot;char_end&quot;:&quot;15357&quot;,&quot;blob_name&quot;:&quot;7eb24b678e473840ae561160c2cc928c4d92eb11c3e2e6b642daa85c274b227e&quot;,&quot;file&quot;:{&quot;repoRoot&quot;:&quot;&quot;,&quot;pathName&quot;:&quot;civ-v5/src/main/java/com/goodwill/hdr/civ/utils/Utils.java&quot;}},{&quot;char_start&quot;:&quot;29775&quot;,&quot;char_end&quot;:&quot;30213&quot;,&quot;blob_name&quot;:&quot;7eb24b678e473840ae561160c2cc928c4d92eb11c3e2e6b642daa85c274b227e&quot;,&quot;file&quot;:{&quot;repoRoot&quot;:&quot;&quot;,&quot;pathName&quot;:&quot;civ-v5/src/main/java/com/goodwill/hdr/civ/utils/Utils.java&quot;}},{&quot;char_start&quot;:&quot;0&quot;,&quot;char_end&quot;:&quot;670&quot;,&quot;blob_name&quot;:&quot;2eb32db70073ea7419bb1cb2b04f25e3b6c6ed8ae20c42a8c8f0a3b7afb7d96f&quot;,&quot;file&quot;:{&quot;repoRoot&quot;:&quot;&quot;,&quot;pathName&quot;:&quot;civ-v5/src/main/java/com/goodwill/hdr/civ/utils/EMRUtils.java&quot;}},{&quot;char_start&quot;:&quot;54726&quot;,&quot;char_end&quot;:&quot;55549&quot;,&quot;blob_name&quot;:&quot;1bfcc1519acc90a509727578d1763ec9d49fada7e58c8a2e75a6692e1e4f7e3a&quot;,&quot;file&quot;:{&quot;repoRoot&quot;:&quot;&quot;,&quot;pathName&quot;:&quot;civ-v5/src/main/java/com/goodwill/hdr/civ/service/impl/MedicalRecordServiceImpl.java&quot;}},{&quot;char_start&quot;:&quot;0&quot;,&quot;char_end&quot;:&quot;691&quot;,&quot;blob_name&quot;:&quot;437ee7ff178e937b6f73ee60bacdc5c6f49d35429c4990f9f15afb54b6418dff&quot;,&quot;file&quot;:{&quot;repoRoot&quot;:&quot;&quot;,&quot;pathName&quot;:&quot;civ-v5/src/main/resources/mapper/SicknessMapper.xml&quot;}},{&quot;char_start&quot;:&quot;16038&quot;,&quot;char_end&quot;:&quot;16869&quot;,&quot;blob_name&quot;:&quot;410d8ec9ec956d432a94ac609fa4c34711295964663e939337bdd649001708cf&quot;,&quot;file&quot;:{&quot;repoRoot&quot;:&quot;&quot;,&quot;pathName&quot;:&quot;civ-v5/src/main/java/com/goodwill/hdr/civ/controller/DictHbaseAction.java&quot;}},{&quot;char_start&quot;:&quot;0&quot;,&quot;char_end&quot;:&quot;713&quot;,&quot;blob_name&quot;:&quot;9eae5942765f5b3af4cec9595c7f7560bbc3a4633a6176121e327c765d9dcd8a&quot;,&quot;file&quot;:{&quot;repoRoot&quot;:&quot;&quot;,&quot;pathName&quot;:&quot;civ-v5/src/main/resources/mapper/SysFiledUserMapper.xml&quot;}},{&quot;char_start&quot;:&quot;0&quot;,&quot;char_end&quot;:&quot;602&quot;,&quot;blob_name&quot;:&quot;3927ddcda7e2c40bd668dd1a00461512925cafe203f24c0de971385d8f284255&quot;,&quot;file&quot;:{&quot;repoRoot&quot;:&quot;&quot;,&quot;pathName&quot;:&quot;civ-v5/src/main/resources/mapper/PageConfigMapper.xml&quot;}},{&quot;char_start&quot;:&quot;900&quot;,&quot;char_end&quot;:&quot;1689&quot;,&quot;blob_name&quot;:&quot;9009675763435bfd59d39465b0041cf1ddf856f84fd5b5665cd4891e131db633&quot;,&quot;file&quot;:{&quot;repoRoot&quot;:&quot;&quot;,&quot;pathName&quot;:&quot;civ-v5/src/main/java/com/goodwill/hdr/civ/utils/GenUtils.java&quot;}},{&quot;char_start&quot;:&quot;87788&quot;,&quot;char_end&quot;:&quot;88496&quot;,&quot;blob_name&quot;:&quot;add6508e1b6fce854cc49302a75ab43d791e40b188ba7309f6cfc45487f7454f&quot;,&quot;file&quot;:{&quot;repoRoot&quot;:&quot;&quot;,&quot;pathName&quot;:&quot;civ-v5/src/main/java/com/goodwill/hdr/civ/service/impl/VisitServiceImpl.java&quot;}},{&quot;char_start&quot;:&quot;90832&quot;,&quot;char_end&quot;:&quot;91458&quot;,&quot;blob_name&quot;:&quot;add6508e1b6fce854cc49302a75ab43d791e40b188ba7309f6cfc45487f7454f&quot;,&quot;file&quot;:{&quot;repoRoot&quot;:&quot;&quot;,&quot;pathName&quot;:&quot;civ-v5/src/main/java/com/goodwill/hdr/civ/service/impl/VisitServiceImpl.java&quot;}},{&quot;char_start&quot;:&quot;2613&quot;,&quot;char_end&quot;:&quot;3277&quot;,&quot;blob_name&quot;:&quot;dedbecb6a74c35cbd86eac334d9770ef19cf6465170161ae14cc06057d7c079d&quot;,&quot;file&quot;:{&quot;repoRoot&quot;:&quot;&quot;,&quot;pathName&quot;:&quot;civ-v5/src/main/java/com/goodwill/hdr/civ/service/impl/CurrentViewServiceImpl.java&quot;}},{&quot;char_start&quot;:&quot;0&quot;,&quot;char_end&quot;:&quot;817&quot;,&quot;blob_name&quot;:&quot;8568e70de219a72840f75108634241cd8f2fde87911cd9eced3e492eba2b4592&quot;,&quot;file&quot;:{&quot;repoRoot&quot;:&quot;&quot;,&quot;pathName&quot;:&quot;civ-v5/src/main/resources/mapper/LogRecordMapper.xml&quot;}},{&quot;char_start&quot;:&quot;13475&quot;,&quot;char_end&quot;:&quot;14491&quot;,&quot;blob_name&quot;:&quot;c5ba7b3b8e1fd716475cb941e532a1c52a562415e1a2fc819d453744fb30a2bf&quot;,&quot;file&quot;:{&quot;repoRoot&quot;:&quot;&quot;,&quot;pathName&quot;:&quot;civ-v5/src/main/java/com/goodwill/hdr/civ/service/impl/CommonURLServiceImpl.java&quot;}},{&quot;char_start&quot;:&quot;5410&quot;,&quot;char_end&quot;:&quot;6251&quot;,&quot;blob_name&quot;:&quot;4ea1e560f5d9f853d5e3cb89958c681217204feaf83703031ab748122aa93e63&quot;,&quot;file&quot;:{&quot;repoRoot&quot;:&quot;&quot;,&quot;pathName&quot;:&quot;civ-v5/src/main/java/com/goodwill/hdr/civ/controller/URLCommonAction.java&quot;}},{&quot;char_start&quot;:&quot;1100&quot;,&quot;char_end&quot;:&quot;1834&quot;,&quot;blob_name&quot;:&quot;48b995bdd53110a4946fdeb285af322ef8826fe667f2ac02122cafc1bda78605&quot;,&quot;file&quot;:{&quot;repoRoot&quot;:&quot;&quot;,&quot;pathName&quot;:&quot;civ-v5/src/main/java/com/goodwill/hdr/civ/service/impl/PatientListServiceImpl.java&quot;}},{&quot;char_start&quot;:&quot;0&quot;,&quot;char_end&quot;:&quot;666&quot;,&quot;blob_name&quot;:&quot;177547f5d1c05ce8eff49e71353986ba23734df51b7fa5066491920e91b74a1f&quot;,&quot;file&quot;:{&quot;repoRoot&quot;:&quot;&quot;,&quot;pathName&quot;:&quot;civ-v5/src/main/resources/mapper/SpecialtyDeptMapper.xml&quot;}},{&quot;char_start&quot;:&quot;479&quot;,&quot;char_end&quot;:&quot;1179&quot;,&quot;blob_name&quot;:&quot;5cf07b767b9707868e18f7479bfa1c7bc524d2e84b5d8e86da8d594a602cc9a7&quot;,&quot;file&quot;:{&quot;repoRoot&quot;:&quot;&quot;,&quot;pathName&quot;:&quot;civ-v5/src/main/java/com/goodwill/hdr/civ/controller/SingleLoginAction.java&quot;}},{&quot;char_start&quot;:&quot;0&quot;,&quot;char_end&quot;:&quot;732&quot;,&quot;blob_name&quot;:&quot;e2b63d3326c94193e57788cf5a1a0ed8b3e05565eed2db6c678d84e4712a82fc&quot;,&quot;file&quot;:{&quot;repoRoot&quot;:&quot;&quot;,&quot;pathName&quot;:&quot;civ-v5/src/main/java/com/goodwill/hdr/civ/mapper/HdrMdPlatformDictItemMapper.java&quot;}},{&quot;char_start&quot;:&quot;333&quot;,&quot;char_end&quot;:&quot;976&quot;,&quot;blob_name&quot;:&quot;14af312c3694b1ef45cbab744daacaaa841dfe6b3b9a4d5aa4a712baca04891f&quot;,&quot;file&quot;:{&quot;repoRoot&quot;:&quot;&quot;,&quot;pathName&quot;:&quot;civ-v5/src/main/java/com/goodwill/hdr/civ/handle/GlobalExceptionHandler.java&quot;}},{&quot;char_start&quot;:&quot;466&quot;,&quot;char_end&quot;:&quot;1275&quot;,&quot;blob_name&quot;:&quot;4992061045afcbf65bd23289d60fa1005a78cf43b853dea0595db2dcbc2289ad&quot;,&quot;file&quot;:{&quot;repoRoot&quot;:&quot;&quot;,&quot;pathName&quot;:&quot;civ-v5/src/main/java/com/goodwill/hdr/civ/controller/OrderCloseLoopAction.java&quot;}}]}],&quot;feedbackStates&quot;:{&quot;temp-fe-b52e48cc-d892-46c0-9278-2781afe57b6b&quot;:{&quot;selectedRating&quot;:0,&quot;feedbackNote&quot;:&quot;&quot;},&quot;temp-fe-31a1e558-5810-41a9-9890-20df09301714&quot;:{&quot;selectedRating&quot;:0,&quot;feedbackNote&quot;:&quot;&quot;}},&quot;toolUseStates&quot;:{},&quot;draftExchange&quot;:{&quot;request_message&quot;:&quot;&quot;,&quot;rich_text_json_repr&quot;:{&quot;type&quot;:&quot;doc&quot;,&quot;content&quot;:[{&quot;type&quot;:&quot;paragraph&quot;}]},&quot;mentioned_items&quot;:[],&quot;status&quot;:&quot;draft&quot;},&quot;requestIds&quot;:[],&quot;isPinned&quot;:false,&quot;isShareable&quot;:true,&quot;extraData&quot;:{&quot;hasDirtyEdits&quot;:false},&quot;personaType&quot;:0,&quot;rootTaskUuid&quot;:&quot;c326fae5-11ab-4e2d-93a6-ea32a32fcccd&quot;},&quot;9eb008c0-88e9-4b9f-bc5e-9bb4dff09d9b&quot;:{&quot;id&quot;:&quot;9eb008c0-88e9-4b9f-bc5e-9bb4dff09d9b&quot;,&quot;createdAtIso&quot;:&quot;2025-07-28T09:17:43.196Z&quot;,&quot;lastInteractedAtIso&quot;:&quot;2025-07-28T09:17:43.196Z&quot;,&quot;chatHistory&quot;:[],&quot;feedbackStates&quot;:{},&quot;toolUseStates&quot;:{},&quot;requestIds&quot;:[],&quot;isPinned&quot;:false,&quot;isShareable&quot;:false,&quot;extraData&quot;:{&quot;hasDirtyEdits&quot;:false},&quot;personaType&quot;:0}},&quot;agentExecutionMode&quot;:&quot;manual&quot;,&quot;isPanelCollapsed&quot;:true,&quot;displayedAnnouncements&quot;:[],&quot;sortConversationsBy&quot;:&quot;lastMessageTimestamp&quot;,&quot;sendMode&quot;:&quot;send&quot;}" />
      </map>
    </option>
  </component>
</project>