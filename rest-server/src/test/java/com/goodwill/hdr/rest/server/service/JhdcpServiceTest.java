package com.goodwill.hdr.rest.server.service;

import com.goodwill.hdr.rest.server.enums.JhdcpServerCode;
import com.goodwill.hdr.rest.server.transmission.JhdcpHttpSender;
import com.goodwill.hdr.rest.server.vo.client.JhdcpCondition;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.AutoConfigureOrder;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit.jupiter.SpringExtension;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.util.Assert;

import java.util.ArrayList;
import java.util.List;

@SpringBootTest
@ExtendWith(SpringExtension.class)
public class JhdcpServiceTest {

    @Autowired
    JhdcpService jhdcpService;

    @Autowired
    JhdcpHttpSender httpSender;

    @Test
    public void getPageDataJsonTest() {

        String authorization = httpSender.getAuthorizationToken();
        System.out.println(authorization);
//        List<JhdcpCondition> conditionList = new ArrayList<>();
//        conditionList.add(new JhdcpCondition("HIS_PAT_ID", "eq", "1000"));
//        String json = jhdcpService.getDataPageJson(conditionList, null, JhdcpServerCode.OUT_VISIT.getCode(), 1, 10);
//        System.out.println(json);
//
//        Assert.hasText(json, "未获取到page");
//        List<JhdcpCondition> conditionList2 = new ArrayList<>();
//        conditionList2.add(new JhdcpCondition("HIS_PAT_ID", "eq", "**********"));
//        String json2 = jhdcpService.getDataPageJson(conditionList2, null, JhdcpServerCode.IN_VISIT.getCode(), 1, 10);
//        System.out.println(json2);
//
//        Assert.hasText(json2, "未获取到page");
//        List<JhdcpCondition> conditionList3 = new ArrayList<>();
//        conditionList3.add(new JhdcpCondition("HIS_PAT_ID", "eq", "**********"));
//        String json3 = jhdcpService.getDataPageJson(conditionList3, null, JhdcpServerCode.PATIENT_INFO.getCode(), 1, 10);
//        System.out.println(json3);
//        Assert.hasText(json3, "未获取到page");
    }

}
