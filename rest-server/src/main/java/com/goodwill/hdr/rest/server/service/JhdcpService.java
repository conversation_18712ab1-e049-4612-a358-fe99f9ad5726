package com.goodwill.hdr.rest.server.service;

import com.goodwill.hdr.rest.server.transmission.JhdcpHttpSender;
import com.goodwill.hdr.rest.server.transmission.JhdcpTransProperties;
import com.goodwill.hdr.rest.server.vo.client.JhdcpCondition;
import com.goodwill.hdr.rest.server.vo.client.JhdcpOrder;
import com.goodwill.hdr.rest.server.vo.jhdcp.JhdcpPageRequestVo;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class JhdcpService {
    private final JhdcpHttpSender httpSender;
    private final JhdcpTransProperties jhdcpTransProperties;


    public JhdcpService(JhdcpHttpSender httpSender, JhdcpTransProperties jhdcpTransProperties) {
        this.httpSender = httpSender;
        this.jhdcpTransProperties = jhdcpTransProperties;
    }

    public String getDataPageJson(List<JhdcpCondition> jhdcpConditionList,
                                  List<JhdcpOrder> orderList, String serverCode, Integer pageNo, Integer pageSize) {

        JhdcpPageRequestVo jhdcpPageRequestVo = new JhdcpPageRequestVo();
        jhdcpPageRequestVo.setPageNo(pageNo);
        jhdcpPageRequestVo.setPageSize(pageSize);
        jhdcpPageRequestVo.setCondition(jhdcpConditionList);
        jhdcpPageRequestVo.setOrders(orderList);
        jhdcpPageRequestVo.setSysCode(jhdcpTransProperties.getClientId());
        jhdcpPageRequestVo.setServerCode(serverCode);
        String authorization = httpSender.getAuthorizationToken();
        String json = httpSender.getDataPageJson(authorization, jhdcpPageRequestVo);
        return json;

    }

}
