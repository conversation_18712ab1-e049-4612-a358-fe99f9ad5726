package com.goodwill.hdr.rest.server.controller;

import com.goodwill.hdr.rest.server.service.JhdcpService;
import com.goodwill.hdr.rest.server.vo.client.JhdcpCondition;
import com.goodwill.hdr.rest.server.vo.client.JhdcpOrder;
import com.goodwill.hdr.rest.server.vo.jhdcp.JhdcpPageRequestVo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;


@RestController
@RequestMapping("/jhdcp")

public class JhdcpController {
    private final JhdcpService jhdcpService;
    private static Logger log = LoggerFactory.getLogger(JhdcpController.class);

    public JhdcpController(JhdcpService jhdcpService) {
        this.jhdcpService = jhdcpService;
    }


    @PostMapping("/dataPageJson")
    public String getDataPageJson(@RequestBody JhdcpPageRequestVo jhdcpPageRequestVo) {
        log.info("查询中台数据传参:" + jhdcpPageRequestVo.toString());
        List<JhdcpCondition> jhdcpConditionList = jhdcpPageRequestVo.getCondition();
        List<JhdcpOrder> orderList = jhdcpPageRequestVo.getOrders();
        String serverCode = jhdcpPageRequestVo.getServerCode();
        Integer pageNo = jhdcpPageRequestVo.getPageNo();
        Integer pageSize = jhdcpPageRequestVo.getPageSize();
        String json = jhdcpService.getDataPageJson(jhdcpConditionList, orderList, serverCode, pageNo, pageSize);
        log.info("查询中台数据返回:" + json);
        return json;
    }


}
