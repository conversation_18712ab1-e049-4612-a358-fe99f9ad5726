package com.goodwill.hdr.rest.server.vo.jhdcp;

import com.goodwill.hdr.rest.server.vo.client.JhdcpCondition;
import com.goodwill.hdr.rest.server.vo.client.JhdcpOrder;

import java.util.List;

/**
 * 从数据中台查询数据的请求模型
 *
 * <AUTHOR>
 * @since 0.0.1
 */
public class JhdcpPageRequestVo {
    /**
     * 查询条件集合
     */
    private List<JhdcpCondition> condition;
    /**
     * 排序条件集合
     */
    private List<JhdcpOrder> orders;
    /**
     * 分页条数
     */
    private Integer pageSize;
    private Integer pageNo;
    /**
     * 服务编码
     */
    private String serverCode;
    /**
     * 系统编码
     */
    private String sysCode;

    public List<JhdcpCondition> getCondition() {
        return condition;
    }

    public void setCondition(List<JhdcpCondition> condition) {
        this.condition = condition;
    }


    public Integer getPageSize() {
        return pageSize;
    }

    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }

    public Integer getPageNo() {
        return pageNo;
    }

    public void setPageNo(Integer pageNo) {
        this.pageNo = pageNo;
    }

    public String getServerCode() {
        return serverCode;
    }

    public void setServerCode(String serverCode) {
        this.serverCode = serverCode;
    }

    public String getSysCode() {
        return sysCode;
    }

    public void setSysCode(String sysCode) {
        this.sysCode = sysCode;
    }

    public List<JhdcpOrder> getOrders() {
        return orders;
    }

    public void setOrders(List<JhdcpOrder> orders) {
        this.orders = orders;
    }

    @Override
    public String toString() {
        return "JhdcpPageRequestVo{" +
                "condition=" + condition +
                ", orders=" + orders +
                ", pageSize=" + pageSize +
                ", pageNo=" + pageNo +
                ", serverCode='" + serverCode + '\'' +
                ", sysCode='" + sysCode + '\'' +
                '}';
    }
}
