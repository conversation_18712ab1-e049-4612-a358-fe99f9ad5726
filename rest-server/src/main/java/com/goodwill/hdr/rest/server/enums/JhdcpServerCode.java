package com.goodwill.hdr.rest.server.enums;

/**
 * 嘉和中台服务编码
 *
 * <AUTHOR>
 * @since 0.0.1
 */
public enum JhdcpServerCode {

    PATIENT_INFO("JHIDS-APS-PAT-001", "患者基本信息"),
    OUT_VISIT("JHIDS-APS-OHR-002", "门急诊就诊列表"),
    IN_VISIT("JHIDS-APS-IHR-003", "住院就诊列表");
    private final String code;
    private final String description;

    JhdcpServerCode(String code, String description) {
        this.code = code;
        this.description = description;
    }

    public String getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }
}
