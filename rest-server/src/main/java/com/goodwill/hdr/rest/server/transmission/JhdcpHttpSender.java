package com.goodwill.hdr.rest.server.transmission;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.goodwill.hdr.rest.server.vo.jhdcp.JhdcpAuthResponseVo;
import com.goodwill.hdr.rest.server.vo.jhdcp.JhdcpPageRequestVo;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.http.converter.StringHttpMessageConverter;
import org.springframework.stereotype.Service;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestClientException;
import org.springframework.web.client.RestTemplate;

import java.nio.charset.StandardCharsets;

@Service
public class JhdcpHttpSender {
    private final JhdcpTransProperties jhdcpTransProperties;

    private final RestTemplate restTemplate;
    private final ObjectMapper objectMapper;


    public JhdcpHttpSender(JhdcpTransProperties jhdcpTransProperties, RestTemplate restTemplate, ObjectMapper objectMapper) {
        this.jhdcpTransProperties = jhdcpTransProperties;
        this.restTemplate = restTemplate;
        this.objectMapper = objectMapper;
    }

    public String getAuthorizationToken() {
        String url = jhdcpTransProperties.getHostAndPort() + jhdcpTransProperties.getAuthUri();

        //提交参数设置
        MultiValueMap<String, String> map = new LinkedMultiValueMap<>();
        map.add("grant_type", jhdcpTransProperties.getGrantType());
        map.add("client_id", jhdcpTransProperties.getClientId());
        map.add("client_secret", jhdcpTransProperties.getClientSecret());
        restTemplate.getMessageConverters().set(1, new StringHttpMessageConverter(StandardCharsets.UTF_8));
//        HttpHeaders headers=new HttpHeaders();
//        headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);
        // 组装请求体

        try {
            ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, map, String.class);
            if (responseEntity.getStatusCode().is2xxSuccessful()) {

                JhdcpAuthResponseVo responseBean = objectMapper.readValue(responseEntity.getBody(), new TypeReference<JhdcpAuthResponseVo>() {
                });

                return responseBean.getData().getToken();
            }

        } catch (RestClientException e) {
            // todo:  响应失败打印日志
            System.out.println(e);
        } catch (JsonProcessingException e) {
            // todo: 解析失败打印日志
            System.out.println(e);
        }
        return "";
    }

    public String getDataPageJson(String authorizationToken, JhdcpPageRequestVo jhdcpPageRequestVo) {
        String url = jhdcpTransProperties.getHostAndPort() + jhdcpTransProperties.getDataUri();
        HttpHeaders headers = new HttpHeaders();
        headers.setBearerAuth(authorizationToken);
        headers.setContentType(MediaType.APPLICATION_JSON);
        restTemplate.getMessageConverters().set(1, new StringHttpMessageConverter(StandardCharsets.UTF_8));


        HttpEntity<JhdcpPageRequestVo> request = new HttpEntity<>(jhdcpPageRequestVo, headers);
        try {
            ResponseEntity<String> stringResponseEntity = restTemplate.postForEntity(url, request, String.class);
            if (stringResponseEntity.getStatusCodeValue() == 401) {
                String newToken = getAuthorizationToken();
                headers.setBearerAuth(newToken);
                ResponseEntity<String> newResponseEntity = restTemplate.postForEntity(url, jhdcpPageRequestVo, String.class);
                return newResponseEntity.getBody();
            }
            return stringResponseEntity.getBody();
        }catch (RestClientException e) {
            // todo:  响应失败打印日志
            e.printStackTrace();
        }

        return "";

    }

}
