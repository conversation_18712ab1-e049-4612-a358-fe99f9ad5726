package com.goodwill;

import com.goodwill.hdr.web.WebApplication;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.annotation.Bean;

import java.util.HashMap;
import java.util.Map;

@SpringBootApplication
@MapperScan("com.goodwill.**.mapper")
public class Application extends WebApplication {

	public static void main(String[] args) {
		SpringApplication.run(Application.class, args);
	}


	@Bean(name = "visitTypeConstantMap")
	public Map<String, String> visitTypeConstantMap() {
		Map<String, String> map = new HashMap<>(2);
		map.put("OUTPV", "01");
		map.put("INPV", "02");
		return map;
	}

}
