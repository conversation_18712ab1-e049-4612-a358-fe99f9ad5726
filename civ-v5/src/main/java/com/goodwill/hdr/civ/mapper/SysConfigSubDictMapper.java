package com.goodwill.hdr.civ.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.goodwill.hdr.civ.entity.SysConfigSub;
import com.goodwill.hdr.civ.entity.SysConfigSubDict;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-03-03
 */
@Mapper
public interface SysConfigSubDictMapper extends BaseMapper<SysConfigSubDict> {
    List<Map<String, String>> getSysSubDictList(@Param("oid") String oid,
                                          @Param("keyWord") String keyWord);
}
