package com.goodwill.hdr.civ.entity;

import com.baomidou.mybatisplus.annotation.TableName;

import java.io.Serializable;
/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2022-03-03
 */
@TableName("civ_feedback_suggestions")
public class FeedbackSuggestions implements Serializable {

    private static final long serialVersionUID = 1L;

    private String id;

    /**
     * 建议提出人
     */
    private String relevantUser;

    /**
     * 描述
     */
    private String description;

    /**
     * 网址
     */
    private String relevantUrl;

    /**
     * 访问的页面
     */
    private String relevantUrlName;

    /**
     * 邮箱
     */
    private String relevantEmail;

    private String submitTime;

    private String ip;

    /**
     * 电话
     */
    private String phone;

    /**
     * 问题类型
     */
    private String type;

    /**
     * 附件/图片
     */
    private String relevantImage;

    /**
     * 图片路径
     */
    private String relevantImageUrl;

    private String fileName;

    private String oid;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getRelevantUser() {
        return relevantUser;
    }

    public void setRelevantUser(String relevantUser) {
        this.relevantUser = relevantUser;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getRelevantUrl() {
        return relevantUrl;
    }

    public void setRelevantUrl(String relevantUrl) {
        this.relevantUrl = relevantUrl;
    }

    public String getRelevantUrlName() {
        return relevantUrlName;
    }

    public void setRelevantUrlName(String relevantUrlName) {
        this.relevantUrlName = relevantUrlName;
    }

    public String getRelevantEmail() {
        return relevantEmail;
    }

    public void setRelevantEmail(String relevantEmail) {
        this.relevantEmail = relevantEmail;
    }

    public String getSubmitTime() {
        return submitTime;
    }

    public void setSubmitTime(String submitTime) {
        this.submitTime = submitTime;
    }

    public String getIp() {
        return ip;
    }

    public void setIp(String ip) {
        this.ip = ip;
    }

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getRelevantImage() {
        return relevantImage;
    }

    public void setRelevantImage(String relevantImage) {
        this.relevantImage = relevantImage;
    }

    public String getRelevantImageUrl() {
        return relevantImageUrl;
    }

    public void setRelevantImageUrl(String relevantImageUrl) {
        this.relevantImageUrl = relevantImageUrl;
    }

    public String getFileName() {
        return fileName;
    }

    public void setFileName(String fileName) {
        this.fileName = fileName;
    }

    public String getOid() {
        return oid;
    }

    public void setOid(String oid) {
        this.oid = oid;
    }

    @Override
    public String toString() {
        return "FeedbackSuggestions{" +
                "id=" + id +
                ", relevantUser=" + relevantUser +
                ", description=" + description +
                ", relevantUrl=" + relevantUrl +
                ", relevantUrlName=" + relevantUrlName +
                ", relevantEmail=" + relevantEmail +
                ", submitTime=" + submitTime +
                ", ip=" + ip +
                ", phone=" + phone +
                ", type=" + type +
                ", relevantImage=" + relevantImage +
                ", relevantImageUrl=" + relevantImageUrl +
                ", fileName=" + fileName +
                ", oid=" + oid +
                "}";
    }
}
