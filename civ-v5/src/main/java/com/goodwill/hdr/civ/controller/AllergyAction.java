package com.goodwill.hdr.civ.controller;


import com.goodwill.hdr.civ.config.Config;
import com.goodwill.hdr.civ.service.AllergyService;
import com.goodwill.hdr.core.orm.Page;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Description 类描述：过敏信息Action
 * @Date 2018年9月28日
 * @modify 修改记录：
 */
@RequestMapping("/allergy")
@RestController
@Api(tags = "过敏查询")
public class AllergyAction {


    @Autowired
    private AllergyService allergyService;

    /**
     * @Description 某次就诊的过敏记录
     */
    @ApiOperation(value = "获取某次就诊的过敏记录", notes = "获取某次就诊的过敏记录", httpMethod = "POST")
    @RequestMapping(value = "/getAllergyList", method = RequestMethod.POST)
    public Page<Map<String, String>> getAllergyList(String oid, String patientId, String visitId, String visitType, String orderBy, String orderDir, int pageNo, int pageSize, String allergyType, String allergyLevel) {
        //过敏类型  类型编码
//        String allergyType = getParameter("allergyType");
//        //过敏程度
//        String allergySeverity = getParameter("allergyLevel");
        //过敏程度数据匹配的列字段名
        String allergyFieldName = Config.getAllergySeverityFieldName(oid);
        String filterString = "";
        if (StringUtils.isNotBlank(allergyType)) {
            filterString = "ALLERGY_CATEGORY_CODE|=|" + allergyType;
        }
        if (filterString.length() > 0) {
            if (StringUtils.isNotBlank(allergyLevel)) {
                filterString += ";" + allergyFieldName + "|=|" + allergyLevel;
            }
        } else if (StringUtils.isNotBlank(allergyLevel)) {
            filterString = allergyFieldName + "|=|" + allergyLevel;
        }
        Page<Map<String, String>> result = allergyService.getAllergyList(oid, patientId, visitId, visitType, orderBy,
                orderDir, pageNo, pageSize, filterString);
        return result;
    }

    /**
     * @Description 所有的过敏信息
     */
    @ApiOperation(value = "获取所有的过敏信息", notes = "获取所有的过敏信息", httpMethod = "POST")
    @RequestMapping(value = "/getAllergys", method = RequestMethod.POST)
    public Page<Map<String, String>> getAllergys(String oid, String patientId, String outPatientId, String orderBy, String orderDir, int pageNo, int pageSize, String allergyType, String allergyLevel) {

        String allergyFieldName = Config.getAllergySeverityFieldName(oid);
        String filterString = "";
        if (StringUtils.isNotBlank(allergyType)) {
            filterString = "ALLERGY_CATEGORY_CODE|=|" + allergyType;
        }
        if (filterString.length() > 0) {
            if (StringUtils.isNotBlank(allergyLevel)) {
                filterString += ";" + allergyFieldName + "|=|" + allergyLevel;
            }
        } else if (StringUtils.isNotBlank(allergyLevel)) {
            filterString = allergyFieldName + "|=|" + allergyLevel;
        }


        Page<Map<String, String>> result = allergyService.getAllergys(oid, patientId, outPatientId, orderBy, orderDir,
                pageNo, pageSize, filterString);
        return result;
    }

    /**
     * @Description 获取过敏类型
     */
    @ApiOperation(value = "获取过敏类型", notes = "获取过敏类型", httpMethod = "POST")
    @RequestMapping(value = "/getAllergyTypes", method = RequestMethod.POST)
    public List<Map<String, String>> getAllergyTypes(String oid) {
        List<Map<String, String>> result = allergyService.getAllergyTypes(oid);
        return result;
    }

    /**
     * @Description 获取过敏程度
     */
    @ApiOperation(value = "获取过敏程度", notes = "获取过敏程度", httpMethod = "POST")
    @RequestMapping(value = "/getAllergySeverity", method = RequestMethod.POST)
    public List<Map<String, String>> getAllergySeverity(String oid) {
        List<Map<String, String>> allergySeverity = allergyService.getAllergySeverity(oid);
        return allergySeverity;
    }

}
