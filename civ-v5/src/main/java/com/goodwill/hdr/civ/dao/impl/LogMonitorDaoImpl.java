package com.goodwill.hdr.civ.dao.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.goodwill.hdr.civ.dao.LogMonitorDao;
import com.goodwill.hdr.civ.entity.LogRecord;
import com.goodwill.hdr.civ.mapper.LogRecordMapper;
import com.goodwill.hdr.civ.mapper.SecurityUserMapper;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Description 类描述：
 * @Date 2019-06-12 14:29
 * @modify 修改记录：
 */
@Repository
public class LogMonitorDaoImpl implements LogMonitorDao {

    @Autowired
    private LogRecordMapper logRecordMapper;
    @Autowired
    private SecurityUserMapper securityUserMapper;
    private static Logger logger = LoggerFactory.getLogger(LogMonitorDaoImpl.class);

    /**
     * 监控日志保存
     *
     * @param logRecord
     * @return
     */
    @Override
    public int insertMonitorLog(LogRecord logRecord) {
        logRecord.setAccesstime(LocalDate.now());
        logger.info(logRecord.toString());
        return logRecordMapper.insert(logRecord);
    }

    public List<Map<String, String>> queryMonitorLog(String oid, String deptCode, String userCode,
                                                     String visitPageCode, String beginDate, String endDate) {

        List<Map<String, String>> monitorLogList = new ArrayList<Map<String, String>>();
        QueryWrapper<LogRecord> queryWrapper = new QueryWrapper<>();
        if ("01".equals(deptCode)) {
            deptCode = "";
        }
        queryWrapper.select("id_pk", "deptcode", "deptname", "usercode", "username", "pagecode", "pagename", "accesstime", "ip", "count(*) as num")
                .eq(StringUtils.isNotBlank(deptCode), "deptcode", deptCode)
                .eq(StringUtils.isNotBlank(userCode), "usercode", userCode)
                .eq("oid", oid)
                .eq(StringUtils.isNotBlank(visitPageCode), "pagecode", visitPageCode)
                .apply(StringUtils.isNotBlank(beginDate), " and  accesstime >=  str_to_date('" + beginDate + "','%Y-%m-%d')")
                .apply(StringUtils.isNotBlank(endDate), " and  accesstime <=  str_to_date('" + endDate + "','%Y-%m-%d')")
                .groupBy("usercode", "pagecode", "deptcode", "accesstime ")
                .orderByDesc("accesstime", "usercode", "deptcode", "pagecode");
        List<Map<String, Object>> maps = logRecordMapper.selectMaps(queryWrapper);
        for (Map<String, Object> mapTmp : maps) {
            Map<String, String> map = new HashMap<>();
            map.put("id_pk", mapTmp.get("id_pk").toString());
            map.put("deptcode", mapTmp.get("deptcode").toString());
            map.put("deptname", mapTmp.get("deptname").toString());
            map.put("usercode", mapTmp.get("usercode").toString());
            map.put("username", mapTmp.get("username").toString());
            map.put("pagecode", mapTmp.get("pagecode").toString());
            map.put("pagename", mapTmp.get("pagename").toString());
            map.put("accesstime", mapTmp.get("accesstime").toString());
            map.put("ip", mapTmp.get("ip").toString());
            map.put("num", mapTmp.get("num").toString());
            monitorLogList.add(map);
        }
//        StringBuffer sql = new StringBuffer();
//        sql.append(" select id_pk,deptcode,deptname,usercode,username,pagecode,pagename,accesstime,ip,count(*) as num from civ_log_record where 1=1 ");
//        if ("01".equals(deptCode)) {
//            deptCode = "";
//        }
//        if (StringUtils.isNotBlank(deptCode)) {
//            sql.append(" and deptcode = '" + deptCode + "'");
//        }
//        if (StringUtils.isNotBlank(userCode)) {
//            sql.append(" and usercode = '" + userCode + "'");
//        }
//        if (StringUtils.isNotBlank(visitPageCode)) {
//            sql.append(" and  pagecode = '" + visitPageCode + "'");
//        }
//        if (StringUtils.isNotBlank(beginDate)) {
//            sql.append(" and  accesstime >=  str_to_date('" + beginDate + "','%Y-%m-%d')");
//        }
//        if (StringUtils.isNotBlank(endDate)) {
//            sql.append(" and  accesstime <=  str_to_date('" + endDate + "','%Y-%m-%d')");
//        }
//        sql.append("  group by  usercode,pagecode,deptcode,accesstime  order by  accesstime desc,usercode ,deptcode,pagecode   ");
//        Query query = createSqlQuery(sql.toString());
//        List<Object> listobj = query.list();
//        executeResult(monitorLogList, listobj);
        return monitorLogList;
    }

    /**
     * @return
     */
    public List<Map<String, Object>> queryAllMonitorLog() {

        List<Map<String, Object>> mapList = logRecordMapper.selectMaps(null);


//        List<Map<String, String>> monitorLogList = new ArrayList<Map<String, String>>();
//        StringBuffer sql = new StringBuffer();
//        sql.append(" select id_pk,deptcode,deptname,usercode,username,pagecode,pagename,accesstime,ip from civ_log_record where 1=1 ");
//        Query query = createSqlQuery(sql.toString());
//        List<Object> listobj = query.list();
//        executeResult(monitorLogList, listobj);
        return mapList;
    }


    /**
     * 以获取用户（医生）列表用于监控日志
     *
     * @param userName
     * @param deptCode
     * @return
     */
    @Override
    public List<Map<String, String>> queryDoctorsByDept(String oid, String userName, String deptCode) {


        List<Map<String, String>> userList = securityUserMapper.getUserList(oid, userName, deptCode);
//        StringBuffer sql = new StringBuffer();
//        sql.append(" select s.pk_user,s.usercode,s.username,s.name from security_user s where 1=1 ");
//        if (StringUtils.isNotBlank(deptCode) && (!deptCode.equals("01"))) {
//            sql.append(" and ( s.deptcode  like '%" + deptCode + "%'  or s.deptname like '%" + deptCode + "%' ) ");
//        }
//        if (StringUtils.isNotBlank(userName)) {
//            sql.append(" and (s.name like '%" + userName + "%' or s.usercode like '%" + userName + "%') ");
//        }
//        Query query = createSqlQuery(sql.toString());
//        List<Object> listobj = query.list();
//        for (Object obj : listobj) {
//            Object[] objArr = (Object[]) obj;
//            Map<String, String> map = new HashMap<String, String>();
//            map.put("pk_user", objArr[0].toString());
//            map.put("usercode", objArr[1].toString());
//            map.put("username", objArr[2].toString());
//            map.put("name", objArr[3].toString());
//            doctorList.add(map);
//        }
        return userList;
    }

    /**
     * 通过不同条件获取监控日志数据
     *
     * @param oid
     * @param beginDate
     * @param endDate
     * @return
     */
    @Override
    public List<Long> getClassifyMonitorLog(String oid, String value, String beginDate, String endDate, String flag, String
            pageCode, List<String> xList) {
        List<Map<String, Object>> mapList = new ArrayList<>();
        StringBuffer sql = new StringBuffer();
        if (flag.equals("according_dept")) {
            //不再关联 security_authority 和 security_user
//            sql.append("select  sa.deptname,count(clr.id_pk) as num ");
//            sql.append("from   (select distinct  deptcode,deptname from civ_log_record) sa left join  (select * from  civ_log_record  where 1=1 ");
//            if (StringUtils.isNotBlank(beginDate)) {
//                sql.append(" and  accesstime >=  str_to_date('" + beginDate + "','%Y-%m-%d')");
//            }
//            if (StringUtils.isNotBlank(endDate)) {
//                sql.append(" and  accesstime <=  str_to_date('" + endDate + "','%Y-%m-%d')");
//            }
//            if (StringUtils.isNotBlank(pageCode)) {
//                sql.append(" and  pagecode = '" + pageCode + "' ");
//            }
//            sql.append(" ) clr  on clr.deptcode = sa.deptcode");
//            sql.append(" group by  sa.deptcode order by sa.deptcode desc ");

            mapList = logRecordMapper.selectMonitorLogAccordingDept(oid, beginDate, endDate, pageCode);

        } else if (flag.equals("according_doctor")) {

//            sql.append("select  su.username,count(clr.id_pk) as num ");
//            sql.append(" from   ( select  distinct usercode,username  from  civ_log_record  ) su left join ( select * from  civ_log_record  where 1=1  ");
//            if (StringUtils.isNotBlank(beginDate)) {
//                sql.append(" and  accesstime >=  str_to_date('" + beginDate + "','%Y-%m-%d')");
//            }
//            if (StringUtils.isNotBlank(endDate)) {
//                sql.append(" and  accesstime <=  str_to_date('" + endDate + "','%Y-%m-%d')");
//            }
//            if (StringUtils.isNotBlank(value) && (!"01".equals(value))) {
//                sql.append(" and  deptcode  = '" + value + "'");
//            }
//            if (StringUtils.isNotBlank(pageCode)) {
//                sql.append(" and  pagecode = '" + pageCode + "' ");
//            }
//            sql.append(" ) clr  on  clr.usercode = su.usercode ");
//            sql.append(" group by   su.usercode order by su.usercode desc ");
            mapList = logRecordMapper.selectMonitorLogAccordingDoctor(oid, beginDate, endDate, pageCode, value);
        }


        List<Long> list = new ArrayList<>();

        for (Map<String, Object> map : mapList) {
//
//            Object[] objArr = (Object[]) obj;
            String xData = map.get("name") == null ? "" : (String) map.get("name");
            Long num = map.get("num") == null ? 0 : (Long) map.get("num");
            list.add(num);
            if (!xList.contains(xData)) {
                xList.add(xData);
            }
        }
        return list;
    }


    /**
     * 通过不同条件获取监控日志数据
     *
     * @param oid
     * @param deptCode
     * @param userCode
     * @param days
     * @return
     */
    public List<Long> getClassifyByTimeMonitorLog(String oid, String flag, String deptCode, String userCode, String
            days, String dateNow, String pageCode, List<String> xList) {
        StringBuffer sql = new StringBuffer();
        List<Map<String, Object>> mapList = new ArrayList<>();
        if (flag.equals("according_depttime")) {//科室按时间统计
            if (days.equals("12")) {//近一年的数据
                mapList = logRecordMapper.selectTimeMonitorLogAccordingDeptTimeNearOneYear(oid, deptCode, dateNow, pageCode);
            } else {//近一个月的数据

                mapList = logRecordMapper.selectTimeMonitorLogAccordingDeptTimeNearOneMonth(oid, deptCode, dateNow, pageCode);
            }
        } else if (flag.equals("according_doctortime")) {
            if (days.equals("12")) {//近一年的数据
                mapList = logRecordMapper.selectTimeMonitorLogAccordingDoctorTimeNearOneYear(oid, deptCode, dateNow, pageCode, userCode);
            } else {//近一个月的数据

                mapList = logRecordMapper.selectTimeMonitorLogAccordingDoctorTimeNearOneMonth(oid, deptCode, dateNow, pageCode, userCode);
            }
        }


        List<Long> res = new ArrayList<>();
        for (Map<String, Object> map : mapList) {

            res.add(map.get("num") == null ? 0 : (Long) map.get("num"));
            String xData = map.get("tdate") == null ? "" : (String) map.get("tdate");
            if (!xList.contains(xData)) {
                xList.add(xData);
            }
        }
        return res;
    }

    /**
     * top 5
     *
     * @param groupField
     * @return
     */
    @Override
    public List<Map<String, Object>> getTopFiveLogData(String groupField, String selName, String pageCode) {


        String groupName = "deptcode";
        if ("username".equals(selName)) {
            groupName = "usercode";
        }

        QueryWrapper<LogRecord> queryWrapper = new QueryWrapper<>();
        queryWrapper.select(selName + " as name", "count(*) as code")
                .eq("pagecode", pageCode)
                .groupBy(groupName)
                .orderByDesc("code")
                .last("limit 5");
        List<Map<String, Object>> mapList = logRecordMapper.selectMaps(queryWrapper);
//        String sql = " select  " + selName + " as name ,count(*) as code  from civ_log_record  where  pagecode='" + pageCode + "'  group  by   " + groupName + "   order by  code desc  limit 5 ";
//        //SQLQuery query = (SQLQuery) createSqlQuery(sql);
//        SQLQuery query = getSession().createSQLQuery(sql).addScalar("name").addScalar("code");
//        //map封装数据
//        query.setResultTransformer(Transformers.ALIAS_TO_ENTITY_MAP);
//        List<Map<String, String>> list = query.list();
        return mapList;
    }

    /**
     * 扇形图
     *
     * @return
     */
    @Override
    public List<Map<String, Object>> getSectorData() {
        QueryWrapper<LogRecord> queryWrapper = new QueryWrapper<>();
        queryWrapper.select("pagename  as name", "count(*)   as y")
                .groupBy("pagecode");
        List<Map<String, Object>> mapList = logRecordMapper.selectMaps(queryWrapper);
//        String sql = "  select pagename  as name ,count(*)   as y  from  civ_log_record group by pagecode  ";
//        //SQLQuery query = (SQLQuery) createSqlQuery(sql);
//        SQLQuery query = getSession().createSQLQuery(sql).addScalar("name").addScalar("y");
//        //map封装数据
//        query.setResultTransformer(Transformers.ALIAS_TO_ENTITY_MAP);
        return mapList;
    }


//    /**
//     * 获取医生
//     *
//     * @return
//     */
//    @Override
//    public List<String> getSecurityUser() {
//        String sql = "  select  distinct username  from  security_user  ";
//        Query query = createSqlQuery(sql.toString());
//        List<String> list = query.list();
//        return list;
//    }
//
//    /**
//     * 获取科室
//     *
//     * @return
//     */
//    public List<String> getDept() {
//        String sql = " select  distinct  deptname from  civ_log_record ";
//        Query query = createSqlQuery(sql.toString());
//        List<String> list = query.list();
//        return list;
//    }
}

