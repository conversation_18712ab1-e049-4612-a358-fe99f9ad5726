package com.goodwill.hdr.civ.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.io.Serializable;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2022-03-03
 */
@TableName("civ_sys_config_sub")
public class SysConfigSub implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    private String superCode;

    private String configCode;

    private String configValue;

    private String configName;

    private String ormFileds;

    private String enabled;

    private String oid;

    private String maskRuleCode;
    
    private String maskRuleName;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getSuperCode() {
        return superCode;
    }

    public void setSuperCode(String superCode) {
        this.superCode = superCode;
    }

    public String getConfigCode() {
        return configCode;
    }

    public void setConfigCode(String configCode) {
        this.configCode = configCode;
    }

    public String getConfigValue() {
        return configValue;
    }

    public void setConfigValue(String configValue) {
        this.configValue = configValue;
    }

    public String getConfigName() {
        return configName;
    }

    public void setConfigName(String configName) {
        this.configName = configName;
    }

    public String getOrmFileds() {
        return ormFileds;
    }

    public void setOrmFileds(String ormFileds) {
        this.ormFileds = ormFileds;
    }

    public String getEnabled() {
        return enabled;
    }

    public void setEnabled(String enabled) {
        this.enabled = enabled;
    }

    public String getOid() {
        return oid;
    }

    public void setOid(String oid) {
        this.oid = oid;
    }

    public String getMaskRuleCode() {
        return maskRuleCode;
    }

    public void setMaskRuleCode(String maskRuleCode) {
        this.maskRuleCode = maskRuleCode;
    }

    public String getMaskRuleName() {
        return maskRuleName;
    }

    public void setMaskRuleName(String maskRuleName) {
        this.maskRuleName = maskRuleName;
    }

    @Override
    public String toString() {
        return "SysConfigSub{" +
                "id=" + id +
                ", superCode=" + superCode +
                ", configCode=" + configCode +
                ", configValue=" + configValue +
                ", configName=" + configName +
                ", ormFileds=" + ormFileds +
                ", enabled=" + enabled +
                ", oid=" + oid +
                "}";
    }
}
