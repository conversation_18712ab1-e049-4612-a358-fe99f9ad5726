package com.goodwill.hdr.civ.service.impl;


import com.goodwill.hdr.civ.config.Config;
import com.goodwill.hdr.civ.dao.LogMonitorDao;
import com.goodwill.hdr.civ.entity.LogRecord;
import com.goodwill.hdr.civ.service.LogMonitorService;
import com.goodwill.hdr.civ.service.PowerService;
import com.goodwill.hdr.core.orm.Page;
import com.goodwill.hdr.security.priority.domain.CustomUserDetails;
import com.goodwill.hdr.security.priority.entity.UserEntity;
import com.goodwill.hdr.security.utils.SecurityCommonUtil;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;

/**
 * <AUTHOR>
 * @Description 类描述：
 * @Date 2019-06-12 14:09
 * @modify 修改记录：
 */
@Service
public class LogMonitorServiceImpl implements LogMonitorService {
    private static Logger logger = LoggerFactory.getLogger(LogMonitorServiceImpl.class);

    @Autowired
    private LogMonitorDao logMonitorDao;

    @Autowired
    private PowerService powerService;


    /**
     * @param logRecord
     * @Description 日志监控保存
     */
    @Override
    public int insertMonitorLog(LogRecord logRecord) {
        //CustomUserDetails user = (CustomUserDetails) SecurityContextHolder.getContext().getAuthentication().getPrincipal();
        UserEntity user = SecurityCommonUtil.getCurrentLoginUser();
        logger.info("....................."+user.getUsername()+";"+user.getUsercode());
        logRecord.setUsercode(user.getUsercode());
        logRecord.setUsername(user.getUsername());
        return logMonitorDao.insertMonitorLog(logRecord);

    }

    /**
     * @param oid
     * @param deptCode
     * @param userCode
     * @param visitPageCode
     * @param pageNo
     * @param pageSize
     * @return
     * @Description 通过查询条件获取监控日志数据
     */
    @Override
    @Transactional
    public Page<Map<String, String>> getMonitorLogPage(String oid, String deptCode, String userCode,
                                                       String visitPageCode, String beginDate, String endDate, int pageNo, int pageSize) {
        Page<Map<String, String>> page = new Page<Map<String, String>>();
        List<Map<String, String>> monitorLogList = logMonitorDao.queryMonitorLog(oid, deptCode, userCode, visitPageCode, beginDate, endDate);
        page.setTotalCount(monitorLogList.size());
        if (monitorLogList.size() == 0) {
            return page;
        }
        if (monitorLogList.size() >= (pageSize * pageNo)) {
            monitorLogList = monitorLogList.subList(pageSize * (pageNo - 1), pageSize * pageNo);
        } else if (monitorLogList.size() < (pageSize * pageNo)) {  // && doctorList.size() >= (pageSize * (pageNo - 1))
            monitorLogList = monitorLogList.subList(pageSize * (pageNo - 1), monitorLogList.size());
        }
        page.setResult(monitorLogList);
        page.setPageNo(pageNo);
        page.setOrderDir("desc");
        page.setPageSize(pageSize);
        page.setCountTotal(true);
        return page;
    }

    /**
     * @return
     * @Description 获取所有的监控日志数据
     */
    @Transactional
    public Page<Map<String, Object>> getAllMonitorLogPage() {
        Page<Map<String, Object>> page = new Page<>();
        List<Map<String, Object>> monitorLogList = logMonitorDao.queryAllMonitorLog();
        page.setResult(monitorLogList);
        page.setOrderDir("desc");
        page.setCountTotal(true);
        page.setTotalCount(monitorLogList.size());
        return page;
    }

    /**
     * @param oid
     * @param userName
     * @param deptCode 科室code
     * @param pageNo
     * @param pageSize
     * @return
     * @Description 获得医生列表
     */
    @Override
    @Transactional
    public Page<Map<String, String>> getDoctorPage(String oid, String userName, String deptCode, int pageNo, int pageSize) {
        Page<Map<String, String>> page = new Page<Map<String, String>>();
        if (pageNo == 0) {
            pageNo = 1;
        }
        if (pageSize == 0) {
            pageSize = 10;
        }
        List<Map<String, String>> doctorList = logMonitorDao.queryDoctorsByDept(oid, userName, deptCode);
        page.setTotalCount(doctorList.size());
        if (doctorList.size() == 0) {
            return page;
        }
        if (doctorList.size() >= (pageSize * pageNo)) {
            doctorList = doctorList.subList(pageSize * (pageNo - 1), pageSize * pageNo);
        } else if (doctorList.size() < (pageSize * pageNo)) {  // && doctorList.size() >= (pageSize * (pageNo - 1))
            doctorList = doctorList.subList(pageSize * (pageNo - 1), doctorList.size());
        }
        page.setResult(doctorList);
        page.setPageNo(pageNo);
        page.setOrderBy("usercode");
        page.setOrderDir("desc");
        page.setPageSize(pageSize);
        page.setCountTotal(true);
        return page;
    }

    /**
     * 处理有权限的视图:添加菜单页面code：菜单名称value 映射
     *
     * @param map
     */
    @Transactional
    public Map<String, String> handleView(String oid, Map<String, String> map) {
        Map<String, String> resMap = new HashMap<String, String>();
        //有权限的视图复制
        for (String str : map.keySet()) {
            if (map.get(str).equals("1")) {
                resMap.put(str, Config.getPageName(oid, str));
            }
        }
        return resMap;

    }

    /**
     * 根据不同条件进行分类获取监控日志数据
     * 按科室，医生查询
     */
    @Transactional
    public Map<String, Object> getClassifyMonitorLog(String oid, String value, String beginDate, String endDate, String flagTemp) {
        //横坐标
        List<String> list = new ArrayList<String>();
        //获取医生
//        if (flagTemp.equals("according_doctor")) {
//            list = logMonitorDao.getSecurityUser();
//        } else {
//            list = logMonitorDao.getDept();
//        }
        //权限视图
        CustomUserDetails user = (CustomUserDetails) SecurityContextHolder.getContext().getAuthentication().getPrincipal();
        Map<String, String> deptMap = powerService.getPowerConfigByPage(oid, user.getUsername());
        deptMap = handleView(oid, deptMap);
        List<Map<String, Object>> dataList = new ArrayList<Map<String, Object>>();
        //获取权限视图数据
        for (Map.Entry<String, String> entry : deptMap.entrySet()) {
            List<Long> logList = logMonitorDao.getClassifyMonitorLog(oid, value, beginDate, endDate, flagTemp, entry.getKey(), list);
            Map<String, Object> dataMap = new HashMap<String, Object>();
            dataMap.put("name", entry.getValue());
            dataMap.put("data", logList);
            dataList.add(dataMap);
        }
        Map<String, Object> res = new HashMap<String, Object>();
        res.put("categories", list);
        res.put("series", dataList);
        return res;
    }

    /**
     * 根据不同条件进行分类获取监控日志数据
     * 按科室时间，医生时间查询
     */
    @Transactional
    public Map<String, Object> getSortedByTimeMonitorLog(String oid, String dept, String doctor, String days, String flagTemp) {
        CustomUserDetails user = (CustomUserDetails) SecurityContextHolder.getContext().getAuthentication().getPrincipal();
        Map<String, String> viewMap = powerService.getPowerConfigByPage(oid, user.getUsername());
        viewMap = handleView(oid, viewMap);
        List<Map<String, Object>> dataList = new ArrayList<Map<String, Object>>();
        DateTimeFormatter dateTimeFormatter=DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        //此日期用于防止数据大时查询耗时出现的问题
        String dateNow = LocalDateTime.now().format(dateTimeFormatter);
        List<String> list = getListByType(days);
//        List<String> list = new ArrayList<String>();
        for (Map.Entry<String, String> entry : viewMap.entrySet()) {
            List<Long> logList = logMonitorDao.getClassifyByTimeMonitorLog(oid, flagTemp, dept, doctor, days, dateNow, entry.getKey(), list);
            Map<String, Object> dataMap = new HashMap<String, Object>();
            dataMap.put("name", entry.getValue());
            dataMap.put("data", logList);
            dataList.add(dataMap);
        }
        Map<String, Object> res = new HashMap<String, Object>();
        res.put("categories", list);
        res.put("series", dataList);
        return res;

    }

    /**
     * 查询top 5
     *
     * @return
     */
    @Transactional
    public List<Map<String, Object>> getTopFiveLogData(String flag, String value, String userCode) {
        String groupName = "deptname";
        String groupField = "deptcode";
        if (StringUtils.isNotBlank(flag) && flag.equals("top5_dept")) {  //科室top 5
            groupField = "deptcode";
            groupName = "deptname";
        } else if (StringUtils.isNotBlank(flag) && flag.equals("top5_doctor")) {  // 医生top5
            groupField = "usercode";
            groupName = "username";
        }
        List<Map<String, Object>> list = logMonitorDao.getTopFiveLogData(groupField, groupName, value);
        return list;
    }

    /**
     * 扇形图
     *
     * @return
     */
    @Transactional
    public List<Map<String, Object>> getSectorData() {
        return logMonitorDao.getSectorData();
    }

    /**
     * 处理近期时间
     *
     * @param days
     * @return
     */
    @Transactional
    public List<String> getListByType(String days) {
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");
        Calendar calendar = Calendar.getInstance();
        List<String> list = new ArrayList<String>();
        if (days.equals("1")) {//按30天计算，因为civ_log_recode_data 设计数据为30天
            for (int i = 29; i >= 0; i--) {
                calendar.setTime(new Date());
                calendar.add(Calendar.DATE, -1 * i);
                list.add((calendar.get(Calendar.MONTH) + 1) + "月" + calendar.get(Calendar.DAY_OF_MONTH) + "日");
            }
        } else if (days.equals("12")) {
            for (int i = 11; i >= 0; i--) {
                calendar.setTime(new Date());
                calendar.add(Calendar.MONTH, -1 * i);
                list.add(calendar.get(Calendar.YEAR) + "年" + (calendar.get(Calendar.MONTH) + 1) + "月");
            }
        }
        return list;
    }

    /**
     * 构造空数据
     *
     * @param days
     * @return
     */
    @Transactional
    public Map<String, Object> getEmptyData(String oid, String days) {
        Map<String, Object> res = new HashMap<String, Object>();
        List<String> list = getListByType(days);
        CustomUserDetails user = (CustomUserDetails) SecurityContextHolder.getContext().getAuthentication().getPrincipal();
        Map<String, String> viewMap = powerService.getPowerConfigByPage(oid, user.getUsername());
        viewMap = handleView(oid, viewMap);
        ArrayList<Map<String, Object>> xList = new ArrayList<Map<String, Object>>();
        for (Map.Entry<String, String> entry : viewMap.entrySet()) {
            Map<String, Object> dataMap = new HashMap<String, Object>();
            dataMap.put("name", entry.getValue());
            dataMap.put("data", new ArrayList<Integer>(list.size()));
            xList.add(dataMap);
        }
        res.put("categories", list);
        res.put("series", xList);
        return res;
    }
}
