package com.goodwill.hdr.civ.config;

import org.apache.commons.lang.StringUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Description 类描述：
 * @modify 修改记录：
 */
public class NursingConfig {

    //配置文件
    private static final String FILE_NAME = "nursing-config.properties";

//	private static final String project_site = ConfigCache.getCache("PROJECT_SITE");
//	private static final String param_num = ConfigCache.getCache("PARAM_NUM");
//	private static final String url = ConfigCache.getCache("PARAM_NUM");
//	private static final String linkType = ConfigCache.getCache("LINKTYPE");

    public static String getProject_site(String oid) {
        return ConfigCache.getCache(oid, "PROJECT_SITE");
    }

    public static String getURL(String oid) {
        String URL = ConfigCache.getCache(oid, ConfigCache.getCache(oid, "PROJECT_SITE") + "_URL");
        return StringUtils.isNotBlank(URL) ? URL : "";
    }

    public static String getLinkType(String oid) {
        String linkType = ConfigCache.getCache(oid, "LINKTYPE");
        return StringUtils.isNotBlank(linkType) ? linkType : "";
    }

    public static List<String> getParams(String oid) {
        List<String> params = new ArrayList<String>();
        if (StringUtils.isNotBlank(ConfigCache.getCache(oid, "PROJECT_SITE"))) {
            for (int i = 1; i <= Integer.valueOf(ConfigCache.getCache(oid, "PARAM_NUM")); i++) {
                String FIELD = ConfigCache.getCache(oid, ConfigCache.getCache(oid, "PROJECT_SITE") + "_PARAMETER_FIELD" + i);
                params.add(FIELD);
            }
        }
        return params;
    }

    public static Map<String, String> getParam_configs(String oid) {
        Map<String, String> param_configs = new HashMap<String, String>();
        if (StringUtils.isNotBlank(ConfigCache.getCache(oid, "PROJECT_SITE"))) {
            for (int i = 1; i <= Integer.valueOf(ConfigCache.getCache(oid, "PARAM_NUM")); i++) {
                String field = ConfigCache.getCache(oid, ConfigCache.getCache(oid, "PROJECT_SITE") + "_PARAMETER_FIELD" + i);
                String config = ConfigCache.getCache(oid, ConfigCache.getCache(oid, "PROJECT_SITE") + "_PARAMETER_FIELD_CONFIG" + i);
                param_configs.put(field, config);
            }
        }
        return param_configs;
    }

}
