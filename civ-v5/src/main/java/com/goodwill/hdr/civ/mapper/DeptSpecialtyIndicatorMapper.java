package com.goodwill.hdr.civ.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.goodwill.hdr.civ.entity.DeptSpecialtyIndicator;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * <p>
 * Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-03-03
 */
@Mapper
public interface DeptSpecialtyIndicatorMapper extends BaseMapper<DeptSpecialtyIndicator> {

    @Select("select *  from  civ_dept_specialty_indicator where dept_code =#{deptCode} and is_inuse = 'Y' and item_code =#{itemCode} group by dept_code,dept_name,item_code,item_name,sub_item_code,sub_item_name order by  array_index asc")
    List<DeptSpecialtyIndicator> getConfigByDept(String deptCode, String itemCode);

}
