package com.goodwill.hdr.civ.service;

import com.goodwill.hdr.civ.enums.DictType;
import com.goodwill.hdr.core.orm.Page;

import java.util.Map;

/**
 * @Description 类描述：字典接口
 */
public interface DictHbaseService {
    /**
     * @param
     * @param filterPage
     * @return
     * @Description 获取字典
     */
    public Page<Map<String, String>> getDict(String oid, String visitId , DictType type, String columnName, String keyword,
                                             Page<Map<String, String>> filterPage);

    /**
     * @param
     * @param filterPage
     * @return
     * @Description 获取门诊检查字典
     */
    public Page<Map<String, String>> getOutExamDict(String oid,String visitId,DictType type, String columnName, String keyword,
                                                    Page<Map<String, String>> filterPage);

    /**
     * @return
     * @Description 获取检验明细字典，由于三院的检验明细数据字典在单独的一张表中，所以该字典的方法单独使用
     */
    public Page<Map<String, String>> getDictSubLab(String oid,String visitId, String columnName, String keyword,
                                                   Page<Map<String, String>> filterPage);

    /**
     * @param filterPage
     * @param configFilter 是否需要从配置中读取查询条件来查询
     * @return
     * @Description 根据code查询名称
     */
    public Page<Map<String, String>> getNamebyCode(String oid ,String visitId,DictType type, String keywords,
                                                   Page<Map<String, String>> filterPage, boolean configFilter);

    public Page<Map<String, String>> getLabSubTypeList(String oid, String search, int pageNo, int pageSize,String patientId, String visitId, String visitType);

    /**
     * 通过二级名称查询字典数据
     *
     * @param type
     * @param keywords
     * @param filterPage
     * @param configFilter
     * @return
     */
    Page<Map<String, String>> getNamebyDetailName(String oid, String visitId,DictType type, String column, String keywords,
                                                  Page<Map<String, String>> filterPage, boolean configFilter);

}
