package com.goodwill.hdr.civ.dao.impl;


import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.goodwill.hdr.civ.dao.PowerDao;
import com.goodwill.hdr.civ.entity.*;
import com.goodwill.hdr.civ.mapper.*;
import com.goodwill.hdr.civ.vo.DeptListWithinUser;
import com.goodwill.hdr.civ.vo.UserVo;
import com.goodwill.hdr.security.priority.entity.SecurityCommonDept;
import com.goodwill.hdr.security.priority.mapper.SecurityCommonDeptMapper;
import com.goodwill.hdr.security.priority.mapper.SecurityCommonDeptUserMapper;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

@Repository
public class PowerDaoImpl implements PowerDao {

    private final SecurityUserMapper securityUserMapper;
    private final PowerConfigMapper powerConfigMapper;
    private final PowerConfigDeptMapper powerConfigDeptMapper;
    private final SysConfigMapper sysConfigMapper;
    private final SysConfigSubMapper sysConfigSubMapper;

    private final SysConfigSubDictMapper sysConfigSubDictMapper;

    private final SecurityCommonDeptMapper securityCommonDeptMapper;
    private final SecurityCommonDeptUserMapper deptUserMapper;
    private final SysMaskRuleMapper sysMaskRuleMapper;
    private final SysFiledUserMapper sysFiledUserMapper;

    @Autowired
    public PowerDaoImpl(SecurityUserMapper securityUserMapper, PowerConfigMapper powerConfigMapper, PowerConfigDeptMapper powerConfigDeptMapper, SysConfigMapper sysConfigMapper, SysConfigSubMapper sysConfigSubMapper, SysConfigSubDictMapper sysConfigSubDictMapper, SecurityCommonDeptMapper securityCommonDeptMapper,
                        SecurityCommonDeptUserMapper deptUserMapper, SysMaskRuleMapper sysMaskRuleMapper,SysFiledUserMapper sysFiledUserMapper) {
        this.securityUserMapper = securityUserMapper;
        this.powerConfigMapper = powerConfigMapper;
        this.powerConfigDeptMapper = powerConfigDeptMapper;
        this.sysConfigMapper = sysConfigMapper;
        this.sysConfigSubMapper = sysConfigSubMapper;
        this.sysConfigSubDictMapper = sysConfigSubDictMapper;
        this.securityCommonDeptMapper = securityCommonDeptMapper;
        this.deptUserMapper = deptUserMapper;
        this.sysMaskRuleMapper = sysMaskRuleMapper;
        this.sysFiledUserMapper=sysFiledUserMapper;
    }


    //系统全局设置
    @Override
    public List<SysConfig> getSysConfig(String oid) {

        QueryWrapper<SysConfig> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("oid", "ALL");
        List<SysConfig> mapList = sysConfigMapper.selectList(queryWrapper);

        if(mapList==null || mapList.size()==0){
            queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("oid", oid);
            mapList = sysConfigMapper.selectList(queryWrapper);
        }
        return mapList;
    }

    /**
     * 系统脱敏设置
     * 20241101 目前不分院区 为全局设置
     */
    @Override
    public List<SysConfig> getSysConfigForHide(String oid) {

       /* QueryWrapper<SysConfig> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("configcode", "StartUse_HidePatKeyM");
        queryWrapper.eq("oid", oid);
        List<SysConfig> mapList = sysConfigMapper.selectList(queryWrapper);

        if(mapList==null || mapList.size()==0){
            queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("configcode", "StartUse_HidePatKeyM");
            queryWrapper.eq("oid", "ALL");
            mapList = sysConfigMapper.selectList(queryWrapper);
        }*/

        QueryWrapper<SysConfig> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("configcode", "StartUse_HidePatKeyM");
        queryWrapper.eq("oid", "ALL");
        List<SysConfig> mapList = sysConfigMapper.selectList(queryWrapper);
        return mapList;
    }

    @Override
    public SysConfig getSysConfigByConfigCode(String oid, String configCode) {

        QueryWrapper<SysConfig> queryWrapper = new QueryWrapper<>();
        if (StringUtils.isNotBlank(oid)) {
            queryWrapper.eq("oid", oid);
        }
        queryWrapper.eq("configcode", configCode);
        SysConfig sysConfig = sysConfigMapper.selectOne(queryWrapper);
        return sysConfig;

    }

    @Override
    @Transactional
    public boolean updateSysConfig(String oid, String configCode, String configValue) {
        UpdateWrapper<SysConfig> updateWrapper = new UpdateWrapper<>();
        updateWrapper.eq("configcode", configCode)
                .eq("oid", oid);
        SysConfig sysConfig = new SysConfig();
        sysConfig.setConfigValue(configValue);
        int update = sysConfigMapper.update(sysConfig, updateWrapper);
        return update > 0;

    }

    //用户权限设置
    @Override
    public PowerConfig getPowerConfigByType(String oid, String userCode, String type) {

        QueryWrapper<PowerConfig> queryWrapper = new QueryWrapper<>();
        if ("ALL".equalsIgnoreCase(oid)) {
            queryWrapper.select("usercode", "type", "GROUP_CONCAT(itemcodes SEPARATOR '/') as itemcodes")
                    .eq("type", type)
                    .eq("usercode", userCode)
                    .groupBy("usercode", "type");
        } else {
            queryWrapper.eq(!"ALL".equalsIgnoreCase(oid), "oid", oid)
                    .eq("type", type)
                    .eq("usercode", userCode);
        }


        return powerConfigMapper.selectOne(queryWrapper);
    }

    @Override
    public PowerConfigDept getPowerConfigByDeptAndType(String oid, String deptcode, String type) {
        QueryWrapper<PowerConfigDept> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq(!"ALL".equalsIgnoreCase(oid), "oid", oid)
                .eq("type", type)
                .eq("deptcode", deptcode);


        return powerConfigDeptMapper.selectOne(queryWrapper);
    }


    @Override
    public List<Map<String, String>> getPowerConfig(String oid, String code, boolean isUser) {
        if (isUser) {
            List<Map<String, String>> powerConfigByUserCode = powerConfigMapper.getPowerConfigByUserCode(oid, code);
            return powerConfigByUserCode;
        } else {
            List<Map<String, String>> powerConfigByDeptCode = powerConfigDeptMapper.getPowerConfigByDeptCode(oid, code);
            return powerConfigByDeptCode;
        }
    }

    @Override
    public List<Map<String, String>> getVipConfig(String oid, String code, boolean isUser) {
        if (isUser) {
            List<Map<String, String>> powerConfigByUserCode = powerConfigMapper.getVipConfigByUserCode(oid, code);
            return powerConfigByUserCode;
        } else {
            List<Map<String, String>> powerConfigByDeptCode = powerConfigDeptMapper.getVipConfigByDeptCode(oid, code);
            return powerConfigByDeptCode;
        }
    }

    @Override
    public boolean insertPowerConfigByUser(String oid, String userCode, String deptCode,
                                           String type, String itemCodes, String date) {
        PowerConfig powerConfig = new PowerConfig();
        powerConfig.setOid(oid);
        powerConfig.setItemcodes(itemCodes);
        powerConfig.setDeptcode(deptCode);
        powerConfig.setUsercode(userCode);
        powerConfig.setType(type);
        powerConfig.setLastupdatetime(date);
        int i = powerConfigMapper.insert(powerConfig);
        return i > 0;

    }

    @Override
    public boolean insertPowerConfigByDept(String oid, String deptCode,
                                           String type, String itemCodes, String date) {

        PowerConfigDept powerConfigDept = new PowerConfigDept();
        powerConfigDept.setOid(oid);
        powerConfigDept.setDeptcode(deptCode);
        powerConfigDept.setType(type);
        powerConfigDept.setItemcodes(itemCodes);
        powerConfigDept.setLastupdatetime(date);

        int i = powerConfigDeptMapper.insert(powerConfigDept);
        return i > 0;

    }

    @Override
    public boolean insertVipConfigByDept(String oid, String deptCode,
                                         String type, String itemCodes, String date) {

        PowerConfigDept powerConfigDept = new PowerConfigDept();
        powerConfigDept.setOid(oid);
        powerConfigDept.setDeptcode(deptCode);
        powerConfigDept.setType(type);
        powerConfigDept.setItemcodes(itemCodes);
        powerConfigDept.setLastupdatetime(date);

        int i = powerConfigDeptMapper.insert(powerConfigDept);
        return i > 0;

    }

    @Override
    public boolean updatePowerConfigByType(String oid, String[] codes, String type,
                                           String value, boolean isUser) {

        int i = 0;

        if (isUser) {
            PowerConfig powerConfig = new PowerConfig();
            powerConfig.setItemcodes(value);
            UpdateWrapper<PowerConfig> updateWrapper = new UpdateWrapper<>();
            updateWrapper.eq("oid", oid)
                    .eq("type", type)
                    .in("usercode", codes);
            i = powerConfigMapper.update(powerConfig, updateWrapper);

        } else {
            UpdateWrapper<PowerConfigDept> updateWrapper = new UpdateWrapper<>();
            updateWrapper.eq("oid", oid)
                    .eq("type", type)
                    .in("deptcode", codes);
            PowerConfigDept powerConfigDept = new PowerConfigDept();
            powerConfigDept.setItemcodes(value);
            i = powerConfigDeptMapper.update(powerConfigDept, updateWrapper);
        }

        return i > 0;

    }

    //查询用户列表
    @Override
    public List<DeptListWithinUser> getUserList(String oid, String keyWord,
                                                String dept) {
        QueryWrapper<SecurityCommonDept> deptQueryWrapper = new QueryWrapper<>();
        deptQueryWrapper.eq(StringUtils.isNotBlank(oid), "org_code", oid);
        deptQueryWrapper.eq(StringUtils.isNotBlank(dept), "deptcode", dept);
        List<SecurityCommonDept> depts = securityCommonDeptMapper.selectList(deptQueryWrapper);
        List<String> deptCodeList = depts.stream().map(SecurityCommonDept::getDeptcode).collect(Collectors.toList());


        QueryWrapper<SecurityUser> userQueryWrapper = new QueryWrapper<>();
        userQueryWrapper.in(!(StringUtils.isBlank(oid) && StringUtils.isBlank(dept)), "deptcode", deptCodeList);
        userQueryWrapper.like(StringUtils.isNotBlank(keyWord), "username", keyWord).or()
                .like(StringUtils.isNotBlank(keyWord), "usercode", keyWord);


        List<SecurityUser> userList = securityUserMapper.selectList(userQueryWrapper);

        Map<String, DeptListWithinUser> deptListWithinUserMap = new HashMap<>();

        //筛选出已配置权限的用户
        QueryWrapper<PowerConfig> queryWrapper = new QueryWrapper<>();
        queryWrapper.isNotNull("itemcodes");
        List<PowerConfig> powerConfigList = powerConfigMapper.selectList(queryWrapper);
        List<String> userCodes = powerConfigList.stream().map(PowerConfig::getUsercode).distinct().collect(Collectors.toList());

        for (SecurityUser securityUser : userList) {
            String deptCode = securityUser.getDeptcode();
            String deptname = securityUser.getDeptname();

            DeptListWithinUser deptListWithinUser = deptListWithinUserMap.computeIfAbsent(deptCode, k -> new DeptListWithinUser(deptCode, deptname));
            List<UserVo> userVoList = deptListWithinUser.getUser();
            String usercode = securityUser.getUsercode();
            String username = securityUser.getUsername();
            //20250321 注释 效率太低
            //int i = powerConfigMapper.confirmItemcodesNotNull(usercode);
            int i = userCodes.contains(usercode) ? 1 : 0;
            UserVo userVo = new UserVo();
            userVo.setUserCode(usercode);
            userVo.setUserName(username);
            userVo.setPower(i);
            userVoList.add(userVo);


        }

        return new ArrayList<>(deptListWithinUserMap.values());
    }

    @Override
    public String selectDeptByUser(String oid, String userCode) {

        String deptcode = securityUserMapper.selectDeptcode(userCode);

        return deptcode;
    }


    @Override
    public String selectUserByUserCode(String oid, String userCode) {
        QueryWrapper<SecurityUser> queryWrapper = new QueryWrapper<>();
        queryWrapper.select("username")
                .eq("hos_oid", oid)
                .eq("usercode", userCode);
        List<Map<String, Object>> maps = securityUserMapper.selectMaps(queryWrapper);
        String name = (String) maps.get(0).get("username");
        return name;
    }

    @Override
    public List<Map<String, Object>> getDeptCodeList(String oid, String deptName) {
        QueryWrapper<SecurityUser> queryWrapper = new QueryWrapper<>();
        queryWrapper.select("DISTINCT deptcode", "deptname")
                .eq("hos_oid", oid)
                .isNotNull("deptcode")
                .apply(" LENGTH(trim(deptcode))>0")
                .isNotNull("deptname")
                .apply(" LENGTH(trim(deptname))>0")
                .like(StringUtils.isNotBlank(deptName), "deptname", deptName);
        List<Map<String, Object>> maps = securityUserMapper.selectMaps(queryWrapper);
        return maps;

//        List<Map<String, String>> rs = new ArrayList<Map<String, String>>();
//        StringBuffer sql = new StringBuffer();
//        sql.append("SELECT DISTINCT deptcode,deptname FROM security_user where oid=? ");
//        if (StringUtils.isNotBlank(deptName)) {
//            sql.append("AND deptname like '%" + deptName + "%' ");
//        }
//
//        Query query = createSqlQuery(sql.toString(),oid);
//        List<Object> listobj = query.list();
//        for (Object obj : listobj) {
//            Map<String, String> map = new HashMap<String, String>();
//            Object[] objArr = (Object[]) obj;
//            if (objArr[0] != null && StringUtils.isNotBlank(objArr[0].toString())) {
//                map.put("deptCode", objArr[0].toString());//
//                map.put("deptName", objArr[1].toString());//
//                rs.add(map);
//            }
//        }
//        return rs;
    }

    @Override
    public List<Map<String, String>> getAllDept(String oid) {
        List<Map<String, String>> deptList = securityUserMapper.getDeptList();
        return deptList;
//        List<Map<String, String>> rs = new ArrayList<Map<String, String>>();
//        StringBuffer sql = new StringBuffer();
//        sql.append("SELECT DISTINCT(deptcode) FROM security_user where oid =? ");
//
//        Query query = createSqlQuery(sql.toString(),oid);
//        List<String> listobj = query.list();
//        for (String obj : listobj) {
//            Map<String, String> map = new HashMap<String, String>();
//            if (StringUtils.isNotBlank(obj)) {
//                map.put("deptCode", obj);//
//                rs.add(map);
//            }
//        }
//        Map<String, String> map = new HashMap<String, String>();
//        map.put("deptCode", "QT");//
//        rs.add(map);
//        return rs;
    }

    @Override
    public boolean deleteDeptPower(String oid) {

        Map<String, Object> condition = new HashMap<>();
        condition.put("oid", oid);
        int i = powerConfigDeptMapper.deleteByMap(condition);
        return i > 0;
//        try {
//            String sql = "delete from civ_power_config_dept where oid = ?";
//            executeBySql(sql,oid);
//        } catch (Exception e) {
//            e.printStackTrace();
//            return false;
//        }
//        return true;
    }

    @Override
    public boolean deleteUserPower(String oid) {
        Map<String, Object> condition = new HashMap<>();
        condition.put("oid", oid);
        int i = powerConfigMapper.deleteByMap(condition);
        return i > 0;
//        try {
//            String sql = "delete from civ_power_config where oid = ?";
//            executeBySql(sql,oid);
//        } catch (Exception e) {
//            e.printStackTrace();
//            return false;
//        }
//        return true;
    }
//
//    //////////////////////////////////////////////////////////////////////////
//    @Override
//    public List<Map<String, String>> getModernPower(String oid,String usercode) {
//        List<Map<String, String>> list = new ArrayList<Map<String, String>>();
//        if (StringUtils.isBlank(usercode)) {
//            return list;
//        }
//
//        String sqlString = "select pk_authority,code,name,project from security_authority where pk_authority in ("
//                + "select pk_authority from security_role_authority where pk_role in("
//                + "select pk_role from security_user_role where pk_user in("
//                + "select pk_user from security_user where oid = ? and usercode = '" + usercode + "')"
//                + "and pk_role in(select pk_role from security_role where project='hdr-civ')"
//                + ")) and code like 'civ_modern%'  and enabled = 0";
//
//        Query query = createSqlQuery(sqlString,oid);
//        List<Object> listobj = query.list();
//        for (Object obj : listobj) {
//            Object[] objArr = (Object[]) obj;
//            Map<String, String> map = new HashMap<String, String>();
//            map.put("pk_authority", objArr[0].toString());//
//            map.put("code", objArr[1].toString());//
//            map.put("name", objArr[2].toString());//
//            map.put("project", objArr[3].toString());//
//            list.add(map);
//        }
//        return list;
//    }
//
//    @Override
//    public List<Map<String, String>> getEMRPower(String oid,String usercode) {
//        List<Map<String, String>> list = new ArrayList<Map<String, String>>();
//        if (StringUtils.isBlank(usercode)) {
//            return list;
//        }
//
//        String sqlString = "select pk_authority,code,name,project from security_authority where pk_authority in ("
//                + "select pk_authority from security_role_authority where pk_role in("
//                + "select pk_role from security_user_role where pk_user in("
//                + "select pk_user from security_user where oid = ? and usercode = '" + usercode + "')"
//                + "and pk_role in(select pk_role from security_role where project='hdr-civ')"
//                + ")) and code like 'civ_emr%'  and enabled = 0";
//
//        Query query = createSqlQuery(sqlString,oid);
//        List<Object> listobj = query.list();
//        for (Object obj : listobj) {
//            Object[] objArr = (Object[]) obj;
//            Map<String, String> map = new HashMap<String, String>();
//            map.put("pk_authority", objArr[0].toString());//
//            map.put("code", objArr[1].toString());//
//            map.put("name", objArr[2].toString());//
//            map.put("project", objArr[3].toString());//
//            list.add(map);
//        }
//        return list;
//    }
//
//    @Override
//    public List<Map<String, String>> getExamPower(String oid,String usercode) {
//        //sql
//        String sqlString = "select pk_authority,code,name,project from security_authority where pk_authority in ("
//                + "select pk_authority from security_role_authority where pk_role in("
//                + "select pk_role from security_user_role where pk_user in("
//                + "select pk_user from security_user where  oid = ? and usercode = '" + usercode + "')"
//                + "and pk_role in(select pk_role from security_role where project='hdr-civ')"
//                + ")) and code like 'civ_exam%'  and enabled = 0";
//        SQLQuery query = (SQLQuery) createSqlQuery(sqlString,oid);
//        //map封装数据
//        query.setResultTransformer(Transformers.ALIAS_TO_ENTITY_MAP);
//        return query.list();
//    }

    /**
     * 获取脱敏字段设置
     *
     * @return
     */
    public List<Map<String, Object>> getSysHideConfig(String oid, String code) {
        QueryWrapper<SysConfigSub> queryWrapper = new QueryWrapper<>();
        queryWrapper.select("config_code", "config_value", "config_name", "enabled", "orm_fileds", "mask_rule_code", "mask_rule_name")
                .eq(StringUtils.isNotBlank(oid), "oid", oid)
                .eq("super_code", "StartUse_HidePatKeyM")
                .eq(StringUtils.isNotBlank(code), "config_code", code);
        List<Map<String, Object>> list = sysConfigSubMapper.selectMaps(queryWrapper);

//        String sqlString = " select config_code,config_value,config_name,enabled,orm_fileds  from civ_sys_config_sub where oid = ? and super_code = 'StartUse_HidePatKeyM' ";
//        if (StringUtils.isNotBlank(code)) {
//            sqlString += " and  config_code = '" + code + "'";
//        }
//        List<Map<String, String>> list = new ArrayList<Map<String, String>>();
//        Query query = createSqlQuery(sqlString, oid);
//        List<Object> listobj = query.list();
//        for (Object obj : listobj) {
//            Object[] objArr = (Object[]) obj;
//            Map<String, String> map = new HashMap<String, String>();
//            map.put("code", objArr[0].toString());
//            map.put("value", objArr[1].toString());
//            map.put("name", objArr[2].toString());
//            map.put("inuse", objArr[3].toString());
//            map.put("orm_fileds", objArr[4].toString());
//            list.add(map);
//        }
        return list;
    }

    public List<Map<String, String>> getSysHideDict(String oid, String keyWord) {

        List<Map<String, String>> sysHideDictList = sysConfigSubDictMapper.getSysSubDictList(oid, keyWord);

        return sysHideDictList;
    }

    /**
     * 更新脱敏字段配置
     *
     * @param code
     * @param value
     * @param enabled
     * @return
     */
    public boolean updateSysHideConfig(String oid, String code, String value, String enabled, String fields,String maskRuleCode, String maskRuleName) {
        UpdateWrapper<SysConfigSub> updateWrapper = new UpdateWrapper<>();
        updateWrapper.eq("config_code", code);
        SysConfigSub sysConfigSub = new SysConfigSub();
        sysConfigSub.setConfigValue(value);
        sysConfigSub.setEnabled(enabled);
        sysConfigSub.setOrmFileds(fields);
        sysConfigSub.setMaskRuleCode(maskRuleCode);
        sysConfigSub.setMaskRuleName(maskRuleName);
        int i = sysConfigSubMapper.update(sysConfigSub, updateWrapper);
        return i > 0;

    }

    @Override
    public int deleteSysHideConfig(String configCode) {
        Map<String, Object> map = new HashMap<>();
        map.put("config_code", configCode);
        int i = sysConfigSubMapper.deleteByMap(map);
        return i;
    }

    @Override
    public int insertSysHideConfig(String configName, String configValue, String fields, String maskRuleCode, String maskRuleName) {
        SysConfigSub sysConfigSub = new SysConfigSub();
        /*String configCode = "INFO_HIDDEN_" + configName;
        sysConfigSub.setConfigCode(configCode);*/
        sysConfigSub.setConfigCode(UUID.randomUUID().toString().replaceAll("-",""));
        sysConfigSub.setConfigName(configName);
        sysConfigSub.setConfigValue(configValue);
        sysConfigSub.setSuperCode("StartUse_HidePatKeyM");
        sysConfigSub.setOrmFileds(fields);
        sysConfigSub.setEnabled("1");
        sysConfigSub.setMaskRuleCode(maskRuleCode);
        sysConfigSub.setMaskRuleName(maskRuleName);
        sysConfigSub.setOid("ALL");
        int i = sysConfigSubMapper.insert(sysConfigSub);
        return i;
    }

    @Override
    public int updateSysConfigSubDict(String fields, String type) {
        SysConfigSubDict sysConfigSubDict = new SysConfigSubDict();
        if ("add".equals(type)) {
            sysConfigSubDict.setIsAdd("1");
        } else {
            sysConfigSubDict.setIsAdd("0");
        }

        String[] field = fields.split(",");
        int j = 0;
        for (int i = 0; i < field.length; i++) {
            QueryWrapper<SysConfigSubDict> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("filed_code", field[i]);
            j = sysConfigSubDictMapper.update(sysConfigSubDict, queryWrapper);
        }

        return j;
    }

    /**
     * 获取脱敏规则
     */
    public List<Map<String, Object>> queryMaskRules(String oid) {
        QueryWrapper<SysMaskRule> queryWrapper = new QueryWrapper<>();
        queryWrapper.select("config_code", "config_name", "config_desc", "params")
                .eq(StringUtils.isNotBlank(oid), "oid", oid)
                .eq("enabled", "1");
        List<Map<String, Object>> list = sysMaskRuleMapper.selectMaps(queryWrapper);
        return list;
    }


    //查询用户列表
    @Override
    public List< Map<String,String>> getUserOrRoleList(String oid, String keyWord, String deptCode) {
        QueryWrapper<SecurityCommonDept> deptQueryWrapper = new QueryWrapper<>();
        deptQueryWrapper.eq(StringUtils.isNotBlank(oid), "org_code", oid);
        deptQueryWrapper.eq(StringUtils.isNotBlank(deptCode), "deptcode", deptCode);
        List<SecurityCommonDept> depts = securityCommonDeptMapper.selectList(deptQueryWrapper);
        List<String> deptCodeList = depts.stream().map(SecurityCommonDept::getDeptcode).collect(Collectors.toList());

        QueryWrapper<SecurityUser> userQueryWrapper = new QueryWrapper<>();
        userQueryWrapper.in( "deptcode", deptCodeList);
        userQueryWrapper.like(StringUtils.isNotBlank(keyWord), "username", keyWord).or().like(StringUtils.isNotBlank(keyWord), "usercode", keyWord);
        List<SecurityUser> userList = securityUserMapper.selectList(userQueryWrapper);

        List< Map<String,String>> resultList=new ArrayList<>();
        for (SecurityUser securityUser : userList) {
            Map<String,String> map=new HashMap<>();
            String usercode = securityUser.getUsercode();
            String username = securityUser.getUsername();
            map.put("usercode",usercode);
            map.put("username",username);
            map.put("oid",oid);
            resultList.add(map);
        }

        return resultList;
    }

    @Override
    public int deleteFiledByUser(String oid,String userCode) {
        Map<String, Object> map = new HashMap<>();
        map.put("oid", oid);
        map.put("user_code", userCode);
        int i = sysFiledUserMapper.deleteByMap(map);
        return i;
    }

    @Override
    public int deleteFiledByUsers(String oid, List<String> userCodes) {
        QueryWrapper<SysFiledUser> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("oid", oid);
        queryWrapper.in("user_code", userCodes);
        int i = sysFiledUserMapper.delete(queryWrapper);
        return i;
    }

    @Override
    public int insertFiledUser(String oid,String userCode,String filedCode) {
        SysFiledUser sysFiledUser = new SysFiledUser();
        sysFiledUser.setId(UUID.randomUUID().toString().replaceAll("-",""));
        sysFiledUser.setFiledCode(filedCode);
        sysFiledUser.setUserCode(userCode);
        sysFiledUser.setOid(oid);
        return sysFiledUserMapper.insert(sysFiledUser);
    }

}
