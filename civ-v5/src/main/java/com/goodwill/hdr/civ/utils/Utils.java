package com.goodwill.hdr.civ.utils;


import com.goodwill.hdr.core.enums.EnumType;
import com.goodwill.hdr.core.orm.Page;
import com.goodwill.hdr.core.utils.CollectionUtils;
import net.sf.json.*;
import net.sf.json.xml.XMLSerializer;
import org.apache.commons.lang.enums.EnumUtils;
import org.apache.commons.lang3.StringUtils;
import org.dom4j.Document;
import org.dom4j.DocumentHelper;
import org.dom4j.Element;
import org.dom4j.io.OutputFormat;
import org.dom4j.io.XMLWriter;
import org.w3c.dom.Node;
import org.w3c.dom.NodeList;
import org.xml.sax.SAXException;
import sun.misc.BASE64Decoder;
import sun.misc.BASE64Encoder;

import javax.xml.parsers.DocumentBuilder;
import javax.xml.parsers.DocumentBuilderFactory;
import javax.xml.parsers.ParserConfigurationException;
import javax.xml.xpath.XPath;
import javax.xml.xpath.XPathConstants;
import javax.xml.xpath.XPathExpressionException;
import javax.xml.xpath.XPathFactory;
import java.io.*;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.math.BigDecimal;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;

public class Utils {

	/**
	 * 默认标准时间格式 yyyy-MM-dd hh:mm:ss
	 */
	public static final String defaultDateFormat = "yyyy-MM-dd hh:mm:ss";

	/**
	 * 由出生日期获得年龄
	 * @param birthDay
	 * @return
	 * @throws Exception
	 */
	public static int getAgebyBirthDay(Date birthDay) {
		Calendar cal = Calendar.getInstance();

		if (cal.before(birthDay)) {
			throw new IllegalArgumentException("The birthDay is before Now.It's unbelievable!");
		}
		int yearNow = cal.get(Calendar.YEAR);
		int monthNow = cal.get(Calendar.MONTH);
		int dayOfMonthNow = cal.get(Calendar.DAY_OF_MONTH);
		cal.setTime(birthDay);

		int yearBirth = cal.get(Calendar.YEAR);
		int monthBirth = cal.get(Calendar.MONTH);
		int dayOfMonthBirth = cal.get(Calendar.DAY_OF_MONTH);

		int age = yearNow - yearBirth;

		if (monthNow <= monthBirth) {
			if (monthNow == monthBirth) {
				if (dayOfMonthNow < dayOfMonthBirth)
					age--;
			} else {
				age--;
			}
		}
		return age;
	}

	/**
	 * @Description
	 * 方法描述: 将字符串编码转化为utf8
	 * @return 返回类型： String
	 * @param str 字符串
	 * @return
	 */
	public static String convertToUtf8(String str) {
		if (StringUtils.isNotBlank(str)) {
			try {
				return new String(str.getBytes("iso-8859-1"), "UTF-8");
			} catch (UnsupportedEncodingException e) {
				e.printStackTrace();
			}
		}
		return str;
	}

	/**
	 * @Description
	 * 方法描述: 根据日期类型字符串计算开始日期
	 * @return 返回类型： String
	 * @param dateType 日期范围   near_7_day
	 * 前端格式：
	 * <li _value="near_7_day">近7天</li>
	 * <li _value="near_30_day">近30天</li>
	 * <li _value="near_3_month">近3个月</li>
	 * <li _value="near_1_year">近1年</li>
	 * <li _value="near_3_year">近3年</li>
	 * @return
	 */
	public static String calStartDate(String dateType) {
		//开始时间
		String startDate = null;
		//分割日期类型
		String[] dates = dateType.split("_");
		//当前日期
		LocalDateTime now = LocalDateTime.now();
		DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
		//近期  时间前推
		if ("near".equals(dates[0])) {
			if ("day".equals(dates[2])) {
				startDate=now.minusDays(Integer.parseInt(dates[1])).format(formatter);
			} else if ("month".equals(dates[2])) {
				startDate=now.minusMonths(Integer.parseInt(dates[1])).format(formatter);
			} else if ("year".equals(dates[2])) {
				startDate=now.minusYears(Integer.parseInt(dates[1])).format(formatter);
			}
		}else {
			startDate="";
		}

		return startDate;
	}


	/**
	 * @Description
	 * 方法描述: 获取时间字符串
	 * @return 返回类型： String
	 * @param fmt 时间格式
	 * @param time 完整的时间字符串 yyyy-MM-dd HH:mm:ss
	 * @return
	 */
	public static String getDate(String fmt, String time) {
	    if(StringUtils.isBlank(time)){
	        return "";
        }
		DateTimeFormatter dateTimeFormatter=DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
		return LocalDateTime.parse(time,dateTimeFormatter).format(DateTimeFormatter.ofPattern(fmt));
	}

	/**
	 * @Description
	 * 方法描述: 比较时间
	 * @return 返回类型： Integer
	 * @param date1  第一个时间字符串
	 * @param date2  第二个时间字符串
	 * @return 0:相等  >0:前大于后  <0:前小于后
	 */
	public static Integer compareStrDateTime(String date1, String date2) {
		Integer result = null;
		DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

			LocalDateTime ndate1 = LocalDateTime.parse(date1, formatter);
			LocalDateTime ndate2 = LocalDateTime.parse(date2, formatter);
			result = ndate1.compareTo(ndate2);

		return result;
	}

	/**
	 * @Description
	 * 方法描述: 向Map中填充数据，若value为空，则使用指定替换符替换
	 * @return 返回类型： void
	 * @param map Map集合
	 * @param key 键
	 * @param value 值
	 * @param replace 空值替换符
	 * @param update 若key已经存在且对应的value不为空，是否更新已有value
	 */
	public static void checkAndPutToMap(Map<String, String> map, String key, String value, String replace,
                                        boolean update) {
		boolean put = false; //是否更新
		if (map.containsKey(key)) { //key已存在
			//key存在 对应的value为空
			if (null != replace && StringUtils.equals(map.get(key).toString(), replace)) {
				put = true;
			} else {
				//key存在 对应的value不为空 根据update决定是否更新key对应的value
				if (update) {
					put = true;
				}
			}
		} else { //key不存在 直接更新
			put = true;
		}

		//终止执行
		if (!put) {
			return;
		}

		//更新
		if (StringUtils.isBlank(value)) {
			if (null != replace) {
				map.put(key, replace);
			}
		} else {
			map.put(key, value);
		}
	}

	/**
	 * @Description
	 * 方法描述: List<Map<>>多字段排序
	 * @return 返回类型： void
	 * @param list 排序集合
	 * @param columns 排序字段  [visit_time]
	 * @param orders 排序规则     [Sort.DESC]
	 */
	public static void sortListMulti(List<?> list, String[] columns, String[] orders) {
		//排序规则
		List<Page.Sort> sorts = new ArrayList<>();
		//构建排序规则
		for (int i = 0; i < columns.length; i++) {
			Page.Sort sort = new Page.Sort(columns[i], orders[i]);
			sorts.add(sort);
		}
		//排序
		CollectionUtils.sortMuti(list, sorts);
	}

	/**
	 * @Description
	 * 方法描述: List<Map<>>按时间排序
	 * @return 返回类型： void
	 * @param list 集合
	 * @param orderby 排序字段
	 * @param dir 排序规则
	 */
	public static void sortListByDate(List<?> list, String orderby, String dir) {
		//排序规则
		List<Page.Sort> sorts = new ArrayList<>();
		//构建排序规则
		Page.Sort sort = new Page.Sort(orderby, dir);
		sorts.add(sort);
		//排序
		CollectionUtils.sortMuti(list, sorts);
	}

	/**
	 * @Description
	 * 方法描述:Map集合 按照key排序
	 * @return 返回类型： Map<String, Object>
	 * @param map 要排序的map 可处理特殊key:2016年8月
	 * @param orderdir 排序规则  默认升序
	 * @return 排序后的map
	 */
	public static Map<String, Object> sortMapByKey(Map<String, Object> map, final String orderdir) {
		if (null == map || map.isEmpty()) {
			return null;
		}
		//创建排序集合 定义排序规则
		Map<String, Object> sortMap = new TreeMap<String, Object>(new Comparator<String>() {

			@Override
			public int compare(String o1, String o2) {
				if (o1.contains("年") && o2.contains("年")) {
					String y1 = o1.substring(0, o1.indexOf("年"));
					String m1 = o1.substring(o1.indexOf("年") + 1, o1.indexOf("月"));
					m1 = (m1.length() == 1) ? "0" + m1 : m1;
					o1 = y1 + "-" + m1;
					String y2 = o2.substring(0, o2.indexOf("年"));
					String m2 = o2.substring(o2.indexOf("年") + 1, o2.indexOf("月"));
					m2 = (m2.length() == 1) ? "0" + m2 : m2;
					o2 = y2 + "-" + m2;
				}

				//降序
				if (StringUtils.isNotBlank(orderdir) && "desc".equals(orderdir)) {
					return o2.compareTo(o1);
				}
				//升序
				return o1.compareTo(o2);
			}
		});
		//排序
		sortMap.putAll(map);
		return sortMap;
	}

	/**
	 * 比较两个数值大小
	 * @param val1
	 * @param val2
	 * @param oper
	 * @return
	 */
	public static boolean compareStr(double val1, double val2, String oper) {
		boolean result = false;
		if (StringUtils.isBlank(oper)) {
			result = false;
		}
		switch (oper) {
		case ">":
			result = (val1 > val2);
			break;
		case "≥":
			result = (val1 >= val2);
			break;
		case "=":
			result = (val1 == val2);
			break;
		case "≤":
			result = (val1 <= val2);
			break;
		case "<":
			result = (val1 < val2);
			break;
		case "≠":
			result = (val1 != val2);
			break;
		default:
			break;
		}
		return result;
	}

	/**
	 * 将带分隔符号的字符串转换为List,空字符串或者空格将被忽略掉
	 * @param str
	 * @param splitOper
	 * @return
	 */
	public static List<String> StrSplitToList(String str, String splitOper) {

		List<String> list = new ArrayList<String>();
		if (!str.equals("")) {
			if (str.contains(splitOper)) {
				String[] arr = str.split(splitOper);
				for (String s : arr) {
					if (StringUtils.isNotBlank(s)) {
						list.add(s);
					}
				}
			} else {
				list.add(str);
			}
		}

		return list;
	}

	/**
	 * 将格式化字符串直接转换为Map<String, String>类型数据
	 * @param str 示例 k1:v1,k2:v2,k3:v3
	 * @param splitMap 分隔每个Map分隔符
	 * @return
	 */
	public static Map<String, String> StrSplitToMap(String str, String splitMap, String splitKV) {

		Map<String, String> mapResult = new HashMap<String, String>();

		if (!str.equals("")) {
			if (str.contains(splitMap)) {
				String[] arr = str.split(splitMap);
				for (String s : arr) {
					if (StringUtils.isNotBlank(s)) {
						if (s.contains(splitKV)) {
							String[] arrMap = s.split(splitKV);
							mapResult.put(arrMap[0], arrMap[1]);
						}
					}
				}
			} else {
				if (str.contains(splitKV)) {
					String[] arrMap = str.split(splitKV);
					mapResult.put(arrMap[0], arrMap[1]);
				}
			}
		}

		return mapResult;
	}

	/**
	 * 将带分隔符号的字符串转换为List,空字符串或者空格将被忽略掉
	 * 最后的结果去重
	 * @param str
	 * @param splitOper
	 * @return
	 */
	public static List<String> StrSplitToListDW(String str, String splitOper) {

		List<String> list = new ArrayList<String>();
		if (!str.equals("")) {
			if (str.contains(splitOper)) {
				String[] arr = str.split(splitOper);
				for (String s : arr) {
					if (StringUtils.isNotBlank(s) && (!list.contains(s))) {
						list.add(s);
					}
				}
			} else {
				list.add(str);
			}
		}

		return list;
	}

	/**
	 * 将List中的数据去重
	 * @param list
	 * @return
	 */
	public static <T> List<T> listDW(List<T> list) {
		List<T> dwList = new ArrayList<T>();
		for (int i = 0; i < list.size(); i++) {
			if (!dwList.contains(list.get(i))) {
				dwList.add(list.get(i));
			}
		}
		return dwList;
	}

	/**
	 * 获取当前时间文本
	 * @param formatStr
	 * @return
	 */
	public static String dateToStr(Date date, String formatStr) {
		SimpleDateFormat df = new SimpleDateFormat(formatStr);
		return df.format(date);
	}

	/**
	 * 将字符串转换为日期类型
	 * @param date
	 * @param formatStr
	 * @return
	 */
	public static Date strToDate(String date, String formatStr) {
		SimpleDateFormat sdf = new SimpleDateFormat(formatStr);
		Date cdate = new Date();
		try {
			cdate = sdf.parse(date);
		} catch (ParseException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
		return cdate;
	}

	/**
	 * 日期减计算，默认为strDate减去diff
	 * @param diff 单位：Y年，M月，D日，H小时，m分钟，s秒
	 * @oper 加 add，减 sub
	 * @return
	 */
	public static Date dateDiff(Date date, String diff, String oper) {
		Calendar rightNow = Calendar.getInstance();
		rightNow.setTime(date);
		int c = -1;
		if (diff.contains("Y")) {
			c = Calendar.YEAR;
		}
		if (diff.contains("M")) {
			c = Calendar.MONTH;
		}
		if (diff.contains("D")) {
			c = Calendar.DAY_OF_YEAR;
		}
		if (diff.contains("H")) {
			c = Calendar.HOUR;
		}
		if (diff.contains("m")) {
			c = Calendar.MINUTE;
		}
		if (diff.contains("S")) {
			c = Calendar.SECOND;
		}
		if (c == -1) {
			return date;
		}
		Integer diffvalue = Integer.parseInt(diff.substring(0, diff.length() - 1));
		if (oper.equals("add")) {
			rightNow.add(c, diffvalue);
		} else {
			rightNow.add(c, -diffvalue);
		}

		return rightNow.getTime();
	}

	/**
	 * 两个时间差
	 * @param begin
	 * @param end
	 * @param diff D H m S
	 * @return
	 */
	public static long dateDiff(Date begin, Date end, String diff) {
		long between = (end.getTime() - begin.getTime()) / 1000;//除以1000是为了转换成秒
		if (diff.contains("D")) {
			return between / (24 * 3600);
		} else if (diff.contains("H")) {
			return between / 3600;
		} else if (diff.contains("m")) {
			return between / 60;
		} else if (diff.contains("S")) {
			//返回秒
			return between;
		} else {
			return 0;
		}
	}

	/**
	 * 计算当前时间向前或者向后的天数时间
	 * @param preback pre天数向前推,back天数向后推
	 * @param dayCount
	 * @return
	 */
	public static String CurrDateDiff(String preback, int dayCount) {
		String result = "";
		Date d = new Date();
		SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd");
		if (preback.equals("pre")) {
			result = df.format(new Date(d.getTime() - dayCount * 24 * 60 * 60 * 1000));
		} else {
			result = df.format(new Date(d.getTime() + dayCount * 24 * 60 * 60 * 1000));
		}
		return result;
	}

	/**
	 * 判断当前时间是否在[startTime, endTime]区间，注意时间格式要一致
	 *
	 * @param nowTime 当前时间
	 * @param startTime 开始时间
	 * @param endTime 结束时间
	 * @return
	 * <AUTHOR>
	 */
	public static boolean isEffectiveDate(Date nowTime, Date startTime, Date endTime) {
		if (nowTime.getTime() == startTime.getTime() || nowTime.getTime() == endTime.getTime()) {
			return true;
		}

		Calendar date = Calendar.getInstance();
		date.setTime(nowTime);

		Calendar begin = Calendar.getInstance();
		begin.setTime(startTime);

		Calendar end = Calendar.getInstance();
		end.setTime(endTime);

		if (date.after(begin) && date.before(end)) {
			return true;
		} else {
			return false;
		}
	}

	/**
	 * 根据code获取枚举label
	 * @return EnumType
	 * @param enumClass
	 * @param code
	 * @return
	 */
	public static String getEnumLabelByCode(Class enumClass, String code) {
		String result = "";
		for (Iterator iter = EnumUtils.iterator(enumClass); iter.hasNext();) {
			Enum enm = (Enum) iter.next();
			EnumType vt = (EnumType) enm;
			if (vt.getCode().equals(code)) {
				result = vt.getLabel();
			}
		}
		return result;
	}

	/**
	 * json 转换为xml
	 * @param json
	 * @return
	 */
	public static synchronized String jsontoXml(String json) {
		try {
			XMLSerializer serializer = new XMLSerializer();
			JSON jsonObject = JSONSerializer.toJSON(json);
			String xmlString = serializer.write(jsonObject);
			return xmlString.replace("<?xml version=\"1.0\" encoding=\"UTF-8\"?>", "");
		} catch (Exception e) {
			e.printStackTrace();
		}
		return null;
	}

	/**
	 * map转换为xml
	 * @param map
	 * @return
	 */
	public static synchronized String maptoXml(Map map) {
		Document document = DocumentHelper.createDocument();
		Element nodeElement = document.addElement("node");
		for (Object obj : map.keySet()) {
			Element keyElement = nodeElement.addElement("key");
			keyElement.addAttribute("label", String.valueOf(obj));
			keyElement.setText(String.valueOf(map.get(obj)));
		}
		return doc2String(document);
	}

	/**
	 * 文档转化为输出流
	 * @param document
	 * @return
	 */
	public static String doc2String(Document document) {
		String s = "";
		try {
			// 使用输出流来进行转化
			ByteArrayOutputStream out = new ByteArrayOutputStream();
			// 使用UTF-8编码
			OutputFormat format = new OutputFormat("   ", true, "UTF-8");
			format.setSuppressDeclaration(false);
			XMLWriter writer = new XMLWriter(out, format);
			writer.write(document);
			s = out.toString("UTF-8");
		} catch (Exception ex) {
			ex.printStackTrace();
		}
		return s;
	}

	/**
	 * 将list转换为字符串，并带分隔符
	 * @param list
	 * @param separator
	 * @return
	 */
	public static String listToString(List list, char separator) {
		return org.apache.commons.lang.StringUtils.join(list.toArray(), separator);
	}

	/**
	 * 字符串数组转换为字符串
	 * @param arr
	 * @return
	 */
	public static String arrToString(String[] arr, String separator) {
		StringBuffer sb = new StringBuffer();
		int i = 0;
		for (String str : arr) {
			if (i == arr.length - 1) {
				sb.append(str);
			} else {
				sb.append(str + separator);
			}
			i++;
		}
		return sb.toString();
	}

	/**
	 * 字符串是否是数字
	 * @param value
	 * @return
	 */
	public static boolean isNumber(String value) {
		if (StringUtils.isBlank(value)) {
			return false;
		}
		try {
			Double.parseDouble(value);
		} catch (NumberFormatException e) {
			return false;
		}
		return true;
	}

	/**
	 * @Description
	 * 方法描述:对一系列Map按照TIME进行排序
	 * @return 返回类型： List<Map<String,String>>
	 * @param list
	 * @return
	 */
	public static List<Map<String, String>> sortListDescByDate(List<Map<String, String>> list, final String sortBy) {
		List<Map<String, String>> result = new LinkedList<Map<String, String>>(list);
		if (list.size() > 1) {
			Collections.sort(result, new Comparator<Map<String, String>>() {
				@Override
				public int compare(Map<String, String> o1, Map<String, String> o2) {
					String s1 = o2.get(sortBy);
					String s2 = o1.get(sortBy);
					if (StringUtils.isBlank(s1)) {
						s1 = "1900-01-01";
					}
					if (StringUtils.isBlank(s2)) {
						s2 = "1900-01-01";
					}
					return (s1).compareTo(s2);
				}
			});
		}
		return result;
	}

	/**
	 * 对list中的Map进行排序
	 * @param list
	 * @param sortColumn 指定排序列
	 * @param sortBy asc，desc
	 * @return
	 */
	public static List<Map<String, String>> sortList(List<Map<String, String>> list, final String sortColumn,
                                                     final String sortBy) {
		List<Map<String, String>> result = new LinkedList<Map<String, String>>(list);
		/*for (Map<String, String> map : list) {
			result.add(map);
		}*/
		if (list.size() > 1) {
			Collections.sort(result, new Comparator<Map<String, String>>() {
				@Override
				public int compare(Map<String, String> o1, Map<String, String> o2) {
					String s1 = o2.get(sortColumn);
					String s2 = o1.get(sortColumn);
					if (StringUtils.isBlank(s1)) {
						s1 = "";
					}
					if (StringUtils.isBlank(s2)) {
						s2 = "";
					}
					if (StringUtils.isBlank(sortBy) || sortBy.equals("asc")) {
						return (s2).compareTo(s1);
					} else {
						return (s1).compareTo(s2);
					}
				}
			});
		}
		return result;
	}

	/**
	 * 排序listmap里面value值为数字 类型字符串
	 * @param list
	 * @param sortColumn
	 * @param sortBy
	 * @return
	 */
	public static List<Map<String, String>> sortNumStringList(List<Map<String, String>> list, final String sortColumn,
                                                              final String sortBy) {
		List<Map<String, String>> result = new LinkedList<Map<String, String>>(list);
		/*for (Map<String, String> map : list) {
			result.add(map);
		}*/
		if (list.size() > 1) {
			Collections.sort(result, new Comparator<Map<String, String>>() {
				@Override
				public int compare(Map<String, String> o1, Map<String, String> o2) {
					String s1 = o2.get(sortColumn);
					String s2 = o1.get(sortColumn);

					if (StringUtils.isBlank(s1)) {
						s1 = "0.00";
					}
					if (StringUtils.isBlank(s2)) {
						s2 = "0.00";
					}
					Double num1 = 0.00;
					Double num2 = 0.00;
					try {
						num1 = Double.valueOf(s1);
						num2 = Double.valueOf(s2);
					}catch (Exception e){
						return 0;
					}
					if (StringUtils.isBlank(sortBy) || sortBy.equals("asc")) {
						return num2.compareTo(num1);
					} else {
						return num1.compareTo(num2);
					}
				}
			});
		}
		return result;
	}

	/**
	 * 排序listmap里面value值为数字 类型字符串
	 * @param list
	 * @param sortColumn
	 * @param sortBy
	 * @return
	 */
	public static List<Map<String, Object>> sortNumObjectList(List<Map<String, Object>> list, final String sortColumn,
                                                              final String sortBy) {
		List<Map<String, Object>> result = new LinkedList<Map<String, Object>>(list);
		/*for (Map<String, String> map : list) {
			result.add(map);
		}*/
		if (list.size() > 1) {
			Collections.sort(result, new Comparator<Map<String, Object>>() {
				public int compare(Map<String, Object> o1, Map<String, Object> o2) {
					String s1 = (String)o2.get(sortColumn);
					String s2 = (String)o1.get(sortColumn);

					if (StringUtils.isBlank(s1)) {
						s1 = "0.00";
					}
					if (StringUtils.isBlank(s2)) {
						s2 = "0.00";
					}
					Double num1 = 0.00;
					Double num2 = 0.00;
					try {
						num1 = Double.valueOf(s1);
						num2 = Double.valueOf(s2);
					}catch (Exception e){
						return 0;
					}
					if (StringUtils.isBlank(sortBy) || sortBy.equals("asc")) {
						return num2.compareTo(num1);
					} else {
						return num1.compareTo(num2);
					}
				}
			});
		}
		return result;
	}

	/**
	 * 对list中的Map进行排序
	 * @param list
	 * @param sortBy asc，desc
	 * @return
	 */
	public static List<Integer> sortList(List<Integer> list, final String sortBy) {
		List<Integer> result = new LinkedList<Integer>(list);
		/*for (Map<String, String> map : list) {
			result.add(map);
		}*/
		if (list.size() > 1) {
			Collections.sort(result, new Comparator<Integer>() {
				@Override
				public int compare(Integer o1, Integer o2) {
					if (StringUtils.isBlank(sortBy) || sortBy.equals("asc")) {
						return (o1).compareTo(o2);
					} else {
						return (o2).compareTo(o1);
					}
				}
			});
		}
		return result;
	}


	/**
	 * 将对象转换为字符串，如果为NULL则转换为空字符串
	 * @param obj
	 * @return
	 */
	public static String objToStr(Object obj) {
		return obj == null ? "" : obj.toString();
	}

	/**
	 * 将对象转换为字符串，如果为NULL则转换为默认值
	 * @param obj 对象
	 * @param def 默认值
	 * @return
	 */
	public static String objToStr(Object obj, String def) {
		return (obj == null|| "".equals(obj)) ? objToStr(def) : obj.toString();
	}

//	public static void main(String[] args) throws ParseException {
//		Map<String, String> map1 = new HashMap<String, String>();
//		map1.put("name", "输血科");
//		map1.put("code", "10104");
//		map1.put("no", "6");
//		Map<String, String> map2 = new HashMap<String, String>();
//		map2.put("name", "检验科");
//		map2.put("code", "10213");
//		map2.put("no", "7");
//		List<Map<String, String>> list = new ArrayList<Map<String, String>>();
//		list.add(map1);
//		list.add(map2);
//
//		for (int i = 0; i < list.size(); i++) {
//			Map<String, String> map = list.get(i);
//			if (map.get("no").equals("6")) {
//				list.remove(map);
//			}
//		}
//	}

	/**
	 * 字符串是否是英文
	 * @param str
	 * @return
	 */
	public static boolean isEnglishChar(String str) {
		boolean temp = false;
		for (int i = 0; i < str.length(); i++) {
			if ((str.charAt(i) <= 'Z' && str.charAt(i) >= 'A') || (str.charAt(i) <= 'z' && str.charAt(i) >= 'a')) {
				temp = true;
			} else {
				temp = false;
				break;
			}
		}
		return temp;
	}

	/**
	 * 比较两个时间大小 date1是否大于date2
	 * @param date1
	 * @param date2
	 * @return
	 */
	public static boolean dateCompare(Date date1, Date date2) {
		long between = (date1.getTime() - date2.getTime());
		if (between >= 0) {
			return true;
		} else {
			return false;
		}
	}



	 /**
	  * 得到几天后的时间
	  * @param d
	  * @param day
	  * @return
	  */
	 public static String getDateAfter(Date d, int day) {
	     SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");
	     Calendar now = Calendar.getInstance();
	     now.setTime(d);
	     now.set(Calendar.DATE, now.get(Calendar.DATE) + day);//+后 -前
	     return format.format(now.getTime());
//		 Calendar calendar = Calendar.getInstance();
//		 calendar.setTime(d);
//	     calendar.set(Calendar.DAY_OF_YEAR, calendar.get(Calendar.DAY_OF_YEAR) + day);
//	     Date today = calendar.getTime();
//	     SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");
//	     String result = format.format(today);
//	     return result;
	 }

	 /**
		 * @Description
		 * 方法描述: 时间相差天数(yyyy-MM-dd)
		 * @return 返回类型： Integer
		 * @param date1  第一个时间字符串
		 * @param date2  第二个时间字符串
		 * @return 0:相等  >0:前大于后  <0:前小于后
		 */
		public static Integer compareStrDate(String date1, String date2) {
			Integer result = null;
			DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");

			LocalDate ndate1 = LocalDate.parse(date1, formatter);
			LocalDate ndate2 = LocalDate.parse(date2, formatter);
			//LocalDateTime ndate1 = LocalDateTime.parse(date1, formatter);
			//LocalDateTime ndate2 = LocalDateTime.parse(date2, formatter);
			result = ndate1.compareTo(ndate2);

			return result;

		}

	/**
	 * 格式化数字--删除后面的0
	 * @return
	 */
	public  static String getRealData(String valueStr) {
		if(!Utils.isNumber(valueStr)){
			return valueStr;
		}
		if (StringUtils.isBlank(valueStr)) {
			return "0";
		}
		String value = "";
		try {
			BigDecimal num = new BigDecimal(valueStr);
			value = num.stripTrailingZeros().toPlainString();
		} catch (Exception e) {
			e.printStackTrace();
			return valueStr;
		}
		return value;
	}

	/**
	 * 加密
	 * @param paraStr
	 * @return
	 */
	public static String UrlEncodeBase64(String paraStr) {
		String value = "";
		if (StringUtils.isNotBlank(paraStr)) {
			value = new BASE64Encoder().encode(paraStr.getBytes()).replace("+", "%2B");
		}
		return value;
	}

	/**
	 *  <summary>
	 * 	 URL传递参数BASE64解码
	 * 	</summary>
	 * 	<param name="paraStr">解码前参数</param>
	 *  <returns>解码后参数</returns>
	 * @param paraStr
	 * @return
	 */
	public static String UrlDecodeBase64(String paraStr) {
		String value = "";

		if (!StringUtils.isNotBlank(paraStr)) {
			try {
				value = String.valueOf((new BASE64Decoder()).decodeBuffer(paraStr.replace("+", "%2B")));
			} catch (IOException e) {
				e.printStackTrace();
			}
		}

		return value;
	}
	/**
	 * 脱敏公共方法
	 * @param no
	 * @param pattern
	 * @return
	 */
	public static String encrypt(String no, String pattern){
		if (StringUtils.isBlank(no)) {
			return no;
		}
		if (StringUtils.isBlank(pattern)) {
			return no;
		}
		if(!pattern.contains("*")){
			return no;
		}
		//解析脱敏模式：即获取加密位数
		List<Integer> indexList = new ArrayList<Integer>();
		char[] patternChar = pattern.toCharArray();
		for(int i = 0;i<patternChar.length;i++){
			char patternByte = patternChar[i];
			if( '*' == patternByte){
				indexList.add(i);
			}
		}
		//将目标信息加密：即替换相应位置为*
		int length = no.length();
		StringBuffer sb = new StringBuffer(no);
		for(Integer index : indexList){
			if(index<length){
				sb.setCharAt(index,'*');
			}
		}
		return sb.toString();
	}

	/**
	 * 获取日期的年月日
	 * @param date
	 * @return
	 */
	public static String getDateYMD(Date date) {
		Calendar calendar = Calendar.getInstance();
		calendar.setTime(date);
		String ymd = calendar.get(Calendar.YEAR) + "-" + (calendar.get(Calendar.MONTH) + 1) + "-" + calendar.get(Calendar.DAY_OF_MONTH);
		return  ymd;
	}


	public static int getStartPageNum(int pageNo, int pageSize){
		return (pageNo-1)*pageSize;
	}

	public static int getEndPageNum(int pageNo, int pageSize,long totalPage){
		if((pageNo*pageSize)>totalPage){
			return (int)(totalPage);
		}else {
			return ((pageNo*pageSize));
		}
	}

    /**
     * 使用XPath将xml转List<Map<String,String>>>
     * @param xmlString xml字符串
     * @return
     */
    public static List<Map<String, String>> xmlToList(String xmlString, Map<String, String> keyMap, String parentNode, String... childNodes) {
        List<Map<String, String>> resultList = new ArrayList<>();

        try {
            DocumentBuilderFactory factory = DocumentBuilderFactory.newInstance();
            DocumentBuilder builder = factory.newDocumentBuilder();
            InputStream stream = new ByteArrayInputStream(xmlString.getBytes());
            org.w3c.dom.Document document = builder.parse(stream);

            XPath xpath = XPathFactory.newInstance().newXPath();
            NodeList parentNodes = (NodeList) xpath.evaluate(parentNode, document, XPathConstants.NODESET);
            for (int i = 0; i < parentNodes.getLength(); i++) {
                org.w3c.dom.Element pdfElement = (org.w3c.dom.Element) parentNodes.item(i);
                Map<String, String> pdfMap = new HashMap<>();
                for (String childNode : childNodes) {
                    String aliasKey = keyMap.get(childNode);
                    Node node = pdfElement.getElementsByTagName(childNode).item(0);
                    String nodeValue = node == null ? "" : node.getTextContent();
                    if(StringUtils.isBlank(nodeValue)){
                        continue;
                    }
                    if (StringUtils.isNotBlank(aliasKey)) {
                        pdfMap.put(aliasKey, nodeValue);
                    } else {
                        pdfMap.put(childNode, nodeValue);
                    }
                }
                if(pdfMap.size()>0){
                    resultList.add(pdfMap);
                }
            }
        } catch (ParserConfigurationException | IOException | org.w3c.dom.DOMException e) {
            e.printStackTrace();
        } catch (SAXException e) {
            e.printStackTrace();
        } catch (XPathExpressionException e) {
            e.printStackTrace();
        }
        return resultList;
    }

    /**
     * 获取字母、数字、汉字的随机值
     *
     */
    private static final String ALPHA = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz";
    private static final String NUMERIC = "0123456789";
    private static final String CHINESE_CHARS = "一二三四五六七八九十中国";
    private static final String ALPNUM = "0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz";

    public static String getRandomByType(int length, String type) {
        String returStr = "";
        Random random = new Random();
        StringBuilder sb = new StringBuilder(length);
        if ("alpnum".equals(type)) {
            for (int i = 0; i < length; i++) {
                int index = random.nextInt(ALPNUM.length());
                sb.append(ALPNUM.charAt(index));
            }
            returStr = sb.toString();
        }
        if ("alphabet".equals(type)) {
            for (int i = 0; i < length; i++) {
                int index = random.nextInt(ALPHA.length());
                sb.append(ALPHA.charAt(index));
            }
            returStr = sb.toString();
        }
        if ("numercal".equals(type)) {
            for (int i = 0; i < length; i++) {
                int index = random.nextInt(NUMERIC.length());
                sb.append(NUMERIC.charAt(index));
            }
            returStr = sb.toString();
        }
        if ("sinogram".equals(type)) {
            if (length > CHINESE_CHARS.length()) {
                throw new IllegalArgumentException("指定的长度大于可用汉字的数量");
            }
            for (int i = 0; i < length; i++) {
                int index = random.nextInt(CHINESE_CHARS.length());
                sb.append(CHINESE_CHARS.charAt(index));
            }
            returStr = sb.toString();
        }
        return returStr;
    }

    /**
     * 通过反射机制调用方法输出结果
     * @param className
     * @param methodName
     * @return
     */
    public static Object reflect(String className, String methodName) {
        Object result ="";
        Class<?> cls = null;
        try {
            cls = Class.forName(className);
            Method method = cls.getDeclaredMethod(methodName);
            method.setAccessible(true);
            result = method.invoke(cls.newInstance());
        } catch (ClassNotFoundException e) {
            e.printStackTrace();
        } catch (NoSuchMethodException e) {
            e.printStackTrace();
        } catch (IllegalAccessException e) {
            e.printStackTrace();
        } catch (InstantiationException e) {
            e.printStackTrace();
        } catch (InvocationTargetException e) {
            e.printStackTrace();
        }
        return result;
    }

}
