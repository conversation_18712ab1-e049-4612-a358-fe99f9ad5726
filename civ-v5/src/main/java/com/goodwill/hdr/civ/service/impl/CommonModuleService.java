package com.goodwill.hdr.civ.service.impl;

import com.fasterxml.jackson.databind.ObjectMapper;

import com.goodwill.hdr.civ.config.ConfigCache;
import com.goodwill.hdr.civ.enums.commonModule.CommonUrlEnum;
import com.goodwill.hdr.civ.enums.commonModule.LianZhongDmrEnum;
import org.apache.commons.lang.StringUtils;

import org.springframework.beans.factory.annotation.Autowired;

import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Description 类描述：
 * @modify 修改记录：
 */
@Service
public class CommonModuleService {

    @Autowired
    private ObjectMapper OBJECT_MAPPER;


    public String getLinkType(String oid, String id) {
        String linkType = ConfigCache.getCache(oid, CommonUrlEnum.LINKTYPE.getCode(id));
        return StringUtils.isNotBlank(linkType) ? linkType : "";
    }

    private String getParam_num(String oid, String id) {
        String param_num = ConfigCache.getCache(oid, CommonUrlEnum.PARAM_NUM.getCode(id));
        return StringUtils.isNotBlank(param_num) ? param_num : "0";
    }

    public String getURL(String oid, String id) {
        String URL = ConfigCache.getCache(oid, CommonUrlEnum.URL.getCode(id));
        return StringUtils.isNotBlank(URL) ? URL : "";

    }

    public List<String> getParams(String oid, String id) {
        List<String> params = new ArrayList<String>();
        if (StringUtils.isNotBlank(getParam_num(oid, id)) && (!"0".equals(getParam_num(oid, id)))) {
            for (int i = 1; i <= Integer.parseInt(getParam_num(oid, id)); i++) {
                String field = ConfigCache.getCache(oid, CommonUrlEnum.PARAMETER_FIELD.getCode(id, i));
                params.add(field.trim());
            }
        }
        return params;
    }

    public Map<String, String> getParam_configs(String oid, String id) {
        Map<String, String> param_configs = new HashMap<String, String>();
        for (int i = 1; i <= Integer.parseInt(getParam_num(oid, id)); i++) {
            String field = ConfigCache.getCache(oid, CommonUrlEnum.PARAMETER_FIELD.getCode(id, i));
            String config = ConfigCache.getCache(oid, CommonUrlEnum.PARAMETER_FIELD_CONFIG.getCode(id, i));
            if (StringUtils.isBlank(config)) {
                param_configs.put(field.trim(), "");
            } else {
                param_configs.put(field.trim(), config.trim());
            }

        }

        return param_configs;
    }


    public String getLzJobNumber(String oid, String id) {
        return ConfigCache.getCache(oid, LianZhongDmrEnum.LZ_DMR_JOB_NUMBER.getCode(id));
    }

    public String getLzEncryptedUrl(String oid, String sysCode) {
        return ConfigCache.getCache(oid, LianZhongDmrEnum.ENCRYPTED_URL.getCode(sysCode));
    }



    public String getTableTemplateConfig(String oid, String id) {
        String templateConfig = ConfigCache.getCache(oid, CommonUrlEnum.TABLE_TEMPLATE_CONFIG.getCode(id));
        return StringUtils.isNotBlank(templateConfig) ? templateConfig : "";
    }

    public String getUrlTableTemplateConfig(String oid, String id) {
        String templateConfig = ConfigCache.getCache(oid, CommonUrlEnum.URL_TABLE_TEMPLATE_CONFIG.getCode(id));
        return StringUtils.isNotBlank(templateConfig) ? templateConfig : "";
    }
    public String getPublicKey(String oid, String id) {
        String publicKey = ConfigCache.getCache(oid, CommonUrlEnum.PUBLIC_KEY.getCode(id));
        return StringUtils.isNotBlank(publicKey) ? publicKey : "";

    }

    public String getTokenUrl(String oid, String id) {
        String TokenUrl = ConfigCache.getCache(oid, CommonUrlEnum.URL_TOKEN_URL.getCode(id));
        return StringUtils.isNotBlank(TokenUrl) ? TokenUrl : "";
    }

}
