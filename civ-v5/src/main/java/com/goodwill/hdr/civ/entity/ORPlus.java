package com.goodwill.hdr.civ.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import org.springframework.data.annotation.Id;

import java.io.Serializable;


@TableName("hdr_kb_orplus")
public class ORPlus implements Serializable {

    private static final long serialVersionUID = 1L;

    private String id_pk;
    private String rolename;
    private String ordercode;
    private String ordername;
    private String ordertype;
    private String sex;
    private String age;
    private String sp;
    private String breath;
    private String heartrate;
    private String temperature;
    private String oxygen;
    private String systolic;
    private String diastolic;
    private String roledesc;
    private String source;

    public String getId_pk() {
        return id_pk;
    }

    public void setId_pk(String id_pk) {
        this.id_pk = id_pk;
    }

    public String getRolename() {
        return rolename;
    }

    public void setRolename(String rolename) {
        this.rolename = rolename;
    }

    public String getOrdercode() {
        return ordercode;
    }

    public void setOrdercode(String ordercode) {
        this.ordercode = ordercode;
    }

    public String getOrdername() {
        return ordername;
    }

    public void setOrdername(String ordername) {
        this.ordername = ordername;
    }

    public String getOrdertype() {
        return ordertype;
    }

    public void setOrdertype(String ordertype) {
        this.ordertype = ordertype;
    }

    public String getSex() {
        return sex;
    }

    public void setSex(String sex) {
        this.sex = sex;
    }

    public String getAge() {
        return age;
    }

    public void setAge(String age) {
        this.age = age;
    }

    public String getSp() {
        return sp;
    }

    public void setSp(String sp) {
        this.sp = sp;
    }

    public String getBreath() {
        return breath;
    }

    public void setBreath(String breath) {
        this.breath = breath;
    }

    public String getHeartrate() {
        return heartrate;
    }

    public void setHeartrate(String heartrate) {
        this.heartrate = heartrate;
    }

    public String getTemperature() {
        return temperature;
    }

    public void setTemperature(String temperature) {
        this.temperature = temperature;
    }

    public String getOxygen() {
        return oxygen;
    }

    public void setOxygen(String oxygen) {
        this.oxygen = oxygen;
    }

    public String getSystolic() {
        return systolic;
    }

    public void setSystolic(String systolic) {
        this.systolic = systolic;
    }

    public String getDiastolic() {
        return diastolic;
    }

    public void setDiastolic(String diastolic) {
        this.diastolic = diastolic;
    }

    public String getRoledesc() {
        return roledesc;
    }

    public void setRoledesc(String roledesc) {
        this.roledesc = roledesc;
    }

    public String getSource() {
        return source;
    }

    public void setSource(String source) {
        this.source = source;
    }

    @Override
    public String toString() {
        return "ORPlus{" +
                "id_pk='" + id_pk + '\'' +
                ", rolename='" + rolename + '\'' +
                ", ordercode='" + ordercode + '\'' +
                ", ordername='" + ordername + '\'' +
                ", ordertype='" + ordertype + '\'' +
                ", sex='" + sex + '\'' +
                ", age='" + age + '\'' +
                ", sp='" + sp + '\'' +
                ", breath='" + breath + '\'' +
                ", heartrate='" + heartrate + '\'' +
                ", temperature='" + temperature + '\'' +
                ", oxygen='" + oxygen + '\'' +
                ", systolic='" + systolic + '\'' +
                ", diastolic='" + diastolic + '\'' +
                ", roledesc='" + roledesc + '\'' +
                ", source='" + source + '\'' +
                '}';
    }
}
