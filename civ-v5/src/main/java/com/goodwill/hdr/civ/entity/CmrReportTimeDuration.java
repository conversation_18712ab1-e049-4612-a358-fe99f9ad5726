package com.goodwill.hdr.civ.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;

import java.io.Serializable;

/**
 * @TableName cmr_report_time_duration
 */
@TableName(value = "cmr_report_time_duration")
public class CmrReportTimeDuration implements Serializable {
    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
    /**
     *
     */
    @TableField(value = "order_item_code")
    private String orderItemCode;
    /**
     *
     */
    @TableField(value = "order_item_name")
    private String orderItemName;
    /**
     *
     */
    @TableField(value = "report_count")
    private Integer reportCount;
    /**
     *
     */
    @TableField(value = "hour_count")
    private Integer hourCount;
    /**
     *
     */
    @TableField(value = "report_type")
    private String reportType;

    /**
     *
     */
    public String getOrderItemCode() {
        return orderItemCode;
    }

    /**
     *
     */
    public void setOrderItemCode(String orderItemCode) {
        this.orderItemCode = orderItemCode;
    }

    /**
     *
     */
    public String getOrderItemName() {
        return orderItemName;
    }

    /**
     *
     */
    public void setOrderItemName(String orderItemName) {
        this.orderItemName = orderItemName;
    }

    /**
     *
     */
    public Integer getReportCount() {
        return reportCount;
    }

    /**
     *
     */
    public void setReportCount(Integer reportCount) {
        this.reportCount = reportCount;
    }

    /**
     *
     */
    public Integer getHourCount() {
        return hourCount;
    }

    /**
     *
     */
    public void setHourCount(Integer hourCount) {
        this.hourCount = hourCount;
    }

    /**
     *
     */
    public String getReportType() {
        return reportType;
    }

    /**
     *
     */
    public void setReportType(String reportType) {
        this.reportType = reportType;
    }

    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        CmrReportTimeDuration other = (CmrReportTimeDuration) that;
        return (this.getOrderItemCode() == null ? other.getOrderItemCode() == null : this.getOrderItemCode().equals(other.getOrderItemCode()))
                && (this.getOrderItemName() == null ? other.getOrderItemName() == null : this.getOrderItemName().equals(other.getOrderItemName()))
                && (this.getReportCount() == null ? other.getReportCount() == null : this.getReportCount().equals(other.getReportCount()))
                && (this.getHourCount() == null ? other.getHourCount() == null : this.getHourCount().equals(other.getHourCount()))
                && (this.getReportType() == null ? other.getReportType() == null : this.getReportType().equals(other.getReportType()));
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getOrderItemCode() == null) ? 0 : getOrderItemCode().hashCode());
        result = prime * result + ((getOrderItemName() == null) ? 0 : getOrderItemName().hashCode());
        result = prime * result + ((getReportCount() == null) ? 0 : getReportCount().hashCode());
        result = prime * result + ((getHourCount() == null) ? 0 : getHourCount().hashCode());
        result = prime * result + ((getReportType() == null) ? 0 : getReportType().hashCode());
        return result;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", orderItemCode=").append(orderItemCode);
        sb.append(", orderItemName=").append(orderItemName);
        sb.append(", reportCount=").append(reportCount);
        sb.append(", hourCount=").append(hourCount);
        sb.append(", reportType=").append(reportType);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}