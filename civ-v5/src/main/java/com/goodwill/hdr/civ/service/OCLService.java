package com.goodwill.hdr.civ.service;

import java.util.Map;

/**
 * @Description
 * 类描述：医嘱闭环，远程调用webservice接口
 * <AUTHOR>
 * @Date 2018年10月22日
 * @modify
 * 修改记录：
 *
 */
public interface OCLService {

	/**
	 * @Description
	 * 方法描述: 口服药品和静脉药品医嘱闭环
	 * @return 返回类型： String
	 * @param pid 患者编号
	 * @param vid 就诊次数
	 * @param orderNo 医嘱号
	 * @return
	 */
	public String getDrugOCL(String oid, String pid, String vid, String orderNo);

	/**
	 * @Description
	 * 方法描述: 检验医嘱闭环
	 * @return 返回类型： String
	 * @param pid 患者编号
	 * @param vid 就诊次数
	 * @param orderNo 医嘱号
	 * @return
	 */
	public String getLabOCL(String oid, String pid, String vid, String orderNo);

	/**
	 * @Description
	 * 方法描述: 检查医嘱闭环
	 * @return 返回类型： String
	 * @param pid 患者编号
	 * @param vid 就诊次数
	 * @param orderNo
	 * @return
	 */
	public String getExamOCL(String oid, String pid, String vid, String orderNo);

	/**
	 * @Description
	 * 方法描述: 手术医嘱闭环
	 * @return 返回类型： String
	 * @param pid 患者编号
	 * @param vid 就诊次数
	 * @param orderNo 医嘱号
	 * @return
	 */
	public String getOperOCL(String oid, String pid, String vid, String orderNo);

	/**
	 * @Description
	 * 方法描述: 输血闭环
	 * @return 返回类型： String
	 * @param pid 患者编号
	 * @param vid 就诊次数
	 * @param timesNo 输血次数编号
	 * @return
	 */
	public String getBloodOCL(String oid, String pid, String vid, String timesNo);

	/**
	 * @Description
	 * 方法描述: 末次住院手术医嘱闭环
	 * @return 返回类型： List<Object>
	 * @param patientId 患者编号
	 * @param visitId 就诊次数
	 * @return
	 */
	public String getCVOperOCL(String oid, String patientId, String visitId);

	Map<String, String> getOclUrl(String oid, String patientId, String visitId, String id, String token);
}
