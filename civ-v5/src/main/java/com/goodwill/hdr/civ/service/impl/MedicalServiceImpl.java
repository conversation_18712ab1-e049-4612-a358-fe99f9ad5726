package com.goodwill.hdr.civ.service.impl;

import com.alibaba.fastjson.JSON;

import com.goodwill.hdr.civ.config.Config;
import com.goodwill.hdr.civ.config.ConfigCache;
import com.goodwill.hdr.civ.enums.HdrConstantEnum;
import com.goodwill.hdr.civ.enums.HdrTableEnum;
import com.goodwill.hdr.civ.service.MedicalService;
import com.goodwill.hdr.civ.service.PowerService;
import com.goodwill.hdr.civ.utils.ListPage;
import com.goodwill.hdr.civ.utils.Utils;
import com.goodwill.hdr.civ.vo.SolrVo;
import com.goodwill.hdr.core.orm.MatchType;
import com.goodwill.hdr.core.orm.Page;
import com.goodwill.hdr.hbase.bo.PropertyFilter;
import com.goodwill.hdr.hbase.dto.responseVo.PageResultVo;
import com.goodwill.hdr.hbase.dto.responseVo.ResultVo;
import com.goodwill.hdr.hbaseQueryClient.builder.PageRequestBuilder;
import com.goodwill.hdr.hbaseQueryClient.service.HbaseQueryClient;
import com.goodwill.hdr.rest.client.enums.JhdcpServerCode;
import com.goodwill.hdr.rest.client.transmission.JhdcpHttpSender;
import com.goodwill.hdr.rest.client.wrapper.imp.JhdcpQueryWrapperImp;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Description 类描述：
 * @modify 修改记录：
 */
@Service
public class MedicalServiceImpl implements MedicalService {
    private static Logger logger = LoggerFactory.getLogger(MedicalServiceImpl.class);

    @Autowired
    private PowerService powerService;

    @Autowired
    JhdcpHttpSender jhdcpHttpSender;
    private final HbaseQueryClient hbaseQueryClient;

    public MedicalServiceImpl(HbaseQueryClient hbaseQueryClient) {
        this.hbaseQueryClient = hbaseQueryClient;
    }

    /**
     * 获取患者信息
     *
     * @param patientId
     * @return
     */
    @Override
    public Map<String, String> getPatientInfo(String oid, String patientId, String visitType) {
        Map<String, String> infos = new HashMap<String, String>();
        infos.put("PATIENT_ID", patientId);
        List<PropertyFilter> filters = new ArrayList<PropertyFilter>();
        if (StringUtils.isNotBlank(visitType)) {
            filters.add(new PropertyFilter("VISIT_TYPE_CODE",  MatchType.EQ.getOperation(), visitType));
        }
        //可能得到三条记录  门诊患者信息 和 住院患者信息  和  体检信息
        List<Map<String, String>> indexs = new ArrayList<>();
        ResultVo<PageResultVo<Map<String, String>>> resultVo = hbaseQueryClient.getPageByCondition(
                PageRequestBuilder.init()
                        .tableName(HdrTableEnum.HDR_PATIENT.getCode())
                        .patientId(patientId)
                        .oid(oid)
                        .visitId("")
                        .visitTypeCode(visitType)
                        .filters(filters)
                        .pageNo(0)
                        .pageSize(0)
                        .orderBy("REPORT_TIME")
                        .desc()
                        .column("EID", "PERSON_NAME", "SEX_NAME", "DATE_OF_BIRTH", "ID_CARD_NO",
                                "IN_PATIENT_ID", "OUT_PATIENT_ID")
                        .build());
        if(resultVo.isSuccess()){
            indexs = resultVo.getContent().getResult();
        }
//        List<Map<String, String>> indexs = hbaseDao.findConditionByPatient(HdrTableEnum.HDR_PATIENT.getCode(), oid,
//                patientId, filters, new String[]{"EID", "PERSON_NAME", "SEX_NAME", "DATE_OF_BIRTH", "ID_CARD_NO",
//                        "IN_PATIENT_ID", "OUT_PATIENT_ID"});
        if (indexs != null && indexs.size() == 0) {//未找到患者
            infos.put("STATUS", "0");
            return infos;
        }
        Map<String, String> temp = new HashMap<String, String>();
        String idCard = indexs.get(0).get("ID_CARD_NO") == null ? "-" : indexs.get(0).get("ID_CARD_NO");
        logger.info("=============idCard=================" + idCard);
        temp.put("id_card_no2", idCard);
        //List<Map<String, String>> infoHidden = powerService.getInfoHidden(indexs);
        List<Map<String, String>> infoHidden = powerService.getInfoHiddenByMaskRule(indexs);
        //循环遍历患者信息  记录需要的字段
        for (Map<String, String> one : infoHidden) {
            Utils.checkAndPutToMap(infos, "person_name", one.get("PERSON_NAME"), "-", false);
            Utils.checkAndPutToMap(infos, "sex_name", one.get("SEX_NAME"), "-", false);
            Utils.checkAndPutToMap(infos, "eid", one.get("EID"), "", false);
            Utils.checkAndPutToMap(infos, "in_patient_id", one.get("IN_PATIENT_ID"), "", false);
            Utils.checkAndPutToMap(infos, "out_patient_id", one.get("OUT_PATIENT_ID"), "", false);
            Utils.checkAndPutToMap(infos, "date_of_birth", one.get("DATE_OF_BIRTH"), "-", false);
            Utils.checkAndPutToMap(infos, "id_card_no1", one.get("ID_CARD_NO"), "-", false);
        }
        //处理出生日期
        String birthday = infos.get("DATE_OF_BIRTH");
        if (StringUtils.isNotBlank(birthday)) {
            infos.put("date_of_birth", birthday);
        }
        infos.putAll(temp);
        return infos;
    }

    /**
     * 获取体检列表
     *
     * @param cardNo
     * @param dateBegin
     * @param dateEnd
     * @param pageNo
     * @param pageSize
     * @return
     */
    @Override
    public List<Map<String, String>> getMedicalListInfo(String oid, String cardNo, String dateBegin,
                                                        String dateEnd, String visitDate, int pageNo, int pageSize) {
        List<Map<String, String>> result = new ArrayList<Map<String, String>>();

        JhdcpQueryWrapperImp queryWrapperMedical = new JhdcpQueryWrapperImp(JhdcpServerCode.ALL_VISIT.getCode(), pageNo, pageSize);
        queryWrapperMedical.eq("ID_CARD_NO", cardNo);
        queryWrapperMedical.eq("SD_VISIT_TYPE_CODE", "03");

        if(StringUtils.isNotBlank(visitDate)){
            dateBegin=Utils.calStartDate(visitDate);
            if (StringUtils.isNotBlank(dateBegin) ) {
                queryWrapperMedical.ge("VISIT_TIME", dateBegin + " 00:00:00");
            }
            queryWrapperMedical.le("VISIT_TIME", LocalDate.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd")) + " 23:59:59");
        }else {
            if (StringUtils.isNotBlank(dateBegin) ) {
                queryWrapperMedical.ge("VISIT_TIME", dateBegin + " 00:00:00");

            }

            if (StringUtils.isNotBlank(dateEnd) ) {
                queryWrapperMedical.le("VISIT_TIME", dateEnd + " 23:59:59");

            }
        }
        if(StringUtils.isNotBlank(oid)&&!"ALL".equals(oid)){
            queryWrapperMedical.eq("ORG_CODE", oid);
        }
        queryWrapperMedical.descOrder("VISIT_TIME");
        String dataPageJson = jhdcpHttpSender.getDataPageJson(queryWrapperMedical);
        SolrVo solrVo= JSON.parseObject(dataPageJson,SolrVo.class);

        if (solrVo == null) {
            return result;
        }

        logger.info("查询数据:" + solrVo.toString());
        List<Map<String, String>> list = solrVo.getData();
        if (!list.isEmpty()) {
            //Utils.sortListByDate(list, "MEDICAL_TIME", "desc");
            Utils.sortListByDate(list, "VISIT_TIME", "desc");
            for (Map<String, String> map : list) {
                Map<String, String> mapTemp = new HashMap<String, String>();
                /*mapTemp.put("medicalNo", map.get("MEDICAL_REPORT_NO"));
                mapTemp.put("medicalType", map.get("MEDICAL_TYPE_NAME"));
                mapTemp.put("medicalDept", map.get("MEDICAL_DEPT_NAME"));
                mapTemp.put("medicalTime", map.get("MEDICAL_TIME"));
                mapTemp.put("patientId", map.get("PATIENT_ID"));*/
                mapTemp.put("medicalNo", map.get("VISIT_NO"));
                mapTemp.put("medicalType", map.get("MEDICAL_TYPE_NAME"));
                mapTemp.put("medicalDept", map.get("ST_VISIT_DEPT"));
                mapTemp.put("medicalTime", map.get("VISIT_TIME"));
                mapTemp.put("patientId", map.get("HIS_PAT_ID"));
                mapTemp.put("visitId", map.get("VISIT_NUM"));
                mapTemp.put("cardNo", cardNo);
                mapTemp.put("oid", map.get("ORG_CODE"));
                result.add(mapTemp);
            }
        }

        return result;
    }

    /**
     * 获取体检视图单次体检的表头配置
     */
    public List<Map<String, String>> getMedicalTableHead(String oid) {
        List<Map<String, String>> list = new ArrayList<Map<String, String>>();
        String tableHead = Config.getMedicalTableConfig(oid);
        if (StringUtils.isNotBlank(tableHead)) {
            String[] configs = tableHead.split(";");
            for (String config : configs) {
                String[] heads = config.split(",");
                Map<String, String> res = new HashMap<String, String>();
                res.put("name", heads[0]);
                if (heads.length > 1) {
                    res.put("code", heads[1]);
                }
                if (heads.length > 2) {
                    res.put("check_type", heads[2]);
                }
                list.add(res);
            }
        }
        return list;
    }

    /**
     * 获取体检视图首页信息
     *
     * @param medicalReportNo
     * @return
     */
    @Override
    public Map<String, Object> getMedicalSummaryHomePage(String visitId,String medicalReportNo, String oid, String patientId, String cardNo) {
        if(HdrConstantEnum.HOSPITAL_JDF_CD.getCode().equals(oid) ||
                HdrConstantEnum.HOSPITAL_JDF_HF.getCode().equals(oid) ||
                HdrConstantEnum.HOSPITAL_JDF_SZ.getCode().equals(oid)){
            patientId=cardNo;
        }

        Map<String, Object> res = new HashMap<String, Object>();
        List<PropertyFilter> filters = new ArrayList<PropertyFilter>();
        filters.add(new PropertyFilter("MEDICAL_REPORT_NO", MatchType.EQ.getOperation(), medicalReportNo));
        List<Map<String, String>> list = new ArrayList<>();
        ResultVo<PageResultVo<Map<String, String>>> resultVo = hbaseQueryClient.getPageByCondition(
                PageRequestBuilder.init()
                        .tableName(HdrTableEnum.HDR_MEDICAL_REPORT_INFO.getCode())
                        .patientId(patientId)
                        .oid(oid)
                        .visitId(visitId)
                        .visitTypeCode("")
                        .filters(filters)
                        .pageNo(0)
                        .pageSize(0)
                        .orderBy("")
                        .desc()
                        .column("PERSON_NAME", "SEX_NAME", "AGE_VALUE", "AGE_UNIT", "EMPLOYER_COMPANY", "MEDICAL_TIME",
                                "REPORT_TIME", "PHONE", "MEDICAL_REPORT_NO", "EXAM_TEXT", "REPORT_DOCTOR_NAME","FILE_URL","URL",
                                "REPORT_CONFIRMER_DOCTOR_NAME", "PATIENT_ID", "EXAM_ADVICE", "EXAM_DESCRIPTION", "VISIT_ID")
                        .build());
        if(resultVo.isSuccess()){
            list = resultVo.getContent().getResult();
        }
//        List<Map<String, String>> list = hbaseDao.findConditionByPatient(HdrTableEnum.HDR_MEDICAL_REPORT_INFO.getCode(), oid,
//                cardNo, filters,
//                new String[]{"PERSON_NAME", "SEX_NAME", "AGE_VALUE", "AGE_UNIT", "EMPLOYER_COMPANY", "MEDICAL_TIME",
//                        "REPORT_TIME", "PHONE", "MEDICAL_REPORT_NO", "EXAM_TEXT", "REPORT_DOCTOR_NAME",
//                        "REPORT_CONFIRMER_DOCTOR_NAME", "PATIENT_ID", "EXAM_ADVICE", "EXAM_DESCRIPTION", "VISIT_ID"});
        List<Map<String, String>> infoHidden = powerService.getInfoHidden(list);
        if (!infoHidden.isEmpty()) {
            Map<String, String> resTemp = infoHidden.get(0);
            res.put("personName", resTemp.get("PERSON_NAME"));
            res.put("sex", resTemp.get("SEX_NAME"));
            res.put("age", resTemp.get("AGE_VALUE") + resTemp.get("AGE_UNIT"));
            res.put("employerCompany", resTemp.get("EMPLOYER_COMPANY"));
            res.put("medicalTime", resTemp.get("MEDICAL_TIME"));
            res.put("reportTime", resTemp.get("REPORT_TIME"));
            res.put("medicalReport_no", resTemp.get("MEDICAL_REPORT_NO"));
            res.put("examDescription", resTemp.get("EXAM_DESCRIPTION"));
            res.put("examAdvice", resTemp.get("EXAM_ADVICE"));
            res.put("reportDoctor", resTemp.get("REPORT_DOCTOR_NAME"));
            res.put("reportConfirmerDoctor", resTemp.get("REPORT_CONFIRMER_DOCTOR_NAME"));
            res.put("url", StringUtils.isBlank(resTemp.get("URL"))?resTemp.get("FILE_URL"):resTemp.get("URL"));
            res.put("phone", resTemp.get("PHONE"));
            if (StringUtils.isNotBlank(patientId)) {
                List<Map<String, String>> patientInfo = new ArrayList<>();
                ResultVo<PageResultVo<Map<String, String>>> resultVo1 = hbaseQueryClient.getPageByCondition(
                        PageRequestBuilder.init()
                                .tableName(HdrTableEnum.HDR_PATIENT.getCode())
                                .patientId(patientId)
                                .oid(oid)
                                .visitId("")
                                .visitTypeCode("")
                                .filters(new ArrayList<>())
                                .pageNo(0)
                                .pageSize(0)
                                .orderBy("")
                                .desc()
                                .column("HOME_PHONE")
                                .build());
                if(resultVo1.isSuccess()){
                    patientInfo = resultVo.getContent().getResult();
                }
//                List<Map<String, String>> patientInfo = hbaseDao.findConditionByPatient(
//                        HdrTableEnum.HDR_PATIENT.getCode(), oid, patientId, new ArrayList<>(),
//                        "HOME_PHONE");
                if (!patientInfo.isEmpty()) {
                    res.put("phone", patientInfo.get(0).get("HOME_PHONE"));
                }
            }
            if (resTemp.get("EXAM_ADVICE") == null || resTemp.get("EXAM_ADVICE") == "") {
                List<PropertyFilter> filters2 = new ArrayList<PropertyFilter>();
                filters2.add(new PropertyFilter("MEDICAL_REPORT_NO", MatchType.EQ.getOperation(), medicalReportNo));
                filters2.add(new PropertyFilter("PATIENT_ID", MatchType.EQ.getOperation(), patientId));
                //filters2.add(new PropertyFilter("VISIT_ID", "STRING", MatchType.EQ.getOperation(), resTemp.get("VISIT_ID")));
                List<Map<String, String>> list2 = new ArrayList<>();
                ResultVo<PageResultVo<Map<String, String>>> resultVo1 = hbaseQueryClient.getPageByCondition(
                        PageRequestBuilder.init()
                                .tableName(HdrTableEnum.HDR_MEDICAL_REPORT_INFO_NEXT.getCode())
                                .patientId(patientId)
                                .oid(oid)
                                .visitId(visitId)
                                .visitTypeCode("")
                                .filters(filters2)
                                .pageNo(0)
                                .pageSize(0)
                                .orderBy("")
                                .desc()
                                .column("EXAM_ADVICE", "EXAM_ADVICE_NO")
                                .build());
                if(resultVo1.isSuccess()){
                    list2 = resultVo1.getContent().getResult();
                }
//                List<Map<String, String>> list2 = hbaseDao.findConditionByPatient(HdrTableEnum.HDR_MEDICAL_REPORT_INFO_NEXT.getCode(), oid,
//                        cardNo, filters2,
//                        new String[]{"EXAM_ADVICE", "EXAM_ADVICE_NO"});
                Utils.sortListMulti(list2, new String[]{"EXAM_ADVICE_NO"}, new String[]{"asc"});
                if (list2 != null && !list2.isEmpty()) {
                    StringBuilder sb = new StringBuilder();
                    for (Map<String, String> map : list2) {
                        sb.append(map.get("EXAM_ADVICE")).append("<br />");
                    }
                    res.put("examAdvice", sb.toString());
                }
            }
        }
        //添加医院信息
        res.put("hospital_info", getMedicalHospialInfo(oid));
        return res;
    }

    /**
     * 体检视图一般体检
     * 一般体检：common
     * 内科;in_check
     * 外科：out_check
     * 眼科：eye_check
     * 耳鼻喉科; ENT
     *
     * @return
     */
    @Override
    public Map<String, Object> getMedicalCommonCheck(String oid,String visitId, String medicalReportNo, String medicalItemClass,
                                                     String checkType,String patientId) {
        if(HdrConstantEnum.HOSPITAL_JDF_CD.getCode().equals(oid) ||
                HdrConstantEnum.HOSPITAL_JDF_HF.getCode().equals(oid) ||
                HdrConstantEnum.HOSPITAL_JDF_SZ.getCode().equals(oid)){
            patientId=medicalReportNo;
        }

        Map<String, Object> res = new HashMap<String, Object>();
        String applyNo = "";
        //查询数据表
        List<PropertyFilter> filtersList = new ArrayList<PropertyFilter>();
        //filtersList.add(new PropertyFilter("MEDICAL_REPORT_NO", "STRING", MatchType.EQ.getOperation(), medicalReportNo));
        //StringBuilder medicalItemClassTmp = new StringBuilder("(");
        StringBuilder medicalItemClassTmp = new StringBuilder("");
        if (StringUtils.isNotBlank(medicalItemClass)) {
            String[] itemCodes = medicalItemClass.split("\\|");
            for (int i = 0; i < itemCodes.length; i++) {
                if (i != (itemCodes.length - 1)) {
                    medicalItemClassTmp.append(itemCodes[i]).append(",");
                } else {
                    medicalItemClassTmp.append(itemCodes[i]);
                }
            }
        }
        //medicalItemClassTmp.append(")");
        filtersList.add(
                new PropertyFilter("MEDICAL_ITEM_CLASS_CODE", MatchType.IN.getOperation(), medicalItemClassTmp.toString()));

        String[] columns = {"SUMMARY", "EXECUTE_DOCTOR_NAME", "EXECUTE_TIME", "APPLY_NO"};
        List<Map<String, String>> listApply = queyHdrMedicalApplyMaster(patientId, oid, visitId, 0, 0, filtersList, columns).getResult();

        Map<String, String> titleMap = new HashMap<String, String>();
        String result = "";
        if (!listApply.isEmpty()) {
            for(Map<String, String> map:listApply){
                if(StringUtils.isBlank(applyNo)){
                    applyNo += map.get("APPLY_NO")+",";
                }else{
                    applyNo += map.get("APPLY_NO");
                }
            }
            Map<String, String> mapApply = listApply.get(0);
            titleMap.put("doctor", mapApply.get("EXECUTE_DOCTOR_NAME"));
            titleMap.put("time", mapApply.get("EXECUTE_TIME"));
            //applyNo = mapApply.get("APPLY_NO");
            result = mapApply.get("SUMMARY");
        } else {
            titleMap.put("doctor", "");
            titleMap.put("time", "");
        }
        //title
        res.put("title", titleMap);
        //构建result
        res.put("result", result);

        //根据体检类型获取体检配置
        String config = getMedicalConfig(oid, checkType);
        //解析配置
        String[] configs = config.split(";");
        //表格显示
        String tableShow = configs[0];
        String[] tableShows = tableShow.split(",");
        List<Map<String, String>> column = new ArrayList<Map<String, String>>();
        List<String> listColumn = new ArrayList<String>();

        //表格显示数据的查询条件
        String tableDataFilter = configs[1];
        String[] tableDataFilters = tableDataFilter.split("\\|");
        //查询数据表
        List<PropertyFilter> filters = new ArrayList<PropertyFilter>();
        //filters.add(new PropertyFilter("MEDICAL_REPORT_NO", "STRING", MatchType.EQ.getOperation(), medicalReportNo));
        filters.add(new PropertyFilter("APPLY_NO", MatchType.IN.getOperation(), applyNo));
        if (tableDataFilters.length == 3) {
            filters.add(new PropertyFilter(tableDataFilters[0], tableDataFilters[1], tableDataFilters[2]));
        }

        String[] columns1 = {"MEDICAL_ITEM_NAME", "EXAM_RESULT", "UNIT", "REFERENCE", "PROMPT", "NORMAL_RESULT", "APPLY_NO"};
        List<Map<String, String>> list = queryHdrMedicalResultDetail(patientId, oid, visitId, filters, columns1);

        //按配置的体检排序
        Map<String, Map<String, String>> relationMap = new HashMap<String, Map<String, String>>();
        for (Map<String, String> map : list) {
            relationMap.put(map.get("MEDICAL_ITEM_NAME"), map);
        }
        List<Map<String, String>> listTemp = new ArrayList<Map<String, String>>();
        //体重指数需要配置在最后一个
        for (String show : tableShows) {
            if (!"体重指数".equals(show)) {
                listTemp.add(null == relationMap.get(show) ? new HashMap<String, String>() : relationMap.get(show));
            }
            Map<String, String> mapConfig = new HashMap<String, String>();
            mapConfig.put("name", show);
            column.add(mapConfig);
            listColumn.add(show);
        }
        listTemp = fieldHandle(listTemp);
        if (listColumn.contains("体重指数") && null != relationMap.get("体重") && null != relationMap.get("身高")
                && StringUtils.isNotBlank(relationMap.get("体重").get("EXAM_RESULT"))
                && StringUtils.isNotBlank(relationMap.get("身高").get("EXAM_RESULT"))) {
            if (isNumber(relationMap.get("体重").get("EXAM_RESULT"))
                    && isNumber(relationMap.get("身高").get("EXAM_RESULT"))) {
                //计算体重指数
                float weight = 0, height = 0;
                weight = null == relationMap.get("体重") ? 0 : Float.parseFloat(relationMap.get("体重").get("EXAM_RESULT"));
                height = null == relationMap.get("身高") ? 0 : Float.parseFloat(relationMap.get("身高").get("EXAM_RESULT"));
                float BMI = calBMI(weight, height / 100);
                //将体重指数放到listTempl
                Map<String, String> map = new HashMap<String, String>();
                map.put("value", String.valueOf(BMI));
                listTemp.add(map);
            } else {
                Map<String, String> map = new HashMap<String, String>();
                map.put("value", "-");
                listTemp.add(map);
            }
        } else {
            Map<String, String> map = new HashMap<String, String>();
            map.put("value", "-");
            listTemp.add(map);
        }
        res.put("config", column);
        res.put("value", listTemp);
        return res;
    }

    /**
     * 体检视图获取检验列表
     *
     * @param medicalReportNo
     * @param medicalItemClass
     * @return
     */
    @Override
    public Page<Map<String, String>> getMedicalLabList(String oid, String visitId, String medicalReportNo, String medicalItemClass, String orderBy,
                                                       String orderDir, int pageNo, int pageSize,String patientId) {
        if(HdrConstantEnum.HOSPITAL_JDF_CD.getCode().equals(oid) ||
                HdrConstantEnum.HOSPITAL_JDF_HF.getCode().equals(oid) ||
                HdrConstantEnum.HOSPITAL_JDF_SZ.getCode().equals(oid)){
            patientId=medicalReportNo;
        }

        Page<Map<String, String>> page = new Page<Map<String, String>>();
        if (StringUtils.isBlank(orderBy)) {
            orderBy = "EXECUTE_TIME";
        }
        if (StringUtils.isBlank(orderDir)) {
            orderDir = "asc";
        }
        if (pageNo == 0) {
            pageNo = 1;
        }
        if (pageSize == 0) {
            pageSize = 10;
        }
        page.setPageNo(pageNo);
        page.setPageSize(pageSize);
        page.setOrderDir(orderDir);
        page.setOrderBy(orderBy);
        List<PropertyFilter> filters = new ArrayList<PropertyFilter>();
        //filters.add(new PropertyFilter("MEDICAL_REPORT_NO", "STRING", MatchType.EQ.getOperation(), medicalReportNo));
        //StringBuffer medicalItemClassTmp = new StringBuffer("(");
        StringBuffer medicalItemClassTmp = new StringBuffer("");
        if (StringUtils.isNotBlank(medicalItemClass)) {
            String[] itemCodes = medicalItemClass.split("\\|");
            for (int i = 0; i < itemCodes.length; i++) {
                if (i != (itemCodes.length - 1)) {
                    medicalItemClassTmp.append(itemCodes[i] + ",");
                } else {
                    medicalItemClassTmp.append(itemCodes[i]);
                }
            }
        }
        //medicalItemClassTmp.append(")");
        filters.add(
                new PropertyFilter("MEDICAL_ITEM_CLASS_CODE", MatchType.IN.getOperation(), medicalItemClassTmp.toString()));

        String[] columns = {"MEDICAL_APPLY_ITEM_NAME", "SUMMARY", "APPLY_NO", "EXECUTE_TIME"};
        PageResultVo<Map<String, String>> pageResultVo = queyHdrMedicalApplyMaster(patientId, oid, visitId, pageNo, pageSize, filters, columns);

        if (pageResultVo == null || pageResultVo.getResult() == null) {
            return page;
        }
        if (pageResultVo.getResult().size() > 0) {
            page.setResult(pageResultVo.getResult());
            page.setTotalCount(pageResultVo.getTotal());
        }

        List<Map<String, String>> list = new ArrayList<Map<String, String>>();
        if (page.getResult() != null && page.getResult().size() > 0) {
            for (Map<String, String> map : page.getResult()) {
                Map<String, String> mapTemp = new HashMap<String, String>();
                String applyNo = map.get("APPLY_NO");
                List<PropertyFilter> filters2 = new ArrayList<PropertyFilter>();
                //filters2.add(new PropertyFilter("MEDICAL_REPORT_NO", "STRING", MatchType.EQ.getOperation(), medicalReportNo));
                filters2.add(new PropertyFilter("APPLY_NO", MatchType.EQ.getOperation(), applyNo));

                String[] columns1 = {"EXAM_RESULT", "UNIT", "REFERENCE", "REFERENCE", "PROMPT", "MEDICAL_ITEM_CODE"};
                List<Map<String, String>> listDetail = queryHdrMedicalResultDetail(patientId, oid, visitId, filters2, columns1);

                int lowNum = 0, highNum = 0;
                for (Map<String, String> map2 : listDetail) {
                    if ("↓".equals(map2.get("PROMPT"))) {
                        lowNum += 1;
                        continue;
                    }
                    if ("↑".equals(map2.get("PROMPT"))) {
                        highNum += 1;
                        continue;
                    }
                }
                mapTemp.put("title", map.get("MEDICAL_APPLY_ITEM_NAME"));
                mapTemp.put("reportNo", medicalReportNo);
                mapTemp.put("applyNo", applyNo);
                mapTemp.put("time", map.get("EXECUTE_TIME"));
                mapTemp.put("low", String.valueOf(lowNum));
                mapTemp.put("high", String.valueOf(highNum));
                list.add(mapTemp);
            }
        }

        page.setResult(list);
        return page;
    }

    /**
     * 体检视图检验报告明细
     */
    @Override
    public Map<String, Object> getMedicalLabDetail(String oid,String visitId, String cardNo, String medicalReportNo, String applyNo,
                                                   String showUnNormal,String patientId) {
        if(HdrConstantEnum.HOSPITAL_JDF_CD.getCode().equals(oid) ||
                HdrConstantEnum.HOSPITAL_JDF_HF.getCode().equals(oid) ||
                HdrConstantEnum.HOSPITAL_JDF_SZ.getCode().equals(oid)){
            patientId=medicalReportNo;
        }

        Map<String, Object> res = new HashMap<String, Object>();
        List<PropertyFilter> filters = new ArrayList<PropertyFilter>();
        filters.add(new PropertyFilter("APPLY_NO", MatchType.EQ.getOperation(), applyNo));
        //        filters.add(new PropertyFilter("MEDICAL_REPORT_NO", "STRING", MatchType.EQ.getOperation(), medicalReportNo));
        //        List<Map<String, String>> listReport = hbaseDao.findConditionByPatient(HdrTableEnum.HDR_MEDICAL_REPORT_INFO.getCode(), cardNo,
        //                filters, new String[]{"REPORT_TIME", "MEDICAL_TIME"});
        //        for (Map<String, String> map : listReport) {
        //            res.put("report_time", map.get("REPORT_TIME"));
        //        }
        //缺少样本类型 样本标号

        String[] columns = {"MEDICAL_ITEM_CLASS_NAME", "MEDICAL_APPLY_ITEM_NAME", "APPLY_NO", "EXECUTE_TIME", "EXECUTE_DEPT_NAME",
                "EXECUTE_DOCTOR_NAME", "APPLY_DEPT_NAME", "APPLY_DOCTOR_NAME", "APPLY_TIME", "REPORT_CONFIRMER_NAME"};
        List<Map<String, String>> list = queyHdrMedicalApplyMaster(patientId, oid, visitId, 0, 0, filters, columns).getResult();

        if(list!=null && list.size()>0){
            for (Map<String, String> map : list) {
                res.put("check_type", map.get("MEDICAL_ITEM_CLASS_NAME"));
                res.put("check_item", map.get("MEDICAL_APPLY_ITEM_NAME"));
                res.put("execute_dept", map.get("EXECUTE_DEPT_NAME"));
                res.put("execute_doctor", map.get("EXECUTE_DOCTOR_NAME"));
                res.put("time", map.get("EXECUTE_TIME"));
                res.put("applyDeptName", map.get("APPLY_DEPT_NAME"));
                res.put("applyDoctorName", map.get("APPLY_DOCTOR_NAME"));
                res.put("applyTime", map.get("APPLY_TIME"));
                res.put("applyNo", map.get("APPLY_NO"));
                res.put("reportConfirmerName", map.get("REPORT_CONFIRMER_NAME"));
            }
        }

        //查询明细数据
        List<PropertyFilter> filters2 = new ArrayList<PropertyFilter>();
        //filters2.add(new PropertyFilter("MEDICAL_REPORT_NO", "STRING", MatchType.EQ.getOperation(), medicalReportNo));
        filters2.add(new PropertyFilter("APPLY_NO", MatchType.EQ.getOperation(), applyNo));

        String[] columns1 = {"EXAM_RESULT", "UNIT", "REFERENCE", "REFERENCE", "PROMPT", "MEDICAL_ITEM_CODE", "MEDICAL_ITEM_NAME"};
        List<Map<String, String>> listDetail = queryHdrMedicalResultDetail(patientId, oid, visitId, filters2, columns1);

        List<Map<String, String>> listDetail2 = new ArrayList<Map<String, String>>();
        if (listDetail != null && listDetail.size() > 0) {
            for (Map<String, String> map : listDetail) {
                //只显示异常结果
                if ("1".equals(showUnNormal) && !("↑".equals(map.get("PROMPT")) || "↓".equals(map.get("PROMPT")))) {
                    continue;
                }
                Map<String, String> map2 = new HashMap<String, String>();
                map2.put("code", map.get("MEDICAL_ITEM_CODE"));
                map2.put("name", map.get("MEDICAL_ITEM_NAME"));
                map2.put("result", map.get("EXAM_RESULT"));
                String status = "";
                String promt = map.get("PROMPT");
                if ("↑".equals(promt)) {
                    status = "high";
                }
                if ("↓".equals(promt)) {
                    status = "low";
                }
                map2.put("resultStatus", status);
                map2.put("reference", map.get("REFERENCE"));
                map2.put("unit", map.get("UNIT"));
                listDetail2.add(map2);
            }
        }

        res.put("data", listDetail2);
        return res;
    }

    /**
     * 体检视图检查报告列表
     *
     * @param medicalReportNo
     * @param medicalItemClass
     * @param orderBy
     * @param orderDir
     * @param pageNo
     * @param pageSize
     * @return
     */
    @Override
    public Page<Map<String, String>> getMedicalExamList(String oid, String visitId,String medicalReportNo, String medicalItemClass, String orderBy,
                                                        String orderDir, int pageNo, int pageSize,String patientId) {
        if(HdrConstantEnum.HOSPITAL_JDF_CD.getCode().equals(oid) ||
                HdrConstantEnum.HOSPITAL_JDF_HF.getCode().equals(oid) ||
                HdrConstantEnum.HOSPITAL_JDF_SZ.getCode().equals(oid)){
            patientId=medicalReportNo;
        }

        Page<Map<String, String>> page = new Page<Map<String, String>>();
        if (StringUtils.isBlank(orderBy)) {
            orderBy = "EXECUTE_TIME";
        }
        if (StringUtils.isBlank(orderDir)) {
            orderDir = "asc";
        }
        if (pageNo == 0) {
            pageNo = 1;
        }
        if (pageSize == 0) {
            pageSize = 10;
        }
        page.setPageNo(pageNo);
        page.setPageSize(pageSize);
        page.setOrderDir(orderDir);
        page.setOrderBy(orderBy);
        List<PropertyFilter> filters = new ArrayList<PropertyFilter>();
        //filters.add(new PropertyFilter("MEDICAL_REPORT_NO", "STRING", MatchType.EQ.getOperation(), medicalReportNo));
        //StringBuffer medicalItemClassTmp = new StringBuffer("(");
        StringBuffer medicalItemClassTmp = new StringBuffer("");
        if (StringUtils.isNotBlank(medicalItemClass)) {
            String[] itemCodes = medicalItemClass.split("\\|");
            for (int i = 0; i < itemCodes.length; i++) {
                if (i != (itemCodes.length - 1)) {
                    medicalItemClassTmp.append(itemCodes[i] + ",");
                } else {
                    medicalItemClassTmp.append(itemCodes[i]);
                }
            }
        }
        //medicalItemClassTmp.append(")");
        filters.add(
                new PropertyFilter("MEDICAL_ITEM_CLASS_CODE", MatchType.IN.getOperation(), medicalItemClassTmp.toString()));

        String[] columns = {"MEDICAL_APPLY_ITEM_NAME", "SUMMARY", "APPLY_NO", "EXECUTE_TIME"};
        PageResultVo<Map<String, String>> pageResultVo = queyHdrMedicalApplyMaster(patientId, oid, visitId, pageNo, pageSize, filters, columns);

        if (pageResultVo == null || pageResultVo.getResult() == null) {
            return page;
        }
        if (pageResultVo.getResult().size() > 0) {
            page.setResult(pageResultVo.getResult());
            page.setTotalCount(pageResultVo.getTotal());
        }

        List<Map<String, String>> list = new ArrayList<Map<String, String>>();
        if (page.getResult() != null && page.getResult().size() > 0) {
            for (Map<String, String> map : page.getResult()) {
                Map<String, String> mapTemp = new HashMap<String, String>();
                mapTemp.put("title", map.get("MEDICAL_APPLY_ITEM_NAME"));
                mapTemp.put("reportNo", medicalReportNo);
                mapTemp.put("applyNo", map.get("APPLY_NO"));
                mapTemp.put("time", map.get("EXECUTE_TIME"));
                list.add(mapTemp);
            }
        }

        page.setResult(list);
        return page;
    }

    /**
     * 体检视图检查报告明细
     */
    @Override
    public Map<String, Object> getMedicalExamDetail(String oid,String visitId, String medicalReportNo, String applyNo,String patientId) {
        if(HdrConstantEnum.HOSPITAL_JDF_CD.getCode().equals(oid) ||
                HdrConstantEnum.HOSPITAL_JDF_HF.getCode().equals(oid) ||
                HdrConstantEnum.HOSPITAL_JDF_SZ.getCode().equals(oid)){
            patientId=medicalReportNo;
        }

        Map<String, Object> res = new HashMap<String, Object>();
        List<PropertyFilter> filters = new ArrayList<PropertyFilter>();
        filters.add(new PropertyFilter("APPLY_NO", MatchType.EQ.getOperation(), applyNo));
        //缺少样本类型
        //温州中心医院修改此处，查询处新加SUMMARY，遍历处新加result作为检查诊断
        String[] columns = {"MEDICAL_ITEM_CLASS_NAME", "MEDICAL_APPLY_ITEM_NAME", "APPLY_NO", "EXECUTE_TIME",
                "EXECUTE_DEPT_NAME", "EXECUTE_DOCTOR_NAME", "SUMMARY", "APPLY_DEPT_NAME", "APPLY_DOCTOR_NAME",
                "APPLY_TIME", "REPORT_CONFIRMER_NAME", "REPORT_CONFIRM_TIME", "URL", "LINK_TYPE", "RESULT_STATUS_NAME"};
        List<Map<String, String>> list = queyHdrMedicalApplyMaster(patientId, oid, visitId, 0, 0, filters, columns).getResult();

        if(list!=null && list.size()>0){
            for (Map<String, String> map : list) {
                res.put("check_type", map.get("MEDICAL_ITEM_CLASS_NAME"));
                res.put("check_item", map.get("MEDICAL_APPLY_ITEM_NAME"));
                res.put("medical_item_name", map.get("MEDICAL_APPLY_ITEM_NAME"));
                res.put("execute_dept", map.get("EXECUTE_DEPT_NAME"));
                res.put("execute_doctor", map.get("EXECUTE_DOCTOR_NAME"));
                res.put("result", map.get("SUMMARY"));
                res.put("reference", map.get("MEDICAL_APPLY_ITEM_NAME"));
                res.put("time", map.get("EXECUTE_TIME"));
                res.put("applyDeptName", map.get("APPLY_DEPT_NAME"));
                res.put("applyDoctorName", map.get("APPLY_DOCTOR_NAME"));
                res.put("applyTime", map.get("APPLY_TIME"));
                res.put("reportConfirmerName", map.get("REPORT_CONFIRMER_NAME"));
                res.put("reportConfirmTime", map.get("REPORT_CONFIRM_TIME"));
                res.put("applyNo", map.get("APPLY_NO"));
                res.put("reportStatusName", map.get("RESULT_STATUS_NAME"));
                //新增检查报告调用url查看
                if(StringUtils.isNotBlank(map.get("URL"))){
                    List<Map<String, String>> urlList = new ArrayList<Map<String, String>>();
                    Map<String, String> pacsUrl = new HashMap<String, String>();
                    pacsUrl.put("name", "查看检查报告");
                    pacsUrl.put("url", map.get("URL"));
                    pacsUrl.put("linkType", map.get("LINK_TYPE"));
                    urlList.add(pacsUrl);
                    res.put("urls", urlList);
                }
            }
        }

        //查询明细数据
        List<PropertyFilter> filters2 = new ArrayList<PropertyFilter>();
        //filters2.add(new PropertyFilter("MEDICAL_REPORT_NO", "STRING", MatchType.EQ.getOperation(), medicalReportNo));
        filters2.add(new PropertyFilter("APPLY_NO", MatchType.EQ.getOperation(), applyNo));

        String[] columns1 = {"EXAM_RESULT", "REFERENCE", "PROMPT", "MEDICAL_ITEM_CODE", "MEDICAL_ITEM_NAME", "NORMAL_RESULT"};
        List<Map<String, String>> listDetail = queryHdrMedicalResultDetail(patientId, oid, visitId, filters2, columns1);
        //温州中心医院修改此处，去掉result,medical_item_name,修改normal_result的对应字段
        if (listDetail != null && !listDetail.isEmpty()) {
            StringBuilder sb = new StringBuilder();

            for (Map<String, String> map : listDetail) {
                sb.append(map.get("MEDICAL_ITEM_NAME")).append(":").append(map.get("EXAM_RESULT")).append("<br/>");
                //res.put("result", map.get("EXAM_RESULT"));
                //res.put("normal_result", map.get("NORMAL_RESULT"));
                //res.put("medical_item_name", map.get("MEDICAL_ITEM_NAME"));
                res.put("medical_item_code", map.get("MEDICAL_ITEM_CODE"));
                //res.put("reference", map.get("REFERENCE"));
            }
            res.put("normal_result", StringUtils.isBlank(sb.toString()) ? "-" : sb.toString());
        }
        return res;
    }

    /**
     * 体检报告数量
     *
     * @return
     */
    @Override
    public Map<String, String> getReportNum(String oid, String visitId,String medicalReportNo, String labClass, String examClass,String patientId) {
        Page<Map<String, String>> pageLab = getMedicalLabList(oid, visitId,medicalReportNo, labClass, "EXECUTE_TIME", "desc", 1,
                10000,patientId);
        Page<Map<String, String>> pageExam = getMedicalExamList(oid,visitId, medicalReportNo, examClass, "EXECUTE_TIME", "desc", 1,
                10000,patientId);
        Map<String, String> res = new HashMap<String, String>();
        res.put("labNum", String.valueOf(pageLab.getTotalCount()));
        res.put("examNum", String.valueOf(pageExam.getTotalCount()));
        return res;
    }

    @Override
    public Map<String, Object> getReportTrendData(String oid, String patientId, String medicalItemCode, int numIndex,
                                                  String beginTime, String endTime) {
        //封装数据
        Map<String, Object> result = new HashMap<String, Object>();
        List<PropertyFilter> filters = new ArrayList<PropertyFilter>();
        filters.add(new PropertyFilter("VITAL_TYPE_NAME", MatchType.LIKE.getOperation(), medicalItemCode));
        if (StringUtils.isNotBlank(beginTime) && StringUtils.isNotBlank(endTime)) {
            filters.add(new PropertyFilter("MEASURING_TIME", MatchType.GE.getOperation(), beginTime));
            filters.add(new PropertyFilter("MEASURING_TIME", MatchType.LE.getOperation(), endTime));
        }
        List<Map<String, String>> listQuery = null;//hbaseDao.findConditionByPatientVisitId(HdrTableEnum.HDR_VITAL_MEASURE.getCode(), patientId, filters, "VITAL_TYPE_CODE", "VITAL_TYPE_NAME", "MEASURING_TIME", "VITAL_SIGN_VALUE", "VITAL_SIGN2_VALUE", "VITAL_SIGN3_VALUE", "UNIT");
        if (null == listQuery || listQuery.size() == 0) {
            result.put("unit", "");
            result.put("series", new ArrayList<Map<String, Object>>());
            result.put("categories", new ArrayList<String>());
            result.put("name", "");
            return result;
        }
        //时间 降序
        Utils.sortListByDate(listQuery, "SEQUENCE_NO", "desc");
        //近3次，近5次，需要分页
        int num = 0;
        if (listQuery.size() == 0) {
            num = 1;
        } else {
            num = listQuery.size();
        }
        ListPage<Map<String, String>> listPage = new ListPage<Map<String, String>>(listQuery, 1, num);

        if (3 == numIndex) {
            listPage = new ListPage<Map<String, String>>(listQuery, 1, 3);
        } else if (5 == numIndex) {
            listPage = new ListPage<Map<String, String>>(listQuery, 1, 5);
        } else if (10 == numIndex) {
            listPage = new ListPage<Map<String, String>>(listQuery, 1, 10);
        } else if (50 == numIndex) {
            listPage = new ListPage<Map<String, String>>(listQuery, 1, 50);
        }
        List<Map<String, String>> pagedList = listPage.getPagedList();
        //当前页记录再排序，为统计时间坐标准备
        Utils.sortListByDate(pagedList, "MEASURING_TIME", "asc");

        //小项名称
        String subItemName = null;
        String unit = "";
        //x坐标 时间
        List<String> categories = new ArrayList<String>();
        List<Map<String, Object>> series = new ArrayList<Map<String, Object>>();
        Map<String, Object> serie = new HashMap<String, Object>();
        List<Object> valueList = new ArrayList<Object>();
        //添加上限和下限
        List<Object> valueListMax = new ArrayList<Object>();
        List<Object> valueListMix = new ArrayList<Object>();
        //遍历检验结果
        for (Map<String, String> map : pagedList) {
            //            Map<String, Object> value = new HashMap<String, Object>();
            if (StringUtils.isBlank(subItemName)) {
                subItemName = map.get("VITAL_TYPE_NAME");
                unit = map.get("UNIT");
            }
            //上限
            valueListMax.add(map.get("VITAL_TYPE_NAME"));
            //下限
            valueListMix.add(map.get("VITAL_TYPE_NAME"));
            //y轴  先取定量结果  若没有值 再取定性结果
            String resultValue = map.get("VITAL_SIGN_VALUE");
            //            value.put("y", null == resultValue || "" == resultValue ? null : Double.valueOf(resultValue));
            //            value.put("name", map.get("VITAL_TYPE_NAME"));
            valueList.add(null == resultValue || "" == resultValue ? null : Double.valueOf(resultValue));
            categories.add(map.get("MEASURING_TIME"));
        }
        //检验细项名称
        if (StringUtils.isNotBlank(subItemName)) {
            result.put("name", subItemName);
        } else {
            result.put("name", medicalItemCode);
        }
        //y坐标
        serie.put("name", subItemName);
        serie.put("data", valueList);

        Map<String, Object> serieMax = new HashMap<String, Object>();
        serieMax.put("name", "上限");
        serieMax.put("dashStyle", "ShortDot");
        Map<String, Boolean> map = new HashMap<String, Boolean>();
        map.put("enabled", false);
        serieMax.put("marker", map);
        serieMax.put("data", valueListMax);

        Map<String, Object> serieMix = new HashMap<String, Object>();
        serieMix.put("name", "下限");
        serieMix.put("dashStyle", "ShortDot");
        Map<String, Boolean> map2 = new HashMap<String, Boolean>();
        map2.put("enabled", false);
        serieMix.put("marker", map2);
        serieMix.put("data", valueListMix);

        series.add(serieMax);
        series.add(serie);
        series.add(serieMix);

        result.put("unit", unit);
        result.put("series", series);
        result.put("categories", categories);
        return result;
    }

    @Override
    public Map<String, String> getMedicalHospialInfo(String oid) {
        String reportShow = Config.getConfigValue(oid, "HOSPITAL_REPORT_SHOWS");
        String contactNumber = Config.getConfigValue(oid, "HOSPITAL_CONTACT_NUMBER");
        String hotilne = Config.getConfigValue(oid, "HOSPITAL_HOTILNE");
        String website = Config.getConfigValue(oid, "HOSPITAL_WEBSITE");
        String address = Config.getConfigValue(oid, "HOSPITAL_PHYSICAL_ADDRESS");
        String busRoutes = Config.getConfigValue(oid, "HOSPITAL_BUS_ROUTES");
        Map<String, String> map = new HashMap<String, String>();
        map.put("reportShow", reportShow);
        map.put("contactNumber", contactNumber);
        map.put("hotilne", hotilne);
        map.put("website", website);
        map.put("address", address);
        map.put("busRoutes", busRoutes);
        return map;
    }

    /**
     * 根据体检类型取体检配置
     * 一般体检：common
     * 内科;in_check
     * 外科：out_check
     * 眼科：eye_check
     * 耳鼻喉科; ENT
     * 妇科：GYN
     * 病情询问：MHT
     * @param check
     * @return
     */
    public String getMedicalConfig(String oid, String check) {
        switch (check) {
            case "common_check":
                return Config.getMEDICAL_COMMON_CHECK_CONFIG(oid);
            case "in_check":
                return Config.getMEDICAL_IN_CHECK_CONFIG(oid);
            case "out_check":
                return Config.getMEDICAL_OUT_CHECK_CONFIG(oid);
            case "eye_check":
                return Config.getMEDICAL_EYE_CHECK_CONFIG(oid);
            case "ent":
                return Config.getMEDICAL_ENT_CHECK_CONFIG(oid);
            case "GYN":
                return ConfigCache.getCache(oid, "MEDICAL_GYN_CHECK_CONFIG");
            case "MHT":
                return ConfigCache.getCache(oid, "MEDICAL_MHT_CHECK_CONFIG");
        }
        return "";
    }

    /**
     * 返回字段处理
     *
     * @param list
     * @return
     */
    public List<Map<String, String>> fieldHandle(List<Map<String, String>> list) {
        List<Map<String, String>> res = new ArrayList<Map<String, String>>();
        for (Map<String, String> map : list) {
            Map<String, String> map1 = new HashMap<String, String>();
            map1.put("value", (null == map.get("EXAM_RESULT") ? "" : map.get("EXAM_RESULT"))
                    + (null == map.get("UNIT") ? "" : map.get("UNIT")));
            res.add(map1);
        }
        return res;
    }

    /**
     * 通过给出体重和身高计算身体质量指数
     *
     * @param weight 体重（单位是千克）
     * @param height 身高（单位是米）
     */
    public float calBMI(float weight, float height) {
        float f = weight / (height * height);
        BigDecimal b = new BigDecimal(f);
        float f1 = b.setScale(2, BigDecimal.ROUND_HALF_UP).floatValue();//四舍五入保留两位小数
        return f1;

    }

    public String maleBMI(float BMI) {
        if (BMI <= 18.4) {
            return "偏瘦";
        } else if (BMI <= 23.9 && BMI >= 18.5) {
            return "正常";
        } else if (BMI <= 27.9 && BMI >= 24.0) {
            return "过重";
        } else if (BMI >= 28.0) {
            return "肥胖";
        } else {
            return "非正常BMI";
        }
    }

    /**
     * 判断字符串是否为数字或小数
     *
     * @param number
     * @return
     */
    public static boolean isNumber(String number) {
        int index = number.indexOf(".");
        if (index < 0) {
            return StringUtils.isNumeric(number);
        } else {
            String num1 = number.substring(0, index);
            String num2 = number.substring(index + 1);

            return StringUtils.isNumeric(num1) && StringUtils.isNumeric(num2);
        }
    }

    public PageResultVo<Map<String, String>> queyHdrMedicalApplyMaster(String patientId,String oid,String visitId,int pageNo,int pageSize, List<PropertyFilter> filters,String[] columns){
        PageResultVo<Map<String, String>> pageResultVo = new PageResultVo<>();
        ResultVo<PageResultVo<Map<String, String>>> resultVo = hbaseQueryClient.getPageByCondition(
                PageRequestBuilder.init()
                        .tableName(HdrTableEnum.HDR_MEDICAL_APPLY_MASTER.getCode())
                        .patientId(patientId)
                        .oid(oid)
                        .visitId(visitId)
                        .visitTypeCode("")
                        .filters(filters)
                        .pageNo(pageNo)
                        .pageSize(pageSize)
                        .orderBy("")
                        .desc()
                        .column("MEDICAL_APPLY_ITEM_NAME", "SUMMARY", "APPLY_NO", "EXECUTE_TIME")
                        .build());
        if(resultVo.isSuccess()){
            pageResultVo = resultVo.getContent();
        }

        return pageResultVo;
    }

    public List<Map<String, String>> queryHdrMedicalResultDetail(String patientId,String oid,String visitId,List<PropertyFilter> filters,String[] columns){
        List<Map<String, String>> listDetail = new ArrayList<>();
        ResultVo<PageResultVo<Map<String, String>>> resultVo2 = hbaseQueryClient.getPageByCondition(
                PageRequestBuilder.init()
                        .tableName(   HdrTableEnum.HDR_MEDICAL_RESULT_DETAIL.getCode())
                        .patientId(patientId)
                        .oid(oid)
                        .visitId(visitId)
                        .visitTypeCode("")
                        .filters(filters)
                        .pageNo(0)
                        .pageSize(0)
                        .orderBy("")
                        .desc()
                        .column(columns)
                        .build());
        if(resultVo2.isSuccess()){
            listDetail = resultVo2.getContent().getResult();
        }
        return listDetail;
    }

}
