package com.goodwill.hdr.civ.enums.commonModule;

/**
 * 前端判断展示方式所需的枚举量
 *
 * <AUTHOR>
 * @since 4.6.8.3
 */
public enum SourceEnum {
    URL("url", "普通模块"), LIST("list", "带列表的模块"), TABLE("table", "纯列表的模块"),
    LIST_TABLE("list_table", "右侧列表+左侧列表"),
    URL_TABLE("url_table", "带url的表格");


    private final String code;
    private final String label;

    SourceEnum(String code, String label) {
        this.code = code;
        this.label = label;
    }


    public String getCode() {
        return code;
    }


    public String getLabel() {
        return label;
    }
}
