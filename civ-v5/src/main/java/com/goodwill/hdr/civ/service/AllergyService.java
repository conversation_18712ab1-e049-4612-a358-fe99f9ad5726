package com.goodwill.hdr.civ.service;



import com.goodwill.hdr.core.orm.Page;

import java.util.List;
import java.util.Map;

/**
 * @Description
 * 类描述：过敏信息服务接口
 * <AUTHOR>
 * @Date 2018年9月28日
 * @modify
 * 修改记录：
 *
 */
public interface AllergyService {

	/**
	 * @Description
	 * 方法描述: 某次就诊的过敏记录
	 * @return 返回类型： Page<Map<String,String>>
	 * @param patientId 患者编号
	 * @param visitId 就诊次数
	 * @param orderBy 排序字段
	 * @param orderDir 排序规则
	 * @param pageNo 页码
	 * @param pageSize 分页单位
	 * @param filterString 过滤条件  column1|like|aa;column2|=|3
	 * @return 分页对象
	 */
	public Page<Map<String, String>> getAllergyList(String oid, String patientId, String visitId, String visitType, String orderBy,
													String orderDir, int pageNo, int pageSize, String filterString);

	/**
	 * @Description
	 * 方法描述: 患者所有的过敏记录
	 * @return 返回类型： Page<Map<String,String>>
	 * @param patientId 患者编号
	 * @param outPatientId 关联的患者号
	 * @param orderBy 排序字段
	 * @param orderDir 排序规则
	 * @param pageNo 页码
	 * @param pageSize 分页单位
	 * @param filterString 过滤条件  column1|like|aa;column2|=|3
	 * @return
	 */
	public Page<Map<String, String>> getAllergys(String oid, String patientId, String outPatientId, String orderBy,
                                                 String orderDir, int pageNo, int pageSize, String filterString);

	/**
	 * @Description
	 * 方法描述: 某次就诊的过敏记录数量
	 * @return 返回类型： long
	 * @param patientId 患者编号
	 * @param visitId 就诊次数
	 * @param visitType 就诊类型
	 * @return
	 */
	public long getAllergyCount(String oid, String patientId, String visitId, String visitType);

	/**
	 * @Description
	 * 方法描述: 获取过敏类型
	 * @return 返回类型： List<Map<String,String>>
	 * @return
	 */
	public List<Map<String, String>> getAllergyTypes(String oid);

	/**
	 * @Description
	 * 方法描述: 获取过敏程度
	 * @return 返回类型： List<Map<String,String>>
	 * @return
	 */
	public List<Map<String, String>> getAllergySeverity(String oid);

}
