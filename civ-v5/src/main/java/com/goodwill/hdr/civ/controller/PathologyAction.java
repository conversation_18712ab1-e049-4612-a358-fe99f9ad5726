package com.goodwill.hdr.civ.controller;


import com.goodwill.hdr.civ.service.PathologyReportService;
import com.goodwill.hdr.core.orm.Page;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Description 类描述：病理Action
 * @Date 2020年8月7日
 */
@RequestMapping("/pathology")
@RestController
@Api(tags = "病理报告查询")
public class PathologyAction {

    @Autowired
    private PathologyReportService pathologyReportService;

    /**
     * @Description 某次就诊的病理报告
     */
    @ApiOperation(value = "获取某次就诊的病理报告", notes = "获取某次就诊的病理报告", httpMethod = "POST")
    @RequestMapping(value = "/getPatientVisitPathology", method = RequestMethod.POST)
    public Page<Map<String, String>> getPatientVisitPathology(String pno, String oid, String patientId, String visitId, String visitType, int pageSize) {
        int pno1 = StringUtils.isBlank(pno) ? 0 : Integer.parseInt(pno);
        //参数 患者编号  就诊次  就诊类型
        Page<Map<String, String>> result = pathologyReportService.getPathologyReportList(oid, patientId, visitId, visitType,
                "REPORT_TIME", "desc", "", "", pno1, pageSize);
        //响应
        return result;
    }

    /**
     * @Description 某份病理报告详情
     */
    @ApiOperation(value = "获取某份病理报告详情", notes = "获取某份病理报告详情", httpMethod = "POST")
    @RequestMapping(value = "/getPathologyDetails", method = RequestMethod.POST)
    public Map<String, String> getPathologyDetails(String reportNo, String oid, String patientId, String visitId) {
        //病理报告主键
//        String reportNo = getParameter("reportNo");
        Map<String, String> result = pathologyReportService.getPathologyReportDetails(oid, patientId, visitId, reportNo);
        //响应
        return result;
    }

    /**
     * @Description 获得患者所有的病理报告
     */
    @ApiOperation(value = "获取患者所有的病理报告", notes = "获取患者所有的病理报告", httpMethod = "POST")
    @RequestMapping(value = "/getPathologyReports", method = RequestMethod.POST)
    public List<Map<String, Object>> getPathologyReports( String oid, String patientId, String outPatientId, String visitType, String year, String key, String type, String click_type) {
//        String outPatientId = getParameter("outPatientId");
//        String visitType = getParameter("visitType");
//        String year = getParameter("year");
//        String key = getParameter("key");
//        String type = getParameter("type");
//        String click_Type = getParameter("click_type");
        List<Map<String, Object>> result = new ArrayList<Map<String, Object>>();
        result = pathologyReportService.getPathologyReports(oid, patientId, 1, 2000, "REPORT_TIME", "desc", outPatientId,
                visitType, year, key, type, click_type);
        //响应
        return result;
    }

    /**
     * @Description 获得患者所有的病理报告类型
     */
    @ApiOperation(value = "获取患者所有的病理报告类型", notes = "获取患者所有的病理报告类型", httpMethod = "POST")
    @RequestMapping(value = "/getPathologyReportTypes", method = RequestMethod.POST)
    public List<Map<String, String>> getPathologyReportTypes(String this_oid, String oid, String patientId, String outPatientId, String visitType, String key) {
//        String outPatientId = getParameter("outPatientId");
//        String visitType = getParameter("visitType");
//        String key = getParameter("key");
        List<Map<String, String>> result = pathologyReportService.getPathologyReportTypes(oid, patientId, outPatientId,
                visitType);
        //响应
        return result;
    }

}
