package com.goodwill.hdr.civ.service;


import com.goodwill.hdr.civ.vo.ExamVoForMedicalTechnologies;
import com.goodwill.hdr.core.orm.Page;
import com.goodwill.hdr.web.common.vo.ResultVO;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Description 类描述：检查报告服务接口
 * @Date 2018年5月2日
 * @modify 修改记录：
 */
public interface CheckReportService {

    /**
     * @param patId     患者编号
     * @param visitId   就诊次数
     * @param visitType 就诊类型
     * @param orderBy   排序字段
     * @param orderDir  排序规则
     * @param pageNo    页码
     * @param pageSize  分页单位
     * @return 分页对象
     * @Description 方法描述: 查询患者某次就诊的检查报告
     */
    public Page<Map<String, String>> getCheckReportList(String oid, String patId, String visitId, String visitType, String orderBy,
                                                        String orderDir, String mainDiag, String deptCode, int pageNo, int pageSize);

    /**
     * 通过检查报告反向关联检查医嘱
     *
     * @param pid
     * @param vid
     * @param order
     * @return
     */
    List<Map<String, String>> getCheckOrderByPid(String oid, String pid, String vid, String order);

    /**
     * @return
     * @Description 方法描述: 查询某份检查报告的详情
     */
    public Map<String, String> getCheckReportDetails(String oid, String pid, String vid, String reportNo);

    /**
     * @param patientId 患者编号
     * @param visitId   就诊次数
     * @param visitType 就诊类型
     * @return
     * @Description 方法描述: 某次就诊的检查报告数量
     */
    public long getCheckCount(String oid, String patientId, String visitId, String visitType);

    /**
     * @param patientId    患者编号
     * @param pageNo       页码
     * @param pageSize     分页单位
     * @param orderby      排序字段
     * @param orderdir     排序规则
     * @param outPatientId 患者ID
     * @param year         年份
     * @return 分页对象
     * @Description 方法描述:查询患者所有的检查报告
     */
    public List<Map<String, Object>> getCheckReports( String oid, String patientId, int pageNo, int pageSize, String orderby,
                                                     String orderdir, String outPatientId, String visitType, String year, String key, String type, String click_Type);

    /**
     * 获取报告总数
     *
     * @param resultMap
     */
    public void getAllReportsCount(Map<String, Object> resultMap, String outPatientId);

    /**
     * 获取报告类型
     */
    public List<Map<String, String>> getAllReportTypes(  String outPatientId);

    ResultVO<Map<String, ExamVoForMedicalTechnologies>> getCheckListForMedicalTechnologies(String this_oid, String oid, String oidLast,String patientId,String visitId,String visitTypeCode,String outPatientId, String date);

    ResultVO<List<Map<String, String>>> getCheckItemList(String this_oid, String outPatientId, String itemName, String date, boolean currentVist);

    /**
     * 获取超声测量值
     *
     * @param oid
     * @param patientId
     * @param visitType
     * @param reportNo
     * @return
     */
    List<Map<String, String>> getUlMeasure(String oid, String patientId, String visitType, String reportNo);
}
