package com.goodwill.hdr.civ.controller;


import com.goodwill.hdr.civ.service.MedicalService;
import com.goodwill.hdr.core.orm.Page;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Description 类描述： 体检视图action
 * @modify 修改记录：
 */
@RequestMapping("/medical")
@RestController
@Api(tags = "体检视图")
public class MedicalAction {
    @Autowired
    private MedicalService medicalService;

    /**
     * 体检视图获取体检人信息
     */
    @ApiOperation(value = "体检视图获取体检人信息", notes = "体检视图获取体检人信息", httpMethod = "POST")
    @RequestMapping(value = "/getPatientInfo", method = RequestMethod.POST)
    public Map<String, String> getPatientInfo(String oid, String patientId, String visitType) {
        Map<String, String> map = medicalService.getPatientInfo(oid, patientId, visitType);
        return map;
    }

    /**
     * 获取体检视图单次体检表头配置
     */
    @ApiOperation(value = "获取体检视图单次体检表头配置", notes = "获取体检视图单次体检表头配置", httpMethod = "POST")
    @RequestMapping(value = "/getMedicalTableConfig", method = RequestMethod.POST)
    public List<Map<String, String>> getMedicalTableConfig(String oid) {
        List<Map<String, String>> config = medicalService.getMedicalTableHead(oid);
        return config;
    }

    /**
     * 获取体检视图列表
     */
    @ApiOperation(value = "获取体检视图列表", notes = "获取体检视图列表", httpMethod = "POST")
    @RequestMapping(value = "/getMedicalVisitList", method = RequestMethod.POST)
    public List<Map<String, String>> getMedicalVisitList(String oid,  String card_no, String begin_date, String end_date, String visitDate,int pageNo,int pageSize) {

        List<Map<String, String>> list = medicalService.getMedicalListInfo(oid, card_no, begin_date, end_date, visitDate, pageNo,pageSize );
        return list;
    }

    /**
     * 获取体检视图首页
     */
    @ApiOperation(value = "获取体检视图首页", notes = "获取体检视图首页", httpMethod = "POST")
    @RequestMapping(value = "/getMedicalSummary", method = RequestMethod.POST)
    public Map<String, Object> getMedicalSummary(String oid, String patientId, String visitId,String medicalReportNo, String card_no) {
//        String medicalReportNo = getParameter("medicalReportNo");
//        String cardNo = getParameter("card_no");
        Map<String, Object> res = medicalService.getMedicalSummaryHomePage(visitId,medicalReportNo, oid, patientId, card_no);
        return res;
    }

    /**
     * 获取体检视图: 一般检查 内科 外科 眼科 耳鼻喉科
     *
     * @return
     */
    @ApiOperation(value = "获取体检视图: 一般检查 内科 外科 眼科 耳鼻喉科", notes = "获取体检视图: 一般检查 内科 外科 眼科 耳鼻喉科", httpMethod = "POST")
    @RequestMapping(value = "/getCommonCheck", method = RequestMethod.POST)
    public Map<String, Object> getCommonCheck(String oid, String visitId,String medicalReportNo, String medicalItemClass, String checkType,String patientId) {
//        String medicalReportNo = getParameter("medicalReportNo");
//        String medicalItemClass = getParameter("medicalItemClass");
//        String checkType = getParameter("checkType");
        Map<String, Object> map = medicalService.getMedicalCommonCheck(oid, visitId,medicalReportNo, medicalItemClass, checkType, patientId);
        return map;
    }

    /**
     * 体检视图检验报告列表
     */
    @ApiOperation(value = "体检视图检验报告列表", notes = "体检视图检验报告列表", httpMethod = "POST")
    @RequestMapping(value = "/getMedicalLabList", method = RequestMethod.POST)
    public Page<Map<String, String>> getMedicalLabList(String oid, String visitId, String medicalReportNo, String medicalItemClass, int pageNo, int pageSize, String patientId) {
//        String medicalReportNo = getParameter("medicalReportNo");
//        String medicalItemClass = getParameter("medicalItemClass");
        Page<Map<String, String>> page = medicalService.getMedicalLabList(oid,visitId, medicalReportNo, medicalItemClass, "EXECUTE_TIME", "desc", pageNo, pageSize,patientId);
        return page;
    }

    /**
     * 体检视图检验报告明细
     */
    @ApiOperation(value = "体检视图检验报告明细", notes = "体检视图检验报告明细", httpMethod = "POST")
    @RequestMapping(value = "/getMedicalLabDetail", method = RequestMethod.POST)
    public Map<String, Object> getMedicalLabDetail(String oid, String visitId,String medicalReportNo, String applyNo, String showUnNormal, String card_no,String patientId) {
//        String medicalReportNo = getParameter("medicalReportNo");
//        String medicalItemClass = getParameter("applyNo");
//        String showUnNormal = getParameter("showUnNormal");
//        String cardNo = getParameter("card_no");
        Map<String, Object> map = medicalService.getMedicalLabDetail(oid, visitId,card_no, medicalReportNo, applyNo, showUnNormal,patientId);
        return map;
    }

    /**
     * 体检视图检查报告列表
     */
    @ApiOperation(value = "体检视图检查报告列表", notes = "体检视图检查报告列表", httpMethod = "POST")
    @RequestMapping(value = "/getMedicalExamList", method = RequestMethod.POST)
    public Page<Map<String, String>> getMedicalExamList(String oid, String visitId,String medicalReportNo, String medicalItemClass, int pageNo, int pageSize,String patientId) {
//        String medicalReportNo = getParameter("medicalReportNo");
//        String medicalItemClass = getParameter("medicalItemClass");
        Page<Map<String, String>> page = medicalService.getMedicalExamList(oid,visitId, medicalReportNo, medicalItemClass, "EXECUTE_TIME", "desc", pageNo, pageSize,patientId);
        return page;
    }

    /**
     * 体检视图检查报告明细
     */
    @ApiOperation(value = "体检视图检查报告明细", notes = "体检视图检查报告明细", httpMethod = "POST")
    @RequestMapping(value = "/getMedicalExamDetail", method = RequestMethod.POST)
    public Map<String, Object> getMedicalExamDetail(String oid, String visitId,String medicalReportNo, String applyNo,String patientId) {
//        String medicalReportNo = getParameter("medicalReportNo");
//        String applyNo = getParameter("applyNo");
        Map<String, Object> map = medicalService.getMedicalExamDetail(oid, visitId,medicalReportNo, applyNo,patientId);
        return map;
    }

    /**
     * 获取医院信息
     */
    @ApiOperation(value = "获取医院信息", notes = "获取医院信息", httpMethod = "POST")
    @RequestMapping(value = "/getMedicalHospialInfo", method = RequestMethod.POST)
    public Map<String, String> getMedicalHospialInfo(String oid) {
        Map<String, String> map = medicalService.getMedicalHospialInfo(oid);
        return map;
    }

    /**
     * 统计检查和检验数量
     */
    @ApiOperation(value = "统计检查和检验数量", notes = "统计检查和检验数量", httpMethod = "POST")
    @RequestMapping(value = "/getMedicalReportNum", method = RequestMethod.POST)
    public Map<String, String> getMedicalReportNum(String oid,String visitId, String medicalReportNo, String lab_class, String exam_class,String patientId) {
//        String medicalReportNo = getParameter("medicalReportNo");
//        String labClass = getParameter("lab_class");
//        String examClass = getParameter("exam_class");
        Map<String, String> map = medicalService.getReportNum(oid, visitId,medicalReportNo, lab_class, exam_class,patientId);
        return map;
    }

    /**
     * 体检趋势图
     */
    @ApiOperation(value = "体检趋势图", notes = "体检趋势图", httpMethod = "POST")
    @RequestMapping(value = "/getMedicalTrendData", method = RequestMethod.POST)
    public void getMedicalTrendData(String oid, String patientId, String dateType, String startDate, String endDate, String medicalItemCode) {
//        String numIndex = getParameter("dateType");
//        String beginTime = getParameter("startDate");
//        String endTime = getParameter("endDate");
//        String medicalItemCode = getParameter("medicalItemCode");
        medicalService.getReportTrendData(oid, patientId, medicalItemCode, Integer.parseInt(dateType), startDate, endDate);
    }
}
