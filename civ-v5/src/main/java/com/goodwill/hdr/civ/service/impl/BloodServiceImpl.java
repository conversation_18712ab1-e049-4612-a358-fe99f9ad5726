package com.goodwill.hdr.civ.service.impl;


import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.goodwill.hdr.civ.config.Config;
import com.goodwill.hdr.civ.config.ConfigCache;
import com.goodwill.hdr.civ.enums.HdrTableEnum;
import com.goodwill.hdr.civ.service.BloodService;
import com.goodwill.hdr.core.orm.MatchType;
import com.goodwill.hdr.core.orm.Page;
import com.goodwill.hdr.hbase.bo.PropertyFilter;
import com.goodwill.hdr.hbase.dto.responseVo.PageResultVo;
import com.goodwill.hdr.hbase.dto.responseVo.ResultVo;
import com.goodwill.hdr.hbaseQueryClient.builder.PageRequestBuilder;
import com.goodwill.hdr.hbaseQueryClient.service.HbaseQueryClient;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


@Service
public class BloodServiceImpl implements BloodService {

    private static final Logger log = LoggerFactory.getLogger(BloodServiceImpl.class);
    private final HbaseQueryClient hbaseQueryClient;
    private final ObjectMapper objectMapper;

    public BloodServiceImpl(HbaseQueryClient hbaseQueryClient, ObjectMapper objectMapper) {
        this.hbaseQueryClient = hbaseQueryClient;
        this.objectMapper = objectMapper;
    }

    @Override
    public Page<Map<String, String>> getPageBloodApplyView(
            Map<String, Object> queryMap, String orderBy, String orderDir,
            int pageNo, int pageSize) {
        String visitType = (String) queryMap.get("visit_type");
        String visitOrder = (String) queryMap.get("visit_order");
        String patientId = (String) queryMap.get("patient_id");
        String oid = (String) queryMap.get("oid");
        if (StringUtils.isEmpty(orderBy)) {
            orderBy = "APPLY_DATE";
        }
        if (StringUtils.isEmpty(orderDir)) {
            orderDir = "asc";
        }
        if (pageNo == 0) {
            pageNo = 1;
        }
        if (pageSize == 0) {
            pageSize = 10;
        }
        // 按条件查询
        List<PropertyFilter> filters = new ArrayList<PropertyFilter>();
        PropertyFilter filter1 = new PropertyFilter();
        filter1.setMatchType(MatchType.EQ.getOperation());
        filter1.setPropertyName("VISIT_TYPE_CODE");
        filter1.setPropertyValue("02");
        filters.add(filter1);
        //List<PropertyFilter> filters = PageRequestBuilder.FilterBuilder.init().build();
        String bloodApplyFilter = Config.getBloodApplyFilter(oid);
        strToFilter(bloodApplyFilter, filters, ";");
//        Page<Map<String, String>> page = new Page<Map<String, String>>();
//        page.setOrderBy(orderBy);
//        page.setOrderDir(orderDir);
//        page.setPageNo(pageNo);
//        page.setPageSize(pageSize);
        ResultVo<PageResultVo<Map<String, String>>> resultVo = hbaseQueryClient.getPageByCondition(
                PageRequestBuilder.init()
                        .tableName(HdrTableEnum.HDR_BLOOD_APPLY.getCode())
                        .patientId(patientId)
                        .oid(oid)
                        .visitId(visitOrder)
                        .visitTypeCode(visitType)
                        .filters(filters)
                        .pageNo(pageNo)
                        .pageSize(pageSize)
                        .orderBy(orderBy)
                        .asc()
                        .column()
                        .build());
        Page<Map<String, String>> resultPage = new Page<>();
        if (resultVo.isSuccess()) {
            resultPage.setResult(resultVo.getContent().getResult());
            resultPage.setTotalCount(resultVo.getContent().getTotal());
        }
//        Page<Map<String, String>> resultPage = hbaseDao
//                .findPageConditionByPatient(
//                        HdrTableEnum.HDR_BLOOD_APPLY.getCode(), oid, patientId,
//                        page, filters);
        return resultPage;
    }

    @Override
    public Page<Map<String, String>> getPageBloodMatchView(
            Map<String, Object> queryMap, String orderBy, String orderDir,
            int pageNo, int pageSize) {
        String visitType = (String) queryMap.get("visit_type");
        String visitOrder = (String) queryMap.get("visit_order");
        String patientId = (String) queryMap.get("patient_id");
        String oid = (String) queryMap.get("oid");
        if (StringUtils.isEmpty(orderBy)) {
            orderBy = "PERPARE_BLOOD_TIME";
        }
        if (StringUtils.isEmpty(orderDir)) {
            orderDir = "asc";
        }
        if (pageNo == 0) {
            pageNo = 1;
        }
        if (pageSize == 0) {
            pageSize = 10;
        }
        // 按条件查询
        List<PropertyFilter> filters = new ArrayList<PropertyFilter>();
        PropertyFilter filter1 = new PropertyFilter();
        filter1.setMatchType(MatchType.EQ.getOperation());
        filter1.setPropertyName("VISIT_TYPE_CODE");
        filter1.setPropertyValue("02");
        filters.add(filter1);
        //List<PropertyFilter> filters = PageRequestBuilder.FilterBuilder.init().build();
        ResultVo<PageResultVo<Map<String, String>>> resultVo = hbaseQueryClient.getPageByCondition(
                PageRequestBuilder.init()
                        .tableName(HdrTableEnum.HDR_BLOU_MATCH.getCode())
                        .patientId(patientId)
                        .oid(oid)
                        .visitId(visitOrder)
                        .visitTypeCode(visitType)
                        .filters(filters)
                        .pageNo(pageNo)
                        .pageSize(pageSize)
                        .orderBy(orderBy)
                        .asc()
                        .column()
                        .build());
        Page<Map<String, String>> resultPage = new Page<>();
        if (resultVo.isSuccess()) {
            resultPage.setResult(resultVo.getContent().getResult());
            resultPage.setTotalCount(resultVo.getContent().getTotal());
        }
//        Page<Map<String, String>> resultPage = hbaseDao
//                .findPageConditionByPatient(
//                        HdrTableEnum.HDR_BLOU_MATCH.getCode(), oid, patientId, page,
//                        filters);
        return resultPage;
    }

    @Override
    public Page<Map<String, String>> getPageBloodView(
            Map<String, Object> queryMap, String orderBy, String orderDir,
            int pageNo, int pageSize) {
        String visitType = (String) queryMap.get("visit_type");
        String visitOrder = (String) queryMap.get("visit_order");
        String patientId = (String) queryMap.get("patient_id");
        String oid = (String) queryMap.get("oid");
        if (StringUtils.isEmpty(orderBy)) {
            orderBy = "PLAN_TRANSFUSION_TIME";
        }
        if (StringUtils.isEmpty(orderDir)) {
            orderDir = "asc";
        }
        if (pageNo == 0) {
            pageNo = 1;
        }
        if (pageSize == 0) {
            pageSize = 10;
        }
        // 按条件查询
        List<PropertyFilter> filters = new ArrayList<PropertyFilter>();
        PropertyFilter filter1 = new PropertyFilter();
        filter1.setMatchType(MatchType.EQ.getOperation());
        filter1.setPropertyName("VISIT_TYPE_CODE");
        filter1.setPropertyValue("02");
        filters.add(filter1);
        Page<Map<String, String>> page = new Page<Map<String, String>>();
        page.setOrderBy(orderBy);
        page.setOrderDir(orderDir);
        page.setPageNo(pageNo);
        page.setPageSize(pageSize);
//        Page<Map<String, String>> resultPage = hbaseDao
//                .findPageConditionByPatient(HdrTableEnum.HDR_BLOU.getCode(), oid,
//                        patientId, page, filters);
        // List<PropertyFilter> filters = PageRequestBuilder.FilterBuilder.init().build();
        ResultVo<PageResultVo<Map<String, String>>> resultVo = hbaseQueryClient.getPageByCondition(
                PageRequestBuilder.init()
                        .tableName(HdrTableEnum.HDR_BLOU.getCode())
                        .patientId(patientId)
                        .oid(oid)
                        .visitId(visitOrder)
                        .visitTypeCode(visitType)
                        .filters(filters)
                        .pageNo(pageNo)
                        .pageSize(pageSize)
                        .orderBy(orderBy)
                        .asc()
                        .column()
                        .build());
        Page<Map<String, String>> resultPage = new Page<>();
        if (resultVo.isSuccess()) {
            resultPage.setResult(resultVo.getContent().getResult());
            resultPage.setTotalCount(resultVo.getContent().getTotal());
        }
        return resultPage;
    }

    /**
     * 字符查询传转换为filter查询对象
     *
     * @param filterString 查询字符串 column|in|080,079,004,035,088,134
     * @param filters
     * @param strSplit     分隔每个查询条件的分隔符
     */
    private static void strToFilter(String filterString, List<PropertyFilter> filters, String strSplit) {
        //将配置字符串转换为查询
        if (StringUtils.isNotBlank(filterString)) {
            String[] filterStrings = filterString.split(strSplit);
            for (String filterItemString : filterStrings) {
                String[] tempString = filterItemString.split("\\|");
                createPropertyFilter(tempString[0], tempString[2], tempString[1], filters);
            }
        }
    }

    /**
     * 创建Filter
     *
     * @param columnName
     * @param keyword
     * @param filters
     */
    public static void createPropertyFilter(String columnName, String keyword, String MatchType,
                                            List<PropertyFilter> filters) {
        if (StringUtils.isNotBlank(keyword)) {
            PropertyFilter filter1 = new PropertyFilter();
            filter1.setMatchType(MatchType);
            filter1.setPropertyName(columnName);
            filter1.setPropertyValue(keyword);
            //filter1.setPropertyType("STRING");
            filters.add(filter1);
        }
    }

    @Override
    public Page<Map<String, String>> getBloodApplyByVisit(String oid, String patientId, String visitId, String visitType, int pageNo, int pageSize) {
        // 按条件查询
        List<PropertyFilter> filters = new ArrayList<PropertyFilter>();
//        if (StringUtils.isNotBlank(visitId)) {
//            filters.add(new PropertyFilter("VISIT_ID", MatchType.EQ.getOperation(), visitId));
//        }
        if (StringUtils.isNotBlank(visitType) && visitType.equals("INPV")) {
            visitType = "02";
            filters.add(new PropertyFilter("VISIT_TYPE_CODE", MatchType.EQ.getOperation(), "02"));
        } else if (StringUtils.isNotBlank(visitType) && visitType.equals("OUTPV")) {
            visitType = "01";
            filters.add(new PropertyFilter("VISIT_TYPE_CODE", MatchType.EQ.getOperation(), "01"));
        }
        Page<Map<String, String>> page = new Page<Map<String, String>>();
        page.setOrderBy("APPLY_DATE");
        page.setOrderDir("desc");
        page.setPageNo(pageNo);
        page.setPageSize(pageSize);
//        Page<Map<String, String>> resultPage = hbaseDao
//                .findPageConditionByPatient(HdrTableEnum.HDR_BLOOD_APPLY.getCode(), oid,
//                        patientId, page, filters);
        // List<PropertyFilter> filters = PageRequestBuilder.FilterBuilder.init().build();
        ResultVo<PageResultVo<Map<String, String>>> resultVo = hbaseQueryClient.getPageByCondition(
                PageRequestBuilder.init()
                        .tableName(HdrTableEnum.HDR_BLOOD_APPLY.getCode())
                        .patientId(patientId)
                        .oid(oid)
                        .visitId(visitId)
                        .visitTypeCode(visitType)
                        .filters(filters)
                        .pageNo(pageNo)
                        .pageSize(pageSize)
                        .orderBy("APPLY_DATE")
                        .desc()
                        .column()
                        .build());
        Page<Map<String, String>> resultPage = new Page<>();
        if (resultVo.isSuccess()) {
            resultPage.setResult(resultVo.getContent().getResult());
            resultPage.setTotalCount(resultVo.getContent().getTotal());
        }
        return resultPage;
    }


    @Override
    public List<Map<String, String>> getBloodApplyNode(String oid, String patientId, String visitId, String visitType, String applyNo) {
        List<Map<String, String>> resultList = new ArrayList<>();
        Map<String, String> map = new HashMap<>();
        // 按条件查询
        List<PropertyFilter> filters = new ArrayList<PropertyFilter>();
//        if (StringUtils.isNotBlank(visitId)) {
//            filters.add(new PropertyFilter("VISIT_ID", "STRING", MatchType.EQ.getOperation(), visitId));
//        }
        if (StringUtils.isNotBlank(visitType) && visitType.equals("INPV")) {
            visitType = "02";
            filters.add(new PropertyFilter("VISIT_TYPE_CODE", MatchType.EQ.getOperation(), "02"));
        } else if (StringUtils.isNotBlank(visitType) && visitType.equals("OUTPV")) {
            visitType = "01";
            filters.add(new PropertyFilter("VISIT_TYPE_CODE", MatchType.EQ.getOperation(), "01"));
        }
        filters.add(new PropertyFilter("APPLY_NO", MatchType.EQ.getOperation(), applyNo));
        Page<Map<String, String>> page = new Page<Map<String, String>>();
        ResultVo<PageResultVo<Map<String, String>>> resultVo = hbaseQueryClient.getPageByCondition(
                PageRequestBuilder.init()
                        .tableName(HdrTableEnum.HDR_BLOOD_APPLY.getCode())
                        .patientId(patientId)
                        .oid(oid)
                        .visitId(visitId)
                        .visitTypeCode(visitType)
                        .filters(filters)
                        .pageNo(0)
                        .pageSize(0)
                        .orderBy("")
                        .desc()
                        .column()
                        .build());
        Page<Map<String, String>> bloodApply = new Page<>();
        if (resultVo.isSuccess()) {
            bloodApply.setResult(resultVo.getContent().getResult());
            bloodApply.setTotalCount(resultVo.getContent().getTotal());
        }
        //Page<Map<String, String>> bloodApply = hbaseDao.findPageConditionByPatient(HdrTableEnum.HDR_BLOOD_APPLY.getCode(), oid, patientId, page, filters);
        map.put("nodeName", "申请");
        map.put("nodeNo", "1");
        map.put("applyDate", "");
        map.put("nodeStatus", "false");
        if (bloodApply.getResult().size() > 0) {
            map.put("applyDate", bloodApply.getResult().get(0).get("LAST_MODIFY_DATE_TIME"));
            map.put("nodeStatus", "true");
        }
        resultList.add(map);
        ResultVo<PageResultVo<Map<String, String>>> prepareBloodResultVo = hbaseQueryClient.getPageByCondition(
                PageRequestBuilder.init()
                        .tableName(HdrTableEnum.HDR_PREPARE_BLOOD.getCode())
                        .patientId(patientId)
                        .oid(oid)
                        .visitId(visitId)
                        .visitTypeCode(visitType)
                        .filters(filters)
                        .pageNo(0)
                        .pageSize(0)
                        .orderBy("")
                        .desc()
                        .column()
                        .build());
        Page<Map<String, String>> prepareBlood = new Page<>();
        if (prepareBloodResultVo.isSuccess()) {
            prepareBlood.setResult(prepareBloodResultVo.getContent().getResult());
            prepareBlood.setTotalCount(prepareBloodResultVo.getContent().getTotal());
        }
        //Page<Map<String, String>> prepareBlood = hbaseDao.findPageConditionByPatient(HdrTableEnum.HDR_PREPARE_BLOOD.getCode(), oid, patientId, page, filters);
        map = new HashMap<>();
        map.put("nodeName", "配发");
        map.put("nodeNo", "2");
        map.put("applyDate", "");
        map.put("nodeStatus", "false");
        if (prepareBlood.getResult().size() > 0) {
            map.put("applyDate", prepareBlood.getResult().get(0).get("PREPARE_BLOOD_TIME"));
            map.put("nodeStatus", "true");
        }
        resultList.add(map);
        ResultVo<PageResultVo<Map<String, String>>> blouResultVo = hbaseQueryClient.getPageByCondition(
                PageRequestBuilder.init()
                        .tableName(HdrTableEnum.HDR_BLOU.getCode())
                        .patientId(patientId)
                        .oid(oid)
                        .visitId(visitId)
                        .visitTypeCode(visitType)
                        .filters(filters)
                        .pageNo(0)
                        .pageSize(0)
                        .orderBy("")
                        .desc()
                        .column()
                        .build());
        Page<Map<String, String>> blou = new Page<>();
        if (blouResultVo.isSuccess()) {
            blou.setResult(blouResultVo.getContent().getResult());
            blou.setTotalCount(blouResultVo.getContent().getTotal());
        }
        //Page<Map<String, String>> blou = hbaseDao.findPageConditionByPatient(HdrTableEnum.HDR_BLOU.getCode(), oid, patientId, page, filters);
        map = new HashMap<>();
        map.put("nodeName", "执行");
        map.put("nodeNo", "3");
        map.put("applyDate", "");
        map.put("nodeStatus", "false");
        if (blou.getResult().size() > 0) {
            map.put("applyDate", blou.getResult().get(0).get("BLOOD_TRANSFUSION_TIME"));
            map.put("nodeStatus", "true");
        }
        resultList.add(map);
        ResultVo<PageResultVo<Map<String, String>>> blouPatrolResultVo = hbaseQueryClient.getPageByCondition(
                PageRequestBuilder.init()
                        .tableName(HdrTableEnum.HDR_BLOU_PATROL.getCode())
                        .patientId(patientId)
                        .oid(oid)
                        .visitId(visitId)
                        .visitTypeCode(visitType)
                        .filters(filters)
                        .pageNo(0)
                        .pageSize(0)
                        .orderBy("")
                        .desc()
                        .column()
                        .build());
        Page<Map<String, String>> blouPatrol = new Page<>();
        if (blouPatrolResultVo.isSuccess()) {
            blouPatrol.setResult(blouPatrolResultVo.getContent().getResult());
            blouPatrol.setTotalCount(blouPatrolResultVo.getContent().getTotal());
        }
        //Page<Map<String, String>> blouPatrol = hbaseDao.findPageConditionByPatient(HdrTableEnum.HDR_BLOU_PATROL.getCode(), oid, patientId, page, filters);
        map = new HashMap<>();
        map.put("nodeName", "完成");
        map.put("nodeNo", "4");
        map.put("applyDate", "");
        map.put("nodeStatus", "false");
        if (blouPatrol.getResult().size() > 0) {
            map.put("applyDate", blouPatrol.getResult().get(0).get("BLOOD_PATROL_TIME"));
            map.put("nodeStatus", "true");
        }
        resultList.add(map);
        return resultList;
    }

    @Override
    public Page<Map<String, String>> getBloodApplyNodeList(String oid, String patientId, String visitId, String visitType, String applyNo, String nameNode) {
        String bloodHead = "BLOOD_HEAD_CONFIG";
        String tableName = HdrTableEnum.HDR_BLOOD_APPLY.getCode();
        switch (nameNode) {
            case "申请":
                tableName = HdrTableEnum.HDR_BLOOD_APPLY.getCode();
                bloodHead = "BLOOD_HEAD_CONFIG";
                break;
            case "配发":
                tableName = HdrTableEnum.HDR_PREPARE_BLOOD.getCode();
                bloodHead = "BLOOD_PREPARE_HEAD_CONFIG";
                break;
            case "执行":
                tableName = HdrTableEnum.HDR_BLOU.getCode();
                bloodHead = "BLOOD_BLOU_HEAD_CONFIG";
                break;
            case "完成":
                tableName = HdrTableEnum.HDR_BLOU_PATROL.getCode();
                bloodHead = "BLOOD_BLOU_PATROL_HEAD_CONFIG";
                break;
        }
        String bloodHeadConfig = ConfigCache.getCache(oid, bloodHead);
        // 按条件查询
        List<PropertyFilter> filters = new ArrayList<PropertyFilter>();
//        if (StringUtils.isNotBlank(visitId)) {
//            filters.add(new PropertyFilter("VISIT_ID", "STRING", MatchType.EQ.getOperation(), visitId));
//        }
        if (StringUtils.isNotBlank(visitType) && visitType.equals("INPV")) {
            visitType = "02";
            filters.add(new PropertyFilter("VISIT_TYPE_CODE", MatchType.EQ.getOperation(), "02"));
        } else if (StringUtils.isNotBlank(visitType) && visitType.equals("OUTPV")) {
            visitType = "01";
            filters.add(new PropertyFilter("VISIT_TYPE_CODE", MatchType.EQ.getOperation(), "01"));
        }
        if (StringUtils.isNotBlank(applyNo)) {
            filters.add(new PropertyFilter("APPLY_NO", MatchType.EQ.getOperation(), applyNo));
        }
        Page<Map<String, String>> page = new Page<Map<String, String>>();
        page.setPageNo(1);
        page.setPageSize(1000000);
        ResultVo<PageResultVo<Map<String, String>>> resultVo = hbaseQueryClient.getPageByCondition(
                PageRequestBuilder.init()
                        .tableName(tableName)
                        .patientId(patientId)
                        .oid(oid)
                        .visitId(visitId)
                        .visitTypeCode(visitType)
                        .filters(filters)
                        .pageNo(0)
                        .pageSize(0)
                        .orderBy("")
                        .desc()
                        .column()
                        .build());
        Page<Map<String, String>> resultPage = new Page<>();
        if (resultVo.isSuccess()) {
            resultPage.setResult(resultVo.getContent().getResult());
            resultPage.setTotalCount(resultVo.getContent().getTotal());
        }
        List<Map<String, String>> tableHead = new ArrayList<>();
        try {
            tableHead = objectMapper.readValue(bloodHeadConfig, new TypeReference<List<Map<String, String>>>() {
            });
        } catch (JsonProcessingException e) {
            log.error("解析表头失败");
        }
        List<Map<String, String>> result = new ArrayList<Map<String, String>>();
        for (Map<String, String> map : resultPage.getResult()) {
            Map<String, String> mapTemp = new HashMap<String, String>();
            for (Map<String, String> bloodConfig: tableHead) {
                if (!"true".equals(bloodConfig.get("hidden"))) {
                    if (StringUtils.isNotBlank(bloodConfig.get("name"))) {
                        mapTemp.put(bloodConfig.get("name"), map.get(bloodConfig.get("column")));
                    }
                }
            }
            result.add(mapTemp);
        }
        resultPage.setResult(result);
        return resultPage;
    }

}
