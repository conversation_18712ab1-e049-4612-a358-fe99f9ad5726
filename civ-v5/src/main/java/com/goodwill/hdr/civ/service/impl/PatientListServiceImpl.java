package com.goodwill.hdr.civ.service.impl;


import cn.hutool.core.util.ArrayUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;

import com.goodwill.hdr.civ.config.Config;
import com.goodwill.hdr.civ.config.ConfigCache;
import com.goodwill.hdr.civ.config.DataCache;
import com.goodwill.hdr.civ.entity.PatienCollect;
import com.goodwill.hdr.civ.enums.HdrConstantEnum;
import com.goodwill.hdr.civ.mapper.PatienCollectMapper;
import com.goodwill.hdr.civ.mapper.SecurityDeptMapper;
import com.goodwill.hdr.civ.mapper.SecurityDeptUserMapper;
import com.goodwill.hdr.civ.mapper.SecurityUserMapper;
import com.goodwill.hdr.civ.service.ConfigService;
import com.goodwill.hdr.civ.service.PatientListService;
import com.goodwill.hdr.civ.service.PowerService;
import com.goodwill.hdr.civ.utils.DateUtil;
import com.goodwill.hdr.civ.vo.NameAndCodeVo;
import com.goodwill.hdr.civ.vo.SolrVo;
import com.goodwill.hdr.civ.vo.TreeNodeVo;
import com.goodwill.hdr.core.orm.Page;
import com.goodwill.hdr.rest.client.enums.JhdcpServerCode;
import com.goodwill.hdr.rest.client.transmission.JhdcpHttpSender;
import com.goodwill.hdr.rest.client.vo.server.JhdcpCondition;
import com.goodwill.hdr.rest.client.vo.server.JhdcpPageRequestVo;
import com.goodwill.hdr.rest.client.wrapper.JhdcpQueryWrapper;
import com.goodwill.hdr.rest.client.wrapper.imp.JhdcpQueryWrapperImp;
import com.goodwill.hdr.security.priority.entity.DeptUserEntity;
import com.goodwill.hdr.security.priority.entity.OrgCommonInformation;
import com.goodwill.hdr.security.priority.entity.SecurityCommonDept;
import com.goodwill.hdr.security.priority.entity.UserEntity;
import com.goodwill.hdr.security.priority.mapper.OrgCommonInformationMapper;
import com.goodwill.hdr.security.utils.SecurityCommonUtil;
import com.goodwill.hdr.web.core.modal.HdrSimplePage;
import org.apache.commons.lang.StringUtils;
import org.apache.http.client.utils.DateUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.Period;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class PatientListServiceImpl implements PatientListService {


    private static Logger log = LoggerFactory.getLogger(PatientListServiceImpl.class);
    //	@Autowired

    @Autowired
    private PowerService powerService;


    @Autowired
    private SecurityDeptUserMapper securityDeptUserMapper;

    @Autowired
    private SecurityDeptMapper securityDeptMapper;

    @Autowired
    private SecurityUserMapper securityUserMapper;
    @Autowired
    private OrgCommonInformationMapper orgCommonInformationMapper;
    @Autowired
    private ObjectMapper objectMapper;

    @Autowired
    JhdcpHttpSender jhdcpHttpSender;
    @Autowired
    private ConfigService configService;
    @Autowired
    private PatienCollectMapper patienCollectMapper;


    private static final Map<String, String> visitFieldMapping = new HashMap<>();


    static {
        visitFieldMapping.put("HIS_PAT_ID", "PATIENT_ID");
        visitFieldMapping.put("SD_VISIT_TYPE_CODE", "VISIT_TYPE_CODE");
        visitFieldMapping.put("SD_VISIT_TYPE_NAME", "VISIT_TYPE_NAME");
        visitFieldMapping.put("VISIT_NO", "VISIT_NO");
        visitFieldMapping.put("VISIT_NUM", "VISIT_ID");
        visitFieldMapping.put("PATIENT_NAME", "PERSON_NAME");
        visitFieldMapping.put("SD_SEX_NAME", "SEX_NAME");
        visitFieldMapping.put("SD_SEX_CODE", "SEX_CODE");
        visitFieldMapping.put("DATE_OF_BIRTH", "DATE_OF_BIRTH");
        visitFieldMapping.put("ID_CARD_NO", "ID_CARD_NO");
        visitFieldMapping.put("ST_VISIT_DEPT", "VISIT_DEPT_NAME");
        visitFieldMapping.put("SC_VISIT_DEPT", "VISIT_DEPT_CODE");
        visitFieldMapping.put("VISIT_TIME", "VISIT_TIME");
        visitFieldMapping.put("DISCHARGE_TIME", "DISCHARGE_TIME");
        visitFieldMapping.put("DIAG_CODE", "DIAGNOSIS_CODE");
        visitFieldMapping.put("DIAG_NAME", "DIAGNOSIS_NAME");
        visitFieldMapping.put("ORG_CODE", "OID");
        visitFieldMapping.put("SD_VISIT_STATE_NAME", "VISIT_STATUS_NAME");
        visitFieldMapping.put("SD_VISIT_STATE_CODE", "VISIT_STATUS_CODE");
        visitFieldMapping.put("ORG_NAME", "ORG_NAME");
        visitFieldMapping.put("EID", "EID");
        visitFieldMapping.put("IN_DAYS", "IN_DAYS");
        visitFieldMapping.put("INP_NO", "VISIT_NO");
        visitFieldMapping.put("OUTP_NO", "VISIT_NO");
        visitFieldMapping.put("ADMISSION_TIME", "ADMISSION_TIME");
        visitFieldMapping.put("ST_ADM_DEPT", "VIST_DEPT_NAME");
        visitFieldMapping.put("ST_OUT_DEPT", "VIST_DEPT_NAME");
        visitFieldMapping.put("SC_DIS_WARD", "DIS_WARD_CODE");
        visitFieldMapping.put("ST_DIS_WARD", "DIS_WARD_NAME");
        visitFieldMapping.put("SC_ADM_WARD", "ADM_WARD_CODE");
        visitFieldMapping.put("ST_ADM_WARD", "ADM_WARD_NAME");
    }




    //查询中台solr
    @Override
    public Page<Map<String, String>> getPatientList_ListByJHDCP(String oid, String patientId, String visitNo, String patientName, String card_Id, String visit_Type, String visit_Dept, String visitBTime, String visitETime, String dischargeBTime, String dischargeETime, String birthdayBTime, String birthdayETime, String district, String visitStatus, int pageNo, int pageSize, String inpvDays, String inpvDaysType, String hospital_second,String diagName,String isCollect,String eid) {
        // 临时解决方案：使用硬编码的服务代码，避免 ALL_VISIT 字段不存在的问题
        String serverCode = "JHIDS-APS-VISIT-005"; // ALL_VISIT 对应的服务代码
        JhdcpQueryWrapperImp queryWrapperAllVisit = new JhdcpQueryWrapperImp(serverCode, pageNo, pageSize);

        Page<Map<String, String>> page = new Page<Map<String, String>>();




        //医院oid
        if (StringUtils.isNotBlank(oid)) {
            queryWrapperAllVisit.in("ORG_CODE", oid);

        }
        String userName = SecurityCommonUtil.getLoginUserCode();

        /*List<String> deptCodes = new ArrayList<String>();*/

        boolean deptQuery = true;

        //获取当前用户授权科室
       /* QueryParamVO queryParamVO = new QueryParamVO();
        String userCode = SecurityCommonUtil.getCurrentLoginUser().getUsercode();
        queryParamVO.setUserCode(userCode);
        List<SecurityDeptUserVO> securityDeptUserList = securityDeptUserMapper.queryAuthronizedDeptList(queryParamVO);
        for (SecurityDeptUserVO securityDeptUserVO : securityDeptUserList) {
            deptCodes.add(securityDeptUserVO.getDeptcode());
        }*/

        String userCode = SecurityCommonUtil.getCurrentLoginUser().getUsercode();
        List<String> pkDeptList = securityDeptUserMapper.selectpkDeptByUserCode(userCode);
        List<Map<String, String>> deptCNList = this.securityDeptUserMapper.selectDeptByOid(oid,"'"+String.join("','",pkDeptList)+"'");
        List<String> deptCodes = deptCNList.stream().map(array -> array.get("deptCode").trim()).collect(Collectors.toList());

        if (ArrayUtil.containsIgnoreCase(deptCodes.toArray(new String[0]), "all")) {
            deptCodes.clear();
            deptQuery = false;
        }
        //vip权限控制
        //=================================

        List<TreeNodeVo> oids = configService.getHospitalOid();
        if (StringUtils.isBlank(oid) ) {
            oid = oids.get(0).getChildren().get(0).getCode();
        }

        //===================================
        //患者唯一标识
        if (StringUtils.isNotBlank(patientId)) {
            if(HdrConstantEnum.HOSPITAL_SJT.getCode().equals(oid) && patientId.length()<10){
                patientId = String.format("%010d", Long.parseLong(patientId));
            }
            queryWrapperAllVisit.eq("HIS_PAT_ID", patientId);
        }
        //患者姓名
        if (StringUtils.isNotBlank(patientName)) {
            queryWrapperAllVisit.like("PATIENT_NAME", patientName);
        }
        //住院号 门诊号
        //住院号 门诊号
        if (StringUtils.isNotBlank(visitNo)) {
            queryWrapperAllVisit.eq("VISIT_NO", visitNo);
        }

        //身份证号
        if (StringUtils.isNotBlank(card_Id)) {
            queryWrapperAllVisit.like("ID_CARD_NO", card_Id);
        }
        if (StringUtils.isNotBlank(inpvDays) && StringUtils.isNotBlank(inpvDaysType)) {
            if ("EQ".equals(inpvDaysType)) {
                queryWrapperAllVisit.eq("IN_DAYS", inpvDays);
            } else if ("GT".equals(inpvDaysType)) {
                queryWrapperAllVisit.gt("IN_DAYS", inpvDays);
            } else if ("LT".equals(inpvDaysType)) {
                queryWrapperAllVisit.lt("IN_DAYS", inpvDays);
            }
        }
        //科室
        if (StringUtils.isNotBlank(visit_Dept) && !"01".equals(visit_Dept)) {
            if ("true".equals(Config.getIS_SHOW_PARENT_DEPT("ALL"))) {
                QueryWrapper<SecurityCommonDept> deptQueryWrapper = new QueryWrapper<>();
                deptQueryWrapper.eq("parent_dept_code", visit_Dept);
                deptQueryWrapper.orderByDesc("deptname");
                List<Map<String, Object>> deptUserEntities = securityDeptMapper.selectMaps(deptQueryWrapper);
                List<String> deptCodeList = new ArrayList<>();
                for (Map<String, Object> securityCommonDept : deptUserEntities) {
                    deptCodeList.add((String) securityCommonDept.get("deptcode"));
                }
                queryWrapperAllVisit.in("SC_VISIT_DEPT", deptCodeList.toArray(new String[deptCodeList.size()]));
            } else {
                queryWrapperAllVisit.eq("SC_VISIT_DEPT", visit_Dept);
            }
        } else if (deptQuery && (!Config.getCiv_Admin(oid).equals(userName)) && (!Config.getAllDeptdata(oid)) || StringUtils.isBlank(visit_Dept)) {
            if (deptQuery) {
                queryWrapperAllVisit.in("SC_VISIT_DEPT", deptCodes.toArray(new String[deptCodes.size()]));
            }
        }
        if (StringUtils.isNotBlank(visitBTime) && StringUtils.isNotBlank(visitETime)) {
            /*if(StringUtils.isNotBlank(visit_Type) && visit_Type.equals("02")){
                queryWrapperAllVisit.ge("ADMISSION_TIME", visitBTime + " 00:00:00");
                queryWrapperAllVisit.le("ADMISSION_TIME", visitETime + " 23:59:59");
            }else{
                queryWrapperAllVisit.ge("OUT_TIME", visitBTime + " 00:00:00");
                queryWrapperAllVisit.le("OUT_TIME", visitETime + " 23:59:59");
            }*/
            queryWrapperAllVisit.ge("VISIT_TIME", visitBTime + " 00:00:00");
            queryWrapperAllVisit.le("VISIT_TIME", visitETime + " 23:59:59");
        }
        if (StringUtils.isNotBlank(dischargeBTime) && StringUtils.isNotBlank(dischargeETime)) {
            queryWrapperAllVisit.ge("DISCHARGE_TIME", dischargeBTime + " 00:00:00");
            queryWrapperAllVisit.le("DISCHARGE_TIME", dischargeETime + " 23:59:59");
        }
        String flag = Config.getBirthday_switchConfig(oid);
        if ("true".equalsIgnoreCase(flag) && StringUtils.isNotBlank(birthdayBTime) && StringUtils.isNotBlank(birthdayETime)) {
            queryWrapperAllVisit.ge("DATE_OF_BIRTH", birthdayBTime);
            queryWrapperAllVisit.le("DATE_OF_BIRTH", birthdayETime);
        }
        //入院病区
        if (StringUtils.isNotBlank(district) && !"01".equals(district)) {
            queryWrapperAllVisit.eq("ST_ADM_WARD", district);
        }
        if (StringUtils.isNotBlank(visitStatus)) {
            queryWrapperAllVisit.eq("SD_VISIT_STATE_CODE", visitStatus);

        }
        //20241120 因北京同仁医院需求添加
        if (StringUtils.isNotBlank(hospital_second)) {
            queryWrapperAllVisit.in("YQMC", hospital_second);
        }
        //20241217 因京东方需求添加
        if (StringUtils.isNotBlank(diagName)) {
            queryWrapperAllVisit.like("DIAG_NAME", diagName);
        }

        //如果只有模糊查询条件时，需要添加时间条件限制，默认查询最近半年
        JhdcpPageRequestVo jhdcpPageRequestVo = queryWrapperAllVisit.build();
        List<JhdcpCondition> conditionList = jhdcpPageRequestVo.getCondition();
        //去除科室in条件的其他条件list
        List<JhdcpCondition> noDeptList = conditionList.stream()
                .filter(condition -> !("SC_VISIT_DEPT".equals(condition.getColumn()) && "in".equals(condition.getType())))
                .collect(Collectors.toList());
        int noDeptListSize = noDeptList != null ? noDeptList.size() : 0;
        //模糊查询list
        List<JhdcpCondition> likeList = conditionList.stream().filter(c -> c.getType().equals("like")).collect(Collectors.toList());
        int likeListSize = likeList != null ? likeList.size() : 0;

        if (noDeptListSize > 0 && likeListSize > 0 && noDeptListSize == likeListSize) {
            String beginTime = DateUtils.formatDate(org.apache.commons.lang3.time.DateUtils.addDays(new Date(), -180), "yyyy-MM-dd");
            String endTime = DateUtils.formatDate(new Date(), "yyyy-MM-dd");
            /*if (StringUtils.isNotBlank(visit_Type) && visit_Type.equals("02")) {
                queryWrapperAllVisit.ge("ADMISSION_TIME", beginTime + " 00:00:00");
                queryWrapperAllVisit.le("ADMISSION_TIME", endTime + " 23:59:59");
            } else {
                queryWrapperAllVisit.ge("OUT_TIME", beginTime + " 00:00:00");
                queryWrapperAllVisit.le("OUT_TIME", endTime + " 23:59:59");
            }*/
            queryWrapperAllVisit.ge("VISIT_TIME", beginTime + " 00:00:00");
            queryWrapperAllVisit.le("VISIT_TIME", endTime + " 23:59:59");
        }

        if(StringUtils.isNotBlank(eid)){
            queryWrapperAllVisit.in("EID", eid);
        }
        queryWrapperAllVisit.descOrder("VISIT_TIME");

        //查询中台solr
        String hospitalStateNames = Config.getHZLBFilter(oid);
        String dataPageJson = "";
        if (!visit_Type.equals("04") && StringUtils.isNotBlank(visit_Type)) {
            if (StringUtils.isNotBlank(hospitalStateNames)) {
                queryWrapperAllVisit.notIn("SD_VISIT_STATE_CODE", new String[]{hospitalStateNames});
                queryWrapperAllVisit.eq("SD_VISIT_TYPE_CODE", visit_Type);
                dataPageJson = this.jhdcpHttpSender.getDataPageJson((JhdcpQueryWrapper) queryWrapperAllVisit);
            } else {
                queryWrapperAllVisit.eq("SD_VISIT_TYPE_CODE", visit_Type);
                dataPageJson = this.jhdcpHttpSender.getDataPageJson((JhdcpQueryWrapper) queryWrapperAllVisit);
            }
        } else if (StringUtils.isNotBlank(hospitalStateNames)) {
            queryWrapperAllVisit.notIn("SD_VISIT_STATE_CODE", new String[]{hospitalStateNames});
            dataPageJson = this.jhdcpHttpSender.getDataPageJson((JhdcpQueryWrapper) queryWrapperAllVisit);
        } else {
            dataPageJson = this.jhdcpHttpSender.getDataPageJson((JhdcpQueryWrapper) queryWrapperAllVisit);
        }



        if (dataPageJson == null) {
            return new Page<Map<String, String>>();
        }
        SolrVo solrVo = JSON.parseObject(dataPageJson, SolrVo.class);
        List<Map<String, String>> solrData = solrVo.getData();
        if (solrData == null || solrData.size() == 0) {
            log.info("查询数据:未查询到中台数据");
            return new Page<Map<String, String>>();
        }
        log.info("查询数据:" + solrData.toString());

        List<Map<String, String>> docList = new ArrayList<>();
        for (Map<String, String> data : solrData) {
            Map<String, String> map = new HashMap<String, String>();
            convertMapKey(data, map);
            docList.add(map);
        }


        //20241226 添加患者收藏功能
        docListIsCollect(docList, userCode);
        if (StringUtils.isNotBlank(isCollect)) {
            docList = docList.stream().filter(map -> "1".equals(map.get("isCollect"))).collect(Collectors.toList());
        }
        //新增脱敏
        //List<Map<String, String>> infoHidden = powerService.getInfoHidden(docList);
        List<Map<String, String>> infoHidden = powerService.getInfoHiddenByMaskRule(docList);
        page.setPageNo(pageNo);
        page.setOrderBy("ADMISSION_TIME");
        page.setOrderDir("desc");
        page.setPageSize(pageSize);
        page.setCountTotal(true);
        page.setTotalCount(solrVo.getTotal());
        page.setResult(infoHidden);
        return page;
    }

    /**
     * 20241226 添加患者收藏功能
     * @param docList
     */
    public void docListIsCollect(List<Map<String, String>> docList,String userCode){
        QueryWrapper<PatienCollect> queryWrapper=new QueryWrapper<>();
        queryWrapper.eq("user_code",userCode);
        List<PatienCollect> collectPatientList=patienCollectMapper.selectList(queryWrapper);

        for(Map<String, String> map: docList){
            List<PatienCollect> patientList=collectPatientList.stream().filter(
                    patient->patient.getPatientId().equals(map.get("PATIENT_ID"))).collect(Collectors.toList());

            for(PatienCollect pc:patientList){
                if(map.get("PATIENT_ID").equals(pc.getPatientId()) && map.get("VISIT_ID").equals(pc.getVisitId()) &&
                        map.get("VISIT_TYPE_CODE").equals(pc.getVisitTypeCode())&& map.get("OID").equals(pc.getOid())){
                    map.put("isCollect",pc.getIsCollect());
                }
            }
        }
    }

    private static void convertMapKey(Map<String, String> data, Map<String, String> map) {

        DateTimeFormatter dateFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");


        for (String key : visitFieldMapping.keySet()) {
            if (StringUtils.isNotBlank(data.get(key))) {
                map.put(visitFieldMapping.get(key), data.get(key));
            }
        }

        String dateOfBirthString = data.get("DATE_OF_BIRTH");
        String visitTimeString = data.get("VISIT_TIME");
        if (StringUtils.isNotBlank(dateOfBirthString) && StringUtils.isNotBlank(visitTimeString)) {
            //处理年龄，返回年。

            LocalDate dateOfBirth = LocalDate.parse(dateOfBirthString, dateFormatter);
            LocalDate visitTime = LocalDate.parse(visitTimeString, dateTimeFormatter);
            Period period = Period.between(dateOfBirth, visitTime);
            String age = "";
            if (period.getYears() == 0) {
                if (period.getMonths() == 0) {
                    age = period.getDays() + "天";
                } else {
                    age = period.getMonths() + "月";
                }
            } else {
                age = period.getYears() + "岁";
            }
            map.put("AGE", age);

        } else {
            map.put("AGE", "-");
        }

    }



    @Override
    public Page<Map<String, String>> getAllFacetDataByJHDCP(String oid, String keyWord, String typeCode, String factCode, String factName, int pageNo, int pageSize) {
        Page<Map<String, String>> page = new Page<Map<String, String>>();
        List<Map<String, String>> list = new ArrayList<Map<String, String>>();
        List<Map<String, String>> result = new ArrayList<Map<String, String>>();
        //每10分钟重新获取下数据。即数据缓存10分钟。
        if (null != DataCache.getCache(oid, factCode) && DateUtil.getMinute(new Date()) % 10 != 0) {
            list = (List<Map<String, String>>) DataCache.getCache(oid, factCode);
        } else {
            if (pageNo == 1) {
                Map<String, String> alldept = new HashMap<String, String>();
                alldept.put("code", "01");
                alldept.put("name", "全部");
                result.add(alldept);
            }
            List<Map<String, String>> rslist = new ArrayList<Map<String, String>>();
            //查询中台
            try {
                JhdcpQueryWrapperImp queryWrapper = new JhdcpQueryWrapperImp(JhdcpServerCode.DEPT.getCode(), pageNo, 10000);
                queryWrapper.eq("ORG_CODE", oid);
                if (!keyWord.isEmpty()) {
                    queryWrapper.likeRight("DEPT_NAME", keyWord);
                }
                String dataPageJson = jhdcpHttpSender.getDataPageJson(queryWrapper);
                SolrVo solrVo = JSON.parseObject(dataPageJson, SolrVo.class);
                List<Map<String, String>> solrVoData = solrVo.getData();
                List<Map<String, String>> resultList = new ArrayList<>();
                //中台参数类型转换成老的类型
                for (Map<String, String> solrVoDatum : solrVoData) {
                    Map<String, String> map = new HashMap<>();
                    map.put(factCode, solrVoDatum.get("DEPT_CODE"));
                    map.put(factName, solrVoDatum.get("DEPT_NAME"));
                    rslist.add(map);
                }
//                for (Map<String, String> map : resultList) {
//                    map.put(factCode, map.get(factName));
//                    rslist.add(map);
//                }
            } catch (Exception e) {
                e.printStackTrace();
                return page;
            }
            //去掉科室名称与科室code为空的科室
            for (int i = 0; i < rslist.size(); i++) {
                if (StringUtils.isNotBlank(rslist.get(i).get(factCode)) && StringUtils.isNotBlank(rslist.get(i).get(factName))) {
                    list.add(rslist.get(i));
                }
            }
            if (Config.isAllDept(oid)) {
//                String userName = SecurityCommonUtil.getLoginUserCode();
//                StringBuilder sql = new StringBuilder();
//                String pkUser = securityUserMapper.selectOne(new QueryWrapper<SecurityUser>().eq("usercode", userName)).getPkUser();
//                List<String> stringList = securityDeptUserMapper.selectDeptByuser(pkUser);
//                for (String deptCode : stringList) {
//                    if (stringList.indexOf(deptCode) > 0)
//                        sql.append(",");
//                    sql.append("'").append(deptCode).append("'");
//                }
//                List<Map<String, String>> listdept = securityDeptMapper.selectDeptCodeNameByPkDept(sql.toString());
               /* QueryParamVO queryParamVO = new QueryParamVO();
                String userCode = SecurityCommonUtil.getCurrentLoginUser().getUsercode();
                queryParamVO.setUserCode(userCode);
                List<SecurityDeptUserVO> securityDeptUserList = securityDeptUserMapper.queryAuthronizedDeptList(queryParamVO);
                List<Map<String, String>> listdept = new ArrayList<>();
                for (SecurityDeptUserVO securityDeptUserVO : securityDeptUserList) {
                    Map<String, String> map = new HashMap<>();
                    map.put("deptName", securityDeptUserVO.getDeptname());
                    map.put("deptCode", securityDeptUserVO.getDeptcode());
                    listdept.add(map);
                }*/

                String userCode = SecurityCommonUtil.getCurrentLoginUser().getUsercode();
                List<String> pkDeptList = securityDeptUserMapper.selectpkDeptByUserCode(userCode);
                List<Map<String, String>> listdept = this.securityDeptUserMapper.selectDeptByOid(oid,"'"+String.join("','",pkDeptList)+"'");

                //取 solr统计的科室和当前登录用户的科室 的交集
                List<Map<String, String>> listTmp = new ArrayList<>();
                boolean flag = false;
                if (null != SecurityCommonUtil.getCurrentLoginUser() && !SecurityCommonUtil.getLoginUserCode().equals(Config.getCiv_Admin(oid))) {
                    for (Map<String, String> deptMap : list) {
                        //有全部科室的值则不再循环直接退出；
                        if (flag) {
                            break;
                        }
                        for (Map<String, String> userDeptMap : listdept) {
                            if (deptMap.get("DEPT_ADMISSION_TO_NAME").equals(userDeptMap.get("deptName"))) {
                                listTmp.add(deptMap);
                            } else if ("全部科室".equals(userDeptMap.get("deptName")) || "all".equals(userDeptMap.get("deptCode"))) {
                                //置空防止下面赋值时有重复数据产生
                                listTmp = null;
                                flag = true;
                                listTmp = list;
                                break;
                            }
                        }
                    }
                    list = listTmp;
                }
            }
            if (list.size() == 0) {
                return page;
            }
            DataCache.setCache(oid, "deptData", list);
        }
        //处理模糊查询的缓存问题
        if (StringUtils.isNotBlank(keyWord)) {
            List<Map<String, String>> listTemp = new ArrayList<Map<String, String>>();
            for (Map<String, String> map : list) {
                if (map.get(factName).contains(keyWord)) {
                    listTemp.add(map);
                }
            }
            list = listTemp;
        }
        if (list.size() > ((pageNo - 1) * pageSize)) {
            List<Map<String, String>> list1 = new ArrayList<Map<String, String>>();
            //左闭右开
            if (list.size() < pageNo * pageSize) {
                list1 = list.subList((pageNo - 1) * pageSize, list.size());
            } else if (list.size() >= pageNo * pageSize) {
                list1 = list.subList((pageNo - 1) * pageSize, pageNo * pageSize);
            }
            for (Map<String, String> map : list1) {
                if (!"0000000".equals(map.get(factCode))) {
                    Map<String, String> dept = new HashMap<String, String>();
                    dept.put("code", map.get(factCode));
                    dept.put("name", map.get(factName));
                    result.add(dept);
                }
            }
            page.setPageNo(pageNo);
            page.setOrderBy("code");
            page.setOrderDir("desc");
            page.setPageSize(pageSize);
            page.setCountTotal(true);
            page.setTotalCount(list.size());
            page.setResult(result);
        } else {
            pageNo = 1;
            List<Map<String, String>> list1 = new ArrayList<Map<String, String>>();
            //左闭右开
            if (list.size() < pageNo * pageSize) {
                list1 = list.subList((pageNo - 1) * pageSize, list.size());
            } else if (list.size() >= pageNo * pageSize) {
                list1 = list.subList((pageNo - 1) * pageSize, pageNo * pageSize);
            }
            for (Map<String, String> map : list1) {
                if (!"0000000".equals(map.get(factCode))) {
                    Map<String, String> dept = new HashMap<String, String>();
                    dept.put("code", map.get(factCode));
                    dept.put("name", map.get(factName));
                    result.add(dept);
                }
            }
            page.setPageNo(pageNo);
            page.setOrderBy("code");
            page.setOrderDir("desc");
            page.setPageSize(pageSize);
            page.setCountTotal(true);
            page.setTotalCount(list.size());
            page.setResult(result);
        }
        return page;
    }


    //	public Page<Map<String, String>> getAllDept(String oid, String keyWord, int pageNo, int pageSize) {
//		// TODO Auto-generated method stub
//		Page<Map<String, String>> page = new Page<Map<String, String>>();
//		List<Map<String, String>> list = new ArrayList<Map<String, String>>();
//
//		List<Map<String, String>> result = new ArrayList<Map<String, String>>();
//		if (pageNo == 1) {
//			Map<String, String> alldept = new HashMap<String, String>();
//			alldept.put("deptCode", "01");
//			alldept.put("deptName", "全部科室");
//			result.add(alldept);
//		}
//		List<PropertyFilter> filters = new ArrayList<PropertyFilter>();
//		if (StringUtils.isNotBlank(keyWord)) {
//			PropertyFilter filter1 = new PropertyFilter("DICT_ITEM_VALUE", "STRING", MatchType.LIKE.getOperation(),
//					keyWord);
//			filters.add(filter1);
//		}
//
//		List<Map<String, String>> rslist = new ArrayList<Map<String, String>>();
//		String rowkey = Config.getCIV_DICT_DEPT_ROWKEY(oid);
//		rslist = hbaseDao.findConditionByKey("HDR_DICT_ITEM", oid,rowkey, filters,
//				new String[] { "DICT_ITEM_CODE", "DICT_ITEM_VALUE" });
//		//去掉科室名称与科室code为空的科室
//		for (int i = 0; i < rslist.size(); i++) {
//			if (StringUtils.isNotBlank(rslist.get(i).get("DICT_ITEM_CODE"))
//					&& StringUtils.isNotBlank(rslist.get(i).get("DICT_ITEM_VALUE"))) {
//				list.add(rslist.get(i));
//			}
//		}
//		if (list.size() == 0) {
//			return page;
//		}
//
//		if (list.size() > ((pageNo - 1) * pageSize)) {
//			List<Map<String, String>> list1 = new ArrayList<Map<String, String>>();
//			//左闭右开
//			if (list.size() < pageNo * pageSize) {
//				list1 = list.subList((pageNo - 1) * pageSize, list.size());
//			} else if (list.size() >= pageNo * pageSize) {
//				list1 = list.subList((pageNo - 1) * pageSize, pageNo * pageSize);
//			}
//			for (Map<String, String> map : list1) {
//				if (!"0000000".equals(map.get("DICT_ITEM_CODE"))) {
//					Map<String, String> dept = new HashMap<String, String>();
//					dept.put("deptCode", map.get("DICT_ITEM_CODE"));
//					dept.put("deptName", map.get("DICT_ITEM_VALUE"));
//					result.add(dept);
//				}
//			}
//			page.setPageNo(pageNo);
//			page.setOrderBy("deptCode");
//			page.setOrderDir("desc");
//			page.setPageSize(pageSize);
//			page.setCountTotal(true);
//			page.setTotalCount(list.size());
//			page.setResult(result);
//		} else {
//			pageNo = 1;
//			List<Map<String, String>> list1 = new ArrayList<Map<String, String>>();
//			//左闭右开
//			if (list.size() < pageNo * pageSize) {
//				list1 = list.subList((pageNo - 1) * pageSize, list.size());
//			} else if (list.size() >= pageNo * pageSize) {
//				list1 = list.subList((pageNo - 1) * pageSize, pageNo * pageSize);
//			}
//			for (Map<String, String> map : list1) {
//				if (!"0000000".equals(map.get("DICT_ITEM_CODE"))) {
//					Map<String, String> dept = new HashMap<String, String>();
//					dept.put("deptCode", map.get("DICT_ITEM_CODE"));
//					dept.put("deptName", map.get("DICT_ITEM_VALUE"));
//					result.add(dept);
//				}
//			}
//			page.setPageNo(pageNo);
//			page.setOrderBy("deptCode");
//			page.setOrderDir("desc");
//			page.setPageSize(pageSize);
//			page.setCountTotal(true);
//			page.setTotalCount(list.size());
//			page.setResult(result);
//		}
//		return page;
//	}
//
//	@Override
//	public Page<Map<String, String>> getUserDeptAuth(String keyWord, int pageNo, int pageSize) {
//
//		Page<Map<String, String>> page = new Page<Map<String, String>>();
//
//		List<Map<String, String>> result = new ArrayList<Map<String, String>>();
//
//		List<DeptUserDetails> depts = new ArrayList<DeptUserDetails>();
//		List<DeptUserDetails> allDepts = SecurityUtils.getCurrentUserDeptAuth();
//
//		if (StringUtils.isNotBlank(keyWord)) {
//			for (DeptUserDetails dept : allDepts) {//test.indexOf("This")
//				if (dept.getDeptname().indexOf(keyWord) != -1) {
//					depts.add(dept);
//				}
//			}
//		} else {
//			depts = allDepts;
//		}
//
//		if (depts.size() == 0) {
//			return page;
//		}
//
//		Map<String, String> alldept = new HashMap<String, String>();
//		alldept.put("deptCode", "01");
//		alldept.put("deptName", "全部科室");
//		result.add(alldept);
//
//		if (depts.size() > ((pageNo - 1) * pageSize)) {
//			List<DeptUserDetails> list1 = new ArrayList<DeptUserDetails>();
//			//左闭右开
//			if (depts.size() < pageNo * pageSize) {
//				list1 = depts.subList((pageNo - 1) * pageSize, depts.size());
//			} else if (depts.size() >= pageNo * pageSize) {
//				list1 = depts.subList((pageNo - 1) * pageSize, pageNo * pageSize);
//			}
//			for (DeptUserDetails dept : list1) {
//				Map<String, String> map = new HashMap<String, String>();
//				map.put("deptCode", dept.getDeptcode());
//				map.put("deptName", dept.getDeptname());
//				result.add(map);
//			}
//			page.setPageNo(pageNo);
//			page.setOrderBy("deptCode");
//			page.setOrderDir("desc");
//			page.setPageSize(pageSize);
//			page.setCountTotal(true);
//			page.setTotalCount(depts.size());
//			page.setResult(result);
//		} else {
//			pageNo = 1;
//			List<DeptUserDetails> list1 = new ArrayList<DeptUserDetails>();
//			//左闭右开
//			if (depts.size() < pageNo * pageSize) {
//				list1 = depts.subList((pageNo - 1) * pageSize, depts.size());
//			} else if (depts.size() >= pageNo * pageSize) {
//				list1 = depts.subList((pageNo - 1) * pageSize, pageNo * pageSize);
//			}
//			for (DeptUserDetails dept : list1) {
//				Map<String, String> map = new HashMap<String, String>();
//				map.put("deptCode", dept.getDeptcode());
//				map.put("deptName", dept.getDeptname());
//				result.add(map);
//			}
//			page.setPageNo(pageNo);
//			page.setOrderBy("deptCode");
//			page.setOrderDir("desc");
//			page.setPageSize(pageSize);
//			page.setCountTotal(true);
//			page.setTotalCount(depts.size());
//			page.setResult(result);
//		}
//		return page;
//	}
//


    public Map<String, String> getUserAuthority(String oid, String deptCode) {
        Map<String, String> result = new HashMap<>();
        result.put("code", "200");
//		CustomUserDetails currentUser = SecurityUtils.getCurrentUser();
        UserEntity currentUser = SecurityCommonUtil.getCurrentLoginUser();
        if (null == currentUser || StringUtils.isBlank(currentUser.getUsername())) {
            result.put("code", "404");
            result.put("msg", "未登录用户！");
            return result;
        }
        //管理员登录
        if (currentUser.getUsername().equals(Config.getCiv_Admin(oid))) {
            return result;
        }
        //是否开启提醒
        if ("true".equals(Config.getAuthorityReminder(oid))) {
            //非医生
            if (!Config.getConfigValueAsList(oid, "CIV_USER_POSITION").contains(currentUser.getUserpositioncode())) {
                result.put("code", "403");
//                result.put("msg", "您无权查看此病历！");
                result.put("msg", Config.getCIV_USER_REMIND(oid));
                return result;
            }

            if (StringUtils.isNotBlank(deptCode) && !deptCode.equals(currentUser.getDeptcode())) {
                result.put("code", "403");
                result.put("msg", "您正在查看他科病历，请注意保密！");
            }
        }

        return result;
    }

    @Override
    public List<Map<String, String>> getAuthorisedOid(List<String> oidList) {
        UserEntity currentLoginUser = SecurityCommonUtil.getCurrentLoginUser();
        String pkUser = currentLoginUser.getPkUser();
        List<OrgCommonInformation> orgCommonInformations = getOrgCommonInformations(pkUser);


        List<Map<String, String>> result = new ArrayList<>();
        /**
         * @param oidList 为空时，返回授权院区；
         *  @param oidList 不为空时，返回授权院区与oidList的交集
         */

        for (OrgCommonInformation org : orgCommonInformations) {
           /* if (!oidList.isEmpty()) {
                if (oidList.contains(org.getCode())) {
                    Map<String, String> map = new HashMap<>();
                    map.put("name", org.getName());
                    map.put("code", org.getCode());
                    result.add(map);
                }

            } else if (StringUtils.isNotBlank(org.getParentCode())) {
                Map<String, String> map = new HashMap<>();
                map.put("name", org.getName());
                map.put("code", org.getCode());
                result.add(map);
            }*/

            if (StringUtils.isNotBlank(org.getParentCode())) {
                Map<String, String> map = new HashMap<>();
                map.put("name", org.getName());
                map.put("code", org.getCode());
                result.add(map);
           }

        }


        return result;
    }

    public List<OrgCommonInformation> getOrgCommonInformations(String pkUser) {
        QueryWrapper<DeptUserEntity> deptUserEntityQueryWrapper = new QueryWrapper<>();
        deptUserEntityQueryWrapper.eq("pk_user", pkUser);
        deptUserEntityQueryWrapper.eq("pk_dept_theme", "crm");
        deptUserEntityQueryWrapper.eq("stype", "ORG");
        List<Map<String, Object>> deptUserEntities = securityDeptUserMapper.selectMaps(deptUserEntityQueryWrapper);
        List<String> orgCodeList = new ArrayList<>();
        for (Map<String, Object> deptUser : deptUserEntities) {
            orgCodeList.add((String) deptUser.get("pk_dept"));
        }


        QueryWrapper<OrgCommonInformation> orgCommonInformationQueryWrapper = new QueryWrapper<>();
        orgCommonInformationQueryWrapper.in("code", orgCodeList);
        return orgCommonInformationMapper.selectList(orgCommonInformationQueryWrapper);
    }

    @Override
    public Page<Map<String, String>> getAuthorisedDept(String keyWord, String oid, int pageNo, int pageSize) {
        UserEntity currentLoginUser = SecurityCommonUtil.getCurrentLoginUser();
        QueryWrapper<DeptUserEntity> deptUserEntityQueryWrapper = new QueryWrapper<>();
        deptUserEntityQueryWrapper.eq("pk_user", currentLoginUser.getPkUser());
        deptUserEntityQueryWrapper.eq("pk_dept_theme", "crm");
        deptUserEntityQueryWrapper.eq("stype", "DEPT");
        List<Map<String, Object>> deptUserEntities = securityDeptUserMapper.selectMaps(deptUserEntityQueryWrapper);

        List<String> pkDeptList = new ArrayList<>();
        for (Map<String, Object> map : deptUserEntities) {
            pkDeptList.add((String) map.get("pk_dept"));
        }
        QueryWrapper<SecurityCommonDept> deptQueryWrapper = new QueryWrapper<>();
        //===================
        boolean deptQuery = true;

        //获取当前用户授权科室
        /*QueryParamVO queryParamVO = new QueryParamVO();
        String userCode = SecurityCommonUtil.getCurrentLoginUser().getUsercode();
        queryParamVO.setUserCode(userCode);
        List<SecurityDeptUserVO> securityDeptUserList = securityDeptUserMapper.queryAuthronizedDeptList(queryParamVO);
        List<String> deptCodes = new ArrayList<String>();
        for (SecurityDeptUserVO securityDeptUserVO : securityDeptUserList) {
            deptCodes.add(securityDeptUserVO.getDeptcode());
        }*/

        String userCode = SecurityCommonUtil.getCurrentLoginUser().getUsercode();
        List<String> pkDeptByUserCode = securityDeptUserMapper.selectpkDeptByUserCode(userCode);
        List<Map<String, String>> deptCNList = this.securityDeptUserMapper.selectDeptByOid(oid,"'"+String.join("','",pkDeptByUserCode)+"'");
        List<String> deptCodes = deptCNList.stream().map(array -> array.get("deptCode")).collect(Collectors.toList());

        if (ArrayUtil.containsIgnoreCase(deptCodes.toArray(new String[deptCodes.size()]), "all")) {
            deptCodes.clear();
            deptQuery = false;
        }
        if (deptQuery) {
            deptQueryWrapper.in("pk_dept", pkDeptList);
        }
        //=================

        //一级科室查询条件
        if ("true".equals(Config.getIS_SHOW_PARENT_DEPT("ALL"))) {
            deptQueryWrapper.select("distinct deptname,deptcode");
            deptQueryWrapper.isNull("parent_dept_code");
            deptQueryWrapper.isNotNull("organization");
        }

        if (StringUtils.isNotBlank(keyWord)) {
            //deptQueryWrapper.like("deptname", keyWord).or().eq("deptcode", keyWord);
            deptQueryWrapper.and(
                    wrapper -> wrapper.lambda().like(SecurityCommonDept::getDeptname, keyWord).or().eq(SecurityCommonDept::getDeptcode, keyWord)
            );
        }
        if (StringUtils.isNotBlank(oid)) {
            deptQueryWrapper.eq("org_code", oid);

        }

        deptQueryWrapper.orderByDesc("deptname");
        HdrSimplePage<SecurityCommonDept> securityCommonDeptHdrSimplePage = securityDeptMapper.selectPage(new HdrSimplePage<>(pageNo, pageSize), deptQueryWrapper);

        Page<Map<String, String>> page = new Page<>(pageNo, pageSize);
        page.setTotalCount(securityCommonDeptHdrSimplePage.getTotal());
        List<Map<String, String>> result = new ArrayList<>();
        for (SecurityCommonDept record : securityCommonDeptHdrSimplePage.getRecords()) {
            Map<String, String> map = new HashMap<>();
            map.put("code", record.getDeptcode());
            map.put("name", record.getDeptname());
            result.add(map);
        }
        page.setResult(result);

        return page;
    }


    @Override
    public List<NameAndCodeVo> getVisitStatus() {
        String s = ConfigCache.getCache("ALL", "VISIT_STATUS_CONFIG");
        List<NameAndCodeVo> nameAndCodeVos = new ArrayList<>();
        if (StringUtils.isNotBlank(s)) {
            try {
                nameAndCodeVos = objectMapper.readValue(s, new TypeReference<List<NameAndCodeVo>>() {
                });
            } catch (JsonProcessingException e) {
                throw new RuntimeException("VISIT_STATUS_CONFIG配置格式不正确，形如[{\"name\":\"已就诊\",\"code\":\"1234\"}]");
            }
        }

        return nameAndCodeVos;
    }

    public static String escapeQueryChars(String s) {
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < s.length(); i++) {
            char c = s.charAt(i);
            // These characters are part of the query syntax and must be escaped
            if (c == '\\' || c == '+' || c == '-' || c == '!' || c == '(' || c == ')' || c == ':' || c == '^' || c == '[' || c == ']' || c == '\"' || c == '{' || c == '}' || c == '~' || c == '*' || c == '?' || c == '|' || c == '&' || c == ';' || c == '/' || Character.isWhitespace(c)) {
                sb.append('\\');
            }
            sb.append(c);
        }
        return sb.toString();
    }

    @Override
    public String collectPatient(String patientId,String visitId, String visitTypeCode, String oid, String isCollect) {
        String result="1";
        try {
            String userCode = SecurityCommonUtil.getCurrentLoginUser().getUsercode();

            QueryWrapper<PatienCollect> wrapper = new QueryWrapper<>();
            wrapper.eq("patient_id", patientId);
            wrapper.eq("visit_id", visitId);
            wrapper.eq("visit_type_code", visitTypeCode);
            wrapper.eq("oid", oid);
            wrapper.eq("user_code", userCode);
            patienCollectMapper.delete(wrapper);

            PatienCollect pc = new PatienCollect();
            pc.setPatientId(patientId);
            pc.setVisitId(visitId);
            pc.setVisitTypeCode(visitTypeCode);
            pc.setOid(oid);
            pc.setUserCode(userCode);
            pc.setIsCollect(isCollect);
            patienCollectMapper.insert(pc);
        }catch (Exception e){
            result="0";
            e.printStackTrace();
        }
        return result;
    }

}
