package com.goodwill.hdr.civ.service;



import com.goodwill.hdr.core.orm.Page;

import java.io.UnsupportedEncodingException;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Description 类描述：手术记录服务接口
 * @Date 2018年5月7日
 * @modify 修改记录：
 */
public interface OperService {

    /**
     * @param patientId 患者编号
     * @param visitType 就诊类型
     * @param visitId   就诊次数
     * @param orderBy   排序字段
     * @param orderDir  排序规则
     * @param pageNo    页码
     * @param pageSize  分页单位
     * @return
     * @Description 方法描述: 某次就诊的手术记录（仅查询住院）
     */
    public Page<Map<String, String>> getOperList(String oid, String patientId, String visitType, String visitId, String orderBy,
                                                 String orderDir, int pageNo, int pageSize);

    /**
     * @param patientId 患者编号
     * @param orderNo   医嘱号
     * @param operNo    手术编号
     * @return
     * @Description 方法描述: 某次手术的术前访视
     */
    public Map<String, Object> getOperVisit(String oid, String patientId, String visitId, String orderNo, String operNo);

    /**
     * @param patientId 患者编号
     * @param orderNo   医嘱号
     * @param operNo    手术编号
     * @return
     * @Description 方法描述: 某次手术的麻醉
     */
    public Map<String, Object> getOperAnaes(String oid, String patientId,String visitId,  String orderNo, String operNo);

    /**
     * @param patientId 患者编号
     * @param orderNo   医嘱号
     * @param operNo    手术编号
     * @return
     * @Description 方法描述: 某次手术的术中信息
     */
    public Map<String, Object> getOperProcess(String oid, String patientId, String visitId,String orderNo, String operNo);

    /**
     * @param patientId 患者编号
     * @param orderNo   医嘱号
     * @param operNo    手术编号
     * @param pageNo    页码
     * @param pageSize  分页单位
     * @return
     * @Description 方法描述: 某次手术的手术用药
     */
    public Page<Map<String, String>> getOperDrug(String oid, String patientId, String visitId,String orderNo, String operNo, int pageNo,
                                                 int pageSize);

    /**
     * @param patientId 患者编号
     * @param orderNo   医嘱号
     * @param operNo    手术编号
     * @return
     * @Description 方法描述: 某次手术的恢复室信息
     */
    public Map<String, Object> getOperRecovery(String oid, String patientId,String visitId, String orderNo, String operNo);

    /**
     * @param patientId 患者编号
     * @param orderNo   医嘱号
     * @param operNo    手术编号
     * @return
     * @Description 方法描述: 某次手术的术后访视
     */
    public Map<String, Object> getOperAfter(String oid, String patientId, String visitId, String orderNo, String operNo);

    /**
     * @param patientId 患者编号
     * @param visitId   就诊次数
     * @param visitType 就诊类型
     * @return
     * @Description 方法描述: 某次就诊的手术记录数量
     */
    public long getOperCount(String oid, String patientId, String visitId, String visitType);

    /**
     * @param patientId 患者编号
     * @param pageNo    页码
     * @param pageSize  分页单位
     * @param orderby   排序字段
     * @param orderdir  排序规则
     * @return
     * @Description 方法描述:查询患者所有的手术记录
     */
    public Page<Map<String, String>> getOpers( String oid, String patientId, String visitType, int pageNo, int pageSize, String orderby,
                                              String orderdir, String outPatientId);

    /**
     * @param resultMap 数量集合
     * @return
     * @Description 方法描述:查询患者所有的手术记录
     */
    public void getOpersNum( Map<String, Object> resultMap, String outPatientId);

    /**
     * @return
     * @Description 方法描述:手术显示配置
     */
    public Map<String, String> getOperConfig(String oid);

    /**
     * @return
     * @Description 方法描述:手术麻醉单显示配置
     */
    public List<Map<String, String>> getAnesthesiaConfig(String oid);

    /**
     * @return
     * @Description 方法描述:手术麻醉单数据
     */
    List<Map<String, Object>> getAnesthesiaData(String oid, String patient_id, String visitId,String order_no);

    List<Map<String, Object>> getOperAnesImg(String oid, String patient_id, String visitId, String order_no, Map<String, String> rs) throws UnsupportedEncodingException;
}
