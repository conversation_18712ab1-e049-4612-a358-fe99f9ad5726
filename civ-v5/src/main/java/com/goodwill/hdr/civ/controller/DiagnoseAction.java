package com.goodwill.hdr.civ.controller;

import com.goodwill.hdr.civ.service.DiagnoseService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Description 类描述：诊断
 * @Date 2018年8月29日
 * @modify 修改记录：
 */
@RequestMapping("/diagnose")
@RestController
@Api(tags = "诊断查询")
public class DiagnoseAction {


    @Autowired
    private DiagnoseService diagnoseService;

    /**
     * @Description 获得所有的诊断记录
     */
    @ApiOperation(value = "获取所有的诊断记录", notes = "获取所有的诊断记录", httpMethod = "POST")
    @RequestMapping(value = "/getDiagsList", method = RequestMethod.POST)
    public List<Map<String, Object>> getDiagsList(String outPatientId, String startDate, String endDate) {

        //患者编号  就诊次  就诊类型
        List<Map<String, Object>> result = diagnoseService.getDiagsList(outPatientId, startDate, endDate);
        //响应
        return result;
    }

}
