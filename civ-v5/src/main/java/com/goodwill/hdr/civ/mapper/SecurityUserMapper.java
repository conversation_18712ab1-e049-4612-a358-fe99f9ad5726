package com.goodwill.hdr.civ.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.goodwill.hdr.civ.entity.SecurityUser;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @description 针对表【security_user(用户信息)】的数据库操作Mapper
 * @createDate 2022-03-09 11:35:04
 * @Entity com.goodwill.hdr.civ.entity.SecurityUser
 */
@DS("security")
@Mapper
public interface SecurityUserMapper extends BaseMapper<SecurityUser> {

    String selectDeptcode(@Param("userCode") String userCode);

    List<Map<String, String>> getUserList(@Param("oid") String oid,
                                          @Param("user") String user,
                                          @Param("dept") String dept);

    List<Map<String, String>> getDeptList();
}




