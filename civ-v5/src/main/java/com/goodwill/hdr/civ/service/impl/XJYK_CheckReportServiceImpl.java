package com.goodwill.hdr.civ.service.impl;


import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.goodwill.hdr.civ.config.Config;
import com.goodwill.hdr.civ.enums.HdrTableEnum;
import com.goodwill.hdr.civ.service.CheckReportService;
import com.goodwill.hdr.civ.service.ConfigService;
import com.goodwill.hdr.civ.service.PowerService;
import com.goodwill.hdr.civ.service.SpecialtyViewPowerService;
import com.goodwill.hdr.civ.utils.CivUtils;
import com.goodwill.hdr.civ.utils.ColumnUtil;
import com.goodwill.hdr.civ.utils.ListPage;
import com.goodwill.hdr.civ.utils.Utils;
import com.goodwill.hdr.civ.vo.ExamVoForMedicalTechnologies;
import com.goodwill.hdr.civ.vo.YXPT_ConfigVo;
import com.goodwill.hdr.core.orm.MatchType;
import com.goodwill.hdr.core.orm.Page;
import com.goodwill.hdr.core.utils.ApplicationException;
import com.goodwill.hdr.hbase.bo.PropertyFilter;
import com.goodwill.hdr.hbase.dto.responseVo.PageResultVo;
import com.goodwill.hdr.hbase.dto.responseVo.ResultVo;
import com.goodwill.hdr.hbaseQueryClient.builder.PageRequestBuilder;
import com.goodwill.hdr.hbaseQueryClient.service.HbaseQueryClient;
import com.goodwill.hdr.security.priority.entity.UserEntity;
import com.goodwill.hdr.security.utils.SecurityCommonUtil;
import com.goodwill.hdr.web.common.vo.ResultVO;
import org.apache.commons.codec.binary.Base64;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.crypto.Cipher;
import javax.crypto.SecretKeyFactory;
import javax.crypto.spec.DESKeySpec;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.security.Key;
import java.security.SecureRandom;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 新疆医科大学第一附属医院昌吉分院+患者统一视图+检查报告+检查报告图像调阅
 * EPM 2415696
 */
@Service
public class XJYK_CheckReportServiceImpl implements CheckReportService {

    protected Logger logger = LoggerFactory.getLogger(getClass());
    @Autowired
    private PowerService powerService;
    @Autowired
    private ObjectMapper objectMapper;

    private ConfigService configService;

    @Autowired
    private SpecialtyViewPowerService specialtyViewPowerService;

    private final HbaseQueryClient hbaseQueryClient;

    public XJYK_CheckReportServiceImpl(ConfigService configService,
                                       HbaseQueryClient hbaseQueryClient) {
        this.configService = configService;
        this.hbaseQueryClient = hbaseQueryClient;
    }

    @Override
    public Page<Map<String, String>> getCheckReportList(String oid, String patId, String visitId, String visitType, String orderBy,
                                                        String orderDir, String mainDiag, String deptCode, int pageNo, int pageSize) {
        //分页 排序
        Page<Map<String, String>> page = new Page<Map<String, String>>();
        //分页
        boolean pageable = true;
        if (pageNo == 0 || pageSize == 0) {
            pageable = false;
        } else {
            page.setPageNo(pageNo);
            page.setPageSize(pageSize);
        }
        //排序
        if (StringUtils.isBlank(orderBy) || StringUtils.isBlank(orderDir)) {
            page.setOrderBy("REPORT_TIME");
            page.setOrderDir(Page.Sort.DESC);
        } else {
            page.setOrderBy(orderBy);
            page.setOrderDir(orderDir);
        }
        try {
            if ("INPV".equals(visitType)) { //住院检查报告
                page = getInpvExamReports(page, oid, patId, visitId, mainDiag, deptCode, pageable);
            } else if ("OUTPV".equals(visitType)) { //门诊检查报告
                page = getOutpvExamReports(page, oid, patId, visitId, mainDiag, deptCode, pageable);
            }
        } catch (Exception e) {
            logger.error("查询hbase数据库失败！ ", e);
            throw new ApplicationException("查询检查报告失败！" + e.getCause());
        }
        //字段映射
        List<Map<String, String>> exams = new ArrayList<Map<String, String>>();
        for (Map<String, String> map : page) {
            //处理检查诊断
            String examDiag = map.get("EXAM_DIAG");
            if (StringUtils.isNotBlank(examDiag)) {
                //去掉 \X000d\
                if (examDiag.contains("\\X000d\\")) {
                    map.put("EXAM_DIAG", examDiag.replace("\\X000d\\", ""));
                }
            }
            Map<String, String> exam = new HashMap<String, String>();
            ColumnUtil.convertMapping(exam, map,
                    new String[]{"EXAM_ITEM_NAME", "REPORT_TIME", "EXAM_DIAG", "REPORT_NO", "OUT_PATIENT_ID", "IN_PATIENT_ID", "VISIT_ID", "OID"});
            exams.add(exam);
        }
        //重置分页
        page.setResult(exams);

        return page;
    }

    /**
     * @param page      分页对象
     * @param patientId 患者编号
     * @param visitId   就诊次数
     * @param pageable  是否分页
     * @return 分页对象
     * @Description 方法描述: 查询患者某次门诊的检查报告
     */
    private Page<Map<String, String>> getOutpvExamReports(Page<Map<String, String>> page, String oid, String patientId,
                                                          String visitId, String mainDiag, String deptCode, boolean pageable) {
        List<Map<String, String>> exams = new ArrayList<Map<String, String>>();
        //优先 根据vid查询
        List<PropertyFilter> filters = new ArrayList<PropertyFilter>();
        //检查报告权限筛选
        String usercode = SecurityCommonUtil.getLoginUserCode();
//        String usercode = SecurityUtils.getCurrentUserName(); //在线用户
        Map<String, Object> power = powerService.getPowerConfigByExam(oid, usercode);
        if ("false".equals(power.get("isAll"))) {
            List<String> typeList = (List<String>) power.get("power");
            String typeStr = StringUtils.join(typeList.toArray(), ",");
            filters.add(new PropertyFilter("EXAM_CLASS_CODE", MatchType.IN.getOperation(), typeStr));
        }
        filters.add(new PropertyFilter("EXAM_CLASS_CODE", MatchType.NOTIN.getOperation(),
                Config.getCIV_PATHOLOGY_VALUE(oid).replace("/", ",")));
//        if (!HdrConstantEnum.HOSPITAL_ZDFB.getCode().equalsIgnoreCase(ConfigCache.getCache(oid, "org_oid"))) {
//            filters.add(new PropertyFilter("VISIT_ID", "STRING", MatchType.EQ.getOperation(), visitId));
//        }
        filters.add(new PropertyFilter("VISIT_TYPE_CODE", MatchType.EQ.getOperation(), "01"));
        Config.setExamStatusFilter(oid, filters);
        //新增专科配置，改成暂时先不分页查询
        int pageNo = page.getPageNo();
        int pageSize = page.getPageSize();
        //分页判断
        if (pageable) {
            page.setPageNo(1);
            page.setPageSize(10000);
            ResultVo<PageResultVo<Map<String, String>>> resultVo = hbaseQueryClient.getPageByCondition(
                    PageRequestBuilder.init()
                            .tableName(HdrTableEnum.HDR_EXAM_REPORT.getCode())
                            .patientId(patientId)
                            .oid(oid)
                            .visitId(visitId)
                            .visitTypeCode("01")
                            .filters(filters)
                            .pageNo(0)
                            .pageSize(0)
                            .orderBy("")
                            .desc()
                            .column("EXAM_ITEM_CODE", "EXAM_ITEM_NAME", "REPORT_TIME", "EXAM_DIAG", "ORDER_NO", "REPORT_NO", "OUT_PATIENT_ID", "IN_PATIENT_ID", "VISIT_ID", "OID")
                            .build());
            if (resultVo.isSuccess()) {
                page.setResult(resultVo.getContent().getResult());
                page.setTotalCount(resultVo.getContent().getTotal());
            }
//            page = hbaseDao.findPageConditionByPatient(HdrTableEnum.HDR_EXAM_REPORT.getCode(), oid, patientId, page, filters,
//                    "EXAM_ITEM_CODE", "EXAM_ITEM_NAME", "REPORT_TIME", "EXAM_DIAG", "ORDER_NO", "REPORT_NO", "OUT_PATIENT_ID", "IN_PATIENT_ID", "VISIT_ID", "OID");
        } else {
//            exams = hbaseDao.findConditionByPatient(HdrTableEnum.HDR_EXAM_REPORT.getCode(), oid, patientId, filters,
//                    "EXAM_ITEM_CODE", "EXAM_ITEM_NAME", "REPORT_TIME", "EXAM_DIAG", "ORDER_NO", "REPORT_NO", "OUT_PATIENT_ID", "IN_PATIENT_ID", "VISIT_ID", "OID");
            ResultVo<PageResultVo<Map<String, String>>> resultVo = hbaseQueryClient.getPageByCondition(
                    PageRequestBuilder.init()
                            .tableName(HdrTableEnum.HDR_EXAM_REPORT.getCode())
                            .patientId(patientId)
                            .oid(oid)
                            .visitId(visitId)
                            .visitTypeCode("01")
                            .filters(filters)
                            .pageNo(0)
                            .pageSize(0)
                            .orderBy("")
                            .desc()
                            .column("EXAM_ITEM_CODE", "EXAM_ITEM_NAME", "REPORT_TIME", "EXAM_DIAG", "ORDER_NO", "REPORT_NO", "OUT_PATIENT_ID", "IN_PATIENT_ID", "VISIT_ID", "OID")
                            .build());
            if (resultVo.isSuccess()) {
                exams = resultVo.getContent().getResult();
            }
            Utils.sortListByDate(exams, page.getOrderBy(), page.getOrderDir()); //排序
            page.setResult(exams);
            page.setTotalCount(exams.size());
        }

        //TODO 若缺失就诊次，可根据CivUtils中的其他条件筛选
        //排序完之后合并分页
        handlerPage(oid, patientId, visitId, page, mainDiag, deptCode, "Exam", pageable, pageNo, pageSize);
        return page;
    }

    /**
     * @param page      分页对象
     * @param patientId 患者编号
     * @param visitId   就诊次数
     * @param pageable  是否分页
     * @return 分页对象
     * @Description 方法描述: 查询患者某次住院的检查报告
     */
    private Page<Map<String, String>> getInpvExamReports(Page<Map<String, String>> page, String oid, String patientId,
                                                         String visitId, String mainDiag, String deptCode, boolean pageable) {
        List<Map<String, String>> exams = new ArrayList<Map<String, String>>();
        //优先 根据vid查询
        List<PropertyFilter> filters = new ArrayList<PropertyFilter>();
        //检查报告权限筛选
        String usercode = SecurityCommonUtil.getLoginUserCode();
//        String usercode = SecurityUtils.getCurrentUserName(); //在线用户
        //String usercode = "admin";
        Map<String, Object> power = powerService.getPowerConfigByExam(oid, usercode);
        if ("false".equals(power.get("isAll"))) {
            List<String> typeList = (List<String>) power.get("power");
            String typeStr = StringUtils.join(typeList.toArray(), ",");
            filters.add(new PropertyFilter("EXAM_CLASS_CODE", MatchType.IN.getOperation(), typeStr));
        }
        filters.add(new PropertyFilter("EXAM_CLASS_CODE", MatchType.NOTIN.getOperation(),
                Config.getCIV_PATHOLOGY_VALUE(oid).replace("/", ",")));
//        if (!HdrConstantEnum.HOSPITAL_ZDFB.getCode().equalsIgnoreCase(ConfigCache.getCache(oid, "org_oid"))) {
//            filters.add(new PropertyFilter("VISIT_ID", "STRING", MatchType.EQ.getOperation(), visitId));
//        }
        filters.add(new PropertyFilter("VISIT_TYPE_CODE", MatchType.EQ.getOperation(), "02"));
        Config.setExamStatusFilter(oid, filters);
        //新增专科配置，改成暂时先不分页查询,因HBASE查询工具有问题。
        int pageNo = page.getPageNo();
        int pageSize = page.getPageSize();
        //分页判断
        if (pageable) {
            page.setPageNo(1);
            page.setPageSize(10000);
//            page = hbaseDao.findPageConditionByPatient(HdrTableEnum.HDR_EXAM_REPORT.getCode(), oid, patientId, page, filters,
//                    "EXAM_ITEM_CODE", "EXAM_ITEM_NAME", "REPORT_TIME", "EXAM_DIAG", "ORDER_NO", "REPORT_NO", "IN_PATIENT_ID", "OUT_PATIENT_ID", "VISIT_ID", "OID");
////			Utils.sortListByDate(page.getResult(), page.getOrderBy(), page.getOrderDir()); //排序
            ResultVo<PageResultVo<Map<String, String>>> resultVo = hbaseQueryClient.getPageByCondition(
                    PageRequestBuilder.init()
                            .tableName(HdrTableEnum.HDR_EXAM_REPORT.getCode())
                            .patientId(patientId)
                            .oid(oid)
                            .visitId(visitId)
                            .visitTypeCode("02")
                            .filters(filters)
                            .pageNo(0)
                            .pageSize(0)
                            .orderBy("")
                            .desc()
                            .column("EXAM_ITEM_CODE", "EXAM_ITEM_NAME", "REPORT_TIME", "EXAM_DIAG", "ORDER_NO", "REPORT_NO", "IN_PATIENT_ID", "OUT_PATIENT_ID", "VISIT_ID", "OID")
                            .build());
            if (resultVo.isSuccess()) {
                page.setResult(resultVo.getContent().getResult());
                page.setTotalCount(resultVo.getContent().getTotal());
            }
            Utils.sortListDescByDate(page.getResult(), page.getOrderBy());
        } else {
//            exams = hbaseDao.findConditionByPatient(HdrTableEnum.HDR_EXAM_REPORT.getCode(), oid, patientId, filters,
//                    "EXAM_ITEM_CODE", "EXAM_ITEM_NAME", "REPORT_TIME", "EXAM_DIAG", "ORDER_NO", "REPORT_NO", "IN_PATIENT_ID", "OUT_PATIENT_ID", "VISIT_ID", "OID");
////			Utils.sortListByDate(exams, page.getOrderBy(), page.getOrderDir()); //排序
            ResultVo<PageResultVo<Map<String, String>>> resultVo = hbaseQueryClient.getPageByCondition(
                    PageRequestBuilder.init()
                            .tableName(HdrTableEnum.HDR_EXAM_REPORT.getCode())
                            .patientId(patientId)
                            .oid(oid)
                            .visitId(visitId)
                            .visitTypeCode("02")
                            .filters(filters)
                            .pageNo(0)
                            .pageSize(0)
                            .orderBy("")
                            .desc()
                            .column("EXAM_ITEM_CODE", "EXAM_ITEM_NAME", "REPORT_TIME", "EXAM_DIAG", "ORDER_NO", "REPORT_NO", "IN_PATIENT_ID", "OUT_PATIENT_ID", "VISIT_ID", "OID")
                            .build());
            if (resultVo.isSuccess()) {
                exams = resultVo.getContent().getResult();
            }
            Utils.sortListDescByDate(page.getResult(), page.getOrderBy());
            page.setResult(exams);
            page.setTotalCount(exams.size());
        }
        //上述未查到，再根据入院出院时间查询
		/*if (page.getTotalCount() <= 0) {
			page.setPageNo(1);
			page.setPageSize(10000);
			if (StringUtils.isBlank(page.getOrderBy()) || StringUtils.isBlank(page.getOrderDir())) {
				page.setOrderBy("REPORT_TIME");
				page.setOrderDir(Sort.DESC);
			}
			page = CivUtils.getInpExamReportsForCheck(page, patientId, visitId, pageable, power);
		}*/
        //排序完之后合并分页
        handlerPage(oid, patientId, visitId, page, mainDiag, deptCode, "Exam", pageable, pageNo, pageSize);
        return page;
    }

    @Override
    public List<Map<String, String>> getCheckOrderByPid(String oid, String pid, String vid, String order) {
        List<Map<String, String>> exams = new ArrayList<Map<String, String>>();
        //优先 根据vid查询
        List<PropertyFilter> filters = new ArrayList<PropertyFilter>();
        if (StringUtils.isNotBlank(order)) {
            filters.add(new PropertyFilter("ORDER_NO", MatchType.EQ.getOperation(), order));
        }
        //filters.add(new PropertyFilter("VISIT_ID", "STRING", MatchType.EQ.getOperation(), vid));
//        exams = hbaseDao.findConditionByPatient(HdrTableEnum.HDR_IN_ORDER.getCode(), oid, pid, filters,
//                new String[]{"ORDER_ITEM_CODE", "ORDER_ITEM_NAME", "IN_PATIENT_ID", "VISIT_ID", "ORDER_NO"});
        ResultVo<PageResultVo<Map<String, String>>> resultVo = hbaseQueryClient.getPageByCondition(
                PageRequestBuilder.init()
                        .tableName(HdrTableEnum.HDR_IN_ORDER.getCode())
                        .patientId(pid)
                        .oid(oid)
                        .visitId(vid)
                        .visitTypeCode("")
                        .filters(filters)
                        .pageNo(0)
                        .pageSize(0)
                        .orderBy("")
                        .desc()
                        .column("ORDER_ITEM_CODE", "ORDER_ITEM_NAME", "IN_PATIENT_ID", "VISIT_ID", "ORDER_NO")
                        .build());


        if (resultVo.isSuccess()) {
            exams = resultVo.getContent().getResult();
        }
        return exams;
    }

    /**
     * 查询专科设置处理结果集
     */
    public void handlerPage(String oid, String patientId, String vid, Page<Map<String, String>> page, String mainDiag,
                            String deptCode, String orderCode, boolean isPage, int pageNo, int pageSize) {
        if (StringUtils.isBlank(mainDiag) && StringUtils.isBlank(deptCode)) {
            return;
        }
        //查询专科视图配置  用药医嘱
        List<Map<String, String>> listDept = specialtyViewPowerService.getSpecialtyConfig(oid, mainDiag, orderCode,
                deptCode);
        List<Map<String, String>> data = new ArrayList<Map<String, String>>();
        List<Map<String, String>> dataTemp = new ArrayList<Map<String, String>>();
        for1:
        for (Map<String, String> map : page) {
            //检查需要再次关联下检查医嘱，因为专科检查配置的是检查医嘱
            List<Map<String, String>> exams = getCheckOrderByPid(oid, patientId, vid, map.get("ORDER_NO"));
            for2:
            for (Map<String, String> mapOrder : exams) {
                String orderCodeTemp = mapOrder.get("ORDER_ITEM_CODE");
                for3:
                for (Map<String, String> mapSpecialty : listDept) {
                    String code = mapSpecialty.get("subItemCode");
                    if (StringUtils.isNotBlank(orderCodeTemp) && orderCodeTemp.equals(code)) {
                        data.add(map);
                        continue for1;
                    }
                }
            }
            dataTemp.add(map);

        }
        //合并数据
        for (Map<String, String> map : dataTemp) {
            data.add(map);
        }
        if (isPage) {
            ListPage<Map<String, String>> listPage = new ListPage<Map<String, String>>(data, pageNo, pageSize);
            data = listPage.getPagedList();
        }
        page.setResult(data);
    }

    @Override
    public Map<String, String> getCheckReportDetails(String oid, String pid, String vid, String reportNo) {
        Map<String, String> result = new HashMap<String, String>();
        //查询检查报告
//		Map<String, String> map = hbaseDao.getByKey(HdrTableEnum.HDR_EXAM_REPORT.getCode(), rowkey,
//				new String[] { "EXAM_CLASS_NAME", "EXAM_ITEM_NAME", "ORDER_NO", "EXAM_PART", "EXAM_PERFORM_TIME",
//						"EXAM_FEATURE", "EXAM_DIAG", "REPORT_TIME", "REPORT_DOCTOR_NAME", "PACS_URL", "VISIT_TYPE_CODE",
//						"EXAM_CLASS_CODE", "OUTP_NO", "INP_NO", "PACS_IMG_URL" });
        //优先 根据vid查询
        List<PropertyFilter> filters = new ArrayList<PropertyFilter>();
//        if (!HdrConstantEnum.HOSPITAL_ZDFB.getCode().equalsIgnoreCase(ConfigCache.getCache(oid, "org_oid"))) {
//            if (StringUtils.isNotBlank(vid) && !"-".equals(vid)) {
//                filters.add(new PropertyFilter("VISIT_ID", "STRING", MatchType.EQ.getOperation(), vid));
//            }
//        }
        if (StringUtils.isNotBlank(reportNo)) {
            filters.add(new PropertyFilter("REPORT_NO", MatchType.EQ.getOperation(), reportNo));
        }
        List<Map<String, String>> list = new ArrayList<>();
        ResultVo<PageResultVo<Map<String, String>>> resultVo = hbaseQueryClient.getPageByCondition(
                PageRequestBuilder.init()
                        .tableName(HdrTableEnum.HDR_EXAM_REPORT.getCode())
                        .patientId(pid)
                        .oid(oid)
                        .visitId(vid)
                        .visitTypeCode("")
                        .filters(filters)
                        .pageNo(0)
                        .pageSize(0)
                        .orderBy("REPORT_TIME")
                        .desc()
                        .column("EXAM_CLASS_NAME", "EXAM_ITEM_NAME", "ORDER_NO", "EXAM_PART_NAME", "EXAM_PERFORM_TIME",
                                "EXAM_FEATURE", "EXAM_DIAG", "REPORT_TIME", "REPORT_DOCTOR_NAME", "PACS_URL", "VISIT_TYPE_CODE",
                                "EXAM_CLASS_CODE", "OUTP_NO", "INP_NO", "PACS_IMG_URL", "APPLY_DOCTOR_NAME", "APPLY_DEPT_NAME",
                                "APPLY_TIME", "EXAM_DEVICE", "EXAM_GOAL", "EXAM_ATTENTION", "APPLY_NO")
                        .build());

        if (resultVo.isSuccess()) {
            list = resultVo.getContent().getResult();
        }
//        List<Map<String, String>> list = hbaseDao.findConditionByPatient(HdrTableEnum.HDR_EXAM_REPORT.getCode(), oid, pid, filters,
//                new String[]{"EXAM_CLASS_NAME", "EXAM_ITEM_NAME", "ORDER_NO", "EXAM_PART_NAME", "EXAM_PERFORM_TIME",
//                        "EXAM_FEATURE", "EXAM_DIAG", "REPORT_TIME", "REPORT_DOCTOR_NAME", "PACS_URL", "VISIT_TYPE_CODE",
//                        "EXAM_CLASS_CODE", "OUTP_NO", "INP_NO", "PACS_IMG_URL", "APPLY_DOCTOR_NAME", "APPLY_DEPT_NAME",
//                        "APPLY_TIME","EXAM_DEVICE","EXAM_GOAL","EXAM_ATTENTION","APPLY_NO"});
        //未找到，终止执行
        if (null == list || list.isEmpty()) {
            return result;
        }
        Map<String, String> map = list.get(0);
        //处理检查所见字段
        String examFeature = map.get("EXAM_FEATURE");
        if (StringUtils.isNotBlank(examFeature)) {
            //去掉 \X000d\
            if (examFeature.contains("\\X000d\\")) {
                result.put("examFeature", examFeature.replace("\\X000d\\", ""));
            } else {
                result.put("examFeature", examFeature);
            }
        } else {
            result.put("examFeature", "-");
        }
        //处理检查诊断字段
        String examDiag = map.get("EXAM_DIAG");
        if (StringUtils.isNotBlank(examDiag)) {
            //去掉 \X000d\
            if (examDiag.contains("\\X000d\\")) {
                result.put("examDiag", examDiag.replace("\\X000d\\", ""));
            } else {
                result.put("examDiag", examDiag);
            }
        } else {
            result.put("examDiag", "-");
        }
        //获取pacsurl
//        personalConfigService.getPacsUrl(map);
        //字段映射
        ColumnUtil.convertMapping(result, map,
                new String[]{"EXAM_CLASS_NAME", "EXAM_ITEM_NAME", "ORDER_NO", "EXAM_PART_NAME", "EXAM_PERFORM_TIME",
                        "REPORT_TIME", "REPORT_DOCTOR_NAME", "PACS_URL", "VISIT_TYPE_CODE", "EXAM_CLASS_CODE",
                        "OUTP_NO", "INP_NO", "APPLY_DOCTOR_NAME", "APPLY_DEPT_NAME", "APPLY_TIME", "EXAM_DEVICE",
                        "EXAM_GOAL", "EXAM_ATTENTION", "APPLY_NO"});
        //新增影像浏览url和pacsurl
        //map.put("imgUrl",map.get("PACS_IMG_URL"));
        List<Map<String, String>> urlList = new ArrayList<Map<String, String>>();
/**
 * todo: 查询参数并加密
 * DoctorID=d0059--->GgCR+TN4WOY=
 * DoctorName=陈建伟--->ADMKIg7sYW0=
 * 	   test--->ShlxDrgNUzY=
 * orgCode=45775911-3--->KHS1a+NHIkHNjHLYXMOt9w==
 *
 * idcardNo=652323196606101465--->ASSfuj/LP8WQTuxvqf3LkfpbXrd9H4B4
 * 	 512922196204296469--->DpEHzmL7ZErpYSJujASFs/aJMTa5z0RA
 *
 * hisPatientId=**********--->jEwGqyndfpJAXleLmfv0cQ==
 */
        List<com.goodwill.hdr.civ.entity.Config> configList = configService
                .getConfigBycode("ALL", "YXPT_CONFIG");
        if (!configList.isEmpty()) {
            com.goodwill.hdr.civ.entity.Config config = configList.get(0);
            String configJson = config.getConfigValue();
            YXPT_ConfigVo yxptConfigVo = null;
            try {
                yxptConfigVo = objectMapper.readValue(configJson, YXPT_ConfigVo.class);
            } catch (JsonProcessingException e) {
                logger.error("configJson转map异常");
            }
            if (yxptConfigVo != null) {
                String moduleName = yxptConfigVo.getModuleName();
                String desKey = yxptConfigVo.getDesKey();
                String url = yxptConfigVo.getUrl();
                List<YXPT_ConfigVo.queryHbaseConfig> queryList = yxptConfigVo.getQueryList();
                Map<String, String> paramMap = yxptConfigVo.getParamMap();
                Map<String, String> paramValueMap = new HashMap<>();

//                UserEntity loginUser = SecurityCommonUtil.getCurrentLoginUser();
//                String usercode = loginUser.getUsercode();
                String usercode = yxptConfigVo.getDoctorId();
                try {
                    usercode = XJYK_DesEncrypt(desKey, usercode);
                } catch (Exception e) {
                    logger.error("usercode加密异常");
                }


//                String username = loginUser.getUsername();
                String username = yxptConfigVo.getDoctorName();
                try {
                    username = XJYK_DesEncrypt(desKey, username);
                } catch (Exception e) {
                    logger.error("username加密异常");
                }

                String encryptOid = "";
                try {
                    encryptOid = XJYK_DesEncrypt(desKey, oid);
                } catch (Exception e) {
                    logger.error("oid加密异常");
                }

                paramValueMap.put("oid", encryptOid);
                paramValueMap.put("userCode", usercode);
                paramValueMap.put("userName", username);

                for (YXPT_ConfigVo.queryHbaseConfig queryHbaseConfig : queryList) {
                    String configTableName = queryHbaseConfig.getTableName();
                    if(HdrTableEnum.HDR_PATIENT.getCode().equals(configTableName)){
                        vid="";
                    }

                    List<PropertyFilter> conditionList = queryHbaseConfig.getConditionList();
                    if (conditionList != null && !conditionList.isEmpty()) {
                        for (PropertyFilter filter : conditionList) {
                            String value = filter.getPropertyValue();
                            if ("#{reportNo}".equals(value)) {
                                filter.setPropertyValue(reportNo);
                            }

                        }
                    }
                    List<String> columnList = queryHbaseConfig.getColumnList();
                    ResultVo<PageResultVo<Map<String, String>>> tmpResultVo = hbaseQueryClient.getPageByCondition(
                            PageRequestBuilder.init()
                                    .tableName(configTableName)
                                    .patientId(pid)
                                    .oid(oid)
                                    .visitId(vid)
                                    .visitTypeCode("")
                                    .filters(conditionList)
                                    .pageNo(0)
                                    .pageSize(0)
                                    .orderBy("")
                                    .desc()
                                    .column(columnList.toArray(new String[0]))
                                    .build());
                    if (!tmpResultVo.isSuccess()) {
                        continue;
                    }
                    List<Map<String, String>> tmpResult = tmpResultVo.getContent().getResult();
                    if (tmpResult.isEmpty()) {
                        continue;
                    }
                    Map<String, String> tmpResultMap = tmpResult.get(0);
                    for (Map.Entry<String, String> entry : tmpResultMap.entrySet()) {
                        String key = entry.getKey();
                        String paramName = paramMap.get(key);
                        if (StringUtils.isBlank(paramName)) {
                            continue;
                        }

                        String value = entry.getValue();
                        try {
                            value = XJYK_DesEncrypt(desKey, value);
                        } catch (Exception e) {
                            continue;
                        }

                        paramValueMap.put(paramName, value);

                    }
                }

                for (Map.Entry<String, String> entry : paramValueMap.entrySet()) {
                    String paramName = entry.getKey();
                    String value = entry.getValue();
                    url = url.replace("#{" + paramName + "}", value);
                }
                Map<String, String> urlMap = new HashMap<>();
                urlMap.put("name", moduleName);
                urlMap.put("url", url);
                urlMap.put("linkType", yxptConfigVo.getLinkType());
                urlList.add(urlMap);
            }


        }
        String json = "";
        try {
            json = objectMapper.writeValueAsString(urlList);
        } catch (JsonProcessingException e) {
            logger.error("urlList转json异常");
        }
        result.put("urls", json);
        return result;
    }


    public String XJYK_DesEncrypt(String keyStr, String plaintext) throws Exception {

        DESKeySpec desKey = new DESKeySpec(keyStr.getBytes());//创建一个密匙工厂，然后用它把DESKeySpec转换成
        SecretKeyFactory keyFactory = SecretKeyFactory.getInstance("DES");
        Key deskey = keyFactory.generateSecret(desKey);
        //实例化Cipher对象，它用于完成实际的加密操作
        Cipher cipher = Cipher.getInstance("DES/ECB/NoPadding");
        SecureRandom random = new SecureRandom();
        //IvParameterSpec random = new IvParameterSpec(key.getBytes());
        //初始化Cipher对象，设置为加密模式
        cipher.init(Cipher.ENCRYPT_MODE, deskey, random);
        //加密字节
        byte[] bytes = plaintext.getBytes("GBK");
        int len = bytes.length;
        if (len % 8 != 0) {
            byte[] temp = new byte[len + (8 - len % 8)];
            for (int i = 0; i < len; i++) {
                temp[i] = bytes[i];
            }
            bytes = temp;
        }
        //加密字节16进制查看
        //hexStringView(bytes);
        byte[] results = cipher.doFinal(bytes);
        //执行加密操作。加密后的结果通常都会用Base64编码进行传输
        String base64String = Base64.encodeBase64String(results);
        return   URLEncoder.encode(base64String, StandardCharsets.UTF_8.toString());

    }

    @Override
    public long getCheckCount(String oid, String patientId, String visitId, String visitType) {
        //查询检查报告
        Page<Map<String, String>> page = getCheckReportList(oid, patientId, visitId, visitType, "", "", "", "", 0, 0);
        long checkCount = page.getTotalCount();
        if (checkCount < 0) {
            checkCount = 0;
        }
        return checkCount;
    }

    @Override
    public List<Map<String, Object>> getCheckReports(String oid, String patientId, int pageNo, int pageSize, String orderby,
                                                     String orderdir, String outPatientId, String visitType, String year, String key, String type,
                                                     String click_Type) {
        List<Map<String, String>> exams = new ArrayList<Map<String, String>>();
        List<Map<String, String>> types = getAllReportTypes(outPatientId);

        if (StringUtils.isBlank(year)) {
            Calendar date = Calendar.getInstance();
            year = String.valueOf((date.get(Calendar.YEAR) + 1));
        }
        int num = 0;
        while (exams.size() == 0) {
            year = (Integer.valueOf(year) - 1) + "";
//            if (StringUtils.isNotBlank(patientId) && StringUtils.isNotBlank(this_oid) && this_oid.equals(oid) || this_oid.equals("ALL")) {
//                getCheckReportsByPat(oid, patientId, visitType, orderby, orderdir, exams, types, year, key, type,
//                        click_Type);
//            }
            if (StringUtils.isNotBlank(outPatientId)) {
                String[] pats = outPatientId.split(",");
                for (int i = 0; i < pats.length; i++) {
                    if (StringUtils.isNotBlank(pats[i])) {
                        String[] pat = pats[i].split("\\|");
                        getCheckReportsByPat(pat[2], pat[1], pat[0], pat[3], orderby, orderdir, exams, types, year, key, type,
                                click_Type);
                    }
                }
            }
            num++;
            if (num == 20)
                break;
        }

        //列表
        Map<String, Object> resultList = new HashMap<String, Object>();
        //按报告时间月份分组
        CivUtils.groupByDate(resultList, exams, "reportTime");
        List<Map<String, Object>> listResult = new ArrayList<Map<String, Object>>();
        for (String time : resultList.keySet()) {
            Map<String, Object> rs = new HashMap<String, Object>();
            rs.put("time", time);
            rs.put("order", Integer.valueOf(CivUtils.changeFormatDate(time)));
            rs.put("data", resultList.get(time));
            listResult.add(rs);
        }
        Utils.sortListByDate(listResult, "order", Page.Sort.DESC);
        return listResult;
    }

    /**
     * @return 分页对象
     * @Description 方法描述: 根据患者ID查询所有的 检查报告
     */
    public void getCheckReportsByPat(String oid, String patientId, String visitType, String visitId, String orderby, String orderdir,
                                     List<Map<String, String>> exams, List<Map<String, String>> types, String year, String key, String filed,
                                     String click_Type) {
        //分页
        Page<Map<String, String>> page = new Page<Map<String, String>>();
        page.setPageNo(1);
        page.setPageSize(1000);
        //排序
        page.setOrderBy(orderby);
        page.setOrderDir(orderdir);
        //查询条件  按照检查项查询
        List<PropertyFilter> filters = new ArrayList<PropertyFilter>();
//        filters.add(new PropertyFilter("VISIT_TYPE_CODE", "STRING", MatchType.EQ.getOperation(), visitType));
//        if(StringUtils.isNotBlank(visitId)){
//            filters.add(new PropertyFilter("VISIT_ID", "STRING", MatchType.EQ.getOperation(), visitId));
//        }

        if (StringUtils.isNotBlank(year)) {
            PropertyFilter filter1 = new PropertyFilter("REPORT_TIME", MatchType.GE.getOperation(),
                    year + "-01-01");
            filters.add(filter1);
            if ("more".equals(click_Type)) {
                PropertyFilter filter2 = new PropertyFilter("REPORT_TIME", MatchType.LE.getOperation(),
                        year + "-12-31 23:59:59");
                filters.add(filter2);
            }
        }
        if (StringUtils.isNotBlank(key)) {
            if ("1".equals(filed)) {
                PropertyFilter filterKey = new PropertyFilter("EXAM_ITEM_NAME", MatchType.LIKE.getOperation(),
                        key);
                filters.add(filterKey);
            } else if ("2".equals(filed)) {
                PropertyFilter filterKey = new PropertyFilter("EXAM_DIAG", MatchType.LIKE.getOperation(),
                        key);
                filters.add(filterKey);
            } else if ("3".equals(filed)) {
                PropertyFilter filterKey = new PropertyFilter("EXAM_FEATURE", MatchType.LIKE.getOperation(),
                        key);
                filters.add(filterKey);
            } else if ("4".equals(filed)) {
                PropertyFilter filterKey = new PropertyFilter("EXAM_CLASS_CODE", MatchType.IN.getOperation(),
                        key);
                filters.add(filterKey);
            }
        }
        //检查报告权限筛选
        String usercode = SecurityCommonUtil.getLoginUserCode(); //在线用户
        Map<String, Object> power = powerService.getPowerConfigByExam(oid, usercode);
        if ("false".equals(power.get("isAll"))) {
            List<String> typeList = (List<String>) power.get("power");
            String typeStr = StringUtils.join(typeList.toArray(), ",");
            filters.add(new PropertyFilter("EXAM_CLASS_CODE", MatchType.IN.getOperation(), typeStr));
        }
        filters.add(new PropertyFilter("EXAM_CLASS_CODE", MatchType.NOTIN.getOperation(),
                Config.getCIV_PATHOLOGY_VALUE(oid).replace("/", ",")));
        Config.setExamStatusFilter(oid, filters);
        ResultVo<PageResultVo<Map<String, String>>> resultVo = hbaseQueryClient.getPageByCondition(
                PageRequestBuilder.init()
                        .tableName(HdrTableEnum.HDR_EXAM_REPORT.getCode())
                        .patientId(patientId)
                        .oid(oid)
                        .visitId(visitId)
                        .visitTypeCode(visitType)
                        .filters(filters)
                        .pageNo(0)
                        .pageSize(0)
                        .orderBy("REPORT_TIME")
                        .desc()
                        .column("EXAM_ITEM_NAME", "REPORT_TIME", "EXAM_CLASS_NAME",
                                "PACS_URL", "EXAM_DIAG", "EXAM_PART", "REPORT_CONFIRM_TIME", "REPORT_NO", "IN_PATIENT_ID", "OUT_PATIENT_ID", "VISIT_ID", "OID", "ORG_NO", "ORG_NAME")
                        .build());


        List<Map<String, String>> list = new ArrayList<>();
        if (resultVo.isSuccess()) {
            list = resultVo.getContent().getResult();
        }
//        Page<Map<String, String>> result = hbaseDao.findPageConditionByPatient(HdrTableEnum.HDR_EXAM_REPORT.getCode(), oid,
//                patientId, page, filters, new String[]{"EXAM_ITEM_NAME", "REPORT_TIME", "EXAM_CLASS_NAME",
//                        "PACS_URL", "EXAM_DIAG", "EXAM_PART", "REPORT_CONFIRM_TIME", "REPORT_NO", "IN_PATIENT_ID", "OUT_PATIENT_ID", "VISIT_ID", "OID", "ORG_NO", "ORG_NAME"});
        //list = result.getResult();
        //报告类型
        for (Map<String, String> map : list) {
            Map<String, String> exam = new HashMap<String, String>();
//			Utils.checkAndPutToMap(exam, "rowkey", map.get("ROWKEY"), "-", false); //rowkey
            Utils.checkAndPutToMap(exam, "examItemName", map.get("EXAM_ITEM_NAME"), "-", false); //检查项目
            Utils.checkAndPutToMap(exam, "reportNo", map.get("REPORT_NO"), "-", false); //检查报告号
            if (StringUtils.isNotBlank(map.get("IN_PATIENT_ID"))) {
                Utils.checkAndPutToMap(exam, "inPatientId", map.get("IN_PATIENT_ID"), "-", false); //患者号
            } else {
                Utils.checkAndPutToMap(exam, "inPatientId", map.get("OUT_PATIENT_ID"), "-", false); //患者号
            }
            Utils.checkAndPutToMap(exam, "visitid", map.get("VISIT_ID"), "-", false); //就诊
            if (map.get("REPORT_TIME") == null)
                Utils.checkAndPutToMap(exam, "reportTime", map.get("REPORT_CONFIRM_TIME"), "-", false); //报告时间
            else
                Utils.checkAndPutToMap(exam, "reportTime", map.get("REPORT_TIME"), "-", false); //报告时间

            Utils.checkAndPutToMap(exam, "pacsUrl", map.get("PACS_URL"), "-", false); //影像链接
            Utils.checkAndPutToMap(exam, "examClassName", map.get("EXAM_CLASS_NAME"), "-", false); //检查类型名称
            Utils.checkAndPutToMap(exam, "oid", map.get("OID"), "-", false); //医院OID
            Utils.checkAndPutToMap(exam, "orgNo", map.get("ORG_NO"), "-", false); //医疗机构标识
            Utils.checkAndPutToMap(exam, "orgName", map.get("ORG_NAME"), "-", false); //医疗机构名称
            for (Map<String, String> type : types) {
                String examClassName = map.get("EXAM_CLASS_NAME");
                if (StringUtils.isNotBlank(examClassName) && examClassName.equals(type.get("name"))) {
                    exam.put("examTypeCode", type.get("id"));
                }
            }
            exams.add(exam);
        }
    }

    @Override
    public List<Map<String, String>> getAllReportTypes(String outPatientId) {

        List<Map<String, String>> list = new ArrayList<Map<String, String>>();
        Map<String, Map<String, String>> items = new HashMap<>();

//        if (StringUtils.isNotBlank(patientId) && StringUtils.isNotBlank(this_oid) && this_oid.equals(oid) || this_oid.equals("ALL")) {
//            getCheckTypesByPat(oid, patientId, visitType, items);
//        }
        if (StringUtils.isNotBlank(outPatientId)) {
            String[] pats = outPatientId.split(",");
            for (int i = 0; i < pats.length; i++) {
                if (StringUtils.isNotBlank(pats[i])) {
                    String[] pat = pats[i].split("\\|");
                    getCheckTypesByPat(pat[2], pat[1], pat[0], pat[3], items);
                }
            }
        }


        for (String item : items.keySet()) {
            Map<String, String> itemMap = items.get(item);
            Map<String, String> map = new HashMap<String, String>();
            map.put("code", itemMap.get("EXAM_CLASS_CODE"));
            map.put("name", itemMap.get("EXAM_CLASS_NAME"));
            list.add(map);
        }
        //返回结果按照配置顺序排序
        if (Config.getCheckSort("ALL")) {
            String strlist = Config.getCheckSortConfig("ALL");
            if (StringUtils.isNotBlank(strlist)) {
                String[] sorts = strlist.split(",");
                List<String> sortlist = Arrays.asList(sorts);
                list = list.stream().sorted(Comparator.comparing(e -> e.get("name"), (x, y) -> {
                    for (String sort : sortlist) {
                        if (sort.equals(x) || sort.equals(y)) {
                            if (x.equals(y)) {
                                return 0;
                            } else if (sort.equals(x)) {
                                return -1;
                            } else {
                                return 1;
                            }
                        }
                    }
                    return 0;
                })).collect(Collectors.toList());
            }
        }
        return list;
    }

    private static int getCount(String item) {
        int count = 0;
        if (item.contains("CT")) {
            count = 1;
        } else if (item.contains("放射")) {
            count = 2;
        } else if (item.contains("核磁")) {
            count = 3;
        } else if (item.contains("超声")) {
            count = 4;
        } else if (item.contains("心电")) {
            count = 5;
        } else if (item.contains("内镜")) {
            count = 6;
        } else {
            count = 7;
        }
        return count;
    }

    private static Map<String, ExamVoForMedicalTechnologies> getItemAndExamVoForMedicalTechnologiesMap(String oidLast, String lastPatientId, String lastVisitId, String lastVisitTypeCode, List<Map<String, String>> list) {
        Map<String, ExamVoForMedicalTechnologies> subMap = new LinkedHashMap<>();
        for (Map<String, String> map : list) {
            String itemName = map.get("EXAM_ITEM_NAME");
            if (subMap.get(itemName) != null) {
                continue;
            }
            ExamVoForMedicalTechnologies vo = new ExamVoForMedicalTechnologies();
            vo.setExamFinding(map.get("EXAM_FEATURE"));
            vo.setExamDiagnosis(map.get("EXAM_DIAG"));
            vo.setTime(map.get("REPORT_TIME"));
            if (lastPatientId.equals(map.get("IN_PATIENT_ID")) || lastPatientId.equals(map.get("OUT_PATIENT_ID"))) {
                vo.setCurrentVisit(lastVisitId.equals(map.get("VISIT_ID"))
                        && lastVisitTypeCode.equals(map.get("VISIT_TYPE_CODE")) && oidLast.equals(map.get("OID")));
            } else {
                vo.setCurrentVisit(false);
            }

            subMap.put(itemName, vo);

        }

        return subMap;
    }

    private static String getDateString(String date) {
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime startDate = null;
        if (date.equals("near_half_year")) {
            startDate = now.minusMonths(6);
        } else if (date.equals("near_1_year")) {
            startDate = now.minusYears(1);
        }
        String dateString = "";
        if (startDate != null) {
            dateString = startDate.toString();
        }
        return dateString;
    }

    @Override
    public ResultVO<List<Map<String, String>>> getCheckItemList(String this_oid, String outPatientId, String itemName, String date, boolean currentVist) {
        String dateString = getDateString(date);
        String[] pats = outPatientId.split(",");
        Map<String, ExamVoForMedicalTechnologies> examVoMap = new HashMap<>();
        List<Map<String, String>> list = new ArrayList<>();
        for (int i = 0; i < pats.length; i++) {
            if (StringUtils.isNotBlank(pats[i])) {
                String[] pat = pats[i].split("\\|");

                list.addAll(queryListByItemName(pat[0], pat[1], pat[2], pat[3], itemName, dateString));
            }
        }
        if (currentVist && !list.isEmpty()) {
            list.get(0).put("currentVisit", "true");
        }
        ResultVO<List<Map<String, String>>> resultVo = new ResultVO<>();
        resultVo.success("查询成功", list);
        return resultVo;
    }

    @Override
    public List<Map<String, String>> getUlMeasure(String oid, String patientId, String visitType, String reportNo) {
        return Collections.emptyList();
    }

    private List<Map<String, String>> queryListByItemName(String visitTypeCode, String patientId, String oid, String visitID, String itemName, String dateString) {
        List<PropertyFilter> filters = new ArrayList<>();
        if (StringUtils.isNotBlank(dateString)) {
            filters.add(new PropertyFilter("REPORT_TIME", MatchType.GE.getOperation(), dateString));
        }
        //filters.add(new PropertyFilter("VISIT_TYPE_CODE", "STRING", MatchType.EQ.getOperation(), visitTypeCode));
        List<Map<String, String>> list = new ArrayList<>();
//        List<Map<String, String>> list = hbaseDao.findConditionByPatient(HdrTableEnum.HDR_EXAM_REPORT.getCode(), oid,
//                patientId, filters, "APPLY_DEPT_NAME", "APPLY_DOCTOR_NAME", "ORG_NAME", "OID", "VISIT_ID", "VISIT_TYPE_CODE", "REPORT_TIME", "REPORT_NO");
        ResultVo<PageResultVo<Map<String, String>>> resultVo = hbaseQueryClient.getPageByCondition(
                PageRequestBuilder.init()
                        .tableName(HdrTableEnum.HDR_EXAM_REPORT.getCode())
                        .patientId(patientId)
                        .oid(oid)
                        .visitId(visitID)
                        .visitTypeCode(visitTypeCode)
                        .filters(filters)
                        .pageNo(0)
                        .pageSize(0)
                        .orderBy("")
                        .desc()
                        .column("APPLY_DEPT_NAME", "APPLY_DOCTOR_NAME", "ORG_NAME", "OID", "VISIT_ID", "VISIT_TYPE_CODE", "REPORT_TIME", "REPORT_NO")
                        .build());


        if (resultVo.isSuccess()) {
            list = resultVo.getContent().getResult();
        }
        list.sort((t1, t2) -> {
                    String s2 = StringUtils.isNotBlank(t2.get("REPORT_TIME")) ? t2.get("REPORT_TIME") : "";
                    String s1 = StringUtils.isNotBlank(t1.get("REPORT_TIME")) ? t1.get("REPORT_TIME") : "";
                    return s2.compareTo(s1);
                }
        );
        return list;

    }

    @Override
    public ResultVO<Map<String, ExamVoForMedicalTechnologies>> getCheckListForMedicalTechnologies(String this_oid, String oid, String oidLast, String pid, String visitId, String visitTypeCode, String outPatientId, String date) {
        String dateString = getDateString(date);

        String[] pats = outPatientId.split(",");
        List<Map<String, String>> list = new ArrayList<>();
        for (String s : pats) {
            if (StringUtils.isNotBlank(s)) {
                String[] pat = s.split("\\|");
                list.addAll(getExamList(pat[2], pat[1], pat[0], dateString, oidLast, pid, visitId, visitTypeCode));
            }
        }
        list.sort((t1, t2) -> t2.get("REPORT_TIME").compareTo(t1.get("REPORT_TIME")));
        Map<String, ExamVoForMedicalTechnologies> map = getItemAndExamVoForMedicalTechnologiesMap(oidLast, pid, visitId, visitTypeCode, list);
        ResultVO<Map<String, ExamVoForMedicalTechnologies>> resultVo = new ResultVO<>();
        resultVo.success("查询成功", map);
        return resultVo;
    }

    private List<Map<String, String>> getExamList(String oid, String pid, String visitType, String date, String oidLast, String lastPatientId, String lastVisitId, String lastVisitTypeCode) {
        List<PropertyFilter> filters = new ArrayList<>();
        if (StringUtils.isNotBlank(date)) {
            filters.add(new PropertyFilter("REPORT_TIME", MatchType.GE.getOperation(), date));
        }
        List<Map<String, String>> list = new ArrayList<>();
        //filters.add(new PropertyFilter("VISIT_TYPE_CODE", "STRING", MatchType.EQ.getOperation(), visitType));
        ResultVo<PageResultVo<Map<String, String>>> resultVo = hbaseQueryClient.getPageByCondition(
                PageRequestBuilder.init()
                        .tableName(HdrTableEnum.HDR_EXAM_REPORT.getCode())
                        .patientId(pid)
                        .oid(oid)
                        .visitId(lastVisitId)
                        .visitTypeCode(visitType)
                        .filters(filters)
                        .pageNo(0)
                        .pageSize(0)
                        .orderBy("REPORT_TIME")
                        .desc()
                        .column("EXAM_ITEM_NAME", "EXAM_DIAG", "EXAM_FEATURE", "ORG_NAME", "REPORT_TIME",
                                "VISIT_TYPE_CODE", "IN_PATIENT_ID", "OUT_PATIENT_ID", "VISIT_ID", "OID")
                        .build());


        if (resultVo.isSuccess()) {
            list = resultVo.getContent().getResult();
        }
//        List<Map<String, String>> list = hbaseDao.findConditionByPatient(HdrTableEnum.HDR_EXAM_REPORT.getCode(), oid,
//                pid, filters, "EXAM_ITEM_NAME", "EXAM_DIAG", "EXAM_FEATURE", "ORG_NAME", "REPORT_TIME",
//                "VISIT_TYPE_CODE", "IN_PATIENT_ID", "OUT_PATIENT_ID", "VISIT_ID", "OID");
        return list;

    }

    @Override
    public void getAllReportsCount(Map<String, Object> resultMap, String outPatientId) {

        int num = 0;
//        if (StringUtils.isNotBlank(patientId) && StringUtils.isNotBlank(this_oid) && this_oid.equals(oid) || this_oid.equals("ALL")) {
//            num = num + getCheckTypesByPat(oid, patientId, visitType);
//        }
        if (StringUtils.isNotBlank(outPatientId)) {
            String[] pats = outPatientId.split(",");
            for (int i = 0; i < pats.length; i++) {
                if (StringUtils.isNotBlank(pats[i])) {
                    String[] pat = pats[i].split("\\|");
                    num = num + getCheckTypesByPat(pat[2], pat[1], pat[0], pat[3]);
                }
            }
        }
        resultMap.put("num", num);
    }

    /**
     * @return 分页对象
     * @Description 方法描述: 根据患者ID查询所有的 检查报告的类型
     */
    public void getCheckTypesByPat(String oid, String patientId, String visitType, String visitId, Map<String, Map<String, String>> items) {
        List<PropertyFilter> filters = new ArrayList<PropertyFilter>();
        //检查报告权限筛选
        String usercode = SecurityCommonUtil.getLoginUserCode(); //在线用户
        Map<String, Object> power = powerService.getPowerConfigByExam(oid, usercode);
        if ("false".equals(power.get("isAll"))) {
            List<String> typeList = (List<String>) power.get("power");
            String typeStr = StringUtils.join(typeList.toArray(), ",");
            filters.add(new PropertyFilter("EXAM_CLASS_CODE", MatchType.IN.getOperation(), typeStr));
        }
        filters.add(new PropertyFilter("EXAM_CLASS_CODE", MatchType.NOTIN.getOperation(),
                Config.getCIV_PATHOLOGY_VALUE(oid).replace("/", ",")));
        //filters.add(new PropertyFilter("VISIT_TYPE_CODE", MatchType.EQ.getOperation(), visitType));
        //filters.add(new PropertyFilter("VISIT_ID", "STRING", MatchType.EQ.getOperation(), visitId));
        Config.setExamStatusFilter(oid, filters);
        List<Map<String, String>> outpage = new ArrayList<>();
        ResultVo<PageResultVo<Map<String, String>>> resultVo = hbaseQueryClient.getPageByCondition(
                PageRequestBuilder.init()
                        .tableName(HdrTableEnum.HDR_EXAM_REPORT.getCode())
                        .patientId(patientId)
                        .oid(oid)
                        .visitId(visitId)
                        .visitTypeCode(visitType)
                        .filters(filters)
                        .pageNo(0)
                        .pageSize(0)
                        .orderBy("REPORT_TIME")
                        .desc()
                        .column("EXAM_ITEM_NAME", "REPORT_TIME", "EXAM_CLASS_NAME", "EXAM_CLASS_CODE")
                        .build());


        if (resultVo.isSuccess()) {
            outpage = resultVo.getContent().getResult();
        }
//        List<Map<String, String>> outpage = hbaseDao.findConditionByPatient(HdrTableEnum.HDR_EXAM_REPORT.getCode(), oid,
//                patientId, filters, "EXAM_ITEM_NAME", "REPORT_TIME", "EXAM_CLASS_NAME");
        for (Map<String, String> map : outpage) {
            if (null == items.get(map.get("EXAM_CLASS_CODE"))) {
                Map<String, String> tmpMap = new HashMap<>();
                tmpMap.put("EXAM_CLASS_NAME", map.get("EXAM_CLASS_NAME"));
                tmpMap.put("EXAM_CLASS_CODE", map.get("EXAM_CLASS_CODE"));
                items.put(map.get("EXAM_CLASS_CODE"), tmpMap);
            }
        }
    }

    /**
     * @return 分页对象
     * @Description 方法描述: 根据患者ID查询总数
     */
    public int getCheckTypesByPat(String oid, String patientId, String visitType, String visitId) {
        List<PropertyFilter> filters = new ArrayList<PropertyFilter>();
        //检查报告权限筛选
        String usercode = SecurityCommonUtil.getLoginUserCode(); //在线用户
        Map<String, Object> power = powerService.getPowerConfigByExam(oid, usercode);
        if ("false".equals(power.get("isAll"))) {
            List<String> typeList = (List<String>) power.get("power");
            String typeStr = StringUtils.join(typeList.toArray(), ",");
            filters.add(new PropertyFilter("EXAM_CLASS_CODE", MatchType.IN.getOperation(), typeStr));
        }
        //filters.add(new PropertyFilter("VISIT_ID", "STRING", MatchType.EQ.getOperation(), visitId));
        //filters.add(new PropertyFilter("VISIT_TYPE_CODE", "STRING", MatchType.EQ.getOperation(), visitType));
        filters.add(new PropertyFilter("EXAM_CLASS_CODE", MatchType.NOTIN.getOperation(),
                Config.getCIV_PATHOLOGY_VALUE(oid).replace("/", ",")));
        Config.setExamStatusFilter(oid, filters);
        List<Map<String, String>> outpage = new ArrayList<>();
        ResultVo<PageResultVo<Map<String, String>>> resultVo = hbaseQueryClient.getPageByCondition(
                PageRequestBuilder.init()
                        .tableName(HdrTableEnum.HDR_EXAM_REPORT.getCode())
                        .patientId(patientId)
                        .oid(oid)
                        .visitId(visitId)
                        .visitTypeCode(visitType)
                        .filters(filters)
                        .pageNo(0)
                        .pageSize(0)
                        .orderBy("REPORT_TIME")
                        .desc()
                        .column("EXAM_ITEM_NAME", "REPORT_TIME", "EXAM_CLASS_NAME")
                        .build());


        if (resultVo.isSuccess()) {
            outpage = resultVo.getContent().getResult();
        }
//        List<Map<String, String>> outpage = hbaseDao.findConditionByPatient(HdrTableEnum.HDR_EXAM_REPORT.getCode(), oid,
//                patientId, filters, "EXAM_ITEM_NAME", "REPORT_TIME", "EXAM_CLASS_NAME");
        return outpage.size();
    }

}
