package com.goodwill.hdr.civ.utils;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.Response;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.StreamUtils;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.io.OutputStream;
import java.net.HttpURLConnection;
import java.net.URL;
import java.nio.charset.Charset;
import java.util.Map;

public class WsUtil {
    private static Logger logger = LoggerFactory.getLogger(WsUtil.class);


    /**
     * 通过webservice方法，查找指定的病历文书文件
     *
     * @return 返回类型： String
     * @return
     */
    public static String callRemoteWebService(String url, String requestSoap) {
        HttpURLConnection conn = null;
        OutputStream os = null;
        String response = null;
        try {
            URL u = new URL(url);
            conn = (HttpURLConnection) u.openConnection();
            conn.setRequestMethod("POST");
            conn.setDoOutput(true);
            conn.setDoInput(true);
            conn.setUseCaches(false);
            conn.setConnectTimeout(30000);
            conn.setReadTimeout(10000);
            conn.setRequestProperty("Content-Type", "text/xml;charset=utf-8");
            os = conn.getOutputStream();
            os.write(requestSoap.getBytes("utf-8"));
            os.flush();

            response = StreamUtils.copyToString(conn.getInputStream(), Charset.forName("UTF-8"));
            return response;
        } catch (Exception e) {
            throw new RuntimeException(e);
        } finally {
            conn.disconnect();
            if (os != null) {
                try {
                    os.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
    }

    /**
     * 2024-11-28
     * 北京同仁医院使用签章获取token
     * @return
     */
    public static String getToken(String param) {
        String token = "";
        try {
            String urls = "http://10.1.9.100:8000/api/studytoken";
            //获取时间戳
            String timeStiamp = requestByGet("http://10.1.9.100:8000/api/timestamp");
            //获取签章
            String requestBody = "{\"AccessionNumber\":\""+param+"\"}";
            String appSecret = "62vq6G2OiBB4X5WnWYZ4Y0VvbGjl";
            String signNature = SignatureUtil.createDigitalSignature(urls, "POST", requestBody, appSecret, timeStiamp);
            //调用接口
            URL url = new URL(urls);
            HttpURLConnection con = (HttpURLConnection) url.openConnection();
            con.setRequestMethod("POST");
            con.setRequestProperty("Content-Type", "application/json");
            con.setRequestProperty("Content-Encoding", "utf-8");
            con.setRequestProperty("HiAuthVersion", "2.0");
            con.setRequestProperty("HiAuthAppKey", "YYSJZX");
            con.setRequestProperty("HiAuthAppSignature", signNature);
            con.setRequestProperty("HiAuthTimeStamp", timeStiamp);
            con.setDoOutput(true);

            OutputStream os = con.getOutputStream();
            os.write(requestBody.getBytes("utf-8"));
            os.flush();
            os.close();

            int responseCode = con.getResponseCode();
            logger.info("responseCode："+responseCode);
            if (responseCode == HttpURLConnection.HTTP_OK) {
                BufferedReader ins = new BufferedReader(new InputStreamReader(
                        con.getInputStream()));
                String inputLine;
                StringBuffer response = new StringBuffer();

                while ((inputLine = ins.readLine()) != null) {
                    response.append(inputLine);
                }
                logger.info("===========返回的值：" + response);
                ins.close();
                Map<String, String> map = JSON.parseObject(response.toString(), new TypeReference<Map<String, String>>() {
                });
                token = map.get("Token");
                logger.info("===========获取的token：" + token);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return token;
    }

    /**
     * get方式调用接口
     */
    public static String requestByGet(String url) {
        String result = "";
        OkHttpClient client = new OkHttpClient();
        Request request = new Request.Builder().url(url).build();
        try (Response response = client.newCall(request).execute()) {
            if (!response.isSuccessful())
                throw new IOException("Unexpected code " + response);
            result = response.body().string();
        } catch (Exception e) {
            e.printStackTrace();
        }
        logger.info("get接口结果值：" + result);
        return result;
    }

    /**
     * post方式调用接口
     */
    public static String getPost(String url,String param) {
        String result="";
        try {
            URL apiUrl = new URL(url);
            HttpURLConnection con = (HttpURLConnection) apiUrl.openConnection();
            con.setRequestMethod("POST");
            con.setDoOutput(true);
            OutputStream os = con.getOutputStream();
            os.write(param.getBytes());
            os.flush();
            os.close();

            int responseCode = con.getResponseCode();
            logger.info("responseCode======="+responseCode);
            if (responseCode == HttpURLConnection.HTTP_OK) {
                BufferedReader ins = new BufferedReader(new InputStreamReader(con.getInputStream()));
                String inputLine;
                StringBuffer response = new StringBuffer();

                while ((inputLine = ins.readLine()) != null) {
                    response.append(inputLine);
                }
                ins.close();
                result=response.toString();
                logger.info("result======="+result);
            }
        }catch (Exception e){
            e.printStackTrace();
        }
        return result;
    }

}
