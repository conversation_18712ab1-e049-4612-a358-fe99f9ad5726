package com.goodwill.hdr.civ.mapper;

import com.goodwill.hdr.civ.entity.ORPlus;
import com.goodwill.hdr.web.core.mapper.HdrBaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;

import java.util.List;

@Mapper
public interface OrpPlusMapper extends HdrBaseMapper<ORPlus> {

    @Select("select t.id_pk,t.rolename,t.ordercode,t.ordername,t.ordertype,t.sex,t.age,t.sp,t.breath,"
            + "t.temperature,t.heartrate,t.systolic,t.diastolic,t.oxygen,t.roledesc,t.source,"
            + "t2.roleid,t2.diag,t2.drug,t2.lab,t2.labreport,t2.exam,t2.examreport,t2.operation,t2.emr,t2.diagnames from hdr_kb_orplus t "
            + "left join "
            + "(select roleid, "
            + "group_concat(case when type = 'diag' then CONCAT_WS(',',codes)end separator ';') as diag,"
            + "group_concat(case when type = 'diag' then CONCAT_WS(',',names)end separator ';') as diagnames,"
            + "group_concat(case when type = 'drug' then CONCAT_WS(',',codes,subtype)end separator ';') as drug, "
            + "group_concat(case when type = 'lab' then CONCAT_WS(',',codes)end separator ';') as lab,"
            + "group_concat(case when type = 'labreport' then CONCAT_WS(',',codes,names,operation,`value`)end separator ';') as labreport,"
            + "group_concat(case when type = 'exam' then CONCAT_WS(',',codes)end separator ';') as exam,"
            + "group_concat(case when type = 'examreport' then CONCAT_WS(',',codes,`value`)end separator ';') as examreport, "
            + "group_concat(case when type = 'operation' then CONCAT_WS(',',codes)end separator ';') as operation,"
            + "group_concat(case when type = 'emr' then CONCAT_WS(',',codes,`value`)end separator ';') as emr "
            + "from hdr_kb_orplus_slave group by roleid) t2 on t.id_pk = t2.roleid where t.data_status = 'ENB' and t.ordertype =#{orderType}")
    List<Object> getRoleEqOrderType(String orderType);

    @Select("select t.id_pk,t.rolename,t.ordercode,t.ordername,t.ordertype,t.sex,t.age,t.sp,t.breath,"
            + "t.temperature,t.heartrate,t.systolic,t.diastolic,t.oxygen,t.roledesc,t.source,"
            + "t2.roleid,t2.diag,t2.drug,t2.lab,t2.labreport,t2.exam,t2.examreport,t2.operation,t2.emr,t2.diagnames from hdr_kb_orplus t "
            + "left join "
            + "(select roleid, "
            + "group_concat(case when type = 'diag' then CONCAT_WS(',',codes)end separator ';') as diag,"
            + "group_concat(case when type = 'diag' then CONCAT_WS(',',names)end separator ';') as diagnames,"
            + "group_concat(case when type = 'drug' then CONCAT_WS(',',codes,subtype)end separator ';') as drug, "
            + "group_concat(case when type = 'lab' then CONCAT_WS(',',codes)end separator ';') as lab,"
            + "group_concat(case when type = 'labreport' then CONCAT_WS(',',codes,names,operation,`value`)end separator ';') as labreport,"
            + "group_concat(case when type = 'exam' then CONCAT_WS(',',codes)end separator ';') as exam,"
            + "group_concat(case when type = 'examreport' then CONCAT_WS(',',codes,`value`)end separator ';') as examreport, "
            + "group_concat(case when type = 'operation' then CONCAT_WS(',',codes)end separator ';') as operation,"
            + "group_concat(case when type = 'emr' then CONCAT_WS(',',codes,`value`)end separator ';') as emr "
            + "from hdr_kb_orplus_slave group by roleid) t2 on t.id_pk = t2.roleid where t.data_status = 'ENB' and t.ordertype in(${orderType})")
    List<Object> getRoleInOrderType(String orderType);

    @Select("select t.id_pk,t.rolename,t.ordercode,t.ordername,t.ordertype,t.sex,t.age,t.sp,t.breath,"
            + "t.temperature,t.heartrate,t.systolic,t.diastolic,t.oxygen,t.roledesc,t.source,"
            + "t2.roleid,t2.diag,t2.drug,t2.lab,t2.labreport,t2.exam,t2.examreport,t2.operation,t2.emr,t2.diagnames from hdr_kb_orplus t "
            + "left join "
            + "(select roleid, "
            + "group_concat(case when type = 'diag' then CONCAT_WS(',',codes)end separator ';') as diag,"
            + "group_concat(case when type = 'diag' then CONCAT_WS(',',names)end separator ';') as diagnames,"
            + "group_concat(case when type = 'drug' then CONCAT_WS(',',codes,subtype)end separator ';') as drug, "
            + "group_concat(case when type = 'lab' then CONCAT_WS(',',codes)end separator ';') as lab,"
            + "group_concat(case when type = 'labreport' then CONCAT_WS(',',codes,names,operation,`value`)end separator ';') as labreport,"
            + "group_concat(case when type = 'exam' then CONCAT_WS(',',codes)end separator ';') as exam,"
            + "group_concat(case when type = 'examreport' then CONCAT_WS(',',codes,`value`)end separator ';') as examreport, "
            + "group_concat(case when type = 'operation' then CONCAT_WS(',',codes)end separator ';') as operation,"
            + "group_concat(case when type = 'emr' then CONCAT_WS(',',codes,`value`)end separator ';') as emr "
            + "from hdr_kb_orplus_slave group by roleid) t2 on t.id_pk = t2.roleid ")
    List<Object> getRole();
}
