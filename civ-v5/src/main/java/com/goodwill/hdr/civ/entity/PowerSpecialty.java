package com.goodwill.hdr.civ.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.io.Serializable;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2022-03-03
 */
@TableName("civ_power_specialty")
public class PowerSpecialty implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 用户编码
     */
    private String usercode;

    /**
     * 科室编码
     */
    private String deptcode;

    /**
     * 配置类型（category分类视图，visit就诊视图，mr病历文书，examreport检查报告）
     */
    private String type;

    private String itemnames;

    /**
     * 项编码
     */
    private String itemcodes;

    /**
     * 最后修改时间
     */
    private String lastupdatetime;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getUsercode() {
        return usercode;
    }

    public void setUsercode(String usercode) {
        this.usercode = usercode;
    }

    public String getDeptcode() {
        return deptcode;
    }

    public void setDeptcode(String deptcode) {
        this.deptcode = deptcode;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getItemnames() {
        return itemnames;
    }

    public void setItemnames(String itemnames) {
        this.itemnames = itemnames;
    }

    public String getItemcodes() {
        return itemcodes;
    }

    public void setItemcodes(String itemcodes) {
        this.itemcodes = itemcodes;
    }

    public String getLastupdatetime() {
        return lastupdatetime;
    }

    public void setLastupdatetime(String lastupdatetime) {
        this.lastupdatetime = lastupdatetime;
    }

    @Override
    public String toString() {
        return "PowerSpecialty{" +
                "id=" + id +
                ", usercode=" + usercode +
                ", deptcode=" + deptcode +
                ", type=" + type +
                ", itemnames=" + itemnames +
                ", itemcodes=" + itemcodes +
                ", lastupdatetime=" + lastupdatetime +
                "}";
    }
}
