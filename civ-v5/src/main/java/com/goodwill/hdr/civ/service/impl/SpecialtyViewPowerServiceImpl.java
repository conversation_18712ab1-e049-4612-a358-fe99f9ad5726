package com.goodwill.hdr.civ.service.impl;


import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;

import com.goodwill.hdr.civ.config.Config;
import com.goodwill.hdr.civ.dao.SpecialtyDeptConfigDao;
import com.goodwill.hdr.civ.dao.SprcialtyViewNewDao;
import com.goodwill.hdr.civ.entity.*;
import com.goodwill.hdr.civ.enums.DictType;
import com.goodwill.hdr.civ.mapper.*;
import com.goodwill.hdr.civ.service.DictHbaseService;
import com.goodwill.hdr.civ.service.SpecialtyViewPowerService;
import com.goodwill.hdr.civ.utils.ListPage;
import com.goodwill.hdr.core.orm.Page;
import com.goodwill.hdr.security.utils.SecurityCommonUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Description 类描述：
 * @modify 修改记录：
 */
@Service
public class SpecialtyViewPowerServiceImpl implements SpecialtyViewPowerService {
    @Autowired
    private SprcialtyViewNewDao sprcialtyViewDaoNew;

    //    @Autowired
//    private HbaseDao hbaseDao;
    @Autowired
    private DictHbaseService dictHbaseService;
    @Autowired
    private SpecialtyDeptConfigDao specialtyDeptConfigDao;
    @Autowired
    private SicknessMapper sicknessMapper;
    @Autowired
    private SpecialtyIndicatorConfigMapper specialtyIndicatorConfigMapper;
    @Autowired
    private SpecialtyDeptMapper specialtyDeptMapper;
    @Autowired
    private DeptSpecialtyConfigMapper deptSpecialtyConfigMapper;
    @Autowired
    private DeptSpecialtyIndicatorMapper deptSpecialtyIndicatorMapper;
    @Autowired
    private SpecialtyConfigMapper specialtyConfigMapper;

    /**
     * 查询是否有自定义设置
     *
     * @param mainDiag
     * @return
     */
    @Override
    public List<SpecialtyConfig> getItemList(String oid, String mainDiag) {
        String usercode = SecurityCommonUtil.getLoginUserCode();
        List<SpecialtyConfig> list = specialtyConfigMapper.getSpecityConfig(usercode, mainDiag);
//        List<SpecialtyConfig> list = sprcialtyViewDaoNew.getSpecityConfig(usercode, mainDiag);
        if (null == list || list.size() == 0) {
            list = specialtyConfigMapper.getSpecityConfig("admin", mainDiag);
        }
        return list;
    }

    /**
     * 查询科室设置的大项明细
     *
     * @param dept
     * @return
     */
    @Override
    public List<DeptSpecialtyConfig> getDeptItemList(String oid, String dept) {
        QueryWrapper<DeptSpecialtyConfig> wrapper = new QueryWrapper<>();
        if (StringUtils.isNotBlank(dept)) {
            wrapper.eq("dept_code", dept);
        }
        wrapper.eq("is_inuse", "Y").groupBy("dept_code", "dept_name", "item_code", "item_name").orderByAsc("array_index");
        List<DeptSpecialtyConfig> list = deptSpecialtyConfigMapper.selectList(wrapper);
//        List<DeptSpecialtyConfig> list = sprcialtyViewDaoNew.getDeptSpecityConfig(dept);
        return list;
    }

    /**
     * 获取自定义疾病列表
     *
     * @return
     */
    public List<Map<String, String>> getSicknessList(String oid) {
        List<Map<String, String>> list = new ArrayList<Map<String, String>>();
        String usercode = SecurityCommonUtil.getLoginUserCode();
//        select *  from  civ_specialty_config where  is_inuse = 'Y'   and doctor_code = ?  " +  //and  sickness_code = ? and
//        " group by sickness_code,sickness_name,item_code,item_name order by  array_index asc
        QueryWrapper<SpecialtyConfig> wrapper = new QueryWrapper<>();
        wrapper.eq("is_inuse", "Y")
                .eq("doctor_code", usercode)
                .groupBy("sickness_code", "sickness_name", "item_code", "item_name")
                .orderByAsc("array_index");
        List<SpecialtyConfig> specityConfig = specialtyConfigMapper.selectList(wrapper);
//        List<SpecialtyConfig> specityConfig = sprcialtyViewDaoNew.getSpecityConfigMainDiag(usercode);
        List<String> listTemp = new ArrayList<String>();
        for (SpecialtyConfig entity : specityConfig) {
            if (!listTemp.contains(entity.getSicknessCode())) {
                listTemp.add(entity.getSicknessCode());
                Map<String, String> configMap = new HashMap<String, String>();
                configMap.put("code", entity.getSicknessCode());
                configMap.put("name", entity.getSicknessName());
                list.add(configMap);
            }
        }
        return list;
    }


    /**
     * 删除疾病
     *
     * @param sciknessCode
     */
    public int delSickness(String oid, String sciknessCode) {
        String usercode = SecurityCommonUtil.getLoginUserCode();
        int status = 0;
        if (usercode.equals(Config.getCiv_Admin(oid))) {
            Sickness sickness = new Sickness();
            sickness.setIsInuse("N");
            UpdateWrapper<Sickness> sicknessUpdateWrapper = new UpdateWrapper<>();
            sicknessUpdateWrapper.eq("sickness_code", sciknessCode);
            status = sicknessMapper.update(sickness, sicknessUpdateWrapper);
//            status = sprcialtyViewDaoNew.updateSicknessByCode(sciknessCode, "N");
        }
        SpecialtyIndicatorConfigEntity specialtyIndicatorConfigEntity = new SpecialtyIndicatorConfigEntity();
        specialtyIndicatorConfigEntity.setIsInuse("N");
        UpdateWrapper<SpecialtyIndicatorConfigEntity> speIndicatorConfigUpdateWrapper = new UpdateWrapper<>();
        speIndicatorConfigUpdateWrapper.eq("doctor_code", usercode).eq("sickness_code", sciknessCode);
        specialtyIndicatorConfigMapper.update(specialtyIndicatorConfigEntity, speIndicatorConfigUpdateWrapper);
//        sprcialtyViewDaoNew.updateSicknessIndicatorConfig(usercode, sciknessCode, "N");
        return status;
    }

    /**
     * 删除科室
     *
     * @param
     */
    public int delDeptConfig(String oid, String dept_code) {
        String usercode = SecurityCommonUtil.getLoginUserCode();
        int status = 0;
        if (usercode.equals(Config.getCiv_Admin(oid))) {
            QueryWrapper<SpecialtyDept> wrapper = new QueryWrapper<>();
            wrapper.eq("dept_code", dept_code);
            status = specialtyDeptMapper.delete(wrapper);
//            status = sprcialtyViewDaoNew.deleteDeptByCode(dept_code);
        }
        QueryWrapper<DeptSpecialtyConfig> deptSpecialtyConfigQueryWrapper = new QueryWrapper<>();
        deptSpecialtyConfigQueryWrapper.eq("dept_code", dept_code);
        deptSpecialtyConfigMapper.delete(deptSpecialtyConfigQueryWrapper);
//        sprcialtyViewDaoNew.delDeptSpecialtyConfig(dept_code);
        QueryWrapper<DeptSpecialtyIndicator> deptSpecialtyIndicatorQueryWrapper = new QueryWrapper<>();
        deptSpecialtyIndicatorQueryWrapper.eq("dept_code", dept_code);
        deptSpecialtyIndicatorMapper.delete(deptSpecialtyIndicatorQueryWrapper);
//        sprcialtyViewDaoNew.deleteDeptIndicatorConfig(dept_code);
        return status;
    }

    /**
     * 获取专科视图生命体征配置
     *
     * @param keyWord
     * @return
     */
    public List<Map<String, String>> getConfigHealth(String oid, String keyWord) {
        String config = Config.getHealthConfigs(oid);
        String[] configes = config.split(";");
        List<Map<String, String>> list = new ArrayList<Map<String, String>>();
        for (String conStr : configes) {
            String[] cons = conStr.split("\\|");
            Map<String, String> map = new HashMap<String, String>();
            if (StringUtils.isBlank(keyWord)) {
                map.put("LAB_ITEM_CODE", cons[0]);
                map.put("LAB_ITEM_NAME", cons[1]);
                map.put("DICT_ITEM_VALUE", cons[1]);
                map.put("DICT_ITEM_CODE", cons[0]);
                list.add(map);
            } else {
                if (keyWord.equals(cons[0])) {
                    map.put("LAB_ITEM_CODE", cons[0]);
                    map.put("LAB_ITEM_NAME", cons[1]);
                    map.put("DICT_ITEM_VALUE", cons[1]);
                    map.put("DICT_ITEM_CODE", cons[0]);
                    list.add(map);
                }
            }
        }
        return list;
    }

    /**
     * 查询生命体征
     *
     * @return
     */
    @Override
    public Page<Map<String, String>> getHealthData(String oid, String diag, String keyWord, int pageNo, int pageSize) {
        List<Map<String, String>> list = getConfigHealth(oid, keyWord);
        Page<SpecialtyIndicatorConfigEntity> page2 = getDetailData(oid, diag, "SicknessSMTZ", 0, 0);
        List<Map<String, String>> listMap = new ArrayList<Map<String, String>>();
        for (Map<String, String> map : list) {
            boolean isAdd = false;
            for (SpecialtyIndicatorConfigEntity entity : page2.getResult()) {
                if (map.get("DICT_ITEM_CODE").equals(entity.getItemIndicatorCode())) {
                    isAdd = true;
                    break;
                }
            }
            if (!isAdd) {
                listMap.add(map);
            }
        }
        Page<Map<String, String>> page = new Page<Map<String, String>>();
        page.setResult(listMap);
        page.setPageNo(pageNo);
        page.setPageSize(pageSize);
        page.setTotalCount(listMap.size());
        return page;
    }

    /**
     * 查询科室生命体征
     *
     * @param deptCode
     * @param keyWord
     * @param pageNo
     * @param pageSize
     * @return
     */
    @Override
    public Page<Map<String, String>> getDeptHealthData(String oid, String deptCode, String keyWord, int pageNo, int pageSize) {
        List<Map<String, String>> list = getConfigHealth(oid, keyWord);
        Page<Map<String, Object>> page2 = getDeptDetailData(oid, deptCode, "Health", 1, 1000);
        List<Map<String, String>> listMap = new ArrayList<Map<String, String>>();
        for (Map<String, String> map : list) {
            boolean isAdd = false;
            for (Map<String, Object> map2 : page2.getResult()) {
                if (map.get("DICT_ITEM_CODE").equals((String) map2.get("itemIndicatorCode"))) {
                    isAdd = true;
                    break;
                }
            }
            if (!isAdd) {
                listMap.add(map);
            }
        }
        Page<Map<String, String>> page = new Page<Map<String, String>>();
        page.setResult(listMap);
        page.setPageNo(pageNo);
        page.setPageSize(pageSize);
        page.setTotalCount(listMap.size());
        return page;
    }


    /**
     * 新增诊断数据
     *
     * @param pageNo
     * @param pageSize
     * @return
     */
    public Page<Map<String, String>> getDictData(String oid, String visitId,String sicknessCode, String type, String keyWord, int pageNo, int pageSize, DictType dictType) {
        String[] column = getColumnByType(oid, dictType);
        Page<Map<String, String>> page = new Page<Map<String, String>>();
        page.setPageNo(pageNo);
        page.setPageSize(pageSize);
        page = dictHbaseService.getNamebyDetailName(oid, visitId,dictType, column[1], keyWord, page, true);
        Page<SpecialtyIndicatorConfigEntity> page2 = getDetailData(oid, sicknessCode, type, 0, 0);
        List<Map<String, String>> list = new ArrayList<Map<String, String>>();
        for (Map<String, String> map : page.getResult()) {
            boolean isAdd = false;
            for (SpecialtyIndicatorConfigEntity entity : page2.getResult()) {
                if (map.get(column[0]).equals(entity.getItemIndicatorCode())) {
                    isAdd = true;
                    break;
                }
            }
            if (!isAdd) {
                Map<String, String> mapTemp = new HashMap<String, String>();
                mapTemp.put("DICT_ITEM_CODE", map.get("DICT_ITEM_CODE"));
                mapTemp.put("DICT_ITEM_VALUE", map.get("DICT_ITEM_VALUE"));
                mapTemp.put("orderName", map.get("orderName"));
                mapTemp.put("orderCode", map.get("orderCode"));
                list.add(mapTemp);
            }
        }
        page.setResult(list);
        page.setCountTotal(true);
        page.setTotalCount(page.getTotalCount());
        return page;
    }

    /**
     * 新增科室数据
     *
     * @param pageNo
     * @param pageSize
     * @return
     */
    public Page<Map<String, String>> getDeptDictData(String oid, String visitId, String deptCode, String type, String keyWord, int pageNo, int pageSize, DictType dictType) {
        String[] column = getColumnByType(oid, dictType);
        Page<Map<String, String>> page = new Page<Map<String, String>>();
        page.setPageNo(pageNo);
        page.setPageSize(pageSize);
        page = dictHbaseService.getNamebyDetailName(oid,visitId, dictType, column[1], keyWord, page, true);
        Page<Map<String, Object>> page2 = getDeptDetailData(oid, deptCode, type, 1, 100000);
        List<Map<String, String>> list = new ArrayList<Map<String, String>>();
        for (Map<String, String> map : page.getResult()) {
            boolean isAdd = false;
            for (Map<String, Object> map2 : page2.getResult()) {
                if (map.get(column[0]).equals((String) map2.get("itemIndicatorCode"))) {
                    isAdd = true;
                    break;
                }
            }
            if (!isAdd) {
                Map<String, String> mapTemp = new HashMap<String, String>();
                mapTemp.put("DICT_ITEM_CODE", map.get("DICT_ITEM_CODE"));
                mapTemp.put("DICT_ITEM_VALUE", map.get("DICT_ITEM_VALUE"));
                mapTemp.put("orderName", map.get("orderName"));
                mapTemp.put("orderCode", map.get("orderCode"));
                list.add(mapTemp);
            }
        }
        page.setResult(list);
        page.setCountTotal(true);
        page.setTotalCount(page.getTotalCount());
        return page;
    }

//    /**
//     * 专科视图获取检查字典数据
//     * @param deptCode
//     * @param type
//     * @param keyWord
//     * @param pageNo
//     * @param pageSize
//     * @param dictType
//     * @return
//     */
//    public Page<Map<String, String>> getExamDictData(String oid,String deptCode, String type, String keyWord, int pageNo, int pageSize, DictType dictType){
//         return  null;
//    }

    /**
     * 新增数据
     *
     * @param pageNo
     * @param pageSize
     * @return
     */
    public Page<Map<String, String>> getDictDiagData(String oid,String visitId, String keyWord, int pageNo, int pageSize, DictType dictType) {
        String[] column = getColumnByType(oid, dictType);
        Page<Map<String, String>> page = new Page<Map<String, String>>();
        page.setPageNo(pageNo);
        page.setPageSize(pageSize);
        page = dictHbaseService.getNamebyDetailName(oid, visitId,dictType, column[1], keyWord, page, false);
        return page;
    }

    /**
     * 去重
     *
     * @param page
     * @param pageDiag
     * @return
     */
    public Page<Map<String, String>> executeDuplicates(String oid, Page<Map<String, String>> page, Page<Sickness> pageDiag) {
        for (Map<String, String> map : page.getResult()) {
            map.put("status", "0");
            map.put("has_template", "无模板");
            for (Sickness entity : pageDiag.getResult()) {
                if (entity.getSicknessCode().equals(map.get("DICT_ITEM_CODE"))) {
                    map.put("status", "1");
                    map.put("has_template", "已有模板");
                    break;
                }
            }
        }
        return page;
    }

    /**
     * 去重
     *
     * @param page
     * @param list
     * @return
     */
    public Page<Map<String, String>> executeDuplicatesList(String oid, Page<Map<String, String>> page, List<Map<String, String>> list) {
        for (Map<String, String> map : page.getResult()) {
            map.put("status", "0");
            for (Map<String, String> mapTemp : list) {
                if (mapTemp.get("code").equals(map.get("DICT_ITEM_CODE"))) {
                    map.put("status", "1");
                    break;
                }
            }
        }
        return page;
    }


    /**
     * 去掉未配置模板的数据
     *
     * @param
     * @return
     */
    public Page<Map<String, String>> removeNoConfigData(String oid, Page<Sickness> pageDiag, List<Map<String, String>> listTemp) {
        Page<Map<String, String>> page = new Page<Map<String, String>>();
        List<Map<String, String>> list = new ArrayList<Map<String, String>>();
        for (Sickness entity : pageDiag) {
            Map<String, String> map = new HashMap<String, String>();
            map.put("name", entity.getSicknessName());
            map.put("code", entity.getSicknessCode());
            map.put("has_template", "已有模板");
            map.put("status", "0");
            for (Map<String, String> mapTemp : listTemp) {
                if (entity.getSicknessCode().equals(mapTemp.get("sickness_code"))) {
                    map.put("status", "1");
                    break;
                }
            }
            list.add(map);
        }

        //分页
        boolean pageable = true;
        if (pageDiag.getPageNo() == 0 || pageDiag.getPageSize() == 0) {
            pageable = false;
        } else {
            page.setPageNo(pageDiag.getPageNo());
            page.setPageSize(pageDiag.getPageSize());
        }
        if (pageable) {
            //分页
            ListPage<Map<String, String>> listPage = new ListPage<Map<String, String>>(list, pageDiag.getPageNo(),
                    pageDiag.getPageSize());
            page.setTotalCount(listPage.getTotalCount());
            page.setResult(listPage.getPagedList());
        } else {
            page.setTotalCount(list.size());
            page.setResult(list);
        }
        page.setPageNo(pageDiag.getPageNo());
        page.setPageSize(pageDiag.getPageSize());
        return page;
    }

    /**
     * 字典字段不一致处理
     *
     * @param dictType
     * @return
     */
    private String[] getColumnByType(String oid, DictType dictType) {
        String columnName = "DICT_ITEM_VALUE";
        String columnCode = "DICT_ITEM_CODE";
        switch (dictType) {
            case DIAG:
                columnName = "name";
                columnCode = "code";
                break;
            case EXAM:
                columnName = "DICT_ITEM_VALUE";
                columnCode = "DICT_ITEM_CODE";
                break;
            case LAB:
                columnName = "DICT_ITEM_VALUE";
                columnCode = "DICT_ITEM_CODE";
                break;
            case LABSUB:
                columnName = "DICT_ITEM_VALUE";
                columnCode = "DICT_ITEM_CODE";
                break;
            case DRUG:
                columnName = "DICT_ITEM_VALUE";
                columnCode = "DICT_ITEM_CODE";
                break;
            case NURSE:
                columnName = "DICT_ITEM_VALUE";
                columnCode = "DICT_ITEM_CODE";
                break;
        }
        return new String[]{columnCode, columnName};
    }
//
//    /**
//     * 添加诊断到mysql数据库
//     *
//     * @param sicknessCode
//     * @param sicknessname
//     */
    @Override
    public int addSickness(String oid,String sicknessCode, String sicknessname) {
        String userCode = SecurityCommonUtil.getLoginUserCode();
        String userName = SecurityCommonUtil.getCurrentLoginUser().getName();
        int res = 0;
        //首先判断是否删除过
        int updateStatus = -1;
        if (userCode.equals(Config.getCiv_Admin(oid))) {
            updateStatus = sprcialtyViewDaoNew.updateSicknessByCode(sicknessCode, "Y");
            if (updateStatus <= 0) {
                res = sprcialtyViewDaoNew.addSickness(sicknessCode, sicknessname);
                sprcialtyViewDaoNew.addSicknessIndicatorConfig(userCode, userName, sicknessCode, sicknessname);
            } else {
                res = updateStatus;
                sprcialtyViewDaoNew.updateSicknessIndicatorConfig(userCode, sicknessCode, "Y");
            }
        } else {
            res = sprcialtyViewDaoNew.updateSicknessIndicatorConfig(userCode, sicknessCode, "Y");
            if (res <= 0) {
                res = sprcialtyViewDaoNew.addSicknessIndicatorConfig(userCode, userName, sicknessCode, sicknessname);
            }
        }
        return res;
    }
//
//    /**
//     * 添加专科科室到mysql
//     *
//     * @param deptCode
//     * @param deptName
//     * @return
//     */
    @Override
    public int addSecurityDept(String oid,String deptCode, String deptName) {
        //先插入到civ_specity_dept
        int res = sprcialtyViewDaoNew.addSpecityDept(deptCode, deptName);
        //初始化civ_dept_specity_config数据
        if (res > 0) {
            res = sprcialtyViewDaoNew.addDeptSpecityConfig(deptCode, deptName);
        }
        return res;
    }

    /**
     * 查询mysql获取诊断的各项明细数据
     *
     * @param sicknessCode
     * @param type
     */
    public Page<SpecialtyIndicatorConfigEntity> getDetailData(String oid, String sicknessCode, String type, int pageNo, int pageSize) {
        String userCode = SecurityCommonUtil.getLoginUserCode();
        List<SpecialtyIndicatorConfigEntity> list = sprcialtyViewDaoNew.getConfigByUserCodeMainDaig(userCode, sicknessCode, type);
        Page<SpecialtyIndicatorConfigEntity> page = new Page<SpecialtyIndicatorConfigEntity>();
        //分页
        boolean pageable = true;
        if (pageNo == 0 || pageSize == 0) {
            pageable = false;
        } else {
            page.setPageNo(pageNo);
            page.setPageSize(pageSize);
        }
        if (pageable) {
            //分页
            ListPage<SpecialtyIndicatorConfigEntity> listPage = new ListPage<SpecialtyIndicatorConfigEntity>(list, page.getPageNo(),
                    page.getPageSize());
            page.setTotalCount(listPage.getTotalCount());
            page.setResult(listPage.getPagedList());
        } else {
            page.setTotalCount(list.size());
            page.setResult(list);
        }
        return page;
    }

    /**
     * 查询mysql获取科室的各项明细数据
     *
     * @param deptCode
     * @param type
     */
    @Override
    public Page<Map<String, Object>> getDeptDetailData(String oid, String deptCode, String type, int pageNo, int pageSize) {
        List<DeptSpecialtyIndicator> list = deptSpecialtyIndicatorMapper.getConfigByDept(deptCode, type);
//        List<SpecialtyDeptIndicatorConfig> list = sprcialtyViewDaoNew.getConfigByDept(deptCode, type);
        Page<Map<String, Object>> page = new Page<Map<String, Object>>();
        List<Map<String, Object>> listRes = new ArrayList<Map<String, Object>>();
        for (DeptSpecialtyIndicator entity : list) {
            Map<String, Object> map = new HashMap<String, Object>();
            map.put("id", entity.getId());
            map.put("itemIndicatorCode", entity.getSubItemCode());
            map.put("itemIndicatorName", entity.getSubItemName());
            listRes.add(map);
        }
        //分页
        ListPage<Map<String, Object>> listPage = new ListPage<Map<String, Object>>(listRes, pageNo, pageSize);
        page.setTotalCount(list.size());
        page.setResult(listPage.getPagedList());
        page.setPageSize(pageSize);
        page.setPageNo(pageNo);
        return page;
    }

//    /**
//     * 删除诊断明细
//     *
//     * @param id
//     */
    public int delInditorConfig(String oid,String id) {
        return sprcialtyViewDaoNew.delIndicatorConfig(id);
    }

//    /**
//     * 删除科室诊断明细
//     *
//     * @param id
//     */
    public int delDeptInditorConfig(String oid,String id) {
        return sprcialtyViewDaoNew.delDeptIndicatorConfig(id);
    }
//
    /**
     * 新增诊断细项
     */
    @Override
    public int addInditorConfig(String oid,Map<String, String> map) {
        String dataType = "Table";
        switch (map.get("itemCode")) {
            case "SicknessSMTZ":
                dataType = "FoldLine";
                break;
            case "LabDetails":
                dataType = "FoldLine";
                break;
//            case "NurseAndEat":
//                dataType = "Table";
//                break;
            case "Exams":
                dataType = "List";
                break;
            case "Drugs":
                dataType = "Table";
                break;
            case "Labs":
                dataType = "List";
                break;
        }
        String userCode = SecurityCommonUtil.getLoginUserCode();
        String userName = SecurityCommonUtil.getCurrentLoginUser().getName();
        map.put("dataType", dataType);
        map.put("userCode", userCode);
        map.put("userName", userName);
        String  index = sprcialtyViewDaoNew.getInditorMaxIndex(userCode,map.get("mainDiag"),map.get("itemCode"));
        map.put("arrayIndex", index);
        return sprcialtyViewDaoNew.addInditorConfig(map);
    }

    /**
     * 新增科室细项
     */
    @Override
    public int addDeptInditorConfig(String oid,Map<String, String> map) {
        String dataType = "Table";
        switch (map.get("itemCode")) {
            case "Health":
                dataType = "FoldLine";
                break;
            case "LabDetail":
                dataType = "FoldLine";
                break;
            case "NurseAndEat":
                dataType = "Table";
                break;
            case "Exam":
                dataType = "List";
                break;
            case "Drag":
                dataType = "Table";
                break;
            case "Lab":
                dataType = "List";
                break;
        }
        map.put("dataType", dataType);
        //先查詢出已經添加的數量
        String  index = sprcialtyViewDaoNew.getDeptInditorMaxIndex(map.get("deptCode"),map.get("itemCode"));
        map.put("arrayIndex", index);
        return sprcialtyViewDaoNew.addDeptInditorConfig(map);
    }


    /**
     * 通过科室查询科室 专科视图下 默认的配置
     *
     * @param
     * @param
     * @return
     */
    public List<DeptSpecialtyIndicator> getDeptConfig(String oid, String deptCode, String itemCode, String subItemCode) {
        List<DeptSpecialtyIndicator> result = new ArrayList<>();
        result = specialtyDeptConfigDao.getDeptSpecialtyConfig(deptCode, itemCode, subItemCode);
        return result;
    }

    /**
     * 查找专科设置
     *
     * @param mainDiag
     * @return
     */
    @Override
    public List<Map<String, String>> getSpecialtyConfig(String oid, String mainDiag, String itemCode, String deptCode) {
        List<Map<String, String>> result = new ArrayList<Map<String, String>>();
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        String username = authentication.getName();
        //先查找医生自定义设置
        List<SpecialtyIndicatorConfigEntity> list = sprcialtyViewDaoNew.getConfigByUserCodeMainDaig(username, mainDiag, itemCode);
        if (list == null || list.size() == 0) {
            //查找管理员定义设置
            list = sprcialtyViewDaoNew.getConfigByUserCodeMainDaig(Config.getCiv_Admin(oid), mainDiag, itemCode);
        }
        if (list == null || list.size() == 0) {
            //查找科室设置
            List<DeptSpecialtyIndicator> deptList = getDeptConfig(oid, deptCode, itemCode, "");
            for (DeptSpecialtyIndicator entity : deptList) {
                Map<String, String> map = new HashMap<String, String>();
                map.put("itemCode", entity.getItemCode());
                map.put("itemName", entity.getItemName());
                map.put("subItemCode", entity.getSubItemCode());
                map.put("subItemName", entity.getSubItemName());
                result.add(map);
            }
        } else {
            for (SpecialtyIndicatorConfigEntity entity : list) {
                Map<String, String> map = new HashMap<String, String>();
                map.put("itemCode", entity.getItemCode());
                map.put("itemName", entity.getItemName());
                map.put("subItemCode", entity.getItemIndicatorCode());
                map.put("subItemName", entity.getItemIndicatorName());
                result.add(map);
            }
        }
        return result;
    }
}
