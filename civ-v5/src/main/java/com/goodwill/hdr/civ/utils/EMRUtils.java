package com.goodwill.hdr.civ.utils;

import com.goodwill.hdr.civ.config.Config;

import java.util.*;



public class EMRUtils {





    /**
     * 将病历文件拆分为元素
     *
     * @param mrContentText
     * @return
     */
    public static Map<String, String> EMRElement(String oid, String mrContentText) {
        String replaceField = Config.getCIV_EMR_REPLACE_FIELD(oid);
        String splitField = Config.getCIV_EMR_SPLIT_FIELD(oid);
        String[] relpaces = replaceField.split(";");
        for (int i = 0; i < relpaces.length; i++) {
            String[] info = relpaces[i].split(",");
            mrContentText = mrContentText.replace(info[0], info[1]);
        }
        mrContentText = mrContentText.replaceAll("( |　|	|	)", "");//英文空格、中文空格、英文tab、中文tab,将其替换掉
        mrContentText = mrContentText.replaceAll("(\r\n|\r|\n|\n\r)", " ");//替换回车
        String[] eleType = splitField.split(",");
        Map<Integer, String> eleIndexMap = new TreeMap<Integer, String>();
        List<Integer> indexList = new ArrayList<Integer>();
        Map<String, String> eleMap = new HashMap<String, String>();
        //先查找到各个关键元素在病历文件中的位置
        for (String ele : eleType) {
            int eleIndex = mrContentText.indexOf(ele);
            if (eleIndex >= 0) {
                eleIndexMap.put(eleIndex, ele);
                indexList.add(eleIndex);
            }
        }
        indexList = Utils.sortList(indexList, "asc");

        //根据位置集合循环取出每个元素
        if (!eleIndexMap.isEmpty()) {
            for (int i = 0; i < indexList.size() - 1; i++) {
                //获取元素名称
                String ele = eleIndexMap.get(indexList.get(i));
                //获取元素索引
                int eleIndex = indexList.get(i);
                int eleIndex2 = indexList.get(i + 1);
                String eleText = "";
                if (i == indexList.size() - 1) {
                    //最后一个元素
                    eleText = mrContentText.substring(eleIndex);
                } else {
                    //普通元素
                    eleText = mrContentText.substring(eleIndex, eleIndex2);

                }
                //过滤掉开头的分段名称
                eleText = eleText.replaceAll("(" + ele + ":|" + ele + "：|" + ele + ")", " ");
                eleMap.put(ele, eleText);
            }
        } else {
            return eleMap;
        }
        return eleMap;

    }



}
