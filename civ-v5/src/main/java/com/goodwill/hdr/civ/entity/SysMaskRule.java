package com.goodwill.hdr.civ.entity;
import com.baomidou.mybatisplus.annotation.TableName;

import java.io.Serializable;

/**
 * 脱敏规则表(SysMaskRule)实体类
 *
 * <AUTHOR>
 * @since 2024-09-24 10:57:29
 */
@TableName("civ_sys_mask_rule")
public class SysMaskRule implements Serializable {
    private static final long serialVersionUID = 680860974874737799L;
    
    private String id;
    /**
     * 脱敏规则编码
     */
    private String configcode;
    /**
     * 脱敏规则名称
     */
    private String configname;
    /**
     * 脱敏规则值
     */
    private String configvalue;
    
    private String configdesc;
    
    private String params;
    
    private String enabled;
    
    private String oid;


    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getConfigcode() {
        return configcode;
    }

    public void setConfigcode(String configcode) {
        this.configcode = configcode;
    }

    public String getConfigname() {
        return configname;
    }

    public void setConfigname(String configname) {
        this.configname = configname;
    }

    public String getConfigvalue() {
        return configvalue;
    }

    public void setConfigvalue(String configvalue) {
        this.configvalue = configvalue;
    }

    public String getConfigdesc() {
        return configdesc;
    }

    public void setConfigdesc(String configdesc) {
        this.configdesc = configdesc;
    }

    public String getParams() {
        return params;
    }

    public void setParams(String params) {
        this.params = params;
    }

    public String getEnabled() {
        return enabled;
    }

    public void setEnabled(String enabled) {
        this.enabled = enabled;
    }

    public String getOid() {
        return oid;
    }

    public void setOid(String oid) {
        this.oid = oid;
    }

}

