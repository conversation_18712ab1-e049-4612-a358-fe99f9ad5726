package com.goodwill.hdr.civ.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.io.Serializable;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2022-03-03
 */
@TableName("civ_timeaxis_config")
public class TimeaxisConfig implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    private String usercode;

    private String patientid;

    private String visittype;

    private String visitid;

    private String itemcode;

    private String itemname;

    private String subitemcode;

    private String subitemname;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getUsercode() {
        return usercode;
    }

    public void setUsercode(String usercode) {
        this.usercode = usercode;
    }

    public String getPatientid() {
        return patientid;
    }

    public void setPatientid(String patientid) {
        this.patientid = patientid;
    }

    public String getVisittype() {
        return visittype;
    }

    public void setVisittype(String visittype) {
        this.visittype = visittype;
    }

    public String getVisitid() {
        return visitid;
    }

    public void setVisitid(String visitid) {
        this.visitid = visitid;
    }

    public String getItemcode() {
        return itemcode;
    }

    public void setItemcode(String itemcode) {
        this.itemcode = itemcode;
    }

    public String getItemname() {
        return itemname;
    }

    public void setItemname(String itemname) {
        this.itemname = itemname;
    }

    public String getSubitemcode() {
        return subitemcode;
    }

    public void setSubitemcode(String subitemcode) {
        this.subitemcode = subitemcode;
    }

    public String getSubitemname() {
        return subitemname;
    }

    public void setSubitemname(String subitemname) {
        this.subitemname = subitemname;
    }

    @Override
    public String toString() {
        return "TimeaxisConfig{" +
                "id=" + id +
                ", usercode=" + usercode +
                ", patientid=" + patientid +
                ", visittype=" + visittype +
                ", visitid=" + visitid +
                ", itemcode=" + itemcode +
                ", itemname=" + itemname +
                ", subitemcode=" + subitemcode +
                ", subitemname=" + subitemname +
                "}";
    }
}
