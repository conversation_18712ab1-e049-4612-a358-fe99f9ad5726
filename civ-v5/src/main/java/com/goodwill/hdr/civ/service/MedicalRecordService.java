package com.goodwill.hdr.civ.service;


import com.goodwill.hdr.core.orm.Page;
import com.goodwill.hdr.web.common.vo.ResultVO;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 * @Description 类描述：病历文书服务接口
 * @Date 2018年5月3日
 * @modify 修改记录：
 */
public interface MedicalRecordService {

    /**
     * @param patientId 患者编号
     * @param visitId   就诊次数
     * @param visitType 就诊类型
     * @param orderBy   排序字段
     * @param orderDir  排序规则
     * @param pageNo    页码
     * @param pageSize  分页单位
     * @return
     * @Description 方法描述: 查询患者某次就诊的病历文书
     */
    public Page<Map<String, String>> getTechnologiesMedicalRecordList(String oid, String patientId, String visitId, String visitType,
                                                                      String orderBy, String orderDir, int pageNo, int pageSize, String emrTypeCode);

    /**
     * @param patientId 患者编号
     * @param visitId   就诊次数
     * @return
     * @Description 方法描述: 当前视图 - 末次住院病历类型
     */
    public List<Map<String, String>> getCVMrTypes( String oid, String patientId, String visitId);

    /**
     * @param patientId 患者编号
     * @param visitId   就诊次数
     * @param type      病历类型编码
     * @param pageNo    页码
     * @param pageSize  分页单位
     * @return
     * @Description 方法描述: 当前视图 - 末次住院病历列表
     */
    public Page<Map<String, String>> getCVMrList(String oid, String patientId, String visitId, String type, int pageNo, int pageSize);

    /**
     * @param
     * @return 病历文书详情
     * @Description 方法描述:查询病历文书详情
     */
    public Map<String, String> getMedicalRecordDetails(String fileNo, String oid, String patientId, String visitId, String visitTypeCode, String mrClassCode, String sysCode);

    /**
     * @param patientId 患者编号
     * @param visitId   就诊次数
     * @param visitType 就诊类型
     * @return
     * @Description 方法描述: 某次就诊的病历文书数量
     */
    public long getMRCount(String oid, String patientId, String visitId, String visitType);


    /**
     * @param patientId 患者编号
     * @return 结果
     * @Description 方法描述:查询患者所有的病历文书
     */
    public List<Map<String, Object>> getMedicalRecords( String oid, String patientId, String visitType, String outPatientId, String year, String key, String type, String click_Type);

    /**
     * 获取病例类型或病例总数
     *
     * @param resultMap
     */
    public void getAllMRCount(  Map<String, Object> resultMap, String outPatientId);

    /**
     * @param patientId 患者编号
     * @return 结果
     * @Description 方法描述:查询患者所有的病历文书类型
     */
    public List<Map<String, String>> getMedicalTypes(String this_oid, String oid, String patientId, String outPatientId, String visitType);


    Map<String, String> getDgHtmlText(String oid, String pid, String vid, String mrClassCode, String fileUniqueId);

    String getPdfData(String imgStr, String oid, String patienId, String visitId, String fileNo, Map<String, String> result, String path);
//
//    /**
//     * 获取文书类型列表
//     *
//     * @return
//     */
//    List<Map<String, String>> getEmrTypes(String oid);


//    /**
//     * 根据pid vid获取患者病历文书所有类型
//     *
//     * @param patientId
//     * @param visitId
//     * @param visitType
//     * @return
//     */
//    List<Map<String, Object>> getEmrTypesByPidVid(String oid, String patientId, String visitId, String visitType);

    /**
     * @param patientId 患者编号
     * @param visitId   就诊次数
     * @param visitType 就诊类型
     * @param orderBy   排序字段
     * @param orderDir  排序规则
     * @return
     * @Description 方法描述: 根据文书类型查询患者某次就诊的病历文书
     */
    public List<Map<String, Object>> getMedicalRecordListByMrClassCode(String oid, String patientId, String visitId, String visitType,
                                                                       String orderBy, String orderDir);

    String getMrText( String oid, String patientId, String visitId, String visitType);

    ResultVO<Set<Map.Entry<String, String>>> getMedicalRecordInfo( String oid, String patientId, String visitId, String visitType);


    ResultVO<List<Map<String, Object>>> getTechnologiesMedicalRecordList( String oid, String oidLast, String lastPatientId, String lastVisitId, String lastVisitTypeCode, String outPatientId);
}
