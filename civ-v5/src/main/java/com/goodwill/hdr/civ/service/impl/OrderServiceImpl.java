package com.goodwill.hdr.civ.service.impl;


import com.fasterxml.jackson.databind.ObjectMapper;
import com.goodwill.hdr.civ.config.Config;
import com.goodwill.hdr.civ.config.ConfigCache;
import com.goodwill.hdr.civ.dao.VisitReportsOverTimeDao;
import com.goodwill.hdr.civ.entity.CmrReportTimeDuration;
import com.goodwill.hdr.civ.enums.HdrConstantEnum;
import com.goodwill.hdr.civ.enums.HdrTableEnum;
import com.goodwill.hdr.civ.enums.VisitTypeEnum;
import com.goodwill.hdr.civ.service.ConfigService;
import com.goodwill.hdr.civ.service.InspectReportService;
import com.goodwill.hdr.civ.service.OrderService;
import com.goodwill.hdr.civ.service.SpecialtyViewPowerService;
import com.goodwill.hdr.civ.utils.CivUtils;
import com.goodwill.hdr.civ.utils.ColumnUtil;
import com.goodwill.hdr.civ.utils.ListPage;
import com.goodwill.hdr.civ.utils.Utils;
import com.goodwill.hdr.civ.vo.OclTableConfigVo;
import com.goodwill.hdr.civ.vo.OrderShowConfigVo;
import com.goodwill.hdr.civ.vo.ResultVo;
import com.goodwill.hdr.core.orm.MatchType;
import com.goodwill.hdr.core.orm.Page;
import com.goodwill.hdr.hbase.bo.PropertyFilter;
import com.goodwill.hdr.hbase.dto.responseVo.PageResultVo;
import com.goodwill.hdr.hbaseQueryClient.builder.PageRequestBuilder;
import com.goodwill.hdr.hbaseQueryClient.service.HbaseQueryClient;
import com.goodwill.hdr.web.common.vo.ResultVO;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.Duration;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

import static com.goodwill.hdr.civ.utils.CivUtils.strToFilter;
import static com.goodwill.hdr.civ.utils.Utils.*;

//
//import java.io.IOException;
//import java.util.*;
//
//import static com.goodwill.hdr.civ.utils.Utils.getEndPageNum;
//import static com.goodwill.hdr.civ.utils.Utils.getStartPageNum;
//
@Service
public class OrderServiceImpl implements OrderService {

    protected Logger logger = LoggerFactory.getLogger(getClass());

    //    @Autowired
//    private AdrMonitorEventDao adrMnDao;
    @Autowired
    private ObjectMapper objectMapper;
    @Autowired
    private VisitReportsOverTimeDao visitReportsOverTimeDao;
    @Autowired
    private SpecialtyViewPowerService specialtyViewPowerService;
    @Autowired
    private ConfigService configService;
    @Autowired
    private InspectReportService inspectReportService;
    @Autowired
    private OrderService orderService;
    @Autowired
    private CommonModuleService commonModuleService;
    @Resource
    private CivUtils civUtils;
    private final HbaseQueryClient hbaseQueryClient;

    public OrderServiceImpl(HbaseQueryClient hbaseQueryClient) {
        this.hbaseQueryClient = hbaseQueryClient;
    }

    /**
     * 创建Filter
     *
     * @param columnName
     * @param keyword
     * @param filters
     */
    public static void createPropertyFilter(String columnName, String keyword, String MatchType,
                                            List<PropertyFilter> filters) {
        if (StringUtils.isNotBlank(keyword)) {
            PropertyFilter filter1 = new PropertyFilter();
            filter1.setMatchType(MatchType);
            filter1.setPropertyName(columnName);
            filter1.setPropertyValue(keyword);
            //filter1.setPropertyType("STRING");
            filters.add(filter1);
        }
    }


    //医嘱类别，属于哪个底下的医嘱查看用
    public String getOrderType(String oid, String visitType, String orderClassCode, String orderType) {
        if (StringUtils.isBlank(orderClassCode)) {
            return "OTHERS";
        }
        List<String> KF = null;
        List<String> JMZS = null;
        List<String> QTYP = null;
        List<String> LAB = null;
        List<String> EXAM = null;
        List<String> OPER = null;
        if ("OUTPV".equals(visitType)) {
//            KF = civUtils.getOrderClass(oid, "OrderClose_MZ_DRUG_KF");
//            JMZS = civUtils.getOrderClass(oid, "OrderClose_MZ_DRUG_JMZS");
//            QTYP = civUtils.getOrderClass(oid, "OrderClose_MZ_DRUG_QTYP");
//            LAB = civUtils.getOrderClass(oid, "OrderClose_MZ_LAB");
//            EXAM = civUtils.getOrderClass(oid, "OrderClose_MZ_EXAM");
//            OPER = civUtils.getOrderClass(oid, "OrderClose_MZ_OPER ");
            KF = getOrder(oid, "OrderClose_MZ_DRUG_KF");
            JMZS = getOrder(oid, "OrderClose_MZ_DRUG_JMZS");
            QTYP = getOrder(oid, "OrderClose_MZ_DRUG_QTYP");
            LAB = getOrder(oid, "OrderClose_MZ_LAB");
            EXAM = getOrder(oid, "OrderClose_MZ_EXAM");
            OPER = getOrder(oid, "OrderClose_MZ_OPER");
        } else if ("INPV".equals(visitType)) {
//            KF = civUtils.getOrderClass(oid, "OrderClose_DRUG_KF");
//            JMZS = civUtils.getOrderClass(oid, "OrderClose_DRUG_JMZS");
//            QTYP = civUtils.getOrderClass(oid, "OrderClose_DRUG_QTYP");
//            LAB = civUtils.getOrderClass(oid, "OrderClose_LAB");
//            EXAM = civUtils.getOrderClass(oid, "OrderClose_EXAM");
//            OPER = civUtils.getOrderClass(oid, "OrderClose_OPER");
            KF = getOrder(oid, "OrderClose_DRUG_KF");
            JMZS = getOrder(oid, "OrderClose_DRUG_JMZS");
            QTYP = getOrder(oid, "OrderClose_DRUG_QTYP");
            LAB = getOrder(oid, "OrderClose_LAB");
            EXAM = getOrder(oid, "OrderClose_EXAM");
            OPER = getOrder(oid, "OrderClose_OPER");
        }
        if (("KF".equals(orderType) || "ALL".equals(orderType))
                && KF != null && KF.size() > 0 && KF.contains(orderClassCode)) {
            return "KF";
        } else if (("JM".equals(orderType) || "ALL".equals(orderType))
                && JMZS != null && JMZS.size() > 0 && JMZS.contains(orderClassCode)) {
            return "JM";
        } else if (("QTYP".equals(orderType) || "ALL".equals(orderType))
                && QTYP != null && QTYP.size() > 0 && QTYP.contains(orderClassCode)) {
            return "QT";
        } else if (("JY".equals(orderType) || "ALL".equals(orderType))
                && LAB != null && LAB.size() > 0 && LAB.contains(orderClassCode)) {
            return "JY";
        } else if (("JC".equals(orderType) || "ALL".equals(orderType))
                && EXAM != null && EXAM.size() > 0 && EXAM.contains(orderClassCode)) {
            return "JC";
        } else if (("SS".equals(orderType) || "ALL".equals(orderType))
                && OPER != null && OPER.size() > 0 && OPER.contains(orderClassCode)) {
            return "SS";
        } else {
            return "OTHERS";
        }
    }

    public List<String> getOrder(String oid, String configCode) {
        List<String> orderClass = new ArrayList<String>();
        List<com.goodwill.hdr.civ.entity.Config> list = configService.getConfigBycode(oid, configCode);
        if (list != null && list.size() > 0) {
            String temp = list.get(0).getConfigValue();
            String orderClose = StringUtils.isNotBlank(temp) ? temp.trim() : "";
            if (StringUtils.isNotBlank(orderClose)) {
                String[] items = orderClose.split(",");
                for (String item : items) {
                    orderClass.add(item);
                }
            }
        }
        return orderClass;
    }

    @Override
    public Page<Map<String, String>> getOperOrderListMZ(String oid, String patId, String visitId, List<String> types,
                                                        String filterStr, String orderBy, String orderDir, int pageNo, int pageSize) {
        Page<Map<String, String>> page = getOrderListMZ(oid, patId, visitId, types, filterStr, "", orderBy, orderDir, pageNo,
                pageSize);
        //处理手术医嘱  补全数据
        for (Map<String, String> map : page) {
            String orderNO = map.get("ORDER_NO");
            //拼接手术申请数据
            List<PropertyFilter> filters1 = new ArrayList<PropertyFilter>();
            createPropertyFilter("OPER_APPLY_NO", orderNO, MatchType.EQ.getOperation(), filters1);
            List<Map<String, String>> operApplyList = new ArrayList<>();
            com.goodwill.hdr.hbase.dto.responseVo.ResultVo<PageResultVo<Map<String, String>>> resultVo1 = hbaseQueryClient.getPageByCondition(
                    PageRequestBuilder.init()
                            .tableName(HdrTableEnum.HDR_OPER_APPLY.getCode())
                            .patientId(patId)
                            .oid(oid)
                            .visitId(visitId)
                            .visitTypeCode("")
                            .filters(filters1)
                            .pageNo(0)
                            .pageSize(0)
                            .orderBy("")
                            .desc()
                            .column("OPERATION_NAME", "DIAG_BEFORE_OPERATION_NAME", "PLAN_OPER_DOCTOR_NAME",
                                    "PLAN_OPER_TIME", "APPLY_OPER_TIME")
                            .build());
            if (resultVo1.isSuccess()) {
                operApplyList = resultVo1.getContent().getResult();
            }
//            List<Map<String, String>> operApplyList = hbaseDao.findConditionByPatient("HDR_OPER_APPLY", oid,
//                    patId, filters1,
//                    new String[]{"OPERATION_NAME", "DIAG_BEFORE_OPERATION_NAME", "PLAN_OPER_DOCTOR_NAME",
//                            "PLAN_OPER_TIME", "APPLY_OPER_TIME"});
            if (operApplyList.size() > 0) {
                map.put("ORDER_ITEM_NAME",
                        Utils.objToStr(operApplyList.get(0).get("OPERATION_NAME"), map.get("ORDER_ITEM_NAME")));
                map.put("DIAG_BEFORE_OPERATION_NAME",
                        Utils.objToStr(operApplyList.get(0).get("DIAG_BEFORE_OPERATION_NAME")));
                map.put("PLAN_OPER_DOCTOR_NAME", Utils.objToStr(operApplyList.get(0).get("PLAN_OPER_DOCTOR_NAME")));
                map.put("PLAN_OPER_TIME", Utils.objToStr(operApplyList.get(0).get("PLAN_OPER_TIME"))); //拟手术日期
                map.put("APPLY_OPER_TIME", Utils.objToStr(operApplyList.get(0).get("APPLY_OPER_TIME"))); //申请手术时间
            }

            //拼接手术过程数据
            List<PropertyFilter> filters2 = new ArrayList<PropertyFilter>();
            createPropertyFilter("ORDER_NO", orderNO, MatchType.EQ.getOperation(), filters2);
            List<Map<String, String>> operAnaesList = new ArrayList<>();
            com.goodwill.hdr.hbase.dto.responseVo.ResultVo<PageResultVo<Map<String, String>>> resultVo2 = hbaseQueryClient.getPageByCondition(
                    PageRequestBuilder.init()
                            .tableName(HdrTableEnum.HDR_OPER_ANAES.getCode())
                            .patientId(patId)
                            .oid(oid)
                            .visitId(visitId)
                            .visitTypeCode("")
                            .filters(filters2)
                            .pageNo(0)
                            .pageSize(0)
                            .orderBy("")
                            .desc()
                            .column("ANESTHESIA_END_TIME", "ANESTHESIA_DOCTOR_NAME")
                            .build());
            if (resultVo2.isSuccess()) {
                operAnaesList = resultVo2.getContent().getResult();
            }
//            List<Map<String, String>> operAnaesList = hbaseDao.findConditionByPatient("HDR_OPER_ANAES", oid,
//                    patId, filters2,
//                    new String[]{"ANESTHESIA_END_TIME", "ANESTHESIA_DOCTOR_NAME"});
            if (operAnaesList.size() > 0) {
                map.put("ANESTHESIA_END_TIME", Utils.objToStr(operAnaesList.get(0).get("ANESTHESIA_END_TIME")));
                map.put("ANESTHESIA_DOCTOR_NAME", Utils.objToStr(operAnaesList.get(0).get("ANESTHESIA_DOCTOR_NAME")));
            }

        }
        return page;
    }


    //
//    @Autowired
//    private VisitReportsOverTimeDao visitReportsOverTimeDao;
//
//    @Autowired
//    private InspectReportServiceImpl inspectReportService;
//
    @Override
    public Page<Map<String, String>> getOrderList(String oid, String patId, String visitId, String visitType,
                                                  List<String> orderClass, String filterString, String orderReporte, String orderby, String orderdir,
                                                  String mainDiag, String orderCode, String deptCode, List<Map<String, Object>> configMapList, int pageNo, int pageSize,String pharmacyWay) {
        //查询患者本次就诊的医嘱，医嘱性质包含长期和临时，医嘱状态包含下达/审核/开始/停止，将状态为撤销的医嘱排除

        //判断就诊类型
        String tableName = null;
        if ("OUTPV".equals(visitType)) { //门诊
            tableName = HdrTableEnum.HDR_OUT_ORDER.getCode();
        } else if ("INPV".equals(visitType)) { //住院
            tableName = HdrTableEnum.HDR_IN_ORDER.getCode();
        }

        //查询条件
        List<PropertyFilter> filters = new ArrayList<PropertyFilter>();

        //排除状态为撤销的医嘱
        if (StringUtils.isNotBlank(Config.getOrderStatus(oid))) {
            createPropertyFilter("ORDER_STATUS_NAME", Config.getOrderStatus(oid), MatchType.NOTIN.getOperation(), filters);
        }

        //医嘱类别   药品，检查，检验...
        if (orderClass != null && orderClass.size() > 0) {
            String orderClassString = "";
            for (String orderString : orderClass) {
                orderClassString += orderString + ",";
            }
            orderClassString = orderClassString.substring(0, orderClassString.length() - 1);
            createPropertyFilter("ORDER_CLASS_CODE", orderClassString, MatchType.IN.getOperation(), filters);
        }
        //医嘱性质 和 医嘱状态 或其他条件
        strToFilter(filterString, filters, ";");
        //获取列包括 医嘱号、父医嘱号、医嘱类别、医嘱性质、医嘱项、开始时间、结束时间、医嘱状态、开据医生
        //读取出医嘱列表，分页查询  按医嘱时间倒序排列
        Page<Map<String, String>> page = new Page<Map<String, String>>();
        //分页判断
        boolean pageable = true;
        if (pageNo == 0 || pageSize == 0) {
            pageable = false;
        } else {
            page.setPageNo(1);
            page.setPageSize(100000);
        }
        //排序
        if (StringUtils.isBlank(orderby) || StringUtils.isBlank(orderdir)) {
            page.setOrderBy("ORDER_TIME");
            page.setOrderDir("desc");
        } else {
            page.setOrderBy(orderby);
            page.setOrderDir(orderdir);
        }

        Optional<String> orderField = Optional.empty();
        for (Map<String, Object> headMap : configMapList) {
            String name = (String) headMap.get("name");
            String column = (String) headMap.get("field");
            if (name.equals(orderby)) {
                orderField = Optional.of(column);
            }
        }

        if (pageable) {
            com.goodwill.hdr.hbase.dto.responseVo.ResultVo<PageResultVo<Map<String, String>>> resultVo2 = hbaseQueryClient.getPageByCondition(
                    PageRequestBuilder.init()
                            .tableName(tableName)
                            .patientId(patId)
                            .oid(oid)
                            .visitId(visitId)
                            .visitTypeCode("")
                            .filters(filters)
                            .pageNo(0)
                            .pageSize(0)
                            .orderBy("")
                            .desc()
                            .column()
                            .build());
            if (resultVo2.isSuccess()) {
                page.setResult(resultVo2.getContent().getResult());
                page.setTotalCount(resultVo2.getContent().getTotal());
            }
            //page = hbaseDao.findPageConditionByPatientVisitId(tableName, oid, patId, visitId, page, filters);
            List<Map<String, String>> tempResultList = page.getResult();
            String orgOid=ConfigCache.getCache(oid, "org_oid");
            if("E48000538".equalsIgnoreCase(orgOid)) {//鞍钢集团公司总医院
                logger.info("进入"+orgOid+orgOid+"定制化代码");
                tempResultList.forEach(map -> {
                    String orderNo = map.get("ORDER_NO");
                    if (orderNo != null && !orderNo.isEmpty()) {
                        String[] parts = orderNo.split("\\|\\|");
                        if (parts.length >= 2) {
                            map.put("orderColumn", parts[1]); // 覆盖原键
                        }
                    }
                });
                tempResultList = tempResultList.stream()
                        .sorted((map1, map2) -> {
                            try {
                                int num1 = Integer.parseInt(map1.getOrDefault("orderColumn", "0"));
                                int num2 = Integer.parseInt(map2.getOrDefault("orderColumn", "0"));
                                return Integer.compare(num2, num1);
                            } catch (NumberFormatException e) {
                                return 0;
                            }
                        })
                        .collect(Collectors.toList());
            }else {
                if ("desc".equals(orderdir)) {
                    Optional<String> finalOrderField = orderField;
                    tempResultList = tempResultList.stream().sorted((map1, map2) -> map2.get(finalOrderField.orElse("ORDER_TIME")).compareToIgnoreCase(map1.get(finalOrderField.orElse("ORDER_TIME")))).collect(Collectors.toList());
                } else {
                    Optional<String> finalOrderField = orderField;
                    tempResultList = tempResultList.stream().sorted((map1, map2) -> map1.get(finalOrderField.orElse("ORDER_TIME")).compareToIgnoreCase(map2.get(finalOrderField.orElse("ORDER_TIME")))).collect(Collectors.toList());
                }
            }
            List<Map<String, String>> subList = tempResultList.subList(getStartPageNum(pageNo, pageSize), getEndPageNum(pageNo, pageSize, page.getTotalCount()));
            page.setResult(subList);
            page.setPageSize(pageSize);
            page.setPageNo(pageNo);


        } else {
            List<Map<String, String>> list = new ArrayList<>();
            com.goodwill.hdr.hbase.dto.responseVo.ResultVo<PageResultVo<Map<String, String>>> resultVo2 = hbaseQueryClient.getPageByCondition(
                    PageRequestBuilder.init()
                            .tableName(tableName)
                            .patientId(patId)
                            .oid(oid)
                            .visitId(visitId)
                            .visitTypeCode("")
                            .filters(filters)
                            .pageNo(0)
                            .pageSize(0)
                            .orderBy("")
                            .desc()
                            .column()
                            .build());
            if (resultVo2.isSuccess()) {
                list = resultVo2.getContent().getResult();
            }
            // List<Map<String, String>> list = hbaseDao.findConditionByPatientVisitId(tableName, oid, patId, visitId, filters);
            page.setTotalCount(list.size());
            //排序
            if ("desc".equals(orderdir)) {
                Optional<String> finalOrderField = orderField;
                list = list.stream().sorted((map1, map2) -> map2.get(finalOrderField.orElse("ORDER_TIME")).compareToIgnoreCase(map1.get(finalOrderField.orElse("ORDER_TIME")))).collect(Collectors.toList());
            } else {
                Optional<String> finalOrderField = orderField;
                list = list.stream().sorted((map1, map2) -> map1.get(finalOrderField.orElse("ORDER_TIME")).compareToIgnoreCase(map2.get(finalOrderField.orElse("ORDER_TIME")))).collect(Collectors.toList());
            }

            //增加设置数据集代码-pjp
            page.setResult(list);

            if (StringUtils.isNotBlank(mainDiag) || StringUtils.isNotBlank(orderCode)) {
                //查询专科视图配置  用药医嘱
                List<Map<String, String>> listDept = specialtyViewPowerService.getSpecialtyConfig(oid, mainDiag, orderCode,
                        deptCode);
                List<Map<String, String>> data = new ArrayList<Map<String, String>>();
                List<Map<String, String>> dataTemp = new ArrayList<Map<String, String>>();
                for (Map<String, String> map : page) {
                    String orderCodeTemp = map.get("orderItemCode");
                    map.remove("orderItemCode");
                    boolean isBreak = false;
                    for (Map<String, String> mapSpecialty : listDept) {
                        String code = mapSpecialty.get("subItemCode");
                        if (StringUtils.isNotBlank(orderCodeTemp) && orderCodeTemp.equals(code)) {
                            data.add(map);
                            isBreak = true;
                            break;
                        }
                    }
                    if (isBreak) {
                        continue;
                    }
                    dataTemp.add(map);
                }
                //合并数据
                for (Map<String, String> map : dataTemp) {
                    data.add(map);
                }
                page.setResult(data);
            }

        }

        //循环该页医嘱的每一条数据，根据医嘱类型，单独从医嘱执行表和报告表中读取执行数据和报告数据
        for (Map<String, String> map : page) {
            //拼接医嘱排序字段
            /*if (StringUtils.isBlank(map.get("PARENT_ORDER_NO"))) {
                map.put("PARENT_ORDER_NO", map.get("ORDER_NO"));
            }
            map.put("GROUPSORTCOLUMN", map.get("ORDER_TIME") + map.get("PARENT_ORDER_NO"));
            */
            if (StringUtils.isNotBlank(orderReporte)) {
                String column = "";
                if ("exam".equals(orderReporte)) {
                    column = Config.getConfigValue(oid, "CIV_ORDER_EXAM");
                    //column = Config.getCIV_ORDER_EXAM(oid);
                } else if ("lab".equals(orderReporte)) {
                    column = Config.getConfigValue(oid, "CIV_ORDER_LAB");
                    //column = Config.getCIV_ORDER_LAB(oid);
                }

                String no = map.get(column);
                //检验医嘱特殊处理getLabList
                if ("lab".equals(orderReporte)) {
                    getLabReportStatus(map, oid, patId, visitId, visitType);
                }
                //检查医嘱特殊处理
                if ("exam".equals(orderReporte)) {
                    map.put("REPORT_STATUS", "报告未出");
                    if (StringUtils.isBlank(no)) {
                        continue;
                    }
                    filters = new ArrayList<PropertyFilter>();
                    String operation = Config.getCIV_ORDER_EXAM_MATCHTYPE(oid);
                    createPropertyFilter(column, no, operation, filters);
                    List<Map<String, String>> labReport = new ArrayList<>();
                    com.goodwill.hdr.hbase.dto.responseVo.ResultVo<PageResultVo<Map<String, String>>> resultVo2 = hbaseQueryClient.getPageByCondition(
                            PageRequestBuilder.init()
                                    .tableName(HdrTableEnum.HDR_EXAM_REPORT.getCode())
                                    .patientId(patId)
                                    .oid(oid)
                                    .visitId(visitId)
                                    .visitTypeCode("")
                                    .filters(filters)
                                    .pageNo(0)
                                    .pageSize(0)
                                    .orderBy("")
                                    .desc()
                                    .column("REPORT_DOCTOR_NAME", "REPORT_DOCTOR_CODE", "REPORT_TIME")
                                    .build());
                    if (resultVo2.isSuccess()) {
                        labReport = resultVo2.getContent().getResult();
                    }
//                    List<Map<String, String>> labReport = hbaseDao.findConditionByPatient(
//                            HdrTableEnum.HDR_EXAM_REPORT.getCode(), oid, patId, filters,
//                            "REPORT_DOCTOR_NAME", "REPORT_DOCTOR_CODE", "REPORT_TIME");
                    if (labReport.size() > 0) {
                        if (StringUtils.isNotBlank(labReport.get(0).get("REPORT_DOCTOR_NAME"))
                                || StringUtils.isNotBlank(labReport.get(0).get("REPORT_DOCTOR_CODE"))
                                || StringUtils.isNotBlank(labReport.get(0).get("REPORT_TIME"))) {
                            map.put("REPORT_STATUS", "报告已出");
                        }
                    }
                }
            }
        }


        List<Map<String, String>> orders = new ArrayList<Map<String, String>>();
        for (Map<String, String> map : page) {
            Map<String, String> order = new HashMap<String, String>();
            Utils.checkAndPutToMap(order, "orderType", getOrderType(oid, visitType, map.get("ORDER_CLASS_CODE"),pharmacyWay), "-", false); //医嘱类别
            for (Map<String, Object> headMap : configMapList) {
                String name = (String) headMap.get("name");
                String column = (String) headMap.get("field");
                if ("overTime".equals(name)) {
                    boolean isOverTime = isReportOverTime(oid, patId, visitId, map.get("ORDER_NO"), map.get("ORDER_ITEM_CODE"), "LAB", map.get("ORDER_TIME"));
                    order.put("overTime", isOverTime ? "超时" : "正常");
                    continue;
                }
                Utils.checkAndPutToMap(order, name, map.get(column), "-", false);
            }
            if(StringUtils.isBlank(order.get("parentOrderNo"))){
                order.put("parentOrderNo", order.get("orderNo"));
            }
            map.put("GROUPSORTCOLUMN", map.get("ORDER_TIME") + map.get("PARENT_ORDER_NO"));
            orders.add(order);
        }

        page.setResult(orders);

        return page;
    }
//
//    /**
//     * 字符查询传转换为filter查询对象
//     *
//     * @param filterString 查询字符串 column|in|080,079,004,035,088,134
//     * @param filters
//     * @param strSplit     分隔每个查询条件的分隔符
//     */
//    private static void strToFilter(String filterString, List<PropertyFilter> filters, String strSplit) {
//        //将配置字符串转换为查询
//        if (StringUtils.isNotBlank(filterString)) {
//            String[] filterStrings = filterString.split(strSplit);
//            for (String filterItemString : filterStrings) {
//                String[] tempString = filterItemString.split("\\|");
//                createPropertyFilter(tempString[0], tempString[2], tempString[1], filters);
//            }
//        }
//    }
//

    @Override
    public Page<Map<String, String>> getBloodApplyList(String oid, String patId, String visitId, String orderBy, String orderDir,
                                                       int pageNo, int pageSize) {
        String tableName = "HDR_BLOOD_APPLY";
        List<PropertyFilter> filters = new ArrayList<PropertyFilter>();
        //输血闭环配置存储到配置文件
        String bloodfilter = Config.getOCLBloodListFilter(oid);
        strToFilter(bloodfilter, filters, ";");
        //createPropertyFilter("TEMPLET_ID", "EMR34.00.01_2", MatchType.EQ.getOperation(), filters);
        //分页
        Page<Map<String, String>> page = new Page<Map<String, String>>();
        if (pageSize == 0) {
            pageSize = 30;
        }
        page.setOrderBy(orderBy);
        page.setOrderDir(orderDir);
        page.setPageNo(pageNo);
        page.setPageSize(pageSize);
        com.goodwill.hdr.hbase.dto.responseVo.ResultVo<PageResultVo<Map<String, String>>> resultVo1 = hbaseQueryClient.getPageByCondition(
                PageRequestBuilder.init()
                        .tableName(tableName)
                        .patientId(patId)
                        .oid(oid)
                        .visitId(visitId)
                        .visitTypeCode("")
                        .filters(filters)
                        .pageNo(0)
                        .pageSize(0)
                        .orderBy("")
                        .desc()
                        .column("ORDER_NO", "APPLY_DEPT_NAME", "TIMES_NO", "TEMPLET_NAME", "APPLY_PERSON_NAME",
                                "APPLY_DATE", "TEMPLET_STATE_NAME", "APPLY_NO")
                        .build());
        if (resultVo1.isSuccess()) {
            page.setResult(resultVo1.getContent().getResult());
            page.setTotalCount(resultVo1.getContent().getTotal());
        }
//        page = hbaseDao.findPageConditionByPatientVisitId(tableName, oid, patId, visitId, page, filters,
//                new String[]{"ORDER_NO", "APPLY_DEPT_NAME", "TIMES_NO", "TEMPLET_NAME", "APPLY_PERSON_NAME",
//                        "APPLY_DATE", "TEMPLET_STATE_NAME", "APPLY_NO"});
        //增加申请单状态
        for (Map<String, String> map : page) {
            List<PropertyFilter> filters2 = new ArrayList<PropertyFilter>();
            createPropertyFilter("APPLY_NO", map.get("APPLY_NO"), MatchType.EQ.getOperation(), filters2);
            List<Map<String, String>> list2 = new ArrayList<>();
            com.goodwill.hdr.hbase.dto.responseVo.ResultVo<PageResultVo<Map<String, String>>> resultVo2 = hbaseQueryClient.getPageByCondition(
                    PageRequestBuilder.init()
                            .tableName("HDR_BLOOD_BANK")
                            .patientId(patId)
                            .oid(oid)
                            .visitId(visitId)
                            .visitTypeCode("")
                            .filters(filters2)
                            .pageNo(0)
                            .pageSize(0)
                            .orderBy("")
                            .desc()
                            .column("SQD_STATUS_NAME")
                            .build());
            if (resultVo2.isSuccess()) {
                list2 = resultVo2.getContent().getResult();
            }
//            List<Map<String, String>> list2 = hbaseDao.findConditionByPatientVisitId("HDR_BLOOD_BANK", oid, patId, visitId,
//                    filters2, new String[]{"SQD_STATUS_NAME"});
            if (list2.size() > 0) {
                map.put("SQD_STATUS_NAME", Utils.objToStr(list2.get(0).get("SQD_STATUS_NAME")));
            } else {
                map.put("SQD_STATUS_NAME", map.get("TEMPLET_STATE_NAME"));
            }
        }
        return page;
    }

    @Override
    public Page<Map<String, String>> getOrderListMZ(String oid, String patId, String visitId, List<String> types, String filterStr,
                                                    String orderReport, String orderBy, String orderDir, int pageNo, int pageSize) {
        String tableName = "HDR_OUT_CHARGE";
        List<PropertyFilter> filters = new ArrayList<PropertyFilter>();
        //医嘱类别   药品，检查，检验...
        if (types != null && types.size() > 0) {
            String orderClassString = "";
            for (String orderString : types) {
                orderClassString += orderString + ",";
            }
            orderClassString = orderClassString.substring(0, orderClassString.length() - 1);
            createPropertyFilter("ORDER_CLASS_CODE", orderClassString, MatchType.IN.getOperation(), filters);
        }

        //医嘱性质 和 医嘱状态 或其他条件
        strToFilter(filterStr, filters, ";");
        //医嘱查询
        List<Map<String, String>> list = new ArrayList<>();
        com.goodwill.hdr.hbase.dto.responseVo.ResultVo<PageResultVo<Map<String, String>>> resultVo1 = hbaseQueryClient.getPageByCondition(
                PageRequestBuilder.init()
                        .tableName(tableName)
                        .patientId(patId)
                        .oid(oid)
                        .visitId(visitId)
                        .visitTypeCode("")
                        .filters(new ArrayList<>())
                        .pageNo(0)
                        .pageSize(0)
                        .orderBy("")
                        .desc()
                        .column("ORDER_NO", "CHARGE_NAME", "TOTAL_DOSAGE_VALUE", "TOTAL_DOSAGE_UNIT",
                                "PHARMACY_WAY_NAME", "FREQUENCY_NAME", "ORDER_CLASS_NAME", "ORDER_ITEM_NAME",
                                "ORDER_DOCTOR_NAME", "ORDER_TIME", "PRESC_TIME", "CHARGE_TIME", "CHARGE_CLASS_NAME",
                                "BILL_ITEM_NAME", "PRES_STATUS_NAME", "PARENT_ORDER_NO", "APPLY_NO")
                        .build());
        if (resultVo1.isSuccess()) {
            list = resultVo1.getContent().getResult();
        }
//        List<Map<String, String>> list = hbaseDao.findConditionByPatientVisitId(tableName, oid, patId, visitId, visitId,
//                new String[]{"ORDER_NO", "CHARGE_NAME", "TOTAL_DOSAGE_VALUE", "TOTAL_DOSAGE_UNIT",
//                        "PHARMACY_WAY_NAME", "FREQUENCY_NAME", "ORDER_CLASS_NAME", "ORDER_ITEM_NAME",
//                        "ORDER_DOCTOR_NAME", "ORDER_TIME", "PRESC_TIME", "CHARGE_TIME", "CHARGE_CLASS_NAME",
//                        "BILL_ITEM_NAME", "PRES_STATUS_NAME", "PARENT_ORDER_NO", "APPLY_NO"});
        for (Map<String, String> map : list) {
            if (StringUtils.isNotBlank(orderReport)) {
                String column = Config.getCIV_ORDER_LABOREXAM(oid);
                String no = map.get(column);
                //检验医嘱特殊处理
                if ("lab".equals(orderReport)) {
                    getLabReportStatus(map, oid, patId, visitId, "OUTPV");
                }
                //检查医嘱特殊处理
                if ("exam".equals(orderReport)) {
                    map.put("REPORT_STATUS", "报告未出");
                    //重置过滤条件
                    filters = new ArrayList<PropertyFilter>();
                    createPropertyFilter(column, no, MatchType.EQ.getOperation(), filters);
                    List<Map<String, String>> labReport = new ArrayList<>();
                    com.goodwill.hdr.hbase.dto.responseVo.ResultVo<PageResultVo<Map<String, String>>> resultVo2 = hbaseQueryClient.getPageByCondition(
                            PageRequestBuilder.init()
                                    .tableName(HdrTableEnum.HDR_EXAM_REPORT.getCode())
                                    .patientId(patId)
                                    .oid(oid)
                                    .visitId(visitId)
                                    .visitTypeCode("")
                                    .filters(filters)
                                    .pageNo(0)
                                    .pageSize(0)
                                    .orderBy("")
                                    .desc()
                                    .column("REPORT_DOCTOR_NAME", "REPORT_DOCTOR_CODE", "REPORT_TIME")
                                    .build());
                    if (resultVo2.isSuccess()) {
                        labReport = resultVo2.getContent().getResult();
                    }
//                    List<Map<String, String>> labReport = hbaseDao.findConditionByPatient(
//                            HdrTableEnum.HDR_EXAM_REPORT.getCode(), oid, patId, filters,
//                            new String[]{"REPORT_DOCTOR_NAME", "REPORT_DOCTOR_CODE", "REPORT_TIME"});
                    if (labReport.size() > 0) {
                        if (StringUtils.isNotBlank(labReport.get(0).get("REPORT_DOCTOR_NAME"))
                                || StringUtils.isNotBlank(labReport.get(0).get("REPORT_DOCTOR_CODE"))
                                || StringUtils.isNotBlank(labReport.get(0).get("REPORT_TIME"))) {
                            map.put("REPORT_STATUS", "报告已出");
                        }
                    }
                }
            } else {
                //为空，中断循环，跳出
                break;
            }
        }
        //排序
        if (StringUtils.isNotBlank(orderBy) && StringUtils.isNotBlank(orderDir)) {
            Utils.sortListMulti(list, new String[]{orderBy}, new String[]{orderDir});
        } else {
            sortListByDate(list, "ORDER_TIME", "desc");
        }
        //分页判断
        Page<Map<String, String>> page = new Page<Map<String, String>>();
        if (pageSize == 0) { //不分页
            page.setResult(list);
            page.setTotalCount(list.size());
        } else { //分页
            ListPage<Map<String, String>> listPage = new ListPage<Map<String, String>>(list, pageNo, pageSize);
            page.setPageNo(pageNo);
            page.setPageSize(pageSize);
            page.setResult(listPage.getPagedList());
            page.setTotalCount(listPage.getTotalCount());
        }
        return page;
    }

    /**
     * 获取检验医嘱的报告状态
     *
     * @param patId 患者号
     * @param map   检验医嘱MAP
     */
    public void getLabReportStatus(Map<String, String> map, String oid, String patId, String visitId,String visitType) {
        map.put("REPORT_STATUS", "报告未出");
        List<PropertyFilter> filters = new ArrayList<PropertyFilter>();
        String operation = Config.getCIV_ORDER_EXAM_MATCHTYPE(oid);
        String column = Config.getCIV_ORDER_LABOREXAM(oid);
        if ("OUTPV".equals(visitType)) { //门诊
            column = Config.getCIV_ORDER_LABOREXAM_MZ(oid);
        }
        String no = map.get(column);
        if (StringUtils.isBlank(no)) {
            if ("OUTPV".equals(visitType)) {
                logger.error("HDR_OUT_ORDER表里无该配置CIV_ORDER_LABOREXAM字段：" + column + "");
            }else {
                logger.error("HDR_IN_ORDER表里无该配置CIV_ORDER_LABOREXAM_MZ字段：" + column + "");
            }
            return;
        }
        createPropertyFilter(column, no, operation, filters);
        List<Map<String, String>> labReport = new ArrayList<>();
        com.goodwill.hdr.hbase.dto.responseVo.ResultVo<PageResultVo<Map<String, String>>> resultVo = hbaseQueryClient.getPageByCondition(
                PageRequestBuilder.init()
                        .tableName(HdrTableEnum.HDR_LAB_REPORT_DETAIL.getCode())
                        .patientId(patId)
                        .oid(oid)
                        .visitId(visitId)
                        .visitTypeCode("")
                        .filters(filters)
                        .pageNo(0)
                        .pageSize(0)
                        .orderBy("")
                        .desc()
                        .column("REPORT_NO", "REPORT_TIME")
                        .build());
        if (resultVo.isSuccess()) {
            labReport = resultVo.getContent().getResult();
        }
//        List<Map<String, String>> labReport = hbaseDao.findConditionByPatient(
//                HdrTableEnum.HDR_LAB_REPORT_DETAIL.getCode(), oid, patId, filters,
//                new String[]{"REPORT_NO", "REPORT_TIME"});
        if (labReport.size() > 0) {
            map.put("REPORT_STATUS", "报告已出");
        }
    }

    @Override
    public Page<Map<String, String>> getDrugListKF(String oid, String patId, String visitId, String visitType, String orderStatus,
                                                   String orderProperty, String mainDiag, String orderCode, String deptCode, String orderItemName, String orderBy, String orderDir, List<Map<String, Object>> kfShowConfig, int pageNo, int pageSize) {
        //过滤条件
        StringBuffer filterStr = new StringBuffer();
        //医嘱性质   1-临时    2-长期
        if (StringUtils.isNotBlank(orderProperty)) {
            if ("1".equals(orderProperty)) {
                filterStr.append("ORDER_PROPERTIES_NAME|in|" + Config.getORDER_SHORT_PROPERTY_CONFIG(oid) + ";");
            } else if ("2".equals(orderProperty)) {
                filterStr.append("ORDER_PROPERTIES_NAME|in|" + Config.getORDER_LONG_PROPERTY_CONFIG(oid) + ";");
            }
        }
        //医嘱状态
        if (StringUtils.isNotBlank(orderStatus)) {
            filterStr.append("ORDER_STATUS_NAME|like|" + orderStatus + ";");
        }
        if (StringUtils.isNotBlank(orderItemName)) {
            filterStr.append("ORDER_ITEM_NAME|like|" + orderItemName + ";");
        }
        Page<Map<String, String>> page = new Page<Map<String, String>>();


        //北医三院门诊医嘱特殊处理
        if (HdrConstantEnum.HOSPITAL_BYSY.getCode().equals(ConfigCache.getCache(oid, "org_oid")) && "OUTPV".equals(visitType)) {
            //置空条件
            filterStr = new StringBuffer();
            //口服药品
            filterStr.append("PHARMACY_WAY_NAME|like|口服;"); //用药方式
            page = getOrderListMZ(oid, patId, visitId, CivUtils.getOrderClass(oid, "BYSY_MZ_DRUG"), filterStr.toString(), "",
                    orderBy, orderDir, pageNo, pageSize);
            //字段映射
            List<Map<String, String>> orders = new ArrayList<Map<String, String>>();

            for (Map<String, String> map : page) {
                Map<String, String> order = new HashMap<String, String>();
                Utils.checkAndPutToMap(order, "orderType", getOrderType(oid, visitType, map.get("ORDER_CLASS_CODE"),"KF"), "-", false); //医嘱类别
                for (Map<String, Object> headMap : kfShowConfig) {
                    String name = (String) headMap.get("name");
                    String column = (String) headMap.get("field");
                    Utils.checkAndPutToMap(order, name, map.get(column), "-", false);
                }
                orders.add(order);
            }

            //重置分页
            page.setResult(orders);
            return page;
        }
        //非北医三院门诊  正常查询
        //用药方式区分门诊和住院
        String PHARMACY_WAY = null;
        if ("OUTPV".equals(visitType)) {
            PHARMACY_WAY = Config.getOclMzKffilter(oid);
        } else if ("INPV".equals(visitType)) {
            PHARMACY_WAY = Config.getOCLKFFilter(oid);
        }
        if (StringUtils.isNotBlank(PHARMACY_WAY)) {
            filterStr.append(PHARMACY_WAY + ";");
        }
        //TODO 区分门诊和住院医嘱类别
        List<String> orderClassStrings = new ArrayList<String>();
        if ("OUTPV".equals(visitType)) {
            orderClassStrings = CivUtils.getOrderClass(oid, "OrderClose_MZ_DRUG_KF");
        } else if ("INPV".equals(visitType)) {
            orderClassStrings = CivUtils.getOrderClass(oid, "OrderClose_DRUG_KF");
        }
        page = getOrderList(oid, patId, visitId, visitType, orderClassStrings, filterStr.toString(), "", orderBy,
                orderDir, mainDiag, orderCode, deptCode, kfShowConfig, pageNo, pageSize,"KF");


        return page;
    }


    @Override
    public Page<Map<String, String>> getDrugListJMZS(String oid, String patId, String visitId, String visitType, String orderStatus,
                                                     String orderProperty, String mainDiag, String orderCode, String deptCode, String orderItemName, String orderBy, String orderDir, List<Map<String, Object>> jmShowConfig, int pageNo, int pageSize) {
        //过滤条件
        StringBuffer filterStr = new StringBuffer();
        //医嘱性质   1-临时    2-长期
        if (StringUtils.isNotBlank(orderProperty)) {
            if ("1".equals(orderProperty)) {
                filterStr.append("ORDER_PROPERTIES_NAME|in|" + Config.getORDER_SHORT_PROPERTY_CONFIG(oid) + ";");
            } else if ("2".equals(orderProperty)) {
                filterStr.append("ORDER_PROPERTIES_NAME|in|" + Config.getORDER_LONG_PROPERTY_CONFIG(oid) + ";");
            }
        }
        //医嘱状态
        if (StringUtils.isNotBlank(orderStatus)) {
            filterStr.append("ORDER_STATUS_NAME|like|" + orderStatus + ";");
        }
        if (StringUtils.isNotBlank(orderItemName)) {
            filterStr.append("ORDER_ITEM_NAME|like|" + orderItemName + ";");
        }
        Page<Map<String, String>> page = new Page<Map<String, String>>();

        //Map<String, String> columnMap = getDisplayColumns(oid, "2");
        //北医三院门诊医嘱特殊处理
        if (HdrConstantEnum.HOSPITAL_BYSY.getCode().equals(ConfigCache.getCache(oid, "org_oid")) && "OUTPV".equals(visitType)) {
            //置空条件
            filterStr = new StringBuffer();
            //静脉药品
            filterStr.append("PHARMACY_WAY_NAME|like|静脉;"); //用药方式
            page = getOrderListMZ(oid, patId, visitId, CivUtils.getOrderClass(oid, "BYSY_MZ_DRUG"), filterStr.toString(), "",
                    orderBy, orderDir, pageNo, pageSize);
            //字段映射
            List<Map<String, String>> orders = new ArrayList<Map<String, String>>();

            for (Map<String, String> map : page) {
                Map<String, String> order = new HashMap<String, String>();

                Utils.checkAndPutToMap(order, "orderType", getOrderType(oid, visitType, map.get("ORDER_CLASS_CODE"),"JM"), "-", false);
                for (Map<String, Object> headMap : jmShowConfig) {
                    String name = (String) headMap.get("name");
                    String column = (String) headMap.get("field");
                    Utils.checkAndPutToMap(order, name, map.get(column), "-", false);
                }
                orders.add(order);
            }
            //重置分页
            page.setResult(orders);
            return page;
        }
        //非北医三院门诊  正常查询
        //用药方式
        if (StringUtils.isNotBlank(Config.getOCLJMZSFilter(oid))) {
            if ("OUTPV".equals(visitType)) {
                filterStr.append(Config.getOclMzJmzsfilter(oid) + ";");
            } else if ("INPV".equals(visitType)) {
                filterStr.append(Config.getOCLJMZSFilter(oid) + ";");
            }
        }
        //TODO 区分门诊和住院医嘱类别
        List<String> orderClassStrings = new ArrayList<String>();
        if ("OUTPV".equals(visitType)) {
            orderClassStrings = CivUtils.getOrderClass(oid, "OrderClose_MZ_DRUG_JMZS");
        } else if ("INPV".equals(visitType)) {
            orderClassStrings = CivUtils.getOrderClass(oid, "OrderClose_DRUG_JMZS");
        }
        page = getOrderList(oid, patId, visitId, visitType, orderClassStrings, filterStr.toString(), "", orderBy,
                orderDir, mainDiag, orderCode, deptCode, jmShowConfig, pageNo, pageSize,"JM");

        return page;
    }

    @Override
    public Page<Map<String, String>> getOperOrderList(String oid, String patId, String visitId, String visitType,
                                                      List<String> types, String orderNo, String orderBy, String orderDir, String orderItemName, int pageNo, int pageSize) {
        //将患者的全部医嘱取出，医嘱性质包含长期和临时，医嘱状态包含下达/审核/开始/停止，将状态为撤销的医嘱排除
        String tableName = "HDR_IN_ORDER";
        if ("OUTPV".equals(visitType)) {
            tableName = "HDR_OUT_ORDER";
        }
        //页面查询条件
        List<PropertyFilter> filters = new ArrayList<PropertyFilter>();
        //		createPropertyFilter("ORDER_STATUS_NAME", "撤销,废弃", MatchType.NOTIN.getOperation(), filters);
        if (StringUtils.isNotBlank(orderNo)) {
            createPropertyFilter("ORDER_NO", orderNo, MatchType.EQ.getOperation(), filters);
        }
        if (StringUtils.isNotBlank(orderItemName)) {
            createPropertyFilter("ORDER_ITEM_NAME", orderItemName, MatchType.LIKE.getOperation(), filters);
        }
        if (types != null && types.size() > 0) {
            String orderClassString = "";
            for (String orderString : types) {
                orderClassString += orderString + ",";
            }
            orderClassString = orderClassString.substring(0, orderClassString.length() - 1);
            createPropertyFilter("ORDER_CLASS_CODE", orderClassString, MatchType.IN.getOperation(), filters);
        }
        //这里是获取手术配置的条件
        Config.setVisitViewOperationFilter(oid, filters);


        //获取列包括 医嘱号、父医嘱号、医嘱类别、医嘱性质、医嘱项、开始时间、结束时间、医嘱状态、开据医生
        //读取出医嘱列表，并按医嘱时间倒序
        Page<Map<String, String>> page = new Page<Map<String, String>>();
        if (pageSize == 0) {
            pageSize = 30;
        }
        List<Map<String, Object>> operShowConfig = OrderShowConfigVo.getOperShowConfig(oid);
        List<String> fieldList = new ArrayList<>();
        for (Map<String, Object> map : operShowConfig) {
            fieldList.add((String) map.get("field"));
        }
        //排序
        if (StringUtils.isBlank(orderBy) || StringUtils.isBlank(orderDir)) {
            page.setOrderBy("ORDER_TIME");
            page.setOrderDir("desc");
        } else {
            page.setOrderBy(orderBy);
            page.setOrderDir(orderDir);
        }
        page.setPageNo(pageNo);
        page.setPageSize(pageSize);
        com.goodwill.hdr.hbase.dto.responseVo.ResultVo<PageResultVo<Map<String, String>>> resultVo2 = hbaseQueryClient.getPageByCondition(
                PageRequestBuilder.init()
                        .tableName(tableName)
                        .patientId(patId)
                        .oid(oid)
                        .visitId(visitId)
                        .visitTypeCode("")
                        .filters(filters)
                        .pageNo(0)
                        .pageSize(0)
                        .orderBy("")
                        .desc()
                        .column(fieldList.toArray(new String[0]))
                        .build());
        if (resultVo2.isSuccess()) {
            page.setResult(resultVo2.getContent().getResult());
            page.setTotalCount(resultVo2.getContent().getTotal());
        }
//        page = hbaseDao.findPageConditionByPatientVisitId(tableName, oid, patId, visitId, page, filters,
//                fieldList.toArray(new String[0]));
        for (Map<String, String> map : page) {
            String orderNO = map.get("ORDER_NO");
            //拼接手术申请数据
            filters = new ArrayList<PropertyFilter>();
            createPropertyFilter("OPER_APPLY_NO", orderNO, MatchType.EQ.getOperation(), filters);
            List<Map<String, String>> operApplyList = new ArrayList<>();
            com.goodwill.hdr.hbase.dto.responseVo.ResultVo<PageResultVo<Map<String, String>>> resultVo1 = hbaseQueryClient.getPageByCondition(
                    PageRequestBuilder.init()
                            .tableName(HdrTableEnum.HDR_OPER_APPLY.getCode())
                            .patientId(patId)
                            .oid(oid)
                            .visitId(visitId)
                            .visitTypeCode("")
                            .filters(filters)
                            .pageNo(0)
                            .pageSize(0)
                            .orderBy("")
                            .desc()
                            .column("OPERATION_NAME", "DIAG_BEFORE_OPERATION_NAME", "PLAN_OPER_DOCTOR_NAME",
                                    "PLAN_OPER_TIME", "APPLY_OPER_TIME")
                            .build());
            if (resultVo1.isSuccess()) {
                operApplyList = resultVo1.getContent().getResult();
            }
//            List<Map<String, String>> operApplyList = hbaseDao.findConditionByPatient("HDR_OPER_APPLY", oid,
//                    patId, filters,
//                    "OPERATION_NAME", "DIAG_BEFORE_OPERATION_NAME", "PLAN_OPER_DOCTOR_NAME",
//                    "PLAN_OPER_TIME", "APPLY_OPER_TIME");
            if (operApplyList.size() > 0) {
                map.put("ORDER_ITEM_NAME",
                        Utils.objToStr(operApplyList.get(0).get("OPERATION_NAME"), map.get("ORDER_ITEM_NAME")));
                map.put("DIAG_BEFORE_OPERATION_NAME",
                        Utils.objToStr(operApplyList.get(0).get("DIAG_BEFORE_OPERATION_NAME")));
                map.put("PLAN_OPER_DOCTOR_NAME", Utils.objToStr(operApplyList.get(0).get("PLAN_OPER_DOCTOR_NAME")));
                map.put("PLAN_OPER_TIME", Utils.objToStr(operApplyList.get(0).get("PLAN_OPER_TIME"))); //拟手术日期
                map.put("APPLY_OPER_TIME", Utils.objToStr(operApplyList.get(0).get("APPLY_OPER_TIME"))); //申请手术时间
            }

            //拼接手术过程数据
            filters = new ArrayList<PropertyFilter>();
            createPropertyFilter("ORDER_NO", orderNO, MatchType.EQ.getOperation(), filters);
            List<Map<String, String>> operAnaesList = new ArrayList<>();
            com.goodwill.hdr.hbase.dto.responseVo.ResultVo<PageResultVo<Map<String, String>>> resultVo3 = hbaseQueryClient.getPageByCondition(
                    PageRequestBuilder.init()
                            .tableName(HdrTableEnum.HDR_OPER_ANAES.getCode())
                            .patientId(patId)
                            .oid(oid)
                            .visitId(visitId)
                            .visitTypeCode("")
                            .filters(filters)
                            .pageNo(0)
                            .pageSize(0)
                            .orderBy("")
                            .desc()
                            .column("ANESTHESIA_END_TIME", "ANESTHESIA_DOCTOR_NAME")
                            .build());
            if (resultVo3.isSuccess()) {
                operAnaesList = resultVo3.getContent().getResult();
            }
//            List<Map<String, String>> operAnaesList = hbaseDao.findConditionByPatient("HDR_OPER_ANAES", oid,
//                    patId, filters,
//                    "ANESTHESIA_END_TIME", "ANESTHESIA_DOCTOR_NAME");
            if (operAnaesList.size() > 0) {
                map.put("ANESTHESIA_END_TIME", Utils.objToStr(operAnaesList.get(0).get("ANESTHESIA_END_TIME")));
                map.put("ANESTHESIA_DOCTOR_NAME", Utils.objToStr(operAnaesList.get(0).get("ANESTHESIA_DOCTOR_NAME")));
            }

        }
        return page;
    }

    @Override
    public Page<Map<String, String>> getLabList(String oid, String patId, String visitId, String visitType, String mainDiag,
                                                String orderCode, String deptCode, String orderItemName, String orderBy, String orderDir, int pageNo, int pageSize) {
        //过滤条件
        StringBuffer filterStr = new StringBuffer();
        Page<Map<String, String>> page = new Page<Map<String, String>>();
        //北医三院门诊医嘱特殊处理
        if (HdrConstantEnum.HOSPITAL_BYSY.getCode().equals(ConfigCache.getCache(oid, "org_oid")) && "OUTPV".equals(visitType)) {
            //检验医嘱
            filterStr.append("ORDER_CLASS_NAME|like|验;");
            page = getOrderListMZ(oid, patId, visitId, null, filterStr.toString(), "lab", "ORDER_TIME", "desc", pageNo,
                    pageSize);
            //字段映射
            List<Map<String, String>> orders = new ArrayList<Map<String, String>>();
            for (Map<String, String> map : page) {
                Map<String, String> order = new HashMap<String, String>();
                ColumnUtil.convertMapping(order, map, "ORDER_NO", "ORDER_TIME", "ORDER_DOCTOR_NAME", "ORDER_CLASS_NAME",
                        "REPORT_STATUS", "ORDER_ITEM_NAME");
                //特殊字段转换
                Utils.checkAndPutToMap(order, "orderItemName", map.get("CHARGE_NAME"), "-", false); //医嘱项名称
                orders.add(order);
            }
            //重置分页
            page.setResult(orders);
            return page;
        }
        //非北医三院门诊  正常查询
        //TODO 区分门诊和住院医嘱类别
        List<String> orderClassStrings = new ArrayList<String>();
        if ("OUTPV".equals(visitType)) {
            orderClassStrings = CivUtils.getOrderClass(oid, "OrderClose_MZ_LAB");
        } else if ("INPV".equals(visitType)) {
            orderClassStrings = CivUtils.getOrderClass(oid, "OrderClose_LAB");
        }
        if (StringUtils.isNotBlank(orderItemName)) {
            filterStr.append("ORDER_ITEM_NAME|like|").append(orderItemName).append(";");
        }
        List<Map<String, Object>> labShowConfig = OrderShowConfigVo.getLabShowConfig(oid);
        page = getOrderList(oid, patId, visitId, visitType, orderClassStrings, "", "lab", orderBy, orderDir, mainDiag,
                orderCode, deptCode, labShowConfig, pageNo, pageSize,"JY");


        return page;
    }

    /**
     * 是否超时
     *
     * @return
     */
    public boolean isReportOverTime(String oid, String pid, String vid, String orderNo, String orderItemCode, String reportType, String orderTime) {
        List<CmrReportTimeDuration> overTimeHour = visitReportsOverTimeDao.getOverTimeHour(orderItemCode, reportType);
        CmrReportTimeDuration duration = null;
        if (null == overTimeHour || overTimeHour.isEmpty()) {
            return false; //未超时
        }
        duration = overTimeHour.get(0);
        int hour = duration.getHourCount();
        //查询报告时间
        List<Map<String, String>> reportDetail = new ArrayList<Map<String, String>>();
        if ("LAB".equalsIgnoreCase(reportType)) {
            reportDetail = inspectReportService.getLabReportDetail(oid, pid, vid, orderNo, orderItemCode);
        } else if (("EXAM".equalsIgnoreCase(reportType))) {
            reportDetail = inspectReportService.getExamReportDetail(oid, pid, vid, orderNo, orderItemCode);
        }
        String reportTimeString = null;
        if (null != reportDetail && !reportDetail.isEmpty()) {
            reportTimeString = reportDetail.get(0).get("REPORT_TIME");
        }
        DateTimeFormatter df2 = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

        LocalDateTime reportTime = LocalDateTime.now();
        if (StringUtils.isNotBlank(reportTimeString)) {
            reportTime = LocalDateTime.parse(reportTimeString, df2);
        }
        if (StringUtils.isBlank(orderTime)) {
            return false;
        }
        LocalDateTime orderTimeParsed = LocalDateTime.parse(orderTime, df2);
        Duration between = Duration.between(orderTimeParsed, reportTime);
        return between.toHours() > hour;
    }

    @Override
    public Page<Map<String, String>> getExamList(String oid, String patId, String visitId, String visitType, String mainDiag,
                                                 String orderCode, String deptCode, String orderItemName, String orderBy, String orderDir, int pageNo, int pageSize) {
        //过滤条件
        StringBuffer filterStr = new StringBuffer();
        Page<Map<String, String>> page = new Page<Map<String, String>>();
        List<Map<String, Object>> examShowConfig = OrderShowConfigVo.getExamShowConfig(oid);
        boolean isOverTimeConfig = Config.getEXAM_OVER_TIME(oid);
//        Map<String, String> columnMap;
//        if (isOverTimeConfig) {
//            columnMap = getDisplayColumns(oid, "8.1");
//        } else {
//            columnMap = getDisplayColumns(oid, "8");
//        }
        //北医三院门诊医嘱特殊处理
        if (HdrConstantEnum.HOSPITAL_BYSY.getCode().equals(ConfigCache.getCache(oid, "org_oid")) && "OUTPV".equals(visitType)) {
            //检查医嘱
            filterStr.append("ORDER_CLASS_NAME|like|查;");
            page = getOrderListMZ(oid, patId, visitId, null, filterStr.toString(), "exam", "ORDER_TIME", "desc", pageNo,
                    pageSize);
            //字段映射
            List<Map<String, String>> orders = new ArrayList<Map<String, String>>();
            for (Map<String, String> map : page) {
                Map<String, String> order = new HashMap<String, String>();
                Utils.checkAndPutToMap(order, "orderType", getOrderType(oid, visitType, map.get("ORDER_CLASS_CODE"),"JC"), "-", false); //医嘱类别
                for (Map<String, Object> headMap : examShowConfig) {
                    String name = (String) headMap.get("name");
                    String column = (String) headMap.get("field");
                    Utils.checkAndPutToMap(order, name, map.get(column), "-", false);
                }
                orders.add(order);
            }
            //重置分页
            page.setResult(orders);
            return page;
        }
        //非北医三院门诊 正常查询
        //TODO 区分门诊和住院医嘱类别
        List<String> orderClassStrings = new ArrayList<String>();
        if ("OUTPV".equals(visitType)) {
            orderClassStrings = CivUtils.getOrderClass(oid, "OrderClose_MZ_EXAM");
        } else if ("INPV".equals(visitType)) {
            orderClassStrings = CivUtils.getOrderClass(oid, "OrderClose_EXAM");
        }
        if (StringUtils.isNotBlank(orderItemName)) {
            filterStr.append("ORDER_ITEM_NAME|like|").append(orderItemName).append(";");
        }

        page = getOrderList(oid, patId, visitId, visitType, orderClassStrings, filterStr.toString(), "exam", orderBy, orderDir, mainDiag,
                orderCode, deptCode, examShowConfig, pageNo, pageSize,"JC");
        //字段映射
        List<Map<String, String>> orders = new ArrayList<Map<String, String>>();
        //处理超时信息


        return page;
    }

    @Override
    public Page<Map<String, String>> getOperList(String oid, String patId, String visitId, String visitType, String orderItemName, String orderBy, String orderDir, int pageNo,
                                                 int pageSize) {
        //过滤条件
        StringBuffer filterStr = new StringBuffer();
        Page<Map<String, String>> page;
        //Map<String, String> columnMap = getDisplayColumns(oid, "5");
        List<Map<String, Object>> operShowConfig = OrderShowConfigVo.getOperShowConfig(oid);
        //北医三院门诊医嘱特殊处理
        if (HdrConstantEnum.HOSPITAL_BYSY.getCode().equals(ConfigCache.getCache(oid, "org_oid")) && "OUTPV".equals(visitType)) {
            //手术医嘱
            filterStr.append("ORDER_CLASS_NAME|like|术;");
            filterStr.append(Config.getVISIT_VIEW_OPERATION_CONFIG(oid) + ";");
            page = getOperOrderListMZ(oid, patId, visitId, null, filterStr.toString(), "ORDER_TIME", "desc", pageNo,
                    pageSize);
            //字段映射
            List<Map<String, String>> orders = new ArrayList<Map<String, String>>();
            for (Map<String, String> map : page) {
                Map<String, String> order = new HashMap<String, String>();
                Utils.checkAndPutToMap(order, "orderType", getOrderType(oid, visitType, map.get("ORDER_CLASS_CODE"),"SS"), "-", false); //医嘱类别
                for (Map<String, Object> headMap : operShowConfig) {
                    String name = (String) headMap.get("name");
                    String field = (String) headMap.get("field");
                    if ("operTime".equals(name)) {
                        if (StringUtils.isNotBlank(map.get("PLAN_OPER_TIME"))) { //拟手术时间
                            order.put("operTime", map.get("PLAN_OPER_TIME"));

                        } else {
                            Utils.checkAndPutToMap(order, "operTime", map.get("APPLY_OPER_TIME"), "-", false); //申请手术时间

                        }
                        continue;
                    }

                    if ("orderType".equals(name)) {
                        Utils.checkAndPutToMap(order, "orderType", getOrderType(oid, visitType, map.get("ORDER_CLASS_CODE"),"SS"), "-", false); //医嘱类别
                        continue;
                    }
                    Utils.checkAndPutToMap(order, name, map.get(field), "-", false);
                }
                //处理手术时间

                orders.add(order);
            }
            //重置分页
            page.setResult(orders);
            return page;
        }
        //非北医三院门诊 正常查询
        //TODO 区分门诊和住院医嘱类别
        List<String> orderClassStrings = new ArrayList<String>();
        if ("OUTPV".equals(visitType)) {
            orderClassStrings = CivUtils.getOrderClass(oid, "OrderClose_MZ_OPER");
        } else if ("INPV".equals(visitType)) {
            orderClassStrings = CivUtils.getOrderClass(oid, "OrderClose_OPER");
        }

        page = getOperOrderList(oid, patId, visitId, visitType, orderClassStrings, "", orderBy, orderDir, orderItemName, pageNo,
                pageSize);
        //字段映射
        List<Map<String, String>> orders = new ArrayList<Map<String, String>>();

        for (Map<String, String> map : page) {
            Map<String, String> order = new HashMap<String, String>();
            Utils.checkAndPutToMap(order, "orderType", getOrderType(oid, visitType, map.get("ORDER_CLASS_CODE"),"SS"), "-", false); //医嘱类别
            for (Map<String, Object> headMap : operShowConfig) {
                String name = (String) headMap.get("name");
                String field = (String) headMap.get("field");
                if ("operationName".equals(name)) {
                    String operaName = map.get("OPERATION_NAME");
                    if (StringUtils.isNotBlank(operaName)) {
                        order.put("operationName", operaName);
                    } else {
                        Utils.checkAndPutToMap(order, "operationName", map.get("ORDER_ITEM_NAME"), "-", false);
                    }
                    continue;
                }
                if ("operTime".equals(name)) {
                    if (StringUtils.isNotBlank(map.get("PLAN_OPER_TIME"))) { //拟手术时间
                        order.put("operTime", map.get("PLAN_OPER_TIME"));
                    } else {
                        Utils.checkAndPutToMap(order, "operTime", map.get("APPLY_OPER_TIME"), "-", false); //申请手术时间
                    }
                    continue;
                }
                Utils.checkAndPutToMap(order, name, map.get(field), "-", false);

            }

            orders.add(order);
        }

        //重置分页
        page.setResult(orders);
        return page;
    }

    @Override
    public Page<Map<String, String>> getBloodList(String oid, String patId, String visitId, int pageNo, int pageSize) {
        Page<Map<String, String>> page = getBloodApplyList(oid, patId, visitId, "APPLY_DATE", "desc", pageNo, pageSize);
        //字段映射
        List<Map<String, String>> bloods = new ArrayList<Map<String, String>>();
        for (Map<String, String> map : page) {
            Map<String, String> blood = new HashMap<String, String>();
            //处理申请单号
            Utils.checkAndPutToMap(blood, "timesNo", map.get("APPLY_NO"), "-", false);
            Utils.checkAndPutToMap(blood, "orderNo", map.get("APPLY_NO"), "-", false);
            ColumnUtil.convertMapping(blood, map, new String[]{"ORDER_NO", "APPLY_DATE", "APPLY_PERSON_NAME",
                    "APPLY_DEPT_NAME", "SQD_STATUS_NAME"});
            blood.put("orderType", "YX");
            bloods.add(blood);
        }
        //重置分页
        page.setResult(bloods);
        return page;
    }

    @Override
    public Page<Map<String, String>> getOrderExeList(String oid, String patId, String visitId, String orderNo, String orderType, int pageNo,
                                                     int pageSize) {
        Page<Map<String, String>> page = new Page<Map<String, String>>();
        if (pageSize == 0) {
            pageSize = 30;
        }
        page.setPageNo(pageNo);
        page.setPageSize(pageSize);
        //计划执行时间 降序
        //page.setOrderBy("PLAN_PRESC_TIME");
        String sortConfigColumn = ConfigCache.getCache(oid, "CIV_ORDER_EXE_SORT");
        String sortColumn = StringUtils.isBlank(sortConfigColumn) ? "PLAN_PRESC_TIME" : sortConfigColumn;
        page.setOrderBy(sortColumn);
        page.setOrderDir("desc");
        //页面查询条件
        List<PropertyFilter> filters = new ArrayList<PropertyFilter>();
        createPropertyFilter("ORDER_NO", orderNo, MatchType.EQ.getOperation(), filters);
        List<Map<String, String>> exeHead = null;
        if (orderType.equals("KF")) {
            exeHead = OclTableConfigVo.getKfExeHead(oid);
        } else if (orderType.equals("JM")) {
            exeHead = OclTableConfigVo.getJmExeHead(oid);
        } else {
            exeHead = OclTableConfigVo.getQtExeHead(oid);
        }
        String[] column = new String[exeHead.size()];
        for (int i = 0; i < exeHead.size(); i++) {
            column[i] = exeHead.get(i).get("field");
        }
        com.goodwill.hdr.hbase.dto.responseVo.ResultVo<PageResultVo<Map<String, String>>> resultVo2 = hbaseQueryClient.getPageByCondition(
                PageRequestBuilder.init()
                        .tableName(HdrTableEnum.HDR_ORDER_EXE.getCode())
                        .patientId(patId)
                        .oid(oid)
                        .visitId(visitId)
                        .visitTypeCode("")
                        .filters(filters)
                        .pageNo(0)
                        .pageSize(0)
                        .orderBy(sortColumn)
                        .desc()
                        .column(column)
                        .build());
        if (resultVo2.isSuccess()) {
            page.setResult(resultVo2.getContent().getResult());
            //page.setTotalCount(resultVo2.getContent().getTotal());
        }
//        page = hbaseDao.findPageConditionByPatient("HDR_ORDER_EXE", oid, patId, page, filters,
//                column);
        //数据处理  字段映射
        List<Map<String, String>> exes = new ArrayList<Map<String, String>>();
        for (Map<String, String> map : page) {
            Map<String, String> exe = new HashMap<String, String>();
            for (Map<String, String> headMap : exeHead) {
                String name = headMap.get("name");
                String field = headMap.get("field");
                //仅保留 实际执行时间存在的
                //if (StringUtils.isNotBlank(map.get("PRESC_TIME"))) {
                    //字段映射

                    Utils.checkAndPutToMap(exe, name, map.get(field), "-", false);

                //}
            }
            exes.add(exe);
        }
        //重置分页
        page.setResult(exes);
        page.setTotalCount(exes.size());
        return page;
    }

    @Override
    public Page<Map<String, String>> getDrugCheckList(String oid, String patId, String visitId, String orderNo, String orderType, int pageNo,
                                                      int pageSize,String visitType) {
        Page<Map<String, String>> page = new Page<Map<String, String>>();
        if (pageSize == 0) {
            pageSize = 30;
        }
        page.setPageNo(pageNo);
        page.setPageSize(pageSize);
        //医嘱发起时间 降序
        page.setOrderBy("OCC_TIME");
        page.setOrderDir("desc");
        List<Map<String, String>> checkHead = new ArrayList<>();
        switch (orderType) {
            case "KF":
                checkHead = OclTableConfigVo.getKfCheckHead(oid);
                break;
            case "JM":
                checkHead = OclTableConfigVo.getJmCheckHead(oid);
                break;
            case "QT":
                checkHead = OclTableConfigVo.getQtCheckHead(oid);
                break;
        }
        String[] column = new String[checkHead.size()];
        for (int i = 0; i < checkHead.size(); i++) {
            column[i] = checkHead.get(i).get("field");
        }
        //查询住院计费表 获取药品医嘱发药审核数据
        List<PropertyFilter> filters = new ArrayList<PropertyFilter>();
        createPropertyFilter("ORDER_NO", orderNo, MatchType.EQ.getOperation(), filters);
        //createPropertyFilter("VISIT_ID", visitId, MatchType.EQ.getOperation(), filters);
        createPropertyFilter("APPLY_STATUS_CODE", "1", MatchType.IN.getOperation(), filters); //已确认

        com.goodwill.hdr.hbase.dto.responseVo.ResultVo<PageResultVo<Map<String, String>>> resultVo=new com.goodwill.hdr.hbase.dto.responseVo.ResultVo<>();
        if(HdrConstantEnum.HOSPITAL_ZQZYY.getCode().equals(ConfigCache.getCache(oid, "org_oid"))){
            logger.info("进入济南市章丘区中医医院(新院区)");
            String visitTypeCode="";
            String tableName="";
            if (visitType.equals(VisitTypeEnum.IN_VISIT.getLabel())) {
                visitTypeCode = VisitTypeEnum.IN_VISIT.getCode();
                tableName="HDR_OUT_HOSPHARM";
            } else if (visitType.equals(VisitTypeEnum.OUT_VISIT.getLabel())) {
                visitTypeCode = VisitTypeEnum.OUT_VISIT.getCode();
                tableName="HDR_OUT_PHARM";
            }
            resultVo = hbaseQueryClient.getPageByCondition(
                    PageRequestBuilder.init()
                            .tableName(tableName)
                            .patientId(patId)
                            .oid(oid)
                            .visitId(visitId)
                            .visitTypeCode(visitTypeCode)
                            .filters(filters)
                            .pageNo(0)
                            .pageSize(0)
                            .orderBy("")
                            .desc()
                            .column(column)
                            .build());
        }else {
            logger.info("进入公共代码区");
            resultVo = hbaseQueryClient.getPageByCondition(
                    PageRequestBuilder.init()
                            .tableName("HDR_IN_CHARGE")
                            .patientId(patId)
                            .oid(oid)
                            .visitId(visitId)
                            .visitTypeCode("02")
                            .filters(filters)
                            .pageNo(0)
                            .pageSize(0)
                            .orderBy("")
                            .desc()
                            .column(column)
                            .build());
        }

        if (resultVo.isSuccess()) {
            page.setTotalCount(resultVo.getContent().getTotal());
            page.setResult(resultVo.getContent().getResult());
        }
        //page = hbaseDao.findByConditionPage("HDR_IN_CHARGE", oid, patId, filters, page, column);
        //数据处理 字段映射
        List<Map<String, String>> checks = new ArrayList<Map<String, String>>();
        for (Map<String, String> map : page) {
            Map<String, String> check = new HashMap<String, String>();
            for (Map<String, String> headMap : checkHead) {
                String name = headMap.get("name");
                String field = headMap.get("field");
                Utils.checkAndPutToMap(check, name, map.get(field), "-", false);
            }


            checks.add(check);
        }
        //重置分页
        page.setResult(checks);
        return page;
    }

    @Override
    public Page<Map<String, String>> getLabReportDetails(String oid, String patId, String visitType, String visitId, String field, String orderNo,
                                                         int pageNo, int pageSize) {
        Map<String, Object> result = new HashMap<String, Object>();
        String tableName = "HDR_LAB_REPORT_DETAIL";
        List<PropertyFilter> filters = new ArrayList<>();
        if ("OUTPV".equals(visitType)) {
            visitType = "01";
            createPropertyFilter("VISIT_TYPE_CODE", "01", MatchType.EQ.getOperation(), filters);
        } else {
            visitType = "02";
            createPropertyFilter("VISIT_TYPE_CODE", "02", MatchType.EQ.getOperation(), filters);
        }
        PropertyFilter pfOrderNo = new PropertyFilter();
        if (StringUtils.isNotBlank(orderNo)) {
            //          createPropertyFilter("ORDER_NO", orderNo, MatchType.EQ.getOperation(), filters);
            pfOrderNo = new PropertyFilter(field, MatchType.EQ.getOperation(), orderNo);
            filters.add(pfOrderNo);
        }
        Page<Map<String, String>> page = new Page<Map<String, String>>();
        if (pageSize == 0) {
            pageSize = 30;
        }
        page.setPageNo(pageNo);
        page.setPageSize(pageSize);
        page.setOrderBy("REPORT_TIME");
        page.setOrderDir("desc");

        List<Map<String, String>> jyExeHead = OclTableConfigVo.getJyExeHead(oid);
        String[] column = new String[jyExeHead.size()];
        for (int i = 0; i < jyExeHead.size(); i++) {
            column[i] = jyExeHead.get(i).get("field");
        }
        //检验大项,检验细项,检验定量结果,检验定性结果,报告时间，参考范围
        com.goodwill.hdr.hbase.dto.responseVo.ResultVo<PageResultVo<Map<String, String>>> resultVo = hbaseQueryClient.getPageByCondition(
                PageRequestBuilder.init()
                        .tableName(tableName)
                        .patientId(patId)
                        .oid(oid)
                        .visitId(visitId)
                        .visitTypeCode(visitType)
                        .filters(filters)
                        .pageNo(0)
                        .pageSize(0)
                        .orderBy("")
                        .desc()
                        .column(column)
                        .build());


        if (resultVo.isSuccess()) {
            page.setResult(resultVo.getContent().getResult());
            page.setTotalCount(resultVo.getContent().getTotal());
        }
//        page = hbaseDao.getPageByCondition(tableName, oid, patId, filters, page,
//                column);
//        "LAB_ITEM_CODE", "LAB_SUB_ITEM_CODE", "LAB_SUB_ITEM_NAME", "REPORT_TIME", "RANGE",
//                "LAB_RESULT_VALUE", "LAB_RESULT_UNIT", "LAB_QUAL_RESULT", "RESULT_STATUS_CODE", "ORDER_NO",
//                "RANGE"
        List<Map<String, String>> items = new ArrayList<Map<String, String>>();
        for (Map<String, String> map : page) {
            if (StringUtils.isNotBlank(map.get("LAB_SUB_ITEM_NAME"))) {
                Map<String, String> item = new HashMap<String, String>();
                ColumnUtil.convertMapping(item, map, column);
                items.add(item);
            }
        }
        //重置分页
        page.setResult(items);
//		result.put("list", page.getResult());
        return page;
    }

    @Override
    public long getOrderCount(String oid, String patientId, String visitId, String visitType) {

        Page<Map<String, String>> all = orderService.getVisitPageView(oid, patientId, visitId, visitType, "", "", "", "", "", "", 0, 0);
        long allCount = all.getTotalCount() < 0 ? 0 : all.getTotalCount(); //所有医嘱
        return allCount;
    }

    @Override
    public Map<String, Object> getTypeOrderCount(String oid, String patientId, String visitId, String visitType) {
        Map<String, Object> result = new HashMap<String, Object>();
        //查询医嘱数量
        List<Map<String, Object>> kfShowConfig = OrderShowConfigVo.getKfShowConfig(oid);
        Page<Map<String, String>> kf = getDrugListKF(oid, patientId, visitId, visitType, "", "", "", "", "", "", "ORDER_TIME", "desc", kfShowConfig, 0, 0);
        List<Map<String, Object>> jmShowConfig = OrderShowConfigVo.getJmShowConfig(oid);
        Page<Map<String, String>> jmzs = getDrugListJMZS(oid, patientId, visitId, visitType, "", "", "", "", "", "", "ORDER_TIME", "desc", jmShowConfig, 0, 0);
        Page<Map<String, String>> qt = getDrugListQTYP(oid, patientId, visitId, visitType, "", "", "", "ORDER_TIME", "desc", 0, 0);
        Page<Map<String, String>> exam = getExamList(oid, patientId, visitId, visitType, "", "", "", "", "ORDER_TIME", "desc", 0, 0);
        Page<Map<String, String>> lab = getLabList(oid, patientId, visitId, visitType, "", "", "", "", "ORDER_TIME", "desc", 0, 0);
        Page<Map<String, String>> opera = getOperList(oid, patientId, visitId, visitType, "", "ORDER_TIME", "desc", 0, 0);
        Page<Map<String, String>> blood = getBloodList(oid, patientId, visitId, 0, 0);
//        Page<Map<String, String>> nurse = getNurseOrderList(oid, patientId, visitId, visitType, "", "", 0, 0);
        Page<Map<String, String>> others = getOthersOrderList(oid, patientId, visitId, visitType, "", "", "", "ORDER_TIME", "desc", 0, 0);
        Page<Map<String, String>> all = orderService.getVisitPageView(oid, patientId, visitId, visitType, "", "", "", "", "ORDER_TIME", "desc", 0, 0);
        long kfCount = kf.getTotalCount() < 0 ? 0 : kf.getTotalCount(); //口服药品
        long jmzsCount = jmzs.getTotalCount() < 0 ? 0 : jmzs.getTotalCount(); //静脉药品
        long qtCount = qt.getTotalCount() < 0 ? 0 : qt.getTotalCount(); //静脉药品
        long examCount = exam.getTotalCount() < 0 ? 0 : exam.getTotalCount(); //检查
        long labCount = lab.getTotalCount() < 0 ? 0 : lab.getTotalCount(); //检验
        long operaCount = opera.getTotalCount() < 0 ? 0 : opera.getTotalCount(); //手术
        long bloodCount = blood.getTotalCount() < 0 ? 0 : blood.getTotalCount(); //用血
//        long nurseCount = nurse.getTotalCount() < 0 ? 0 : nurse.getTotalCount(); //护理
        long othersCount = others.getTotalCount() < 0 ? 0 : others.getTotalCount(); //其他类别的医嘱
        long allCount = all.getTotalCount() < 0 ? 0 : all.getTotalCount(); //所有医嘱
        result.put("kfCount", kfCount);
        result.put("jmzsCount", jmzsCount);
        result.put("qtCount", qtCount);
        result.put("examCount", examCount);
        result.put("labCount", labCount);
        result.put("operaCount", operaCount);
        result.put("bloodCount", bloodCount);
//        result.put("nurseCount", nurseCount);
        result.put("othersCount", othersCount);
        result.put("allCount", allCount);
        return result;
    }


    @Override
    public Map<String, String> getExamReportDetails(String oid, String patId, String visitType, String visitId, String field, String orderNo) {
        Map<String, String> result = new HashMap<String, String>();
        String tableName = "HDR_EXAM_REPORT";

        List<PropertyFilter> filters = new ArrayList<PropertyFilter>();
        if ("OUTPV".equals(visitType)) {
            visitType = "01";
            createPropertyFilter("VISIT_TYPE_CODE", "01", MatchType.EQ.getOperation(), filters);
        } else {
            visitType = "02";
            createPropertyFilter("VISIT_TYPE_CODE", "02", MatchType.EQ.getOperation(), filters);
        }
        String operation = Config.getCIV_ORDER_EXAM_MATCHTYPE(oid);
        createPropertyFilter(field, orderNo, operation, filters);

        List<Map<String, String>> jcExeHead = OclTableConfigVo.getJcExeHead(oid);
        String[] column = new String[jcExeHead.size()];
        for (int i = 0; i < jcExeHead.size(); i++) {
            column[i] = jcExeHead.get(i).get("field");
        }

        //"EXAM_FEATURE", "EXAM_DIAG", "EXAM_ITEM_NAME", "EXAM_ITEM_CODE", "REPORT_TIME",
        //                "ORDER_ITEM_CODE", "ORDER_ITEM_NAME"
        List<Map<String, String>> list = new ArrayList<>();
        com.goodwill.hdr.hbase.dto.responseVo.ResultVo<PageResultVo<Map<String, String>>> resultVo2 = hbaseQueryClient.getPageByCondition(
                PageRequestBuilder.init()
                        .tableName(tableName)
                        .patientId(patId)
                        .oid(oid)
                        .visitId(visitId)
                        .visitTypeCode("")
                        .filters(filters)
                        .pageNo(0)
                        .pageSize(0)
                        .orderBy("")
                        .desc()
                        .column(column)
                        .build());
        if (resultVo2.isSuccess()) {
            list = resultVo2.getContent().getResult();
        }
//        List<Map<String, String>> list = hbaseDao.findConditionByPatient(tableName, oid, patId, filters,
//                column);
        //未找到，终止执行
        if (list.size() == 0) {
            return result;
        }
        Map<String, String> map = list.get(0);
        //处理检查所见字段
        String examFeature = map.get("EXAM_FEATURE");
        if (StringUtils.isNotBlank(examFeature)) {
            //去掉 \X000d\
            if (examFeature.contains("\\X000d\\")) {
                result.put("examFeature", examFeature.replace("\\X000d\\", ""));
            } else {
                result.put("examFeature", examFeature);
            }
        } else {
            result.put("examFeature", "-");
        }
        //处理检查诊断字段
        String examDiag = map.get("EXAM_DIAG");
        if (StringUtils.isNotBlank(examDiag)) {
            //去掉 \X000d\
            if (examDiag.contains("\\X000d\\")) {
                result.put("examDiag", examDiag.replace("\\X000d\\", ""));
            } else {
                result.put("examDiag", examDiag);
            }
        } else {
            result.put("examDiag", "-");
        }
        ColumnUtil.convertMapping(result, map, column);
        return result;
    }

    /**
     * 查询护理医嘱
     *
     * @param patId
     * @param visitId
     * @param visitType
     * @param orderStatus
     * @param orderProperty
     * @param pageNo
     * @param pageSize
     * @return
     */
//    @Override
//    public Page<Map<String, String>> getNurseOrderList(String oid, String patId, String visitId, String visitType,
//                                                       String orderStatus, String orderProperty, int pageNo, int pageSize) {
//        Page<Map<String, String>> page = new Page<Map<String, String>>();
//        //过滤条件
//        StringBuffer filterStr = new StringBuffer();
//        //用药方式
//        if (StringUtils.isNotBlank(Config.getOCLJMZSFilter(oid))) {
//            if ("OUTPV".equals(visitType)) {
//                filterStr.append(Config.getOCL_MZ_NurseFilter(oid) + ";");
//            } else if ("INPV".equals(visitType)) {
//                filterStr.append(Config.getOCL_INPV_NurseFilter(oid) + ";");
//            }
//        }
//        //TODO 区分门诊和住院医嘱类别
//        List<String> orderClassStrings = new ArrayList<String>();
//        if ("OUTPV".equals(visitType)) {
//            orderClassStrings = CivUtils.getOrderClass(oid, "OrderClose_MZ_NURSE");
//        } else if ("INPV".equals(visitType)) {
//            orderClassStrings = CivUtils.getOrderClass(oid, "OrderClose_NURSE");
//        }
//        page = getOrderList(oid, patId, visitId, visitType, orderClassStrings, filterStr.toString(), "", "ORDER_TIME",
//                "desc", "", "", "", , pageNo, pageSize);
//
//
//        return page;
//    }

    /**
     * 查询其他医嘱
     *
     * @param patId
     * @param visitId
     * @param visitType
     * @param orderStatus
     * @param orderProperty
     * @param orederBy
     * @param orderDir
     * @param pageNo
     * @param pageSize
     * @return
     */
    @Override
    public Page<Map<String, String>> getOthersOrderList(String oid, String patId, String visitId, String visitType,
                                                        String orderStatus, String orderProperty, String orderItemName, String orederBy, String orderDir, int pageNo, int pageSize) {
        Page<Map<String, String>> page = new Page<Map<String, String>>();
        //过滤条件
        StringBuffer filterStr = new StringBuffer();

        if ("OUTPV".equals(visitType)) {
            filterStr.append(Config.getOCL_MZ_OthersFilter(oid) + ";");
        } else if ("INPV".equals(visitType)) {
            filterStr.append(Config.getOCL_INPV_OthersFilter(oid) + ";");
        }

        //TODO 区分门诊和住院医嘱类别
        List<String> orderClassStrings = new ArrayList<String>();
        if ("OUTPV".equals(visitType)) {
            orderClassStrings = CivUtils.getOrderClass(oid, "OrderClose_MZ_OTHERS");
        } else if ("INPV".equals(visitType)) {
            orderClassStrings = CivUtils.getOrderClass(oid, "OrderClose_OTHERS");
        }
        if (StringUtils.isNotBlank(orderItemName)) {
            filterStr.append("ORDER_ITEM_NAME|like|" + orderItemName + ";");
        }
        List<Map<String, Object>> otherShowConfig = OrderShowConfigVo.getOtherShowConfig(oid);
        page = getOrderList(oid, patId, visitId, visitType, orderClassStrings, filterStr.toString(), "", orederBy,
                orderDir, "", "", "", otherShowConfig, pageNo, pageSize,"");

        return page;
    }

    @Override
    public Page<Map<String, String>> getOrders(String oid, String patId, String orderStatus, String orderProperty, String orderType,
                                               String orderTimeBegin, String orderTimeEnd, String orderName, String orderby, String orderdir, int pageNo,
                                               int pageSize, String outPatientId, String visitType, String drugType, String drugProperty) {

        List<Map<String, String>> list = new ArrayList<Map<String, String>>();

//        if (StringUtils.isNotBlank(patId) && StringUtils.isNotBlank(this_oid) && this_oid.equals(oid) || this_oid.equals("ALL")) {
//            getOrdersByPat(oid, patId, visitType, orderStatus, orderProperty, orderType, orderTimeBegin, orderTimeEnd,
//                    orderName, orderby, orderdir, list);
//        }

        if (StringUtils.isNotBlank(outPatientId)) {
            String[] pats = outPatientId.split(",");
            for (int i = 0; i < pats.length; i++) {
                if (StringUtils.isNotBlank(pats[i])) {
                    String[] pat = pats[i].split("\\|");
                    getOrdersByPat(pat[2], pat[1], pat[0], pat[3], orderStatus, orderProperty, orderType, orderTimeBegin, orderTimeEnd,
                            orderName, orderby, orderdir, drugType, drugProperty, list);
                }
            }
        }
        if (StringUtils.isBlank(orderby)) {
            orderby = "orderBeginTime";
        }
        if(StringUtils.isBlank(orderdir)){
            orderdir = "desc";
        }
        if ("asc".equals(orderdir)) {
            sortListByDate(list, orderby, Page.Sort.ASC);
        } else {
            sortListByDate(list, orderby, Page.Sort.DESC);
        }
        //排序

        Page<Map<String, String>> page = new Page<Map<String, String>>();
        if (list.size() == 0) {
            return page;
        }
        ListPage<Map<String, String>> listPage = new ListPage<Map<String, String>>(list, pageNo, pageSize);


        //记录分页结果
        page.setPageNo(pageNo);
        page.setPageSize(pageSize);
        page.setOrderBy(orderby);
        page.setOrderDir(orderdir);
        page.setTotalCount(list.size());
        page.setTotalPages(listPage.getTotalPage());
        page.setResult(listPage.getPagedList());

        return page;
    }

    @Override
    public void getOrdersNum(Map<String, Object> resultMap, String orderType, String outPatientId) {

        int num = 0;


        if (StringUtils.isNotBlank(outPatientId)) {
            String[] pats = outPatientId.split(",");
            for (int i = 0; i < pats.length; i++) {
                if (StringUtils.isNotBlank(pats[i])) {
                    String[] pat = pats[i].split("\\|");
                    num = num + getOrdersByPat(pat[2], pat[1], pat[0], pat[3], orderType);
                }
            }
        }
        resultMap.put("num", num);

    }

    public int getOrdersByPat(String oid, String patId, String visitType, String visitId, String orderType) {
        int num = 0;
        StringBuffer filterStr = new StringBuffer(); //过滤条件
        //查询条件
        if ("01".equals(visitType)) {
            List<PropertyFilter> filters = new ArrayList<PropertyFilter>();

            String tableName = "";
            if (HdrConstantEnum.HOSPITAL_BYSY.getCode().equals(ConfigCache.getCache(oid, "org_oid"))) {
                tableName = HdrTableEnum.HDR_OUT_CHARGE.getCode();
                List<PropertyFilter> BYSYfilter = new ArrayList<PropertyFilter>();
                for (PropertyFilter propertyFilter : filters) {
                    BYSYfilter.add(propertyFilter);
                }
                //医嘱类型   KF-口服药   JM-静脉药  JY-检验  JC-检查  SS-手术   YX-临床用血
                if ("KF".equals(orderType)) { //口服药品
                    createPropertyFilter("PHARMACY_WAY_NAME", "口服", MatchType.LIKE.getOperation(), BYSYfilter);
                } else if ("JM".equals(orderType)) { //静脉药物
                    createPropertyFilter("PHARMACY_WAY_NAME", "静脉", MatchType.LIKE.getOperation(), BYSYfilter);
                }
                List<String> types = CivUtils.getOrderClass(oid, "BYSY_MZ_DRUG");
                //医嘱类别   药品，检查，检验...
                if (types != null && types.size() > 0) {
                    String orderClassString = "";
                    for (String orderString : types) {
                        orderClassString += orderString + ",";
                    }
                    orderClassString = orderClassString.substring(0, orderClassString.length() - 1);
                    createPropertyFilter("ORDER_CLASS_CODE", orderClassString, MatchType.IN.getOperation(), BYSYfilter);
                }
                //门诊医嘱
                List<Map<String, String>> page = new ArrayList<>();
                com.goodwill.hdr.hbase.dto.responseVo.ResultVo<PageResultVo<Map<String, String>>> resultVo2 = hbaseQueryClient.getPageByCondition(
                        PageRequestBuilder.init()
                                .tableName(tableName)
                                .patientId(patId)
                                .oid(oid)
                                .visitId(visitId)
                                .visitTypeCode("")
                                .filters(BYSYfilter)
                                .pageNo(0)
                                .pageSize(0)
                                .orderBy("")
                                .desc()
                                .column("ORDER_NO", "VISIT_ID")
                                .build());
                if (resultVo2.isSuccess()) {
                    page = resultVo2.getContent().getResult();
                }
//                List<Map<String, String>> page = hbaseDao.findConditionByPatient(tableName, oid, patId, BYSYfilter,
//                        new String[]{"ORDER_NO", "VISIT_ID"});
                num = num + page.size();

            } else {
                List<String> types = new ArrayList<String>();
                //医嘱类型   KF-口服药   JM-静脉药  JY-检验  JC-检查  SS-手术   YX-临床用血
                if ("KF".equals(orderType)) { //口服药品
                    types = CivUtils.getOrderClass(oid, "OrderClose_MZ_DRUG_KF");
                    filterStr.append(Config.getOclMzKffilter(oid) + ";");
                } else if ("JM".equals(orderType)) { //静脉药物
                    types = CivUtils.getOrderClass(oid, "OrderClose_MZ_DRUG_JMZS");
                    //静脉药物识别码
                    filterStr.append(Config.getOclMzJmzsfilter(oid) + ";");
                } else if ("QT".equals(orderType)) {//其他药物
                    types = CivUtils.getOrderClass(oid, "OrderClose_MZ_DRUG_QTYP");
                    //其他
                    filterStr.append(Config.getOclMzQTfilter(oid) + ";");
                } else if ("ALL".equals(orderType)) {//新增全部
                    filterStr.setLength(0);
                }
                strToFilter(filterStr.toString(), filters, ";");
                if (null != types && types.size() > 0) {
                    String orderClass = "";
                    //拼接医嘱类型
                    for (String type : types) {
                        orderClass = orderClass + type + ",";
                    }
                    orderClass = orderClass.substring(0, orderClass.length() - 1);
                    createPropertyFilter("ORDER_CLASS_CODE", orderClass, MatchType.IN.getOperation(), filters);
                }
                //createPropertyFilter("VISIT_ID", visitId, MatchType.EQ.getOperation(), filters);
                tableName = HdrTableEnum.HDR_OUT_ORDER.getCode();
                //门诊医嘱
                List<Map<String, String>> outOrders = new ArrayList<>();
                com.goodwill.hdr.hbase.dto.responseVo.ResultVo<PageResultVo<Map<String, String>>> resultVo2 = hbaseQueryClient.getPageByCondition(
                        PageRequestBuilder.init()
                                .tableName(tableName)
                                .patientId(patId)
                                .oid(oid)
                                .visitId(visitId)
                                .visitTypeCode("")
                                .filters(filters)
                                .pageNo(0)
                                .pageSize(0)
                                .orderBy("")
                                .desc()
                                .column()
                                .build());
                if (resultVo2.isSuccess()) {
                    outOrders = resultVo2.getContent().getResult();
                }
                //List<Map<String, String>> outOrders = hbaseDao.findConditionByPatient(tableName, oid, patId, filters
                //);
                num = num + outOrders.size();
            }
        } else {
            List<PropertyFilter> filters = new ArrayList<PropertyFilter>();
            filterStr = new StringBuffer();
            //住院医嘱
            List<String> types = new ArrayList<String>();
            //医嘱类型   KF-口服药   JM-静脉药  JY-检验  JC-检查  SS-手术   YX-临床用血
            if ("KF".equals(orderType)) { //口服药品
                types = CivUtils.getOrderClass(oid, "OrderClose_DRUG_KF");
                filterStr.append(Config.getOCLKFFilter(oid) + ";");
            } else if ("JM".equals(orderType)) { //静脉药物
                types = CivUtils.getOrderClass(oid, "OrderClose_DRUG_JMZS");
                //静脉药物识别码
                filterStr.append(Config.getOCLJMZSFilter(oid) + ";");
            } else if ("QT".equals(orderType)) {//其他药物
                types = CivUtils.getOrderClass(oid, "OrderClose_DRUG_QTYP");
                //其他
                filterStr.append(Config.getOCLQTFilter(oid) + ";");
            } else if ("ALL".equals(orderType)) {//新增全部
                filterStr.setLength(0);
            }
            strToFilter(filterStr.toString(), filters, ";");
            if (null != types && types.size() > 0) {
                String orderClass = "";
                //拼接医嘱类型
                for (String type : types) {
                    orderClass = orderClass + type + ",";
                }
                orderClass = orderClass.substring(0, orderClass.length() - 1);
                createPropertyFilter("ORDER_CLASS_CODE", orderClass, MatchType.IN.getOperation(), filters);
            }
            //createPropertyFilter("VISIT_ID", visitId, MatchType.EQ.getOperation(), filters);
            //排除状态为撤销的医嘱
            //			createPropertyFilter("ORDER_STATUS_NAME", "撤销,废弃", MatchType.NOTIN.getOperation(), filters);
            //住院医嘱
            List<Map<String, String>> inpOrders = new ArrayList<>();
            com.goodwill.hdr.hbase.dto.responseVo.ResultVo<PageResultVo<Map<String, String>>> resultVo2 = hbaseQueryClient.getPageByCondition(
                    PageRequestBuilder.init()
                            .tableName(HdrTableEnum.HDR_IN_ORDER.getCode())
                            .patientId(patId)
                            .oid(oid)
                            .visitId(visitId)
                            .visitTypeCode("")
                            .filters(filters)
                            .pageNo(0)
                            .pageSize(0)
                            .orderBy("")
                            .desc()
                            .column()
                            .build());
            if (resultVo2.isSuccess()) {
                inpOrders = resultVo2.getContent().getResult();
            }
//            List<Map<String, String>> inpOrders = hbaseDao.findConditionByPatient(HdrTableEnum.HDR_IN_ORDER.getCode(), oid,
//                    patId, filters);
            num = num + inpOrders.size();
        }

        return num;
    }


    /**
     * 获取患者末次就诊显示配置
     *
     * @return
     */
    @Override
    public Map<String, String> getPatLastInfoViewConfig(String oid) {
        Map<String, String> confgiMap = new HashMap<String, String>();
        confgiMap.put("name", "姓名");
        confgiMap.put("birthday", "出生年月");
        confgiMap.put("patient_id", "患者ID");
        //就诊号  门诊显示
        confgiMap.put("visit_no", "门诊号");
        //就诊号  住院显示
        confgiMap.put("in_no", "住院号");
        confgiMap.put("visit_dept", "科室");
        //就诊日期 门诊显示
        confgiMap.put("visit_time", "就诊日期");
        //就诊日期 住院显示
        confgiMap.put("admission_time", "入院日期");
        confgiMap.put("sex", "性别");
        confgiMap.put("bed_no", "床号");
        confgiMap.put("main_diag", "主诊断");
        confgiMap.put("visit_num", "全部就诊");
        String configStr = Config.getPATIENT_LAST_VISIT_INFO_VIEW(oid);
        if (org.apache.commons.lang.StringUtils.isNotBlank(configStr)) {
            String[] configs = configStr.split(";");
            for (String viewItem : configs) {
                if (org.apache.commons.lang.StringUtils.isNotBlank(viewItem) && viewItem.contains("=")) {
                    String[] viewItems = viewItem.split("=");
                    confgiMap.put(viewItems[0], viewItems[1]);
                }
            }
        }
        return confgiMap;
    }

    public void getOrdersByPat(String oid, String patId, String visitType, String visitId, String orderStatus, String orderProperty,
                               String orderType, String orderTimeBegin, String orderTimeEnd, String orderName, String orderby,
                               String orderdir, String drugType, String drugProperty, List<Map<String, String>> list) {
        StringBuffer filterStr = new StringBuffer(); //过滤条件
        List<Map<String, Object>> headMapList = null;
        if ("01".equals(visitType)) {
            //查询条件
            List<PropertyFilter> filters = new ArrayList<PropertyFilter>();
            filters.add(new PropertyFilter("VISIT_TYPE_CODE", MatchType.EQ.getOperation(), visitType));
            //医嘱性质   1-临时    2-长期
            if (StringUtils.isNotBlank(orderProperty)) {
                if ("1".equals(orderProperty)) {
                    createPropertyFilter("ORDER_PROPERTIES_NAME", Config.getORDER_SHORT_PROPERTY_CONFIG(oid),
                            MatchType.IN.getOperation(), filters);
                } else if ("2".equals(orderProperty)) {
                    createPropertyFilter("ORDER_PROPERTIES_NAME", Config.getORDER_LONG_PROPERTY_CONFIG(oid),
                            MatchType.IN.getOperation(), filters);
                }
            }
            //医嘱状态
            if (StringUtils.isNotBlank(orderStatus)) {
                createPropertyFilter("ORDER_STATUS_NAME", orderStatus, MatchType.LIKE.getOperation(), filters);
            }
            if (StringUtils.isNotBlank(drugType)) {
                createPropertyFilter("YPSX1", drugType, MatchType.EQ.getOperation(), filters);
            }
            if (StringUtils.isNotBlank(drugProperty)) {
                createPropertyFilter("YPSX2", drugProperty, MatchType.EQ.getOperation(), filters);
            }

            //医嘱状态条件  排除撤销 和 废弃的医嘱
            //		createPropertyFilter("ORDER_STATUS_NAME", "撤销,废弃", MatchType.NOTIN.getOperation(), filters);
            //开立时间
            if (StringUtils.isNotBlank(orderTimeBegin)) {
                createPropertyFilter("ORDER_TIME", orderTimeBegin, MatchType.GE.getOperation(), filters);
            }
            if (StringUtils.isNotBlank(orderTimeEnd)) {
                createPropertyFilter("ORDER_TIME", orderTimeEnd, MatchType.LE.getOperation(), filters);
            }

            String tableName = "";
            if (HdrConstantEnum.HOSPITAL_BYSY.getCode().equals(ConfigCache.getCache(oid, "org_oid"))) {
                tableName = HdrTableEnum.HDR_OUT_CHARGE.getCode();
                List<PropertyFilter> BYSYfilter = new ArrayList<PropertyFilter>();
                for (PropertyFilter propertyFilter : filters) {
                    BYSYfilter.add(propertyFilter);
                }
                if (StringUtils.isNotBlank(orderName)) {
                    createPropertyFilter("CHARGE_NAME", orderName, MatchType.LIKE.getOperation(), BYSYfilter);
                }

                //医嘱类型   KF-口服药   JM-静脉药  JY-检验  JC-检查  SS-手术   YX-临床用血
                if ("KF".equals(orderType)) { //口服药品
                    createPropertyFilter("PHARMACY_WAY_NAME", "口服", MatchType.LIKE.getOperation(), BYSYfilter);
                    headMapList = OrderShowConfigVo.getKfShowConfig(oid);
                } else if ("JM".equals(orderType)) { //静脉药物
                    createPropertyFilter("PHARMACY_WAY_NAME", "静脉", MatchType.LIKE.getOperation(), BYSYfilter);
                    headMapList = OrderShowConfigVo.getJmShowConfig(oid);
                }
                List<String> types = CivUtils.getOrderClass(oid, "BYSY_MZ_DRUG");
                //医嘱类别   药品，检查，检验...
                if (types != null && types.size() > 0) {
                    String orderClassString = "";
                    for (String orderString : types) {
                        orderClassString += orderString + ",";
                    }
                    orderClassString = orderClassString.substring(0, orderClassString.length() - 1);
                    createPropertyFilter("ORDER_CLASS_CODE", orderClassString, MatchType.IN.getOperation(), BYSYfilter);
                }
                //门诊医嘱
                List<Map<String, String>> page = new ArrayList<>();
                com.goodwill.hdr.hbase.dto.responseVo.ResultVo<PageResultVo<Map<String, String>>> resultVo2 = hbaseQueryClient.getPageByCondition(
                        PageRequestBuilder.init()
                                .tableName(tableName)
                                .patientId(patId)
                                .oid(oid)
                                .visitId(visitId)
                                .visitTypeCode("")
                                .filters(BYSYfilter)
                                .pageNo(0)
                                .pageSize(0)
                                .orderBy(orderby)
                                .desc()
                                .column()
                                .build());
                if (resultVo2.isSuccess()) {
                    page = resultVo2.getContent().getResult();
                }
                //List<Map<String, String>> page = hbaseDao.findConditionByPatient(tableName, oid, patId, BYSYfilter);
                for (Map<String, String> map : page) {
                    Map<String, String> order = new HashMap<String, String>();

                    for (Map<String, Object> headMap : headMapList) {
                        String name = (String) headMap.get("name");
                        String field = (String) headMap.get("field");
                        if ("JM".equals(orderType)) {
                            Utils.checkAndPutToMap(order, "groupSortColumn", map.get("GROUPSORTCOLUMN"), "-", false); //组标志
                            if ("parentOrderNo".equals(name)) {
                                if (StringUtils.isNotBlank(map.get("PARENT_ORDER_NO"))) {
                                    Utils.checkAndPutToMap(order, "parentOrderNo", map.get("PARENT_ORDER_NO"), "-", false);
                                    //父医嘱
                                } else {
                                    Utils.checkAndPutToMap(order, "parentOrderNo", map.get("ORDER_NO"), "-", false);//父医嘱				}
                                }
                            }
                        }
                        Utils.checkAndPutToMap(order, name, map.get(field), "-", false);
                    }


                    order.put("patient_id", patId);
                    list.add(order);
                }

            } else {
                List<String> types = new ArrayList<String>();
                //医嘱类型   KF-口服药   JM-静脉药  JY-检验  JC-检查  SS-手术   YX-临床用血
                if ("KF".equals(orderType)) { //口服药品
                    types = CivUtils.getOrderClass(oid, "OrderClose_MZ_DRUG_KF");
                    filterStr.append(Config.getOclMzKffilter(oid) + ";");
                    headMapList = OrderShowConfigVo.getKfShowConfig(oid);
                } else if ("JM".equals(orderType)) { //静脉药物
                    types = CivUtils.getOrderClass(oid, "OrderClose_MZ_DRUG_JMZS");
                    //静脉药物识别码
                    filterStr.append(Config.getOclMzJmzsfilter(oid) + ";");
                    headMapList = OrderShowConfigVo.getJmShowConfig(oid);
                } else if ("QT".equals(orderType)) {//其他药物
                    types = CivUtils.getOrderClass(oid, "OrderClose_MZ_DRUG_QTYP");
                    //其他
                    filterStr.append(Config.getOclMzQTfilter(oid) + ";");
                    headMapList = OrderShowConfigVo.getQtShowConfig(oid);
                } else if ("ALL".equals(orderType)) {//新增全部
                    filterStr.setLength(0);
                    headMapList = OrderShowConfigVo.getAllShowConfig(oid);
                }

                if (StringUtils.isNotBlank(orderName)) {
                    createPropertyFilter("ORDER_ITEM_NAME", orderName, MatchType.LIKE.getOperation(), filters);
                }
                strToFilter(filterStr.toString(), filters, ";");
                if (null != types && types.size() > 0) {
                    String orderClass = "";
                    //拼接医嘱类型
                    for (String type : types) {
                        orderClass = orderClass + type + ",";
                    }
                    orderClass = orderClass.substring(0, orderClass.length() - 1);
                    createPropertyFilter("ORDER_CLASS_CODE", orderClass, MatchType.IN.getOperation(), filters);
                }
                tableName = HdrTableEnum.HDR_OUT_ORDER.getCode();
                //门诊医嘱
                //filters.add(new PropertyFilter("VISIT_ID", "STRING", MatchType.EQ.getOperation(), visitId));
                List<Map<String, String>> outOrders = new ArrayList<>();
                com.goodwill.hdr.hbase.dto.responseVo.ResultVo<PageResultVo<Map<String, String>>> resultVo2 = hbaseQueryClient.getPageByCondition(
                        PageRequestBuilder.init()
                                .tableName(tableName)
                                .patientId(patId)
                                .oid(oid)
                                .visitId(visitId)
                                .visitTypeCode("01")
                                .filters(filters)
                                .pageNo(0)
                                .pageSize(0)
                                .orderBy("")
                                .desc()
                                .column()
                                .build());
                if (resultVo2.isSuccess()) {
                    outOrders = resultVo2.getContent().getResult();
                }
                //List<Map<String, String>> outOrders = hbaseDao.findConditionByPatient(tableName, oid, patId, filters);
                if (outOrders.size() > 0) {

                    for (Map<String, String> map : outOrders) {
                        Map<String, String> order = new HashMap<String, String>();

                        for (Map<String, Object> headMap : headMapList) {
                            String name = (String) headMap.get("name");
                            String field = (String) headMap.get("field");
                            if ("JM".equals(orderType)) {
                                Utils.checkAndPutToMap(order, "groupSortColumn", map.get("GROUPSORTCOLUMN"), "-", false); //组标志
                                if ("parentOrderNo".equals(name)) {
                                    if (StringUtils.isNotBlank(map.get("PARENT_ORDER_NO"))) {
                                        Utils.checkAndPutToMap(order, "parentOrderNo", map.get("PARENT_ORDER_NO"), "-", false);
                                        //父医嘱
                                    } else {
                                        Utils.checkAndPutToMap(order, "parentOrderNo", map.get("ORDER_NO"), "-", false);//父医嘱				}
                                    }
                                }
                            }
                            Utils.checkAndPutToMap(order, name, map.get(field), "-", false);
                        }
                        order.put("patient_id", patId);
                        list.add(order);
                    }
                }
            }
        } else {
            //住院医嘱
            List<PropertyFilter> filters = new ArrayList<PropertyFilter>();
            filterStr = new StringBuffer();
            //医嘱性质   1-临时    2-长期
            if (StringUtils.isNotBlank(orderProperty)) {
                if ("1".equals(orderProperty)) {
                    createPropertyFilter("ORDER_PROPERTIES_NAME", Config.getORDER_SHORT_PROPERTY_CONFIG(oid),
                            MatchType.IN.getOperation(), filters);
                } else if ("2".equals(orderProperty)) {
                    createPropertyFilter("ORDER_PROPERTIES_NAME", Config.getORDER_LONG_PROPERTY_CONFIG(oid),
                            MatchType.IN.getOperation(), filters);
                }
            }
            //医嘱状态
            if (StringUtils.isNotBlank(orderStatus)) {
                createPropertyFilter("ORDER_STATUS_NAME", orderStatus, MatchType.LIKE.getOperation(), filters);
            }

            //医嘱状态条件  排除撤销 和 废弃的医嘱
            //createPropertyFilter("ORDER_STATUS_NAME", "撤销,废弃", MatchType.NOTIN.getOperation(), filters);
            //开立时间
            if (StringUtils.isNotBlank(orderTimeBegin)) {
                createPropertyFilter("ORDER_TIME", orderTimeBegin, MatchType.GE.getOperation(), filters);
            }
            if (StringUtils.isNotBlank(orderTimeEnd)) {
                createPropertyFilter("ORDER_TIME", orderTimeEnd, MatchType.LE.getOperation(), filters);
            }
            List<String> types = new ArrayList<String>();
            //医嘱类型   KF-口服药   JM-静脉药  JY-检验  JC-检查  SS-手术   YX-临床用血
            if ("KF".equals(orderType)) { //口服药品
                types = CivUtils.getOrderClass(oid, "OrderClose_DRUG_KF");
                filterStr.append(Config.getOCLKFFilter(oid) + ";");
                headMapList = OrderShowConfigVo.getKfShowConfig(oid);
            } else if ("JM".equals(orderType)) { //静脉药物
                types = CivUtils.getOrderClass(oid, "OrderClose_DRUG_JMZS");
                //静脉药物识别码
                filterStr.append(Config.getOCLJMZSFilter(oid) + ";");
                headMapList = OrderShowConfigVo.getJmShowConfig(oid);
            } else if ("QT".equals(orderType)) {
                types = CivUtils.getOrderClass(oid, "OrderClose_DRUG_QTYP");
                //静脉药物识别码
                filterStr.append(Config.getOCLQTFilter(oid) + ";");
                headMapList = OrderShowConfigVo.getQtShowConfig(oid);
            } else if ("ALL".equals(orderType)) {//新增全部
                filterStr.setLength(0);
                headMapList = OrderShowConfigVo.getAllShowConfig(oid);
            }
            strToFilter(filterStr.toString(), filters, ";");
            if (null != types && types.size() > 0) {
                String orderClass = "";
                //拼接医嘱类型
                for (String type : types) {
                    orderClass = orderClass + type + ",";
                }
                orderClass = orderClass.substring(0, orderClass.length() - 1);
                createPropertyFilter("ORDER_CLASS_CODE", orderClass, MatchType.IN.getOperation(), filters);
            }
            //排除状态为撤销的医嘱
            //			createPropertyFilter("ORDER_STATUS_NAME", "撤销,废弃", MatchType.NOTIN.getOperation(), filters);
            if (StringUtils.isNotBlank(orderName)) {
                createPropertyFilter("ORDER_ITEM_NAME", orderName, MatchType.LIKE.getOperation(), filters);
            }
            //filters.add(new PropertyFilter("VISIT_ID", "STRING", MatchType.EQ.getOperation(), visitId));
            //住院医嘱
            List<Map<String, String>> inpOrders = new ArrayList<>();
            com.goodwill.hdr.hbase.dto.responseVo.ResultVo<PageResultVo<Map<String, String>>> resultVo2 = hbaseQueryClient.getPageByCondition(
                    PageRequestBuilder.init()
                            .tableName(HdrTableEnum.HDR_IN_ORDER.getCode())
                            .patientId(patId)
                            .oid(oid)
                            .visitId(visitId)
                            .visitTypeCode("02")
                            .filters(filters)
                            .pageNo(0)
                            .pageSize(0)
                            .orderBy("")
                            .desc()
                            .column()
                            .build());
            if (resultVo2.isSuccess()) {
                inpOrders = resultVo2.getContent().getResult();
            }
//            List<Map<String, String>> inpOrders = hbaseDao.findConditionByPatient(HdrTableEnum.HDR_IN_ORDER.getCode(), oid,
//                    patId, filters);
            if (inpOrders.size() > 0) {


                for (Map<String, String> map : inpOrders) {
                    Map<String, String> order = new HashMap<String, String>();

                    for (Map<String, Object> headMap : headMapList) {
                        String name = (String) headMap.get("name");
                        String field = (String) headMap.get("field");
                        /*if ("JM".equals(orderType)) {
                            Utils.checkAndPutToMap(order, "groupSortColumn", map.get("GROUPSORTCOLUMN"), "-", false); //组标志
                            if ("parentOrderNo".equals(name)) {
                                if (StringUtils.isNotBlank(map.get("PARENT_ORDER_NO"))) {
                                    Utils.checkAndPutToMap(order, "parentOrderNo", map.get("PARENT_ORDER_NO"), "-", false);
                                    //父医嘱
                                } else {
                                    Utils.checkAndPutToMap(order, "parentOrderNo", map.get("ORDER_NO"), "-", false);//父医嘱				}
                                }
                            }
                        }*/
                        Utils.checkAndPutToMap(order, name, map.get(field), "-", false);
                    }
                    if ("JM".equals(orderType)) {
                        Utils.checkAndPutToMap(order, "groupSortColumn", map.get("GROUPSORTCOLUMN"), "-", false); //组标志
                        if (StringUtils.isBlank(order.get("parentOrderNo"))) {
                            order.put("parentOrderNo", order.get("orderNo"));
                        }
                    }
                    order.put("patient_id", patId);
                    list.add(order);
                }

            }
        }
        sortListByDate(list, orderby, orderdir);

    }

    @Override
    public Page<Map<String, String>> getVisitPageView(String oid, String patientId, String visitId, String visitType,
                                                      String orderStatus, String orderProperty, String orderType, String orderItemName, String orderBy, String orderDir, int pageNo,
                                                      int pageSize) {
        String tableName = null;
        if ("OUTPV".equals(visitType)) {
            tableName = HdrTableEnum.HDR_OUT_ORDER.getCode();
        } else if ("INPV".equals(visitType)) {
            tableName = HdrTableEnum.HDR_IN_ORDER.getCode();
        }

        if (StringUtils.isEmpty(orderBy)) {
            orderBy = "ORDER_TIME";
        }
        if (StringUtils.isEmpty(orderDir)) {
            orderDir = "desc";
        }
        if (pageNo == 0) {
            pageNo = 1;
        }
        if (pageSize == 0) {
            pageSize = 10;
        }
        //按条件查询
        List<PropertyFilter> filters = new ArrayList<PropertyFilter>();
//        PropertyFilter filter1 = new PropertyFilter();
//        filter1.setMatchType("=");
//        filter1.setPropertyName("VISIT_ID");
//        filter1.setPropertyValue(visitId);
//        //filter1.setPropertyType("STRING");
//        filters.add(filter1);
        StringBuffer filterStr = new StringBuffer();
        //医嘱性质   1-临时    2-长期
        if (StringUtils.isNotBlank(orderProperty)) {
            if ("1".equals(orderProperty)) {
                filterStr.append("ORDER_PROPERTIES_NAME|in|" + Config.getORDER_SHORT_PROPERTY_CONFIG(oid) + ";");
            } else if ("2".equals(orderProperty)) {
                filterStr.append("ORDER_PROPERTIES_NAME|in|" + Config.getORDER_LONG_PROPERTY_CONFIG(oid) + ";");
            }
        }
        //医嘱状态
        if (StringUtils.isNotBlank(orderStatus)) {
            filterStr.append("ORDER_STATUS_NAME|like|" + orderStatus + ";");
        }
        //医嘱名称
        if (StringUtils.isNotBlank(orderItemName)) {
            filterStr.append("ORDER_ITEM_NAME|like|" + orderItemName + ";");
        }
        //医嘱性质 和 医嘱状态 或其他条件
        strToFilter(filterStr.toString(), filters, ";");
        Page<Map<String, String>> page = new Page<Map<String, String>>();
        page.setOrderBy(orderBy);
        page.setOrderDir(orderDir);
        page.setPageNo(1);
        page.setPageSize(100000);
        //排除状态为撤销的医嘱
        if (StringUtils.isNotBlank(Config.getOrderStatus(oid))) {
            createPropertyFilter("ORDER_STATUS_NAME", Config.getOrderStatus(oid), MatchType.NOTIN.getOperation(), filters);
        }

        Page<Map<String, String>> resultPage = new Page<>();
        com.goodwill.hdr.hbase.dto.responseVo.ResultVo<PageResultVo<Map<String, String>>> resultVo2 = hbaseQueryClient.getPageByCondition(
                PageRequestBuilder.init()
                        .tableName(tableName)
                        .patientId(patientId)
                        .oid(oid)
                        .visitId(visitId)
                        .visitTypeCode("")
                        .filters(filters)
                        .pageNo(0)
                        .pageSize(0)
                        .orderBy(orderBy)
                        .desc()
                        .column()
                        .build());
        if (resultVo2.isSuccess()) {
            resultPage.setResult(resultVo2.getContent().getResult());
            resultPage.setTotalCount(resultVo2.getContent().getTotal());
        }

        //Page<Map<String, String>> resultPage = hbaseDao.findPageConditionByPatient(tableName, oid, patientId, page, filters);
        List<Map<String, Object>> allShowConfig = OrderShowConfigVo.getAllShowConfig(oid);
        Optional<String> orderByField = Optional.empty();
        for (Map<String, Object> headMap : allShowConfig) {
            String name = (String) headMap.get("name");
            String column = (String) headMap.get("field");
            if (name.equals(orderBy)) {
                orderByField = Optional.of(column);
            }

        }


        //解决先分页再排序问题
        List<Map<String, String>> listRes = resultPage.getResult();
        String orgOid=ConfigCache.getCache(oid, "org_oid");
        logger.info("oid..........."+orgOid);
        if("E48000538".equalsIgnoreCase(orgOid)) {//鞍钢集团公司总医院
            listRes.forEach(map -> {
                String orderNo = map.get("ORDER_NO");
                if (orderNo != null && !orderNo.isEmpty()) {
                    String[] parts = orderNo.split("\\|\\|");
                    if (parts.length >= 2) {
                        map.put("orderColumn", parts[1]);
                    }
                }
            });
            listRes = listRes.stream()
                    .sorted((map1, map2) -> {
                        try {
                            int num1 = Integer.parseInt(map1.getOrDefault("orderColumn", "0"));
                            int num2 = Integer.parseInt(map2.getOrDefault("orderColumn", "0"));
                            return Integer.compare(num2, num1);
                        } catch (NumberFormatException e) {
                            return 0;
                        }
                    })
                    .collect(Collectors.toList());
        }else {
            if (orderDir.equals("desc")) {
                Optional<String> finalOrderByField = orderByField;
                listRes = listRes.stream().sorted((map1, map2) -> map2.get(finalOrderByField.orElse("ORDER_TIME")).compareToIgnoreCase(map1.get(finalOrderByField.orElse("ORDER_TIME")))).collect(Collectors.toList());
            } else {
                Optional<String> finalOrderByField = orderByField;
                listRes = listRes.stream().sorted((map1, map2) -> map1.get(finalOrderByField.orElse("ORDER_TIME")).compareToIgnoreCase(map2.get(finalOrderByField.orElse("ORDER_TIME")))).collect(Collectors.toList());
            }
        }

        //分页
        ListPage<Map<String, String>> listPage = new ListPage<Map<String, String>>(listRes, pageNo, pageSize);
        page.setResult(listPage.getPagedList());
        page.setPageNo(pageNo);
        page.setPageSize(pageSize);
        //字段转换
        List<Map<String, String>> list = new ArrayList<Map<String, String>>();


        for (Map<String, String> map : page.getResult()) {
            Map<String, String> order = new HashMap<String, String>();
            Utils.checkAndPutToMap(order, "orderType", getOrderType(oid, visitType, map.get("ORDER_CLASS_CODE"),"ALL"), "-", false); //医嘱类别

            for (Map<String, Object> headMap : allShowConfig) {
                String name = (String) headMap.get("name");
                String column = (String) headMap.get("field");
                Utils.checkAndPutToMap(order, name, map.get(column), "-", false);

            }

            list.add(order);
        }


        page.setResult(list);
        page.setTotalCount(resultPage.getTotalCount());
        return page;
    }

    @Override
    public Page<Map<String, String>> getDrugListQTYP(String oid, String patId, String visitId, String visitType, String orderStatus,
                                                     String orderProperty, String orderItemName, String orderBy, String orderDir, int pageNo, int pageSize) {
        //过滤条件
        StringBuffer filterStr = new StringBuffer();
        //医嘱性质   1-临时    2-长期
        if (StringUtils.isNotBlank(orderProperty)) {
            if ("1".equals(orderProperty)) {
                filterStr.append("ORDER_PROPERTIES_NAME|in|" + Config.getORDER_SHORT_PROPERTY_CONFIG(oid) + ";");
            } else if ("2".equals(orderProperty)) {
                filterStr.append("ORDER_PROPERTIES_NAME|in|" + Config.getORDER_LONG_PROPERTY_CONFIG(oid) + ";");
            }
        }
        //医嘱状态
        if (StringUtils.isNotBlank(orderStatus)) {
            filterStr.append("ORDER_STATUS_NAME|like|" + orderStatus + ";");
        }
        if (StringUtils.isNotBlank(orderItemName)) {
            filterStr.append("ORDER_ITEM_NAME|like|" + orderItemName + ";");
        }
        Page<Map<String, String>> page = new Page<Map<String, String>>();
        List<Map<String, Object>> qtShowConfig = OrderShowConfigVo.getQtShowConfig(oid);
        //Map<String, String> columnMap = getDisplayColumns(oid, "4");
        //北医三院门诊医嘱特殊处理
        if (HdrConstantEnum.HOSPITAL_BYSY.getCode().equals(ConfigCache.getCache(oid, "org_oid")) && "OUTPV".equals(visitType)) {
            //置空条件
            filterStr = new StringBuffer();
            //其他药品
            page = getOrderListMZ(oid, patId, visitId, CivUtils.getOrderClass(oid, "OrderClose_MZ_DRUG_QTYP"),
                    filterStr.toString(), "", "ORDER_TIME", "desc", pageNo, pageSize);
            //字段映射
            List<Map<String, String>> orders = new ArrayList<Map<String, String>>();

            for (Map<String, String> map : page) {
                Map<String, String> order = new HashMap<String, String>();

                for (Map<String, Object> headMap : qtShowConfig) {
                    String name = (String) headMap.get("name");
                    String column = (String) headMap.get("field");
                    if ("orderType".equals(name)) {
                        Utils.checkAndPutToMap(order, "orderType", getOrderType(oid, visitType, map.get("ORDER_CLASS_CODE"),"QTYP"), "-", false); //医嘱类别
                        continue;
                    }
                    Utils.checkAndPutToMap(order, name, map.get(column), "-", false);
                }
                orders.add(order);
            }
            //重置分页
            page.setResult(orders);
            return page;
        }
        //非北医三院门诊  正常查询
        //TODO 区分门诊和住院医嘱类别
        List<String> orderClassStrings = new ArrayList<String>();
        //用药方式
        if (StringUtils.isNotBlank(Config.getOclMzQTfilter(oid)) || StringUtils.isNotBlank(Config.getOCLQTFilter(oid))) {
            if ("OUTPV".equals(visitType)) {
                filterStr.append(Config.getOclMzQTfilter(oid) + ";");
            } else if ("INPV".equals(visitType)) {
                filterStr.append(Config.getOCLQTFilter(oid) + ";");
            }
        }
        if ("OUTPV".equals(visitType)) {
            orderClassStrings = CivUtils.getOrderClass(oid, "OrderClose_MZ_DRUG_QTYP");
        } else if ("INPV".equals(visitType)) {
            orderClassStrings = CivUtils.getOrderClass(oid, "OrderClose_DRUG_QTYP");
        }

        page = getOrderList(oid, patId, visitId, visitType, orderClassStrings, filterStr.toString(), "", orderBy,
                orderDir, "", "", "", qtShowConfig, pageNo, pageSize,"QTYP");
        List<Map<String, String>> orders = new ArrayList<Map<String, String>>();


        return page;
    }

    @Override
    public Page<Map<String, String>> getCVDrugList(String oid, String patientId, String visitId, String orderType, String visitType,
                                                   String orderStatus, String orderProperty, String mainDiag, String deptCode, String orderCode, String orderItemName, String orderBy, String orderDir, int pageNo,
                                                   int pageSize) {
        Page<Map<String, String>> page = new Page<Map<String, String>>();
        //类型判断 口服 药品
        if ("kf".equals(orderType)) {
            List<Map<String, Object>> kfShowConfig = OrderShowConfigVo.getCurrentKfShowConfig(oid);
            page = getDrugListKF(oid, patientId, visitId, visitType, orderStatus, orderProperty, mainDiag, orderCode,
                    deptCode, orderItemName, orderBy, orderDir, kfShowConfig, pageNo, pageSize);
        } else if ("jm".equals(orderType)) {
            List<Map<String, Object>> jmShowConfig = OrderShowConfigVo.getCurrentJmShowConfig(oid);
            page = getDrugListJMZS(oid, patientId, visitId, visitType, orderStatus, orderProperty, mainDiag, orderCode,
                    deptCode, orderItemName, orderBy, orderDir, jmShowConfig, pageNo, pageSize);
        }

        return page;
    }

    /**
     * 获取医技视图末次就诊手术医嘱
     *
     * @param this_oid
     * @param oid
     * @param patientId
     * @param visitId
     * @param visitType
     * @return
     */
    @Override
    public ResultVO<List<Map<String, String>>> getOperApplyList(String this_oid, String oid, String patientId, String visitId, String visitType) {
        ResultVO<List<Map<String, String>>> resultVO = new ResultVO<>();
        List<Map<String, String>> result = new ArrayList<>();
        List<PropertyFilter> filters = new ArrayList<PropertyFilter>();
        //createPropertyFilter("VISIT_ID", visitId, MatchType.EQ.getOperation(), filters);
        createPropertyFilter("VISIT_TYPE_CODE", visitType, MatchType.EQ.getOperation(), filters);
        if (this_oid.equals(oid) || "ALL".equals(this_oid)) {
            this_oid = oid;
        }
        List<Map<String, String>> list = new ArrayList<>();
        com.goodwill.hdr.hbase.dto.responseVo.ResultVo<PageResultVo<Map<String, String>>> resultVo2 = hbaseQueryClient.getPageByCondition(
                PageRequestBuilder.init()
                        .tableName(HdrTableEnum.HDR_OPER_APPLY.getCode())
                        .patientId(patientId)
                        .oid(this_oid)
                        .visitId(visitId)
                        .visitTypeCode("")
                        .filters(filters)
                        .pageNo(0)
                        .pageSize(0)
                        .orderBy("")
                        .desc()
                        .column("OPERATION_NAME", "DIAG_BEFORE_OPERATION_NAME", "PLAN_OPER_DOCTOR_NAME", "PLAN_OPER_TIME", "APPLY_OPER_TIME")
                        .build());
        if (resultVo2.isSuccess()) {
            list = resultVo2.getContent().getResult();
        }
//        List<Map<String, String>> list = hbaseDao.findConditionByPatient("HDR_OPER_APPLY", this_oid, patientId, filters,
//                new String[]{"OPERATION_NAME", "DIAG_BEFORE_OPERATION_NAME", "PLAN_OPER_DOCTOR_NAME", "PLAN_OPER_TIME", "APPLY_OPER_TIME"});
        if (list.size() > 0) {
            for (Map<String, String> stringStringMap : list) {
                Map<String, String> map = new HashMap<>();
                map.put("operationName", Utils.objToStr(stringStringMap.get("OPERATION_NAME")));
                map.put("diagBeforeOperationName", Utils.objToStr(stringStringMap.get("DIAG_BEFORE_OPERATION_NAME")));
                map.put("planOperDoctorName", Utils.objToStr(stringStringMap.get("PLAN_OPER_DOCTOR_NAME")));
                map.put("applyOperTime", Utils.objToStr(stringStringMap.get("APPLY_OPER_TIME"))); //申请手术时间
                result.add(map);
            }
        }
        resultVO.success("查询成功", result);
        return resultVO;
    }

    public ResultVo<Map<String, List<Map<String, String>>>> getOclTableHead(String oid, String orderType) {
        switch (orderType) {
            case "KF":
                return OclTableConfigVo.buildKfResultVo(oid);
            case "JM":
                return OclTableConfigVo.buildJmResultVo(oid);
            case "QT":
                return OclTableConfigVo.buildQtResultVo(oid);
            case "JY":
                return OclTableConfigVo.buildLabResultVo(oid);
            case "JC":
                return OclTableConfigVo.buildExamResultVo(oid);
        }
        return ResultVo.error("orderType匹配错误", null);
    }

    /**
     * 调用单医嘱节点闭环
     *
     * @param oid
     * @param patientId
     * @param visitId
     * @param visitTypeCode
     * @param orderProperty
     * @param orderType
     * @return
     */
    @Override
    public Map<String, String> getSingleOcl(String oid, String patientId, String visitId, String visitTypeCode, String orderProperty, String orderType,String orderNo) {
        Map<String, String> rs = new HashMap<String, String>();

        //住院医嘱
        String oclConfkeyZy = ConfigCache.getCache(oid, "SINGLEOCL_CONFKEY_ZY");
        //门诊医嘱
        String oclConfkeyMz = ConfigCache.getCache(oid, "SINGLEOCL_CONFKEY_MZ");
        if (StringUtils.isBlank(oclConfkeyZy)) {
            rs.put("status", "0");
            rs.put("msg", "先配置OCL_CONFKEY_ZY，示例：KF_LONG=key00101;KF_SHORT=key00106;JM_LONG=key00102;......");
            return rs;
        }
        if (StringUtils.isBlank(oclConfkeyMz)) {
            rs.put("status", "0");
            rs.put("msg", "先配置OCL_CONFKEY_MZ，示例：先配置OCL_CONFKEY_MZ，示例：KF=key00201;JM=key00201;......");
            return rs;
        }

        String[] pairs = "02".equals(visitTypeCode)? oclConfkeyZy.split(";") : oclConfkeyMz.split(";");
        Map<String, String> confKeymap = new HashMap<>();
        for (String pair : pairs) {
            String[] keyValue = pair.split("=");
            if (keyValue.length == 2) {
                confKeymap.put(keyValue[0], keyValue[1]);
            }
        }

        String confKey="";
        String orderShortPropertyConfig = Config.getORDER_SHORT_PROPERTY_CONFIG(oid);
        String orderLongPropertyConfig = Config.getORDER_LONG_PROPERTY_CONFIG(oid);
        logger.info("orderShortPropertyConfig=【"+orderShortPropertyConfig+"】；orderLongPropertyConfig=【"+orderLongPropertyConfig+"】；orderProperty="+orderProperty);
        if("02".equals(visitTypeCode)){
            if(StringUtils.isBlank(orderProperty) || orderShortPropertyConfig.contains(orderProperty)){
                confKey = confKeymap.get(orderType+"_SHORT");
            }else if(orderLongPropertyConfig.contains(orderProperty)){
                confKey = confKeymap.get(orderType+"_LONG");
            }else{
                rs.put("status", "0");
                rs.put("msg", "该医嘱类型"+orderProperty+"在配置ORDER_SHORT_PROPERTY_CONFIG、ORDER_LONG_PROPERTY_CONFIG均不存在");
                return rs;
            }
        }else{
            confKey = confKeymap.get(orderType);
        }

        String url = ConfigCache.getCache(oid, "SINGLEOCL_URL");
        logger.info("读取到url:" + url);
        List<String> params = commonModuleService.getParams(oid, "SINGLEOCL");
        logger.info("获取到参数:" + params);
        Map<String, String> param_configs = commonModuleService.getParam_configs(oid, "SINGLEOCL");
        logger.info("获取到参数配置：" + param_configs);
        Map<String, String> param_value = getParamValue(oid, patientId, visitId, visitTypeCode,orderNo,confKey, param_configs);
        logger.info("获取到参数和值：" + param_value);

        for (String field : params) {
            logger.info("进入赋值阶段，此处获取到的field：" + field);
            String value = param_value.get(field);
            logger.info("进入赋值阶段，此处获取到的value:" + value);
            if (org.apache.commons.lang.StringUtils.isNotBlank(value)) {
                url = url.replace("#{" + field + "}", value);
            } else {
                rs.put("status", "0");
                rs.put("msg", "未查询到" + field + "参数！");
                return rs;
            }
        }
        rs.put("status", "1");
        rs.put("linkType", commonModuleService.getLinkType(oid, "SINGLEOCL"));
        rs.put("url", url);
        return rs;
    }

    private Map<String, String> getParamValue(String oid, String patientId, String visitId, String visitType,String orderNo,String confKey, Map<String, String> configs) {
        Map<String, String> rs = new HashMap<String, String>();
        for (String key : configs.keySet()) {
            if (StringUtils.isNotBlank(patientId) && "patientId".equalsIgnoreCase(key)) {
                rs.put(key, patientId);
                continue;
            }
            if (StringUtils.isNotBlank(visitId) && "visitId".equalsIgnoreCase(key)) {
                rs.put(key, visitId);
                continue;
            }
            if (StringUtils.isNotBlank(oid) && "oid".equalsIgnoreCase(key)) {
                rs.put(key, oid);
                continue;
            }
            if (StringUtils.isNotBlank(confKey) && "confKey".equalsIgnoreCase(key)) {
                rs.put(key, confKey);
                continue;
            }
            if (StringUtils.isNotBlank(configs.get(key))) {
                String mapValue = configs.get(key);
                String[] config = mapValue.split(",");
                if (config.length == 1) {
                    //固定值
                    rs.put(key, config[0]);
                    continue;
                }
                logger.info("key=="+key+"mapValue="+mapValue);
                String tableName = config[1];
                String columnName = config[0];
                if(mapValue.contains("|")){
                    //应用于查询表和字段，分门诊、住院情况事
                    String[] tableArr=mapValue.split("\\|");
                    if("01".equals(visitType)){
                        config=tableArr[0].split(",");
                        tableName=config[1];
                        columnName=config[0];
                    }else if("02".equals(visitType)){
                        config=tableArr[1].split(",");
                        tableName=config[1];
                        columnName=config[0];
                    }
                }

                List<PropertyFilter> filters = new ArrayList<PropertyFilter>();
                createPropertyFilter("ORDER_NO", orderNo, MatchType.EQ.getOperation(), filters);
                com.goodwill.hdr.hbase.dto.responseVo.ResultVo<PageResultVo<Map<String, String>>> resultVo = hbaseQueryClient.getPageByCondition(
                        PageRequestBuilder.init()
                                .tableName(tableName)
                                .patientId(patientId)
                                .oid(oid)
                                .visitId(visitId)
                                .visitTypeCode(visitType)
                                .filters(filters)
                                .pageNo(0)
                                .pageSize(0)
                                .orderBy("")
                                .desc()
                                .column(columnName)
                                .build());
                List<Map<String, String>> list = new ArrayList<Map<String, String>>();
                if (resultVo.isSuccess()) {
                    list = resultVo.getContent().getResult();
                }
                if (list.size() > 0) {
                    String value = list.get(0).get(config[0]);
                    if (StringUtils.isBlank(value)) {
                        rs.put(key, "-");
                    } else {
                        rs.put(key, value);
                    }
                }
            } else {
                rs.put(key, "");
            }
        }
        return rs;
    }

}
