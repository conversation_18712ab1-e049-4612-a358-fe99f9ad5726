package com.goodwill.hdr.civ.vo;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

public class SolrVo implements Serializable {
    private Integer total;
    private Integer pages;
    private Integer pageNo;
    private Integer pageSize;
    private List<Map<String,String>> data;

    public Integer getTotal() {
        return total;
    }

    public void setTotal(Integer total) {
        this.total = total;
    }

    public Integer getPages() {
        return pages;
    }

    public void setPages(Integer pages) {
        this.pages = pages;
    }

    public Integer getPageNo() {
        return pageNo;
    }

    public void setPageNo(Integer pageNo) {
        this.pageNo = pageNo;
    }

    public Integer getPageSize() {
        return pageSize;
    }

    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }

    public List<Map<String, String>> getData() {
        return data;
    }

    public void setData(List<Map<String, String>> data) {
        this.data = data;
    }

    @Override
    public String toString() {
        return "SolrVo{" +
                "total=" + total +
                ", pages=" + pages +
                ", pageNo=" + pageNo +
                ", pageSize=" + pageSize +
                ", data=" + data +
                '}';
    }
}
