package com.goodwill.hdr.civ.controller;


import com.goodwill.hdr.civ.service.BloodService;
import com.goodwill.hdr.civ.utils.UpvUtil;
import com.goodwill.hdr.core.orm.Page;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Description 类描述：
 * @Date 2021-04-25 15:35
 * @modify 修改记录：
 */
@RequestMapping("/blood")
@RestController
@Api(tags = "临床用血")
public class BloodAction {

    @Autowired
    private BloodService bloodService;

    /**
     * @Description 类描述：展示输血申请
     * <AUTHOR>
     * @Date 2021年1月14日
     */
    @ApiOperation(value = "展示输血申请", notes = "展示输血申请", httpMethod = "POST")
    @RequestMapping(value = "/getBloodApplyView", method = RequestMethod.POST)
    public Page<Map<String, String>> getBloodApplyView(String searchStr, String orderBy, String orderDir, int pageNo, int pageSize) {
        return bloodService.getPageBloodApplyView(
                UpvUtil.parserToMap(searchStr), orderBy, orderDir, pageNo,
                pageSize);

    }

    /**
     * @Description 类描述：展示配血记录
     * <AUTHOR>
     * @Date 2021年1月14日
     */
    @ApiOperation(value = "展示配血记录", notes = "展示配血记录", httpMethod = "POST")
    @RequestMapping(value = "/getBloodMatchView", method = RequestMethod.POST)
    public Page<Map<String, String>> getBloodMatchView(String searchStr, String orderBy, String orderDir, int pageNo, int pageSize) {
        return bloodService.getPageBloodMatchView(
                UpvUtil.parserToMap(searchStr), orderBy, orderDir, pageNo,
                pageSize);
    }

    /**
     * @Description 类描述：展示输血记录
     * <AUTHOR>
     * @Date 2021年1月14日
     */
    @ApiOperation(value = "展示输血记录", notes = "展示输血记录", httpMethod = "POST")
    @RequestMapping(value = "/getBloodView", method = RequestMethod.POST)
    public Page<Map<String, String>> getBloodView(String searchStr, String orderBy, String orderDir, int pageNo, int pageSize) {
        return bloodService.getPageBloodView(
                UpvUtil.parserToMap(searchStr), orderBy, orderDir, pageNo,
                pageSize);
    }

    /**
     * @Description 类描述：获取某次就诊的输血申请
     * <AUTHOR>
     * @Date 2022年10月27日
     */
    @ApiOperation(value = "获取某次就诊的输血申请", notes = "获取某次就诊的输血申请", httpMethod = "POST")
    @RequestMapping(value = "/getBloodApplyByVisit", method = RequestMethod.POST)
    public Page<Map<String, String>> getBloodApplyByVisit(String oid, String patientId, String visitId, String visitType, int pageNo, int pageSize) {
        return bloodService.getBloodApplyByVisit(oid, patientId, visitId, visitType, pageNo, pageSize);
    }

    /**
     * @Description 类描述：获取某次输血申请的节点
     * <AUTHOR>
     * @Date 2022年10月27日
     */
    @ApiOperation(value = "获取某次输血申请的节点", notes = "获取某次输血申请的节点", httpMethod = "POST")
    @RequestMapping(value = "/getBloodApplyNode", method = RequestMethod.POST)
    public List<Map<String, String>> getBloodApplyNode(String oid, String patientId, String visitId, String visitType, String applyNo) {
        return bloodService.getBloodApplyNode(oid, patientId, visitId, visitType, applyNo);
    }

    /**
     * @Description 类描述：获取某次输血申请节点的具体列表
     * <AUTHOR>
     * @Date 2022年10月27日
     */
    @ApiOperation(value = "获取某次输血申请节点的具体列表", notes = "获取某次输血申请节点的具体列表", httpMethod = "POST")
    @RequestMapping(value = "/getBloodApplyNodeList", method = RequestMethod.POST)
    public Page<Map<String, String>> getBloodApplyNodeList(String oid, String patientId, String visitId, String visitType, String applyNo,String nodeName) {
        return bloodService.getBloodApplyNodeList(oid, patientId, visitId, visitType, applyNo,nodeName);
    }
}
