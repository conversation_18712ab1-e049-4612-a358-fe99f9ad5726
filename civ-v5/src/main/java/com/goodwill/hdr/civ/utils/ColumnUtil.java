package com.goodwill.hdr.civ.utils;


import org.apache.commons.lang3.StringUtils;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.io.InputStream;
import java.util.Collection;
import java.util.Map;
import java.util.Properties;




/**
 * <AUTHOR>
 * @Description 类描述：字段映射工具类
 * @Date 2018年6月7日
 * @modify 修改记录：
 */
@Service
public class ColumnUtil {

    //映射文件路径
    public static final String COLUMN_MAP_FILE = "/column.properties";
    //读取映射文件
    public static final Properties properties = new Properties();
    static {
        try (InputStream input = ColumnUtil.class.getResourceAsStream(COLUMN_MAP_FILE)) {
            // 加载一个流
            properties.load(input);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }
    //下划线
    public static final char UNDERLINE = '_';



    /**
     * @param desc    目标集合
     * @param src     存储原生字段的集合
     * @param columns 需要转化的原生字段
     * @return 返回类型： void
     * @Description 方法描述: 根据字段映射规则转化字段名称
     */
    public static void convertMapping(Map<String, String> desc, Map<String, String> src, String... columns) {

        try {
            if (null != columns) {
                for (String column : columns) {
                    String field = properties.getProperty(column);
                    if (StringUtils.isNotBlank(field)) {
                        //字段为空 默认使用 '-'替换
                        Utils.checkAndPutToMap(desc, field, src.get(column), "-", false);
                    } else {
                        desc.put(underlineToCamel(StringUtils.lowerCase(column)), "字段未映射");
                    }
                }
            }
        } catch (Exception e) {
            throw new RuntimeException(e);
        }

    }


    /**
     * @param column 下划线字段
     * @return 返回类型： String
     * @return
     * @Description 方法描述: 下划线字段转化为驼峰变量
     */
    public static String underlineToCamel(String column) {
        if (null == column || "".equals(column.trim())) {
            return "";
        }
        int len = column.length();
        StringBuilder sb = new StringBuilder(len);
        for (int i = 0; i < len; i++) {
            char c = column.charAt(i);
            if (c == UNDERLINE) {
                if (++i < len) {
                    sb.append(Character.toUpperCase(column.charAt(i)));
                }
            } else {
                sb.append(c);
            }
        }
        return sb.toString();
    }

    /**
     * 建立映射关系,转换Key值
     */

    public static <T> void convertMappingByRule(Map<String, T> source, Map<String, T> desc, Map<String, String> rule) {
        for (Map.Entry<String, ?> sourceEntry : source.entrySet()) {
            String sourceKey = sourceEntry.getKey();
            String newKey = rule.get(sourceKey);
            if (StringUtils.isNotBlank(newKey)) {
                desc.put(newKey, source.get(sourceKey));
            }

        }
    }


}
