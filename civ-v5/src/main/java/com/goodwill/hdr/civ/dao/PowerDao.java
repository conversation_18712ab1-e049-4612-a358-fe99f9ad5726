package com.goodwill.hdr.civ.dao;


import com.goodwill.hdr.civ.entity.PowerConfig;
import com.goodwill.hdr.civ.entity.PowerConfigDept;
import com.goodwill.hdr.civ.entity.SysConfig;
import com.goodwill.hdr.civ.vo.DeptListWithinUser;

import java.util.List;
import java.util.Map;

public interface PowerDao {

    //全局设置

    /**
     * @return
     * @Description 获取全局设置
     */
    public List<SysConfig> getSysConfig(String oid);

    //系统脱敏设置
    List<SysConfig> getSysConfigForHide(String oid);

    /**
     * @return
     * @Description 获取全局设置
     */
    public SysConfig getSysConfigByConfigCode(String oid, String configCode);

    /**
     * @return
     * @Description 修改全局设置
     */
    public boolean updateSysConfig(String oid, String configCode, String configValue);
    //权限设置

    /**
     * @return
     * @Description 获取用户某个权限设置
     */
    public PowerConfig getPowerConfigByType(String oid, String userCode, String type);

    /**
     * @return
     * @Description 获取部门某个权限设置
     */
    public PowerConfigDept getPowerConfigByDeptAndType(String oid, String deptcode, String type);

    /**
     * @return
     * @Description 获取用户所有权限设置
     */
    public List<Map<String, String>> getPowerConfig(String oid, String userCode, boolean isUser);
    public List<Map<String, String>> getVipConfig(String oid, String userCode, boolean isUser);

    /**
     * @return
     * @Description 修改权限设置
     */
    public boolean updatePowerConfigByType(String oid, String[] users, String type, String value, boolean isUser);

    /**
     * @param userName
     * @param deptCode
     * @return
     * @Description 方法描述: 查询用户列表
     */
    public List<DeptListWithinUser> getUserList(String oid, String keyWord, String deptCode);

    /**
     * @return
     * @Description 方法描述: 查询部门列表
     */
    public List<Map<String, Object>> getDeptCodeList(String oid, String deptName);

    /**
     * @return
     * @Description 方法描述: 查询全部用户
     */
    public List<Map<String, String>> getAllDept(String oid);

    /**
     * @return
     * @Description 方法描述: 删除科室 权限
     */
    public boolean deleteDeptPower(String oid);

    /**
     * @return
     * @Description 方法描述: 删除用户权限
     */
    public boolean deleteUserPower(String oid);


    /**
     * @param userCode  账号
     * @param deptCode  部门
     * @param type      类型
     * @param itemCodes 值
     * @param date      时间
     * @return
     * @Description 方法描述: 插入用户权限
     */
    public boolean insertPowerConfigByUser(String oid, String userCode, String deptCode,
                                           String type, String itemCodes, String date);


    /**
     * @param deptCode  部门
     * @param type      类型
     * @param itemCodes 值
     * @param date      时间
     * @return
     * @Description 方法描述: 插入用户权限
     */
    public boolean insertPowerConfigByDept(String oid, String deptCode,
                                           String type, String itemCodes, String date);
    public boolean insertVipConfigByDept(String oid, String deptCode,
                                           String type, String itemCodes, String date);


    /**
     * @Description 方法描述: 插入用户权限
     */
    public String selectDeptByUser(String oid, String userCode);

    public String selectUserByUserCode(String oid, String userCode);

    List<Map<String, Object>> getSysHideConfig(String oid, String code);
    List<Map<String, String>> getSysHideDict(String oid, String keyWord);

    boolean updateSysHideConfig(String oid, String code, String value, String enabled, String fields,String maskRuleCode, String maskRuleName);

    int insertSysHideConfig(String configName, String configValue, String fields, String maskRuleCode, String maskRuleName);

    int updateSysConfigSubDict(String fields,String type);

    int deleteSysHideConfig(String configCode);

    /**
     * 获取脱敏规则
     */
    List<Map<String, Object>> queryMaskRules(String oid);

    /**
     * 查询用户列表
     * 只返回用户数据 不包含科室信息
     */
    List< Map<String,String>> getUserOrRoleList(String oid, String keyWord, String deptCode);

    int deleteFiledByUser(String oid,String userCode);

    int deleteFiledByUsers(String oid, List<String> userCodes);

    int insertFiledUser(String oid,String userCode,String filedCode);

}
