package com.goodwill.hdr.civ.utils;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * @Description
 * 类描述：List集合分页
 * <AUTHOR>
 * @Date 2018年5月31日
 * @param <T>
 * @modify
 * 修改记录：
 *
 */
public class ListPage<T> {
	//需要分页的数据
	private List<T> data;
	//上一页
	private int lastPage;
	//当前页
	private int nowPage;
	//下一页
	private int nextPage;
	//每页条数
	private int pageSize;
	//总页数
	private int totalPage;
	//总记录数
	private int totalCount;

	public ListPage(List<T> data, int nowPage, int pageSize) {
		//保证集合不为空
		if (null == data) {
			data = new ArrayList<T>();
		}
		this.data = data;
		this.pageSize = (pageSize <= 0) ? 1 : pageSize;
		this.nowPage = (nowPage <= 0) ? 1 : nowPage;
		this.totalCount = data.size();
		this.totalPage = (totalCount + pageSize - 1) / pageSize;
		this.lastPage = nowPage - 1 > 1 ? nowPage - 1 : 1;
		this.nextPage = nowPage >= totalPage ? totalPage : nowPage + 1;

	}

	/**
	 * @Description
	 * 方法描述: 获取当前页记录
	 * @return 返回类型： List<T>
	 * @return
	 */
	public List<T> getPagedList() {
		int fromIndex = (nowPage - 1) * pageSize;
		if (fromIndex >= data.size()) {
			return Collections.emptyList();//空数组
		}
		if (fromIndex < 0) {
			return Collections.emptyList();//空数组
		}
		int toIndex = nowPage * pageSize;
		if (toIndex >= data.size()) {
			toIndex = data.size();
		}
		return data.subList(fromIndex, toIndex);
	}

	public int getPageSize() {
		return pageSize;
	}

	public List<T> getData() {
		return data;
	}

	public int getLastPage() {
		return lastPage;
	}

	public int getNowPage() {
		return nowPage;
	}

	public int getNextPage() {
		return nextPage;
	}

	public int getTotalPage() {
		return totalPage;
	}

	public int getTotalCount() {
		return totalCount;
	}


}