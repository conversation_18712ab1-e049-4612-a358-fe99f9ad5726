package com.goodwill.hdr.civ.service;



import com.goodwill.hdr.core.orm.Page;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Description 病案首页信息查询
 * @date 2018年4月25日
 */
public interface SummaryService {

    /**
     * @param patId   患者编号
     * @param visitId 就诊次数
     * @return
     * @Description 获取患者住院病案首页
     */
    public List<String> getInpSummary(String oid, String patId, String visitId);

    /**
     * @param patId   患者编号
     * @param visitId 就诊次数
     * @return
     * @Description 获取患者住院病案首页
     */
    public List<String> getInpSummaryInfo(String oid, String patId, String visitId);

    /**
     * @param patId
     * @param visitId
     * @return
     * @Description 获取患者病案首页诊断
     */
    public Map<String, Object> getInpSummaryDiag(String oid, String patId, String visitId);

    /**
     * @param patId
     * @param visitId
     * @return
     * @Description 获取患者病案首页手术
     */
    public Map<String, Object> getInpSummaryOperation(String oid, String patId, String visitId);

    /**
     * @param patId   患者编号
     * @param visitId 就诊次数
     * @return
     * @Description 方法描述: 获取患者病案首页住院费
     */
    public Map<String, Object> getInpSummaryFee(String oid, String patId, String visitId);

    /**
     * @param patId
     * @param visitId
     * @return
     * @Description 获取患者门诊患者信息
     */
    public List<Map<String, String>> getOutSummary(String oid, String patId, String visitId);

    /**
     * @param patId
     * @param visitId
     * @return
     * @Description 获取患者门诊诊断
     */
    public List<Map<String, String>> getOutSummaryDiag(String oid, String patId, String visitId);

    /**
     * @param patientId 患者编号
     * @param visitId   就诊次数
     * @return
     * @Description 方法描述: 查询患者某次门诊的详细信息   门诊就诊信息，门诊诊断
     */
    public Map<String, Object> getPatientOutpSummary(String oid, String patientId, String visitId);

    /**
     * @param patientId 患者编号
     * @param visitType 就诊类型
     * @return
     * @Description 方法描述: 从患者信息表中查询患者信息
     */
    public Map<String, String> getPatientInfo(String oid, String patientId, String visitType, String visitId);

    /**
     * 费用明细table展示字段

     * @param visitType
     * @return
     */
    String getPatientFeeTable(String oid, String visitType);

    /**
     * 费用明细类型
     * @param patientId
     * @param visitType
     * @return
     */
    Page<Map<String, String>> getPatientInpSummaryFeeTypes(String oid, String patientId, String visitId, String visitType, int pageNo, int pageSize);

    /**
     * 获取费用明细数据
     * @param patientId
     * @param visitId
     * @param visitType
     * @param pageNo
     * @param pageSize
     * @return
     */
    Page<Map<String, String>> getPatientInpSummaryFeeData(String oid, String patientId, String visitId, String visitType, String feeType, int pageNo, int pageSize);

    public Map<String, Object> getInpSummaryBeforeDiag(String oid, String patId, String visitId);
    public Map<String, Object> getInpSummaryBeforeOperation(String oid, String patId, String visitId);
    public List<String> getInpSummaryBeforeInfo(String oid, String patId, String visitId);
    public List<String> getInpSummaryBefore(String oid, String patId, String visitId);

    /**
     * @param patientId 患者编号
     * @param visitId   就诊次数
     * @return
     * @Description 方法描述: 查询患者某次门诊的详细信息   门诊就诊信息，门诊诊断
     * 北京佑安医院门诊首页
     */
    Map<String, Object> getPatientOutpSummaryYouAn(String oid, String patientId, String visitId);

}
