package com.goodwill.hdr.civ.enums;

/**
 * 就诊类型枚举
 *
 * <AUTHOR>
 */

public enum VisitTypeEnum {
    /**
     * 门诊
     */
    OUT_VISIT("01", "OUTPV"),
    /**
     * 住院
     */
    IN_VISIT("02", "INPV"),
    /**
     * 急诊
     */
    Emergency("01", "OUTPV"),
    /**
     * 体检
     */
    PHYSICAL_EXAMINATION("03", "");
    private final String code;
    private final String label;

    VisitTypeEnum(String code, String label) {
        this.code = code;
        this.label = label;
    }

    public String getCode() {
        return code;
    }

    public String getLabel() {
        return label;
    }
}
