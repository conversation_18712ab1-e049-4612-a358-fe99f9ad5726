package com.goodwill.hdr.civ.service.impl;


import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;

import com.goodwill.hdr.civ.config.CommonConfig;
import com.goodwill.hdr.civ.config.Config;
import com.goodwill.hdr.civ.config.ConfigCache;
import com.goodwill.hdr.civ.dao.OrganizationDao;
import com.goodwill.hdr.civ.dao.PowerDao;
import com.goodwill.hdr.civ.entity.*;
import com.goodwill.hdr.civ.enums.HdrConstantEnum;
import com.goodwill.hdr.civ.mapper.PowerConfigDeptMapper;
import com.goodwill.hdr.civ.mapper.PowerConfigMapper;
import com.goodwill.hdr.civ.mapper.SysConfigSubDictMapper;
import com.goodwill.hdr.civ.mapper.SysFiledUserMapper;
import com.goodwill.hdr.civ.service.ConfigService;
import com.goodwill.hdr.civ.service.OperService;
import com.goodwill.hdr.civ.service.PowerService;
import com.goodwill.hdr.civ.utils.ListPage;
import com.goodwill.hdr.civ.utils.Utils;
import com.goodwill.hdr.civ.utils.WsUtil;
import com.goodwill.hdr.civ.vo.*;
import com.goodwill.hdr.core.orm.Page;
import com.goodwill.hdr.security.priority.entity.UserEntity;
import com.goodwill.hdr.security.utils.SecurityCommonUtil;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.io.OutputStream;
import java.net.HttpURLConnection;
import java.net.URL;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

@Service

public class PowerServiceImpl implements PowerService {
    private static Logger log = LoggerFactory.getLogger(PowerServiceImpl.class);

    private final PowerDao powerDao;


    private final PowerConfigMapper powerConfigMapper;


    private final PowerConfigDeptMapper powerConfigDeptMapper;


    private final OperService operService;


    private final OrganizationDao organizationDao;



    private final ConfigService configService;

    private final ObjectMapper objectMapper;
    private final EncryptService encryptService;
    private final SysFiledUserMapper sysFiledUserMapper;
    private final SysConfigSubDictMapper sysConfigSubDictMapper;

    public PowerServiceImpl(PowerDao powerDao, PowerConfigMapper powerConfigMapper, PowerConfigDeptMapper powerConfigDeptMapper, OperService operService, OrganizationDao organizationDao, ConfigService configService, ObjectMapper objectMapper,
                            EncryptService encryptService,SysFiledUserMapper sysFiledUserMapper, SysConfigSubDictMapper sysConfigSubDictMapper) {
        this.powerDao = powerDao;

        this.powerConfigMapper = powerConfigMapper;
        this.powerConfigDeptMapper = powerConfigDeptMapper;
        this.operService = operService;

        this.organizationDao = organizationDao;


        this.configService = configService;
        this.objectMapper = objectMapper;
        this.encryptService = encryptService;
        this.sysFiledUserMapper=sysFiledUserMapper;
        this.sysConfigSubDictMapper=sysConfigSubDictMapper;
    }

    //
//    @Autowired
//    private CommonURLService commonURLService;
//
//    private static final String CONFIG_FILE_NAME = "civ.properties";
//
    //判断是不是管理员用户
    @Override
    public Map<String, String> getCheckAdmin(String oid, String userCode) {
        // TODO Auto-generated method stub
        Map<String, String> map = new HashMap<String, String>();
        String admin = Config.getConfigValue(oid, "CIV_ADMIN");// PropertiesUtils.getPropertyValue(CONFIG_FILE_NAME, "CIV_ADMIN");
        if (userCode.equals(admin))
            map.put("result", "1");
        else
            map.put("result", "0");
        return map;
    }


    //全局设置
    @Override
    public List<SysConfig> getSysConfig(String oid) {
        List<SysConfig> list = new ArrayList<>();
        list = powerDao.getSysConfig(oid);
        Utils.sortListByDate(list, "sort", Page.Sort.ASC);
//        for (SysConfig map : list) {
//            String s = String.valueOf(map.get("sort"));
//            map.put("sort", s);
//        }
        return list;
    }


    /**
     * 获取脱敏设置
     *
     * @return
     */
    public List<SysConfig> getSysConfigForHide(String oid){
        return powerDao.getSysConfigForHide(oid);
    }

    @Override
    public boolean updateSysConfig(String oid, String configCode, String configValue) {
        boolean is = powerDao.updateSysConfig(oid, configCode, configValue);
        return is;
    }


    @Override
    public Map<String, String> getSysConfigByType(String oid, String configCode) {
        // TODO Auto-generated method stub
        Map<String, String> result = new HashMap<String, String>();
        SysConfig sysConfig = powerDao.getSysConfigByConfigCode(oid, configCode);
        if ("1".equals(sysConfig.getConfigValue())) {
            result.put("result", "1");
        } else {
            result.put("result", "0");
        }

        return result;
    }

    /**
     * 查询页面权限
     *
     * @param oid      院区标识
     * @param userCode 用户编号
     * @param typeList 类型类别
     * @return 封装权限的map
     */
    private Map<String, String> getPagePower(String oid, String userCode, List<String> typeList) {
        Map<String, String> rs = new HashMap<String, String>();
        for (String type : typeList) {
            String itemCodes = getItemCodesByType(oid, userCode, type);
            if (StringUtils.isBlank(itemCodes)) {
                rs.put(type, "0");
            } else {
                rs.put(type, "1");
            }

        }
        return rs;
    }

    /**
     * 根据config_type 来获取对应的 itemCodes 。
     * 先查用户的itemCodes，若查询数据为空，再查部门的itemCodes，2次查询均为空，返回空字符串。
     *
     * @param oid      院区标识
     * @param userCode 用户编号
     * @param type     类型
     * @return 空字符串或itemCode字符串
     */
    private String getItemCodesByType(String oid, String userCode, String type) {
        PowerConfig powerConfigByType = powerDao.getPowerConfigByType(oid, userCode, type);
        String itemCodes = "";
        if (powerConfigByType != null) {
            itemCodes = powerConfigByType.getItemcodes();
        }
        if (StringUtils.isBlank(itemCodes)) {
            String deptCode = powerDao.selectDeptByUser(oid, userCode);
            PowerConfigDept powerConfigByDeptAndType = powerDao.getPowerConfigByDeptAndType(oid, deptCode, type);
            if (powerConfigByDeptAndType != null) {
                itemCodes = powerConfigByDeptAndType.getItemcodes();
            }
        }
        return itemCodes;
    }

    @Override
    public Map<String, String> getPowerConfigByPage(String oid, String userCode) {

        Map<String, String> rs = new HashMap<String, String>();
        if (userCode.equals(Config.getCiv_Admin(oid))) {
            if ("current".equals(Config.getCIV_CURRENT_VALUE(oid))) {
                rs.put("Current", "1");
            } else {
                rs.put("Current", "0");
            }
            if ("specialty".equals(Config.getCIV_SPECIALTY_VALUE(oid))) {
                rs.put("Specialty", "1");
            } else {
                rs.put("Specialty", "0");
            }

            if ("timeaxis".equals(Config.getCIV_TIMEAXIS_VALUE(oid))) {
                rs.put("TimeAxis", "1");
            } else {
                rs.put("TimeAxis", "0");
            }
            if ("specialtytimeaxis".equals(Config.getCIV_SPECIALTYTIMEAXIS_VALUE(oid))) {
                rs.put("SpecialtyTimeAxis", "1");
            } else {
                rs.put("SpecialtyTimeAxis", "0");
            }
            //新增体检视图
            if ("medicalview".equals(Config.getCIV_MEDICAL_VALUE(oid))) {
                rs.put("medicalView", "1");
            } else {
                rs.put("medicalView", "0");
            }
            rs.put("Visit", "1");
            rs.put("Category", "1");
            return rs;
        }


        List<String> pageList = new ArrayList<>();
        pageList.add("Current");
        pageList.add("Specialty");
        pageList.add("TimeAxis");
        pageList.add("Visit");
        pageList.add("Category");
        pageList.add("SpecialtyTimeAxis");
        pageList.add("MedicalView");

        rs = getPagePower(oid, userCode, pageList);

        return rs;
    }

    @Override
    public List<Map<String, Object>> getPowerConfigByVisit(String oid, String userCode) {
        // TODO Auto-generated method stub
        List<Map<String, Object>> result = new ArrayList<Map<String, Object>>();
        String admin = Config.getCiv_Admin(oid);
        if (admin.equals(userCode)) {
            getAdminPowerByVisit(oid, result);
            Utils.sortNumObjectList(result, "order", Page.Sort.ASC);
            return result;
        }
        String itemCodes = getItemCodesByType(oid, userCode, "Visit");
        String[] values = itemCodes.split("/");
        /**
         *通用模块，全都调用第三方Url
         */
        for (String value : values) {
            Map<String, Object> rs = new HashMap<String, Object>();
            if (StringUtils.isNotBlank(value)) {
                switch (value) {
                    case "index_module":
                        rs.put("name", "首页");
                        rs.put("id", "index_module");
                        rs.put("order", "1");
                        result.add(rs);
                        break;
                    case "index_before_module":
                        rs.put("name", "首页(编目前)");
                        rs.put("id", "index_before_module");
                        rs.put("order", "2");
                        result.add(rs);
                        break;
                    case "order_module":
                        rs.put("name", "医嘱");
                        rs.put("id", "order_module");
                        rs.put("order", "3");
                        result.add(rs);
                        break;
                    case "exam_module":
                        rs.put("name", "检验报告");
                        rs.put("id", "exam_module");
                        rs.put("order", "4");
                        result.add(rs);
                        break;
                    case "check_module":
                        rs.put("name", "检查报告");
                        rs.put("id", "check_module");
                        rs.put("order", "5");
                        result.add(rs);
                        break;
                    case "pathology_module":
                        rs.put("name", "病理报告");
                        rs.put("id", "pathology_module");
                        rs.put("order", "6");
                        result.add(rs);
                        break;
                    case "record_module":
                        rs.put("name", "病历文书");
                        rs.put("id", "record_module");
                        rs.put("linkType", "html");
                        rs.put("order", "7");
                        result.add(rs);
                        break;
                    case "oper_module":
                        String moduleName = "手术记录";
                        if (HdrConstantEnum.HOSPITAL_WHET.getCode().equals(ConfigCache.getCache(oid, "org_oid"))) {
                            moduleName = "手术过程";
                        }
                        rs.put("name", moduleName);
                        rs.put("id", "oper_module");
                        if (StringUtils.isNotBlank(CommonConfig.getURL(oid, "OP"))) {
                            rs.put("linkType", CommonConfig.getLinkType(oid, "OP"));
                        } else {
                            rs.put("linkType", "civ");
                        }
                        rs.put("order", "8");
                        result.add(rs);
                        break;
                    case "nurse_module":
                        rs.put("name", "护理记录");
                        rs.put("id", "nurse_module");
                        rs.put("linkType", "civ");
                        boolean isDistinguish = Config.getCIV_NURSE_URL_OUT_OR_IN(oid);
                        if (StringUtils.isNotBlank(CommonConfig.getURL(oid, "NURSE"))) {
                            rs.put("linkType", CommonConfig.getLinkType(oid, "NURSE"));
                        }
                        if (isDistinguish && StringUtils.isNotBlank(CommonConfig.getURL(oid, "IN_NURSE"))) {
                            rs.put("linkType", CommonConfig.getLinkType(oid, "IN_NURSE"));
                        }
                        if (isDistinguish && StringUtils.isNotBlank(CommonConfig.getURL(oid, "OUT_NURSE"))) {
                            rs.put("linkType", CommonConfig.getLinkType(oid, "OUT_NURSE"));
                        }
                        rs.put("order", "9");
                        result.add(rs);
                        break;
                    case "allergy_module":
                        rs.put("name", "过敏记录");
                        rs.put("id", "allergy_module");
                        rs.put("order", "10");
                        result.add(rs);
                        break;
                    case "inpvOCL_module":
                        rs.put("name", "住院医嘱闭环");
                        rs.put("id", "inpvOCL_module");
                        rs.put("linkType", "Iframe");
                        rs.put("order", "11");
                        result.add(rs);
                        break;
                    case "outpvOCL_module":
                        rs.put("name", "门诊医嘱闭环");
                        rs.put("id", "outpvOCL_module");
                        rs.put("linkType", "Iframe");
                        rs.put("order", "12");
                        result.add(rs);
                        break;
                    case "businessOCL_module":
                        rs.put("name", "重点业务闭环");
                        rs.put("id", "businessOCL_module");
                        rs.put("linkType", "Iframe");
                        rs.put("order", "13");
                        result.add(rs);
                        break;
                    case "blood_module":
                        rs.put("name", "临床用血");
                        rs.put("id", "blood_module");
                        rs.put("order", "14");
                        result.add(rs);
                        break;
                    case "web_emr_module":
                        rs.put("name", "WEB版电子病历");
                        rs.put("id", "web_emr_module");
                        if (StringUtils.isNotBlank(CommonConfig.getURL(oid, "WEBEMR"))) {
                            rs.put("linkType", CommonConfig.getLinkType(oid, "WEBEMR"));
                        } else {
                            rs.put("linkType", "html");
                        }
                        rs.put("order", "15");
                        result.add(rs);
                        break;
                    case "fee_detail_module":
                        Map<String, Object> feeMap = new HashMap<String, Object>();
                        feeMap.put("name", "费用明细");
                        feeMap.put("id", "fee_detail_module");
                        feeMap.put("order", "16");
                        result.add(feeMap);
                        break;
                    case "danger_nurse_module":
                        rs.put("name", "危重护理记录");
                        rs.put("id", "danger_nurse_module");
                        if (StringUtils.isNotBlank(CommonConfig.getURL(oid, "DANGERNURSE"))) {
                            rs.put("linkType", CommonConfig.getLinkType(oid, "DANGERNURSE"));
                        } else {
                            rs.put("linkType", "html");
                        }
                        rs.put("order", "17");
                        result.add(rs);
                        break;
                    case "hd_module":
                        Map<String, Object> dangerMap = new HashMap<String, Object>();
                        dangerMap.put("name", "血透报告");
                        dangerMap.put("id", "hd_module");
                        dangerMap.put("order", "18");
                        result.add(dangerMap);
                        break;
                    default:
                        /**
                         *通用模块，全都调用第三方Url
                         */
                        Map<String, Object> modules_config = Config.getMODULES_CONFIG(oid, value);
                        if (modules_config != null && modules_config.size() > 0) {
                            //配合前端设置通用模块的id都为web_emr_module
//						modules_config.put("id","web_emr_module");
                            result.add(modules_config);
                        }
                        break;
                }

            }
        }
        Utils.sortNumObjectList(result, "order", "asc");
        return result;
    }

    private void getAdminPowerByVisit(String oid, List<Map<String, Object>> result) {
        // TODO Auto-generated method stub
        String[] config = Config.getCIV_VISIT_VALUE(oid).split("/");
        for (String module : config) {
            if ("index_module".equals(module)) {
                Map<String, Object> index = new HashMap<String, Object>();
                index.put("name", "首页");
                index.put("id", "index_module");
                index.put("order", "1");
                result.add(index);
            }
            if ("index_before_module".equals(module)) {
                Map<String, Object> index = new HashMap<String, Object>();
                index.put("name", "首页(编目前)");
                index.put("id", "index_before_module");
                index.put("order", "2");
                result.add(index);
            }
            if ("order_module".equals(module)) {
                Map<String, Object> order = new HashMap<String, Object>();
                order.put("name", "医嘱");
                order.put("id", "order_module");
                order.put("order", "3");
                result.add(order);
            }
            if ("exam_module".equals(module)) {
                Map<String, Object> exam = new HashMap<String, Object>();
                exam.put("name", "检验报告");
                exam.put("id", "exam_module");
                exam.put("order", "4");
                result.add(exam);
            }
            if ("check_module".equals(module)) {
                Map<String, Object> check = new HashMap<String, Object>();
                check.put("name", "检查报告");
                check.put("id", "check_module");
                check.put("order", "5");
                result.add(check);
            }
            if ("pathology_module".equals(module)) {
                Map<String, Object> pathology = new HashMap<String, Object>();
                pathology.put("name", "病理报告");
                pathology.put("id", "pathology_module");
                pathology.put("order", "6");
                result.add(pathology);
            }
            if ("record_module".equals(module)) {
                Map<String, Object> record = new HashMap<String, Object>();
                record.put("name", "病历文书");
                record.put("id", "record_module");
                if (StringUtils.isNotBlank(CommonConfig.getURL(oid, "RM"))) {
                    record.put("linkType", CommonConfig.getLinkType(oid, "RM"));
                } else {
                    record.put("linkType", "html");
                }
                record.put("order", "7");
                result.add(record);
            }
            if ("oper_module".equals(module)) {
                Map<String, Object> oper = new HashMap<String, Object>();
                String moduleName = "手术记录";
                if (HdrConstantEnum.HOSPITAL_WHET.getCode().equals(ConfigCache.getCache(oid, "org_oid"))) {
                    moduleName = "手术过程";
                }
                oper.put("name", moduleName);
                oper.put("id", "oper_module");
                if (StringUtils.isNotBlank(CommonConfig.getURL(oid, "OP"))) {
                    oper.put("linkType", CommonConfig.getLinkType(oid, "OP"));
                } else {
                    oper.put("linkType", "civ");
                }
                oper.put("order", "8");
                result.add(oper);
            }
            if ("nurse_module".equals(module)) {
                Map<String, Object> nurse = new HashMap<String, Object>();
                nurse.put("name", "护理记录");
                nurse.put("id", "nurse_module");
                nurse.put("linkType", "civ");
                boolean isDistinguish = Config.getCIV_NURSE_URL_OUT_OR_IN(oid);
                if (StringUtils.isNotBlank(CommonConfig.getURL(oid, "NURSE"))) {
                    nurse.put("linkType", CommonConfig.getLinkType(oid, "NURSE"));
                }
                if (isDistinguish && StringUtils.isNotBlank(CommonConfig.getURL(oid, "IN_NURSE"))) {
                    nurse.put("linkType", CommonConfig.getLinkType(oid, "IN_NURSE"));
                }
                if (isDistinguish && StringUtils.isNotBlank(CommonConfig.getURL(oid, "OUT_NURSE"))) {
                    nurse.put("linkType", CommonConfig.getLinkType(oid, "OUT_NURSE"));
                }
                nurse.put("order", "9");
                result.add(nurse);
            }
            if ("allergy_module".equals(module)) {
                Map<String, Object> allergy = new HashMap<String, Object>();
                allergy.put("name", "过敏记录");
                allergy.put("id", "allergy_module");
                allergy.put("order", "10");
                result.add(allergy);
            }
            if ("inpvOCL_module".equals(module)) {
                Map<String, Object> oclMap = new HashMap<String, Object>();
                oclMap.put("name", "住院医嘱闭环");// 病历扫描件 医嘱闭环
                oclMap.put("linkType", "Iframe");
                oclMap.put("id", "inpvOCL_module");
                oclMap.put("order", "11");
                result.add(oclMap);
            }
            if ("outpvOCL_module".equals(module)) {
                Map<String, Object> oclMap = new HashMap<String, Object>();
                oclMap.put("name", "门诊医嘱闭环");// 病历扫描件 医嘱闭环
                oclMap.put("linkType", "Iframe");
                oclMap.put("id", "outpvOCL_module");
                oclMap.put("order", "12");
                result.add(oclMap);
            }
            if ("businessOCL_module".equals(module)) {
                Map<String, Object> oclMap = new HashMap<String, Object>();
                oclMap.put("name", "重点业务闭环");// 病历扫描件 医嘱闭环
                oclMap.put("linkType", "Iframe");
                oclMap.put("id", "businessOCL_module");
                oclMap.put("order", "13");
                result.add(oclMap);
            }
            if ("blood_module".equals(module)) {
                Map<String, Object> bloodMap = new HashMap<String, Object>();
                bloodMap.put("name", "临床用血");
                bloodMap.put("id", "blood_module");
                bloodMap.put("order", "14");
                result.add(bloodMap);
            }
            if ("fee_detail_module".equals(module)) {
                Map<String, Object> feeMap = new HashMap<String, Object>();
                feeMap.put("name", "费用明细");
                feeMap.put("id", "fee_detail_module");
                feeMap.put("order", "15");
                result.add(feeMap);
            }
            if ("web_emr_module".equals(module)) {
                Map<String, Object> webEmrMap = new HashMap<String, Object>();
                webEmrMap.put("name", "WEB版电子病历");
                webEmrMap.put("id", "web_emr_module");
                if (StringUtils.isNotBlank(CommonConfig.getURL(oid, "WEBEMR"))) {
                    webEmrMap.put("linkType", CommonConfig.getLinkType(oid, "WEBEMR"));
                } else {
                    webEmrMap.put("linkType", "html");
                }
                webEmrMap.put("order", "16");
                result.add(webEmrMap);
            }
            if ("danger_nurse_module".equals(module)) {
                Map<String, Object> dangerMap = new HashMap<String, Object>();
                dangerMap.put("name", "危重护理记录");
                dangerMap.put("id", "danger_nurse_module");
                if (StringUtils.isNotBlank(CommonConfig.getURL(oid, "DANGERNURSE"))) {
                    dangerMap.put("linkType", CommonConfig.getLinkType(oid, "DANGERNURSE"));
                } else {
                    dangerMap.put("linkType", "html");
                }
                dangerMap.put("order", "17");
                result.add(dangerMap);
            }

            if ("hd_module".equals(module)) {
                Map<String, Object> dangerMap = new HashMap<String, Object>();
                dangerMap.put("name", "血透报告");
                dangerMap.put("id", "hd_module");
                dangerMap.put("order", "18");
                result.add(dangerMap);
            } else {
                /**
                 *通用模块，全都调用第三方Url
                 */
                Map<String, Object> modules_config = Config.getMODULES_CONFIG(oid, module);
                if (modules_config != null && modules_config.size() > 0) {
                    //配合前端设置通用模块的id都为web_emr_module
//					modules_config.put("id","web_emr_module");
                    result.add(modules_config);
                }
            }
        }
    }

    @Override
    public List<Map<String, Object>> getPowerConfigByCategory(String oid, String userCode) {
        // TODO Auto-generated method stub
        List<Map<String, Object>> result = new ArrayList<Map<String, Object>>();
        String admin = Config.getCiv_Admin(oid);
        if (admin.equals(userCode)) {
            getAdminPowerByCategory(oid, result);
            Utils.sortNumObjectList(result, "order", Page.Sort.ASC);
            return result;
        }
        String category = getItemCodesByType(oid, userCode, "Category");

        String[] values = category.split("/");
        for (int i = 0; i < values.length; i++) {
            Map<String, Object> rs = new HashMap<String, Object>();
            String code = values[i];
            if (StringUtils.isNotBlank(values[i])) {
                if ("check_module".equals(code)) {
                    rs.put("name", "检查报告");
                    rs.put("id", "check_module");
                    rs.put("order", "1");
                    result.add(rs);
                } else if ("exam_module".equals(code)) {
                    rs.put("name", "检验报告");
                    rs.put("id", "exam_module");
                    rs.put("order", "2");
                    result.add(rs);
                } else if ("oper_module".equals(code)) {
                    String moduleName = "手术记录";
                    if (HdrConstantEnum.HOSPITAL_WHET.getCode().equals(ConfigCache.getCache(oid, "org_oid"))) {
                        moduleName = "手术过程";
                    }
                    rs.put("name", moduleName);
                    rs.put("id", "oper_module");
                    if (StringUtils.isNotBlank(CommonConfig.getURL(oid, "OP"))) {
                        rs.put("linkType", CommonConfig.getLinkType(oid, "OP"));
                    } else {
                        rs.put("linkType", "civ");
                    }
                    rs.put("order", "3");
                    result.add(rs);
                } else if ("main_diag_module".equals(code)) {
                    rs.put("name", "主要疾病诊断");
                    rs.put("id", "main_diag_module");
                    rs.put("order", "4");
                    result.add(rs);
                } else if ("pathology_module".equals(code)) {
                    rs.put("name", "病理报告");
                    rs.put("id", "pathology_module");
                    rs.put("order", "5");
                    result.add(rs);
                } else if ("record_module".equals(code)) {
                    rs.put("name", "病历文书");
                    rs.put("id", "record_module");
                    if (StringUtils.isNotBlank(CommonConfig.getURL(oid, "RM"))) {
                        rs.put("linkType", CommonConfig.getLinkType(oid, "RM"));
                    } else {
                        rs.put("linkType", "html");
                    }
                    rs.put("order", "6");
                    result.add(rs);
                } else if ("nurse_module".equals(code)) {
                    rs.put("name", "护理记录");
                    rs.put("id", "nurse_module");
                    rs.put("linkType", "civ");
                    boolean isDistinguish = Config.getCIV_NURSE_URL_OUT_OR_IN(oid);
                    if (StringUtils.isNotBlank(CommonConfig.getURL(oid, "NURSE"))) {
                        rs.put("linkType", CommonConfig.getLinkType(oid, "NURSE"));
                    }
                    if (isDistinguish && StringUtils.isNotBlank(CommonConfig.getURL(oid, "IN_NURSE"))) {
                        rs.put("linkType", CommonConfig.getLinkType(oid, "IN_NURSE"));
                    }
                    if (isDistinguish && StringUtils.isNotBlank(CommonConfig.getURL(oid, "OUT_NURSE"))) {
                        rs.put("linkType", CommonConfig.getLinkType(oid, "OUT_NURSE"));
                    }
                    rs.put("order", "7");
                    result.add(rs);
                } else if ("durg_orally_module".equals(code)) {
                    rs.put("name", "口服药品");
                    rs.put("id", "durg_orally_module");
                    rs.put("order", "8");
                    result.add(rs);
                } else if ("durg_vein_module".equals(code)) {
                    rs.put("name", "静脉药品");
                    rs.put("id", "durg_vein_module");
                    rs.put("order", "9");
                    result.add(rs);
                } else if ("durg_qt_module".equals(code)) {
                    rs.put("name", "其他药品");
                    rs.put("id", "durg_qt_module");
                    rs.put("order", "10");
                    result.add(rs);
                } else if ("history_module".equals(code)) {
                    rs.put("name", "院前用药");
                    rs.put("id", "history_module");
                    rs.put("order", "11");
                    result.add(rs);
                } else if ("dialysis_module".equals(code)) {
                    rs.put("name", "血液透析");
                    rs.put("id", "dialysis_module");
                    if (StringUtils.isNotBlank(CommonConfig.getURL(oid, "HD"))) {
                        rs.put("linkType", CommonConfig.getLinkType(oid, "HD"));
                    } else {
                        rs.put("linkType", "html");
                    }
                    rs.put("order", "12");
                    result.add(rs);
                } else if ("web_emr_module".equals(code)) {
                    rs.put("name", "WEB版电子病历");
                    rs.put("id", "web_emr_module");
                    if (StringUtils.isNotBlank(CommonConfig.getURL(oid, "WEBEMR"))) {
                        rs.put("linkType", CommonConfig.getLinkType(oid, "WEBEMR"));
                    } else {
                        rs.put("linkType", "html");
                    }
                    rs.put("order", "13");
                    result.add(rs);
                } else if ("fee_detail_module".equals(code)) {
                    rs.put("name", "费用明细");
                    rs.put("id", "fee_detail_module");
                    rs.put("linkType", "html");
                    rs.put("order", "94");
                    result.add(rs);
                } else if ("danger_nurse_module".equals(code)) {
                    rs.put("name", "危重护理记录");
                    rs.put("id", "danger_nurse_module");
                    if (StringUtils.isNotBlank(CommonConfig.getURL(oid, "DANGERNURSE"))) {
                        rs.put("linkType", CommonConfig.getLinkType(oid, "DANGERNURSE"));
                    } else {
                        rs.put("linkType", "html");
                    }
                    rs.put("order", "95");
                    result.add(rs);
                } else if ("hd_module".equals(code)) {
                    Map<String, Object> dangerMap = new HashMap<String, Object>();
                    dangerMap.put("name", "血透报告");
                    dangerMap.put("id", "hd_module");
                    dangerMap.put("order", "15");
                    result.add(dangerMap);
                }else if ("order_module".equals(code)) {
                    Map<String, Object> orderMap = new HashMap<String, Object>();
                    orderMap.put("name", "全部医嘱");
                    orderMap.put("id", "order_module");
                    orderMap.put("order", "16");
                    result.add(orderMap);
                } else {
                    /**
                     *通用模块，全都调用第三方Url
                     */
                    Map<String, Object> modules_config = Config.getMODULES_CONFIG(oid, code);
                    if (modules_config != null && modules_config.size() > 0) {
                        result.add(modules_config);
                    }
                }
            }
        }
        Utils.sortNumObjectList(result, "order", Page.Sort.ASC);
        return result;
    }

    //
    private void getAdminPowerByCategory(String oid, List<Map<String, Object>> result) {
        // TODO Auto-generated method stub

        String[] config = Config.getCIV_CATEGORY_VALUE(oid).split("/");
        for (int i = 0; i < config.length; i++) {
            Map<String, Object> map = new HashMap<String, Object>();
            String key = config[i];
            if ("check_module".equals(key)) {
                map.put("name", "检查报告");
                map.put("id", "check_module");
                result.add(map);
            } else if ("exam_module".equals(key)) {
                map.put("name", "检验报告");
                map.put("id", "exam_module");
                result.add(map);
            } else if ("oper_module".equals(key)) {
                String moduleName = "手术记录";
                if (HdrConstantEnum.HOSPITAL_WHET.getCode().equals(ConfigCache.getCache(oid, "org_oid"))) {
                    moduleName = "手术过程";
                }
                map.put("name", moduleName);
                map.put("id", "oper_module");
                if (StringUtils.isNotBlank(CommonConfig.getURL(oid, "OP"))) {
                    map.put("linkType", CommonConfig.getLinkType(oid, "OP"));
                } else {
                    map.put("linkType", "civ");
                }
                result.add(map);
            } else if ("main_diag_module".equals(key)) {
                map.put("name", "主要疾病诊断");
                map.put("id", "main_diag_module");
                result.add(map);
            } else if ("pathology_module".equals(key)) {
                map.put("name", "病理报告");
                map.put("id", "pathology_module");
                result.add(map);
            } else if ("record_module".equals(key)) {
                map.put("name", "病历文书");
                map.put("id", "record_module");
                String linkType = "html";
                if (StringUtils.isNotBlank(CommonConfig.getURL(oid, "RM"))) {
                    linkType = CommonConfig.getLinkType(oid, "RM");
                } else {
                    map.put("linkType", "html");
                }
                map.put("linkType", linkType);
                result.add(map);
            } else if ("nurse_module".equals(key)) {
                map.put("name", "护理记录");
                map.put("id", "nurse_module");
                map.put("linkType", "civ");
                boolean isDistinguish = Config.getCIV_NURSE_URL_OUT_OR_IN(oid);
                if (StringUtils.isNotBlank(CommonConfig.getURL(oid, "NURSE"))) {
                    map.put("linkType", CommonConfig.getLinkType(oid, "NURSE"));
                }
                if (isDistinguish && StringUtils.isNotBlank(CommonConfig.getURL(oid, "IN_NURSE"))) {
                    map.put("linkType", CommonConfig.getLinkType(oid, "IN_NURSE"));
                }
                if (isDistinguish && StringUtils.isNotBlank(CommonConfig.getURL(oid, "OUT_NURSE"))) {
                    map.put("linkType", CommonConfig.getLinkType(oid, "OUT_NURSE"));
                }
                result.add(map);
            } else if ("durg_orally_module".equals(key)) {
                map.put("name", "口服药品");
                map.put("id", "durg_orally_module");
                result.add(map);
            } else if ("durg_vein_module".equals(key)) {
                map.put("name", "静脉药品");
                map.put("id", "durg_vein_module");
                result.add(map);
            } else if ("durg_qt_module".equals(key)) {
                map.put("name", "其他药品");
                map.put("id", "durg_qt_module");
                result.add(map);
            } else if ("history_module".equals(key)) {
                map.put("name", "院前用药");
                map.put("id", "history_module");
                result.add(map);
            } else if ("dialysis_module".equals(key)) {
                map.put("name", "血液透析");
                map.put("id", "dialysis_module");
                if (StringUtils.isNotBlank(CommonConfig.getURL(oid, "HD"))) {
                    map.put("linkType", CommonConfig.getLinkType(oid, "HD"));
                } else {
                    map.put("linkType", "html");
                }
                map.put("order", "92");
                result.add(map);
            } else if ("web_emr_module".equals(key)) {
                map.put("name", "WEB版电子病历");
                map.put("id", "web_emr_module");
                if (StringUtils.isNotBlank(CommonConfig.getURL(oid, "WEBEMR"))) {
                    map.put("linkType", CommonConfig.getLinkType(oid, "WEBEMR"));
                } else {
                    map.put("linkType", "html");
                }
                map.put("order", "93");
                result.add(map);
            } else if ("fee_detail_module".equals(key)) {
                map.put("name", "费用明细");
                map.put("id", "fee_detail_module");
                map.put("linkType", "html");
                map.put("order", "94");
                result.add(map);
            } else if ("danger_nurse_module".equals(key)) {
                map.put("name", "危重护理记录");
                map.put("id", "danger_nurse_module");
                if (StringUtils.isNotBlank(CommonConfig.getURL(oid, "DANGERNURSE"))) {
                    map.put("linkType", CommonConfig.getLinkType(oid, "DANGERNURSE"));
                } else {
                    map.put("linkType", "html");
                }
                map.put("order", "95");
                result.add(map);
            } else if ("hd_module".equals(key)) {
                Map<String, Object> dangerMap = new HashMap<String, Object>();
                dangerMap.put("name", "血透报告");
                dangerMap.put("id", "hd_module");
                dangerMap.put("order", "15");
                result.add(dangerMap);
            } else if ("order_module".equals(key)) {
                Map<String, Object> orderMap = new HashMap<String, Object>();
                orderMap.put("name", "全部医嘱");
                orderMap.put("id", "order_module");
                orderMap.put("order", "16");
                result.add(orderMap);
            }else {
                /**
                 *通用模块，全都调用第三方Url
                 */
                Map<String, Object> modules_config = Config.getMODULES_CONFIG(oid, key);
                if (modules_config != null && modules_config.size() > 0) {
                    result.add(modules_config);
                }
            }
        }
    }

    @Override
    public Map<String, Object> getPowerConfigByEMR(String oid, String userCode) {
        // TODO Auto-generated method stub
        Map<String, Object> result = new HashMap<String, Object>();
        String admin = Config.getCiv_Admin(oid);
        if (admin.equals(userCode)) {
            result.put("isAll", "true");
            result.put("power", "all");
            return result;
        }
        String mr = getItemCodesByType(oid, userCode, "Mr");

        String[] values = mr.split("/");
        List<String> tmp = new ArrayList<String>();
        for (int i = 0; i < values.length; i++) {
            if (StringUtils.isNotBlank(values[i])) {
                tmp.add(values[i]);
            }
        }
        //如果权限中包含all，则返回true，不包含all，返回false和相应权限
        if (tmp.contains("all")) {
            result.put("isAll", "true");
            result.put("power", "all");
        } else {
            result.put("isAll", "false");
            result.put("power", tmp);
        }
        return result;
    }

    @Override
    public Map<String, Object> getPowerConfigByExam(String oid, String userCode) {
        // TODO Auto-generated method stub
        Map<String, Object> result = new HashMap<String, Object>();
        String admin = Config.getCiv_Admin(oid);
        if (admin.equals(userCode)) {
            result.put("isAll", "true");
            result.put("power", "all");
            return result;
        }
        Map<String, String> map = new HashMap<String, String>();
        QueryWrapper<PowerConfig> wrapper = new QueryWrapper<>();
        wrapper.eq("oid", oid).eq("type", "Exam").eq("usercode", userCode);
        List<PowerConfig> powerConfigList = powerConfigMapper.selectList(wrapper);
        for (PowerConfig powerConfig : powerConfigList) {
            map.put("userCode", powerConfig.getUsercode());
            map.put("deptCode", powerConfig.getDeptcode());
            map.put("type", powerConfig.getType());
            map.put("itemCodes", powerConfig.getItemcodes());
            break;
        }
//        map = powerDao.getPowerConfigByType(oid, userCode, "Exam");
        if (StringUtils.isBlank(map.get("itemCodes"))) {
            map.clear();
            String deptcode = powerDao.selectDeptByUser(oid, userCode);
            QueryWrapper<PowerConfigDept> configDeptWrapper = new QueryWrapper<>();
            configDeptWrapper.eq("oid", oid).eq("type", "Exam").eq("deptcode", deptcode);
            List<PowerConfigDept> powerConfigDeptList = powerConfigDeptMapper.selectList(configDeptWrapper);
            for (PowerConfigDept powerConfigDept : powerConfigDeptList) {
                map.put("deptCode", powerConfigDept.getDeptcode());//
                map.put("type", powerConfigDept.getType());//
                map.put("itemCodes", powerConfigDept.getItemcodes());//
                break;
            }
        }

        List<String> tmp = new ArrayList<String>();
        if(StringUtils.isNotBlank(map.get("itemCodes"))){
            String[] values = map.get("itemCodes").toString().split("/");
            for (int i = 0; i < values.length; i++) {
                if (StringUtils.isNotBlank(values[i])) {
                    tmp.add(values[i]);
                }
            }
        }
        //如果权限中包含all，则返回true，不包含all，返回false和相应权限
        if (tmp.contains("all")) {
            result.put("isAll", "true");
            result.put("power", "all");
        } else {
            result.put("isAll", "false");
            result.put("power", tmp);
        }
        return result;
    }

    @Override
    public Map<String, Object> getPowerConfigByPathology(String oid, String userCode) {
        // TODO Auto-generated method stub
        Map<String, Object> result = new HashMap<String, Object>();
        String admin = Config.getCiv_Admin(oid);
        if (admin.equals(userCode)) {
            result.put("isAll", "true");
            result.put("power", "all");
            return result;
        }
        Map<String, String> map = new HashMap<String, String>();
        QueryWrapper<PowerConfig> wrapper = new QueryWrapper<>();
        wrapper.eq("oid", oid).eq("type", "Pathology").eq("usercode", userCode);
        List<PowerConfig> powerConfigList = powerConfigMapper.selectList(wrapper);
        for (PowerConfig powerConfig : powerConfigList) {
            map.put("userCode", powerConfig.getUsercode());
            map.put("deptCode", powerConfig.getDeptcode());
            map.put("type", powerConfig.getType());
            map.put("itemCodes", powerConfig.getItemcodes());
            break;
        }
        if (StringUtils.isBlank(map.get("itemCodes"))) {
            map.clear();
            String deptcode = powerDao.selectDeptByUser(oid, userCode);
            QueryWrapper<PowerConfigDept> powerConfigDeptWrapper = new QueryWrapper<>();
            powerConfigDeptWrapper.eq("oid", oid).eq("type", "Pathology").eq("deptcode", deptcode);
            List<PowerConfigDept> powerConfigDeptList = powerConfigDeptMapper.selectList(powerConfigDeptWrapper);
            for (PowerConfigDept powerConfigDept : powerConfigDeptList) {
                map.put("deptCode", powerConfigDept.getDeptcode());
                map.put("type", powerConfigDept.getType());
                map.put("itemCodes", powerConfigDept.getItemcodes());
                break;
            }
        }
        String[] values = map.get("itemCodes").toString().split("/");
        List<String> tmp = new ArrayList<String>();
        for (int i = 0; i < values.length; i++) {
            if (StringUtils.isNotBlank(values[i])) {
                tmp.add(values[i]);
            }
        }
        //如果权限中包含all，则返回true，不包含all，返回false和相应权限
        if (tmp.contains("all")) {
            result.put("isAll", "true");
            result.put("power", "all");
        } else {
            result.put("isAll", "false");
            result.put("power", tmp);
        }
        return result;
    }

    @Override
    public Map<String, String> updatePowerConfigByUser(String oid, String userCodes, String Current, String Specialty,
                                                       String TimeAxis, String Visit, String Category, String Mr, String Exam, String Pathology,
                                                       String specialtyTimeAxis, String medicalView, String userOid) {
        // TODO Auto-generated method stub
        //所有院区oid

        String result = "";
        Map<String, String> map = new HashMap<String, String>();
        String[] users = userCodes.split("/");

        for (int i = 0; i < users.length; i++) {
            List<Map<String, String>> configs = new ArrayList<Map<String, String>>();
            configs = powerDao.getPowerConfig(oid, users[i], true);
            if (configs.size() == 0) {
                String deptCode = powerDao.selectDeptByUser(oid, users[i]);
//				insertPowerConfigByUser(users[i], deptCode);
                //上面一行注释掉了，原来第一次设置权限的时候是从科室里面取权限，不是页面设置的权限，导致第一次配置的权限不生效
                //原本的设计是第一次是初始化的
                DateFormat bf = new SimpleDateFormat("yyyy-MM-dd E a HH:mm:ss");
                String date = bf.format(new Date());
                if ("current".equals(Config.getCIV_CURRENT_VALUE(oid))) {
                    if (!powerDao.insertPowerConfigByUser(oid, users[i], deptCode, "Current", Current, date))
                        result = result + "当前视图设置权限失败。";
                }
                if ("specialty".equals(Config.getCIV_SPECIALTY_VALUE(oid))) {
                    if (!powerDao.insertPowerConfigByUser(oid, users[i], deptCode, "Specialty", Specialty, date))
                        result = result + "专科视图设置权限失败。";
                }
                if ("timeaxis".equals(Config.getCIV_TIMEAXIS_VALUE(oid))) {
                    if (!powerDao.insertPowerConfigByUser(oid, users[i], deptCode, "TimeAxis", StringUtils.isBlank(TimeAxis) ? "" : TimeAxis, date))
                        result = result + "时间轴视图设置权限失败。";
                }
                if ("specialtytimeaxis".equals(Config.getCIV_SPECIALTYTIMEAXIS_VALUE(oid))) {
                    if (!powerDao.insertPowerConfigByUser(oid, users[i], deptCode, "SpecialtyTimeAxis", StringUtils.isBlank(specialtyTimeAxis) ? "" : specialtyTimeAxis, date))
                        result = result + "专科视图时间轴设置权限失败。";
                }
                if ("medicalview".equals(Config.getCIV_MEDICAL_VALUE(oid))) {
                    if (!powerDao.insertPowerConfigByUser(oid, users[i], deptCode, "MedicalView", StringUtils.isBlank(medicalView) ? "" : medicalView, date))
                        result = result + "体检视图设置权限失败。";
                }
                if (!powerDao.insertPowerConfigByUser(oid, users[i], deptCode, "Visit", StringUtils.isBlank(Visit) ? "" : Visit, date))
                    result = result + "就诊视图设置权限失败。";
                if (!powerDao.insertPowerConfigByUser(oid, users[i], deptCode, "Category", StringUtils.isBlank(Category) ? "" : Category, date))
                    result = result + "分类视图设置权限失败。";
                if (!powerDao.insertPowerConfigByUser(oid, users[i], deptCode, "Mr", StringUtils.isBlank(Mr) ? "" : Mr, date))
                    result = result + "病历文书设置权限失败。";
                if (!powerDao.insertPowerConfigByUser(oid, users[i], deptCode, "Exam", StringUtils.isBlank(Exam) ? "" : Exam, date))
                    result = result + "检查报告设置权限失败。";
                if (!powerDao.insertPowerConfigByUser(oid, users[i], deptCode, "Pathology", StringUtils.isBlank(Pathology) ? "" : Pathology, date))
                    result = result + "病理报告设置权限失败。";

            } else {
                if ("current".equals(Config.getCIV_CURRENT_VALUE(oid))) {
                    if (!updatePowerConfigByType(oid, userCodes, "Current", Current, true))
                        result = result + "当前视图设置权限失败。";
                }
                if ("specialty".equals(Config.getCIV_SPECIALTY_VALUE(oid))) {
                    if (!updatePowerConfigByType(oid, userCodes, "Specialty", Specialty, true))
                        result = result + "专科视图设置权限失败。";
                }
                if ("timeaxis".equals(Config.getCIV_TIMEAXIS_VALUE(oid))) {
                    if (!updatePowerConfigByType(oid, userCodes, "TimeAxis", StringUtils.isBlank(TimeAxis) ? "" : TimeAxis,
                            true))
                        result = result + "时间轴视图设置权限失败。";
                }
                if ("specialtytimeaxis".equals(Config.getCIV_SPECIALTYTIMEAXIS_VALUE(oid))) {
                    if (!updatePowerConfigByType(oid, userCodes, "SpecialtyTimeAxis",
                            StringUtils.isBlank(specialtyTimeAxis) ? "" : specialtyTimeAxis, true))
                        result = result + "专科视图时间轴设置权限失败。";
                }
                if ("medicalview".equals(Config.getCIV_MEDICAL_VALUE(oid))) {
                    if (!updatePowerConfigByType(oid, userCodes, "MedicalView",
                            StringUtils.isBlank(medicalView) ? "" : medicalView, true))
                        result = result + "体检视图设置权限失败。";
                }
                if (!updatePowerConfigByType(oid, userCodes, "Visit", Visit, true))
                    result = result + "就诊视图设置权限失败。";
                if (!updatePowerConfigByType(oid, userCodes, "Category", Category, true))
                    result = result + "分类视图设置权限失败。";
                if (!updatePowerConfigByType(oid, userCodes, "Mr", Mr, true))
                    result = result + "病历文书设置权限失败。";
                if (!updatePowerConfigByType(oid, userCodes, "Exam", Exam, true))
                    result = result + "检查报告设置权限失败。";
                if (!updatePowerConfigByType(oid, userCodes, "Pathology", Pathology, true))
                    result = result + "病理报告设置权限失败。";
            }
        }

        if (!"".equals(result)) {
            map.put("result", "0");
            map.put("msg", result);
        } else {
            map.put("result", "1");
        }
        return map;
    }

    @Override
    public Map<String, String> updatePowerConfigByDept(String oid, String deptCodes, String Current, String Specialty,
                                                       String TimeAxis, String Visit, String Category, String Mr, String Exam, String Pathology,
                                                       String specialtyTimeAxis, String medicalView) {
        // TODO Auto-generated method stub
        Map<String, String> map = new HashMap<String, String>();
        String result = "";
        if ("current".equals(Config.getCIV_CURRENT_VALUE(oid))) {
            if (!updatePowerConfigByType(oid, deptCodes, "Current", StringUtils.isBlank(Current) ? "" : Current, false))
                result = result + "当前视图设置权限失败。";
        }
        if ("specialty".equals(Config.getCIV_SPECIALTY_VALUE(oid))) {
            if (!updatePowerConfigByType(oid, deptCodes, "Specialty", Specialty, false))
                result = result + "专科视图设置权限失败。";
        }
        if ("timeaxis".equals(Config.getCIV_TIMEAXIS_VALUE(oid))) {
            if (!updatePowerConfigByType(oid, deptCodes, "TimeAxis", StringUtils.isBlank(TimeAxis) ? "" : TimeAxis, false))
                result = result + "时间轴视图设置权限失败。";
        }
        if ("specialtytimeaxis".equals(Config.getCIV_SPECIALTYTIMEAXIS_VALUE(oid))) {
            if (!updatePowerConfigByType(oid, deptCodes, "SpecialtyTimeAxis",
                    StringUtils.isBlank(specialtyTimeAxis) ? "" : specialtyTimeAxis, false))
                result = result + "专科视图时间轴设置权限失败。";
        }
        if ("medicalview".equals(Config.getCIV_MEDICAL_VALUE(oid))) {
            if (!updatePowerConfigByType(oid, deptCodes, "MedicalView", StringUtils.isBlank(medicalView) ? "" : medicalView,
                    false))
                result = result + "体检视图设置权限失败。";
        }
        if (!updatePowerConfigByType(oid, deptCodes, "Visit", Visit, false))
            result = result + "就诊视图设置权限失败。";
        if (!updatePowerConfigByType(oid, deptCodes, "Category", Category, false))
            result = result + "分类视图设置权限失败。";
        if (!updatePowerConfigByType(oid, deptCodes, "Mr", Mr, false))
            result = result + "病历文书设置权限失败。";
        if (!updatePowerConfigByType(oid, deptCodes, "Exam", Exam, false))
            result = result + "检查报告设置权限失败。";
        if (!updatePowerConfigByType(oid, deptCodes, "Pathology", Pathology, false))
            result = result + "病理报告设置权限失败。";
        if (!"".equals(result)) {
            map.put("result", "0");
            map.put("msg", result);
        } else {
            map.put("result", "1");
        }
        return map;
    }

    public boolean updatePowerConfigByType(String oid, String codes, String type, String value, boolean isUser) {
        // TODO Auto-generated method stub
        String[] users = codes.split("/");
        boolean is = powerDao.updatePowerConfigByType(oid, users, type, value, isUser);
        return is;
    }

    @Override
    public Map<String, Object> getPowerConfigByUser(String oid, String userCode) {
        // TODO Auto-generated method stub
        Map<String, Object> rs = new HashMap<String, Object>();
        List<Map<String, String>> configs = new ArrayList<Map<String, String>>();
        String admin = Config.getCiv_Admin(oid);
        if (admin.equals(userCode)) {
            getAdminPower(oid, configs, userCode, false);
        } else {
            configs = powerDao.getPowerConfig(oid, userCode, true);
        }
        if (configs.size() == 0) {
            String deptCode = powerDao.selectDeptByUser(oid, userCode);
            rs = getPowerConfigByDept(oid, deptCode);
            return rs;
        }
        List<Map<String, Object>> visitSets = new ArrayList<Map<String, Object>>();
        List<Map<String, Object>> contentSets = new ArrayList<Map<String, Object>>();
        for (Map<String, String> config : configs) {
            Map<String, Object> map = new HashMap<String, Object>();
            if ("Current".equals(config.get("type"))) {
                map.put("name", "当前视图");
                map.put("id", "Current");
                map.put("text", "");
                map.put("order", "1");
                List<Map<String, String>> list = new ArrayList<Map<String, String>>();
                Map<String, String> map2 = new HashMap<String, String>();
                map2.put("name", "选择");
                if (config.get("itemcodes") == null || "".equals(config.get("itemcodes"))) {
                    map2.put("value", "0");
                } else {
                    map2.put("value", "1");
                }
                map2.put("code", "current");
                list.add(map2);
                map.put("list", list);
                visitSets.add(map);
            } else if ("Specialty".equals(config.get("type"))) {
                map.put("name", "专科视图");
                map.put("id", "Specialty");
                map.put("text", "");
                map.put("order", "2");
                List<Map<String, String>> list = new ArrayList<Map<String, String>>();
                Map<String, String> map2 = new HashMap<String, String>();
                map2.put("name", "选择");
                if (config.get("itemcodes") == null || "".equals(config.get("itemcodes"))) {
                    map2.put("value", "0");
                } else {
                    map2.put("value", "1");
                }
                map2.put("code", "specialty");
                list.add(map2);
                map.put("list", list);
                visitSets.add(map);
            } else if ("TimeAxis".equals(config.get("type"))) {
                map.put("name", "时间轴视图");
                map.put("id", "TimeAxis");
                map.put("text", "");
                map.put("order", "3");
                List<Map<String, String>> list = new ArrayList<Map<String, String>>();
                Map<String, String> map2 = new HashMap<String, String>();
                map2.put("name", "选择");
                if (config.get("itemcodes") == null || "".equals(config.get("itemcodes"))) {
                    map2.put("value", "0");
                } else {
                    map2.put("value", "1");
                }
                map2.put("code", "timeaxis");
                list.add(map2);
                map.put("list", list);
                visitSets.add(map);
            } else if ("Visit".equals(config.get("type"))) {
                map.put("name", "就诊视图");
                map.put("id", "Visit");
                map.put("text", "选择该页面的全部子页面");
                map.put("order", "4");
                List<Map<String, String>> list = new ArrayList<Map<String, String>>();
                List<Map<String, String>> types = Config.getCiv_Visit(oid);
                getTypeList(types, list, config.get("itemcodes"));
                map.put("list", list);
                visitSets.add(map);
            } else if ("Category".equals(config.get("type"))) {
                map.put("name", "分类视图");
                map.put("id", "Category");
                map.put("text", "选择该页面的全部子页面");
                map.put("order", "5");
                List<Map<String, String>> list = new ArrayList<Map<String, String>>();
                List<Map<String, String>> types = Config.getCiv_Category(oid);
                getTypeList(types, list, config.get("itemcodes"));
                map.put("list", list);
                visitSets.add(map);
            } else if ("Mr".equals(config.get("type"))) {
                map.put("name", "病历文书");
                map.put("id", "Mr");
                map.put("text", "选择全部文书类型");
                map.put("order", "6");
                List<Map<String, String>> list = new ArrayList<Map<String, String>>();
                List<Map<String, String>> types = Config.getCiv_Emr_Types(oid);
                getTypeList(types, list, config.get("itemcodes"));
                map.put("list", list);
                contentSets.add(map);
            } else if ("Exam".equals(config.get("type"))) {
                map.put("name", "检查报告");
                map.put("id", "Exam");
                map.put("text", "选择全部报告类型");
                map.put("order", "7");
                List<Map<String, String>> list = new ArrayList<Map<String, String>>();
                List<Map<String, String>> types = Config.getCiv_Exam_Types(oid);
                getTypeList(types, list, config.get("itemcodes"));
                map.put("list", list);
                contentSets.add(map);
            } else if ("SpecialtyTimeAxis".equals(config.get("type"))) {
                map.put("name", "专科视图时间轴");
                map.put("id", "SpecialtyTimeAxis");
                map.put("text", "");
                map.put("order", "8");
                List<Map<String, String>> list = new ArrayList<Map<String, String>>();
                Map<String, String> map2 = new HashMap<String, String>();
                map2.put("name", "选择");
                if (config.get("itemcodes") == null || "".equals(config.get("itemcodes"))) {
                    map2.put("value", "0");
                } else {
                    map2.put("value", "1");
                }
                map2.put("code", "specialtytimeaxis");
                list.add(map2);
                map.put("list", list);
                visitSets.add(map);
            } else if ("MedicalView".equals(config.get("type"))) {
                map.put("name", "体检视图");
                map.put("id", "MedicalView");
                map.put("text", "选择");
                map.put("order", "9");
                List<Map<String, String>> list = new ArrayList<Map<String, String>>();
                Map<String, String> map2 = new HashMap<String, String>();
                map2.put("name", "选择");
                if (config.get("itemcodes") == null || "".equals(config.get("itemcodes"))) {
                    map2.put("value", "0");
                } else {
                    map2.put("value", "1");
                }
                map2.put("code", "medicalview");
                list.add(map2);
                map.put("list", list);
                visitSets.add(map);
            } else if ("Pathology".equals(config.get("type"))) {
                map.put("name", "病理报告");
                map.put("id", "Pathology");
                map.put("text", "选择全部报告类型");
                map.put("order", "10");
                List<Map<String, String>> list = new ArrayList<Map<String, String>>();
                List<Map<String, String>> types = Config.getCIV_PATHOLOGY_TYPE(oid);
                getTypeList(types, list, config.get("itemcodes"));
                map.put("list", list);
                contentSets.add(map);
            }
        }
        Utils.sortListByDate(visitSets, "order", Page.Sort.ASC);
        Utils.sortListByDate(contentSets, "order", Page.Sort.ASC);
        rs.put("visitSet", visitSets);
        rs.put("contentSet", contentSets);

        return rs;
    }
    @Override
    public List<Map<String, String>> getVipConfigByUser(String oid, String userCode) {
        // TODO Auto-generated method stub
        List<Map<String, String>> rs = new ArrayList<>();
        List<Map<String, String>> configs = new ArrayList<Map<String, String>>();
        String admin = Config.getCiv_Admin(oid);
        if (admin.equals(userCode)) {
            getAdminVip(oid, configs, userCode, false);
        } else {
            configs = powerDao.getVipConfig(oid, userCode, true);
        }
        if (configs.size() == 0) {
            String deptCode = powerDao.selectDeptByUser(oid, userCode);
            rs = getVipConfigByDept(oid, deptCode);
            return rs;
        }
        Map<String, String> map = new HashMap<String, String>();
        List<Map<String, Object>> contentSets = new ArrayList<Map<String, Object>>();
        for (Map<String, String> config : configs) {
            if ("VIP".equals(config.get("type")) && "1".equals(config.get("itemcodes"))) {
                map.put("configName", "VIP患者数据访问");//
                map.put("configCode", "VIP");//
                map.put("configValue","1");//
            }else {
                map.put("configName", "VIP患者数据访问");//
                map.put("configCode", "VIP");//
                map.put("configValue","0");//
            }
        }
        rs.add(map);
        return rs;
    }

    @Override
    public Map<String, Object> getPowerConfigByDept(String oid, String deptCode) {
        // TODO Auto-generated method stub
        Map<String, Object> rs = new HashMap<String, Object>();
        List<Map<String, String>> configs = new ArrayList<Map<String, String>>();
        configs = powerDao.getPowerConfig(oid, deptCode, false);
        if (configs.size() == 0) {
            insertPowerConfigByDept(oid, deptCode);
            getAdminPower(oid, configs, deptCode, true);
        }
        List<Map<String, Object>> visitSets = new ArrayList<Map<String, Object>>();
        List<Map<String, Object>> contentSets = new ArrayList<Map<String, Object>>();
        for (Map<String, String> config : configs) {
            Map<String, Object> map = new HashMap<String, Object>();
            if ("Current".equals(config.get("type"))) {
                map.put("name", "当前视图");
                map.put("id", "Current");
                map.put("text", "");
                map.put("order", "1");
                List<Map<String, String>> list = new ArrayList<Map<String, String>>();
                Map<String, String> map2 = new HashMap<String, String>();
                map2.put("name", "选择");
                if (config.get("itemcodes") == null || "".equals(config.get("itemcodes"))) {
                    map2.put("value", "0");
                } else {
                    map2.put("value", "1");
                }
                map2.put("code", "current");
                list.add(map2);
                map.put("list", list);
                visitSets.add(map);
            } else if ("Specialty".equals(config.get("type"))) {
                map.put("name", "专科视图");
                map.put("id", "Specialty");
                map.put("text", "");
                map.put("order", "2");
                List<Map<String, String>> list = new ArrayList<Map<String, String>>();
                Map<String, String> map2 = new HashMap<String, String>();
                map2.put("name", "选择");
                if (config.get("itemcodes") == null || "".equals(config.get("itemcodes"))) {
                    map2.put("value", "0");
                } else {
                    map2.put("value", "1");
                }
                map2.put("code", "specialty");
                list.add(map2);
                map.put("list", list);
                visitSets.add(map);
            } else if ("TimeAxis".equals(config.get("type"))) {
                map.put("name", "时间轴视图");
                map.put("id", "TimeAxis");
                map.put("text", "");
                map.put("order", "3");
                List<Map<String, String>> list = new ArrayList<Map<String, String>>();
                Map<String, String> map2 = new HashMap<String, String>();
                map2.put("name", "选择");
                if (config.get("itemcodes") == null || "".equals(config.get("itemcodes"))) {
                    map2.put("value", "0");
                } else {
                    map2.put("value", "1");
                }
                map2.put("code", "timeaxis");
                list.add(map2);
                map.put("list", list);
                visitSets.add(map);
            } else if ("Visit".equals(config.get("type"))) {
                map.put("name", "就诊视图");
                map.put("id", "Visit");
                map.put("text", "选择该页面的全部子页面");
                map.put("order", "4");
                List<Map<String, String>> list = new ArrayList<Map<String, String>>();
                List<Map<String, String>> types = Config.getCiv_Visit(oid);
                getTypeList(types, list, config.get("itemcodes"));
                map.put("list", list);
                visitSets.add(map);
            } else if ("Category".equals(config.get("type"))) {
                map.put("name", "分类视图");
                map.put("id", "Category");
                map.put("text", "选择该页面的全部子页面");
                map.put("order", "5");
                List<Map<String, String>> list = new ArrayList<Map<String, String>>();
                List<Map<String, String>> types = Config.getCiv_Category(oid);
                getTypeList(types, list, config.get("itemcodes"));
                map.put("list", list);
                visitSets.add(map);
            } else if ("Mr".equals(config.get("type"))) {
                map.put("name", "病历文书");
                map.put("id", "Mr");
                map.put("text", "选择全部文书类型");
                map.put("order", "6");
                List<Map<String, String>> list = new ArrayList<Map<String, String>>();
                List<Map<String, String>> types = Config.getCiv_Emr_Types(oid);
                getTypeList(types, list, config.get("itemcodes"));
                map.put("list", list);
                contentSets.add(map);
            } else if ("Exam".equals(config.get("type"))) {
                map.put("name", "检查报告");
                map.put("id", "Exam");
                map.put("text", "选择全部报告类型");
                map.put("order", "7");
                List<Map<String, String>> list = new ArrayList<Map<String, String>>();
                List<Map<String, String>> types = Config.getCiv_Exam_Types(oid);
                getTypeList(types, list, config.get("itemcodes"));
                map.put("list", list);
                contentSets.add(map);
            } else if ("SpecialtyTimeAxis".equals(config.get("type"))) {
                map.put("name", "专科视图时间轴");
                map.put("id", "SpecialtyTimeAxis");
                map.put("text", "");
                map.put("order", "8");
                List<Map<String, String>> list = new ArrayList<Map<String, String>>();
                Map<String, String> map2 = new HashMap<String, String>();
                map2.put("name", "选择");
                if (config.get("itemcodes") == null || "".equals(config.get("itemcodes"))) {
                    map2.put("value", "0");
                } else {
                    map2.put("value", "1");
                }
                map2.put("code", "specialtytimeaxis");
                list.add(map2);
                map.put("list", list);
                visitSets.add(map);
            } else if ("MedicalView".equals(config.get("type"))) {
                map.put("name", "体检视图");
                map.put("id", "MedicalView");
                map.put("text", "选择");
                map.put("order", "9");
                List<Map<String, String>> list = new ArrayList<Map<String, String>>();
                Map<String, String> map2 = new HashMap<String, String>();
                map2.put("name", "选择");
                if (config.get("itemcodes") == null || "".equals(config.get("itemcodes"))) {
                    map2.put("value", "0");
                } else {
                    map2.put("value", "1");
                }
                map2.put("code", "medicalview");
                list.add(map2);
                map.put("list", list);
                visitSets.add(map);
            } else if ("Pathology".equals(config.get("type"))) {
                map.put("name", "病理报告");
                map.put("id", "Pathology");
                map.put("text", "选择全部报告类型");
                map.put("order", "10");
                List<Map<String, String>> list = new ArrayList<Map<String, String>>();
                List<Map<String, String>> types = Config.getCIV_PATHOLOGY_TYPE(oid);
                getTypeList(types, list, config.get("itemcodes"));
                map.put("list", list);
                contentSets.add(map);
            }
        }
        Utils.sortListByDate(visitSets, "order", Page.Sort.ASC);
        Utils.sortListByDate(contentSets, "order", Page.Sort.ASC);
        rs.put("visitSet", visitSets);
        rs.put("contentSet", contentSets);

        return rs;
    }
    @Override
    public List<Map<String, String>> getVipConfigByDept(String oid, String deptCode) {
        // TODO Auto-generated method stub
        List<Map<String, String>> rs = new ArrayList<Map<String, String>>();
        List<Map<String, String>> configs = new ArrayList<Map<String, String>>();
        configs = powerDao.getVipConfig(oid, deptCode, false);
        if (configs.size() == 0) {
            insertVipConfigByDept(oid, deptCode);
            getAdminVip(oid, configs, deptCode, true);
        }
        Map<String, String> map = new HashMap<String, String>();
        for (Map<String, String> config : configs) {
           if ("VIP".equals(config.get("type")) && "1".equals(config.get("itemcodes"))) {
               map.put("configName", "VIP患者数据访问");//
               map.put("configCode", "VIP");//
               map.put("configValue","1");//
            }else {
               map.put("configName", "VIP患者数据访问");//
               map.put("configCode", "VIP");//
               map.put("configValue","0");//
           }
        }
        rs.add(map);
        return rs;
    }
    private void insertPowerConfigByUser(String oid, String userCode, String deptCode) {
        // TODO Auto-generated method stub
        List<Map<String, String>> configs = new ArrayList<Map<String, String>>();
        configs = powerDao.getPowerConfig(oid, deptCode, false);
        DateFormat bf = new SimpleDateFormat("yyyy-MM-dd E a HH:mm:ss");
        String date = bf.format(new Date());
        for (Map<String, String> map : configs) {
            powerDao.insertPowerConfigByUser(oid, userCode, deptCode, map.get("type"), map.get("itemCodes"), date);
        }
    }

    private void insertPowerConfigByDept(String oid, String deptCode) {
        // TODO Auto-generated method stub
        DateFormat bf = new SimpleDateFormat("yyyy-MM-dd E a HH:mm:ss");
        String date = bf.format(new Date());
        if ("current".equals(Config.getCIV_CURRENT_VALUE(oid))) {
            powerDao.insertPowerConfigByDept(oid, deptCode, "Current", Config.getCIV_CURRENT_VALUE(oid), date);
        }
        if ("specialty".equals(Config.getCIV_SPECIALTY_VALUE(oid))) {
            powerDao.insertPowerConfigByDept(oid, deptCode, "Specialty", Config.getCIV_SPECIALTY_VALUE(oid), date);
        }
        if ("timeaxis".equals(Config.getCIV_TIMEAXIS_VALUE(oid))) {
            powerDao.insertPowerConfigByDept(oid, deptCode, "TimeAxis", Config.getCIV_TIMEAXIS_VALUE(oid), date);
        }
        if ("specialtytimeaxis".equals(Config.getCIV_SPECIALTYTIMEAXIS_VALUE(oid))) {
            powerDao.insertPowerConfigByDept(oid, deptCode, "SpecialtyTimeAxis", Config.getCIV_SPECIALTYTIMEAXIS_VALUE(oid),
                    date);
        }
        if ("medicalview".equals(Config.getCIV_MEDICAL_VALUE(oid))) {
            powerDao.insertPowerConfigByDept(oid, deptCode, "MedicalView", Config.getCIV_MEDICAL_VALUE(oid), date);
        }
        powerDao.insertPowerConfigByDept(oid, deptCode, "Visit", Config.getCIV_VISIT_VALUE(oid), date);
        powerDao.insertPowerConfigByDept(oid, deptCode, "Category", Config.getCIV_CATEGORY_VALUE(oid), date);
        powerDao.insertPowerConfigByDept(oid, deptCode, "Mr", Config.getCIV_EMR_VALUE(oid), date);
        powerDao.insertPowerConfigByDept(oid, deptCode, "Exam", Config.getCIV_EXAM_VALUE(oid), date);
        powerDao.insertPowerConfigByDept(oid, deptCode, "Pathology", Config.getCIV_PATHOLOGY_VALUE(oid), date);
    }
    private void insertVipConfigByDept(String oid, String deptCode) {
        // TODO Auto-generated method stub
        DateFormat bf = new SimpleDateFormat("yyyy-MM-dd E a HH:mm:ss");
        String date = bf.format(new Date());
        powerDao.insertPowerConfigByDept(oid, deptCode, "VIP", "0", date);
    }

    private void getAdminPower(String oid, List<Map<String, String>> configs, String code, boolean isDept) {
        // TODO Auto-generated method stub
        String userCode;
        String deptCode;
        if (isDept) {
            userCode = "";
            deptCode = code;
        } else {
            userCode = code;
            deptCode = "";
        }

        if ("current".equals(Config.getCIV_CURRENT_VALUE(oid))) {
            Map<String, String> Current = new HashMap<String, String>();
            Current.put("itemcodes", Config.getCIV_CURRENT_VALUE(oid));
            Current.put("userCode", userCode);
            Current.put("deptCode", deptCode);
            Current.put("type", "Current");
            configs.add(Current);
        }

        if ("specialty".equals(Config.getCIV_SPECIALTY_VALUE(oid))) {
            Map<String, String> Specialty = new HashMap<String, String>();
            Specialty.put("itemcodes", Config.getCIV_SPECIALTY_VALUE(oid));
            Specialty.put("userCode", userCode);
            Specialty.put("deptCode", deptCode);
            Specialty.put("type", "Specialty");
            configs.add(Specialty);
        }

        if ("timeaxis".equals(Config.getCIV_TIMEAXIS_VALUE(oid))) {
            Map<String, String> TimeAxis = new HashMap<String, String>();
            TimeAxis.put("itemcodes", Config.getCIV_SPECIALTY_VALUE(oid));
            TimeAxis.put("userCode", userCode);
            TimeAxis.put("deptCode", deptCode);
            TimeAxis.put("type", "TimeAxis");
            configs.add(TimeAxis);
        }
        if ("specialtytimeaxis".equals(Config.getCIV_SPECIALTYTIMEAXIS_VALUE(oid))) {
            Map<String, String> specialtyTimeaxis = new HashMap<String, String>();
            specialtyTimeaxis.put("itemcodes", Config.getCIV_SPECIALTYTIMEAXIS_VALUE(oid));
            specialtyTimeaxis.put("userCode", userCode);
            specialtyTimeaxis.put("deptCode", deptCode);
            specialtyTimeaxis.put("type", "SpecialtyTimeAxis");
            configs.add(specialtyTimeaxis);
        }

        Map<String, String> Category = new HashMap<String, String>();
        Category.put("itemcodes", Config.getCIV_CATEGORY_VALUE(oid));
        Category.put("userCode", userCode);
        Category.put("deptCode", deptCode);
        Category.put("type", "Category");
        configs.add(Category);

        Map<String, String> Mr = new HashMap<String, String>();
        Mr.put("itemcodes", Config.getCIV_EMR_VALUE(oid));
        Mr.put("userCode", userCode);
        Mr.put("deptCode", deptCode);
        Mr.put("type", "Mr");
        configs.add(Mr);

        Map<String, String> Exam = new HashMap<String, String>();
        Exam.put("itemcodes", Config.getCIV_EXAM_VALUE(oid));
        Exam.put("userCode", userCode);
        Exam.put("deptCode", deptCode);
        Exam.put("type", "Exam");
        configs.add(Exam);

        Map<String, String> Visit = new HashMap<String, String>();
        Visit.put("itemcodes", Config.getCIV_VISIT_VALUE(oid));
        Visit.put("userCode", userCode);
        Visit.put("deptCode", deptCode);
        Visit.put("type", "Visit");
        configs.add(Visit);

        //新增体检视图权限
        if ("medicalview".equals(Config.getCIV_MEDICAL_VALUE(oid))) {
            Map<String, String> medicalView = new HashMap<String, String>();
            medicalView.put("itemcodes", "MedicalView");
            medicalView.put("userCode", userCode);
            medicalView.put("deptCode", deptCode);
            medicalView.put("type", "MedicalView");
            configs.add(medicalView);
        }

        Map<String, String> Pathology = new HashMap<String, String>();
        Pathology.put("itemcodes", Config.getCIV_PATHOLOGY_VALUE(oid));
        Pathology.put("userCode", userCode);
        Pathology.put("deptCode", deptCode);
        Pathology.put("type", "Pathology");
        configs.add(Pathology);

    }
    private void getAdminVip(String oid, List<Map<String, String>> configs, String code, boolean isDept) {
        // TODO Auto-generated method stub
        String userCode;
        String deptCode;
        if (isDept) {
            userCode = "";
            deptCode = code;
        } else {
            userCode = code;
            deptCode = "";
        }
        Map<String, String> VIP = new HashMap<String, String>();
        VIP.put("itemcodes", "1");
        VIP.put("userCode", userCode);
        VIP.put("deptCode", deptCode);
        VIP.put("type", "VIP");
        configs.add(VIP);

    }

    /**
     * 根据配置文件和数据对比查询权限
     *
     * @param types
     * @param list
     * @param
     */
    private void getTypeList(List<Map<String, String>> types, List<Map<String, String>> list, String itemCodes) {
        // TODO Auto-generated method stub
//		itemCodes = itemCodes.replaceAll("\\\\","\\\\\\\\");
        for (int i = 0; i < types.size(); i++) {
            Map<String, String> map = new HashMap<String, String>();
            map.put("name", types.get(i).get("name"));
            map.put("code", types.get(i).get("code"));
            if (itemCodes.indexOf(types.get(i).get("code")) != -1) {
                map.put("value", "1");
            } else {
                map.put("value", "0");
            }
            list.add(map);
        }
    }

    //用户列表
    @Override
    public Map<String, Object> getUserList(String oid, String userName, String deptCode, int pageNo, int pageSize) {
        Map<String, Object> rs = new HashMap<String, Object>();
        if (pageNo == 0) {
            pageNo = 1;
        }
        if (pageSize == 0) {
            pageSize = 10;
        }
        List<DeptListWithinUser> users = new ArrayList<>();
        List<DeptListWithinUser> list = powerDao.getUserList(oid, userName, deptCode);
        if (list.size() >= (pageSize * pageNo)) {
            users = list.subList(pageSize * (pageNo - 1), pageSize * pageNo);
        } else if (list.size() < (pageSize * pageNo) && list.size() >= (pageSize * (pageNo - 1))) {
            users = list.subList(pageSize * (pageNo - 1), list.size());
        }
        rs.put("pageNo", pageNo);
        rs.put("result", users);

        return rs;
    }

    //查询部门列表
    @Override
    public Page<Map<String, Object>> getDeptCodeList(String oid, String deptName, int pageNo, int pageSize) {
        // TODO Auto-generated method stub
        Page<Map<String, Object>> page = new Page<>();

        List<Map<String, Object>> result = new ArrayList<>();
        if (pageNo == 1) {
            Map<String, Object> alldept = new HashMap<>();
            alldept.put("deptCode", "01");
            alldept.put("deptName", "全部科室");
            result.add(alldept);
        }

        //全部结果
        List<Map<String, Object>> list = new ArrayList<>();
        list = powerDao.getDeptCodeList(oid, deptName);
        if (list.size() == 0) {
            return page;
        }

        if (list.size() > ((pageNo - 1) * pageSize)) {
            //左闭右开
            if (list.size() < pageNo * pageSize) {
                result = list.subList((pageNo - 1) * pageSize, list.size());
            } else if (list.size() >= pageNo * pageSize) {
                result = list.subList((pageNo - 1) * pageSize, pageNo * pageSize);
            }
            page.setPageNo(pageNo);
            page.setOrderBy("deptCode");
            page.setOrderDir("desc");
            page.setPageSize(pageSize);
            page.setCountTotal(true);
            page.setTotalCount(list.size());
            page.setResult(result);
        } else {
            pageNo = 1;
            //左闭右开
            if (list.size() < pageNo * pageSize) {
                result = list.subList((pageNo - 1) * pageSize, list.size());
            } else if (list.size() >= pageNo * pageSize) {
                result = list.subList((pageNo - 1) * pageSize, pageNo * pageSize);
            }
            page.setPageNo(pageNo);
            page.setOrderBy("deptCode");
            page.setOrderDir("desc");
            page.setPageSize(pageSize);
            page.setCountTotal(true);
            page.setTotalCount(list.size());
            page.setResult(result);
        }
        return page;
    }

    @Override
    public Map<String, String> initDeptPower(String oid) {


        //删除所有院区权限表
        Map<String, String> rs = new HashMap<String, String>();

        powerDao.deleteDeptPower(oid);


        powerDao.deleteUserPower(oid);

        try {
            List<Map<String, String>> depts = new ArrayList<Map<String, String>>();
            depts = powerDao.getAllDept(oid);

            for (int i = 0; i < depts.size(); i++) {

                insertDeptPower(oid, depts.get(i).get("deptcode"));

            }
        } catch (Exception e) {
            rs.put("result", "0");
            e.printStackTrace();
            return rs;
        }
        rs.put("result", "1");
        return rs;
    }

    private void insertDeptPower(String oid, String deptCode) {
        // TODO Auto-generated method stub
        DateFormat bf = new SimpleDateFormat("yyyy-MM-dd E a HH:mm:ss");
        String date = bf.format(new Date());
        if ("current".equals(Config.getCIV_CURRENT_VALUE(oid))) {
            powerDao.insertPowerConfigByDept(oid, deptCode, "Current", Config.getCIV_CURRENT_VALUE(oid), date);
        }

        if ("specialty".equals(Config.getCIV_SPECIALTY_VALUE(oid))) {
            powerDao.insertPowerConfigByDept(oid, deptCode, "Specialty", Config.getCIV_SPECIALTY_VALUE(oid), date);
        }

        if ("timeaxis".equals(Config.getCIV_TIMEAXIS_VALUE(oid))) {
            powerDao.insertPowerConfigByDept(oid, deptCode, "TimeAxis", Config.getCIV_TIMEAXIS_VALUE(oid), date);
        }
        if ("specialtytimeaxis".equals(Config.getCIV_SPECIALTYTIMEAXIS_VALUE(oid))) {
            powerDao.insertPowerConfigByDept(oid, deptCode, "SpecialtyTimeAxis", Config.getCIV_SPECIALTYTIMEAXIS_VALUE(oid),
                    date);
        }
        if ("medicalview".equals(Config.getCIV_MEDICAL_VALUE(oid))) {
            powerDao.insertPowerConfigByDept(oid, deptCode, "MedicalView", Config.getCIV_MEDICAL_VALUE(oid), date);
        }
        powerDao.insertPowerConfigByDept(oid, deptCode, "Visit", Config.getCIV_VISIT_VALUE(oid), date);
        powerDao.insertPowerConfigByDept(oid, deptCode, "Category", Config.getCIV_CATEGORY_VALUE(oid), date);
        powerDao.insertPowerConfigByDept(oid, deptCode, "Mr", Config.getCIV_EMR_VALUE(oid), date);
        powerDao.insertPowerConfigByDept(oid, deptCode, "Exam", Config.getCIV_EXAM_VALUE(oid), date);
        powerDao.insertPowerConfigByDept(oid, deptCode, "Pathology", Config.getCIV_PATHOLOGY_VALUE(oid), date);
    }

    /**
     * 获取脱敏字段配置
     *
     * @return
     */
    public List<Map<String, Object>> getInfoHiddenField(String oid, String code) {
        List<Map<String, Object>> list = powerDao.getSysHideConfig(oid, code);
        List<Map<String, Object>> result = new ArrayList<>();
        for (Map<String, Object> map : list) {
            Map<String, Object> tmpMap = new HashMap<>();
            map.put("code", map.get("config_code"));
            map.put("value", map.get("config_value"));
            map.put("name", map.get("config_name"));
            map.put("inuse", map.get("enabled"));
            map.put("orm_fileds", map.get("orm_fileds"));
            map.put("maskRuleCode", map.get("mask_rule_code"));
            map.put("maskRuleName", map.get("mask_rule_name"));
            result.add(tmpMap);
        }
        return list;
    }
    public    Page<Map<String, String>> getInfoHiddenDict(String oid, String keyWord,int pageNo,int pageSize) {
        if (pageNo == 0) {
            pageNo = 1;
        }
        if (pageSize == 0) {
            pageSize = 10;
        }
        Page<Map<String, String>> page = new Page<Map<String, String>>();
        page.setPageNo(pageNo);
        page.setPageSize(pageSize);
        List<Map<String, String>> list = powerDao.getSysHideDict(oid, keyWord);
        List<Map<String, String>> typeList = new ArrayList<Map<String, String>>();
        for (Map<String, String> map : list) {
            Map<String, String> mapRes = new HashMap<String, String>();
            mapRes.put("code", map.get("filed_code"));
            mapRes.put("name", map.get("filed_name"));
            typeList.add(mapRes);
        }
        page.setTotalCount(typeList.size());
        ListPage<Map<String, String>> listPage = new ListPage<Map<String, String>>(typeList, pageNo, pageSize);
        page.setResult(listPage.getPagedList());
        return page;
    }


    /**
     * 更新脱敏配置字段
     *
     * @return
     */
    public boolean updateSysHideConfig(String oid, String code, String value, String enabled, String fields, String maskRuleCode, String maskRuleName) {
        return powerDao.updateSysHideConfig(oid, code, value, enabled, fields, maskRuleCode, maskRuleName);
    }

    /**
     * List<Map>获取脱敏数据
     *
     * @return
     */
    public List<Map<String, String>> getInfoHidden(List<Map<String, String>> info) {
        Map<String, String> config = this.getSysConfigByType("ALL", "StartUse_HidePatKeyM");
        if ("0".equals(config.get("result"))) {
            return info;
        }
        Map<String, String> fields = new HashMap<String, String>();
        //脱敏字段配置
        List<Map<String, Object>> list = this.getInfoHiddenField("", "");
        Map<String, Object> rule = new HashMap<String, Object>();
        for (Map<String, Object> map : list) {
            Map<String, String> map1 = new HashMap<String, String>();
            map1.put("inuse", (String) map.get("inuse"));
            map1.put("pattern", (String) map.get("value"));
            String ormFields = (String) map.get("orm_fileds");
            if (StringUtils.isNotBlank(ormFields)) {
                String[] ormFieldss = ormFields.split(",");
                for (String field : ormFieldss) {
                    fields.put(field.toUpperCase(), (String) map.get("code"));
                }
            }
            rule.put((String) map.get("code"), map1);
        }

        List<Map<String, String>> hiddenList = new ArrayList<>();
        for (Map<String, String> map : info) {
            Map<String, String> tmpMap = new HashMap<>(map);
            for (String field : fields.keySet()) {
                String fieldValue = fields.get(field);
                Map<String, String> ruleMap = (Map<String, String>) rule.get(fieldValue);
                if (null != ruleMap && "1".equals(ruleMap.get("inuse"))) {
                    String pattern = ruleMap.get("pattern");
                    String value = map.get(field);

                    tmpMap.put(field, Utils.encrypt(value, pattern));

                }
            }

            hiddenList.add(tmpMap);
        }

        return hiddenList;
    }


    /**
     * List<Map>获取脱敏数据
     * 2024-09-25
     * @return
     */
    public List<Map<String, String>> getInfoHiddenByMaskRule(List<Map<String, String>> info) {
        Map<String, String> config = this.getSysConfigByType("ALL", "StartUse_HidePatKeyM");
        if ("0".equals(config.get("result"))) {
            return info;
        }

        Map<String, Map<String, Object>> fieldMap = getMaskConfigForCurrentUser();
        if(fieldMap.size()==0){
            return info;
        }

        List<Map<String, String>> hiddenList = new ArrayList<>();
        for (Map<String, String> infoMap : info) {
            Map<String, String> tmpMap = new HashMap<>(infoMap);
            for (Map.Entry<String, Map<String, Object>> entrySet : fieldMap.entrySet()) {
                String field=entrySet.getKey();
                String fieldValue=infoMap.get(field);
                Map<String, Object> ruleMap = entrySet.getValue();

                String vaule=maskFiledValue(fieldValue,ruleMap);
                tmpMap.put(field, vaule);
            }
            hiddenList.add(tmpMap);
        }
        return hiddenList;
    }


    /**
     * 获取通用配置
     *
     * @return
     */
    @Override
    public Map<String, Object> getCommonConfig() {
        Map<String, Object> res = new HashMap<String, Object>();
        Map<String, String> loginOrgInfo = organizationDao.getLoginOrgInfo();
        String oid = loginOrgInfo.get("code");
        String userCode = SecurityCommonUtil.getLoginUserCode();

        Map<String, Object> noticeMap = getNoticeConfig(oid);
        //是否启用通知
        res.put("NOTICE_SWITCH", noticeMap);

        Map<String, String> rs = getCheckAdmin(oid, userCode);
        res.put("CIV_ADMIN", rs);

        //获取菜单
        Map<String, String> pagePower = getPowerConfigByPage(oid, userCode);
        res.put("pagePower", pagePower);

        Map<String, Object> logMap = Config.getLogoInfo(oid);
        res.put("logoInfo", logMap);

        Map<String, Object> patientListSwitch = Config.getExchangePatient(oid);
        res.put("patientListConfig", patientListSwitch);
        //是否启用闭环
        String value = Config.getConfigValue("ALL", "StartUse_OrderClose");
        Map<String, String> map = new HashMap<>();
        // getSysConfigByType("StartUse_OrderClose");
        map.put("result", "true".equalsIgnoreCase(value) ? "1" : "0");
        res.put("StartUse_OrderClose", map);
        //是否隐藏医嘱相关的table操作列
        Map<String, String> result = Config.getCIV_CATEGARY_ORDER_SHOW_CONFIG("ALL");
        res.put("CIV_CATEGARY_ORDER_SHOW_CONFIG", result);

        //过敏程度过滤条件是否在页面展示
        Map<String, Object> allergyFilter = Config.getAllergyFilter("ALL");
        res.put("allergyFilter", allergyFilter);

        //获取CDSS的地址
        String tokenUrl = ConfigCache.getCache(oid,"CDSS_TOKEN_URL");
        String detailUrl = ConfigCache.getCache(oid,"CDSS_DETAIL_URL");
        Map<String, String> cdss = new HashMap<>();
        cdss.put("tokenUrl", tokenUrl);
        cdss.put("detailUrl", detailUrl);
        res.put("CDSS", cdss);

        Map<String, Object> lineChart = new HashMap<>();
        String dataLabelsSwitch = ConfigCache.getCache("all", "DATA_LABEL_SWITCH");
        lineChart.put("dataLabelsSwitch", Boolean.valueOf(dataLabelsSwitch));
        res.put("lineChart", lineChart);

        res.put("vid_show_config", Config.getVISIT_SHOW_VID_CONFIG("ALL"));
        //是否显示就诊次
        res.put("visitIdShow", Config.getCIV_HIDDEN_VISITlIST_VISITID("ALL"));

        //就诊试图 卡片默认查询时间范围
        String timeChoice = Config.getVISIT_TIME_CHOICE_CONFIG("ALL");
        res.put("timeChoice", timeChoice);

        //就诊试图 卡片默认查询时间范围
        String categoryTimeChoice = Config.getCATEGORY_TIME_CHOICE_CONFIG("ALL");
        res.put("categoryTimeChoice", categoryTimeChoice);
        return res;
    }

    @Override
    public Map<String, Object> getNoticeConfig(String oid) {
        Map<String, Object> noticeMap = new HashMap<>();
        String noticeSwitch = Config.getNoticeSwitch(oid);
        List<String> list = Config.getNoticeContent(oid);
        noticeMap.put("result", noticeSwitch);
        noticeMap.put("noticeContent", list);
        return noticeMap;
    }

    /**
     * 获取患者列表页配置
     *
     * @return
     */
    @Override
    public Map<String, Object> getPatListConfig() {
        Map<String, Object> res = new HashMap<String, Object>();


        //获取查询条件的标签和类型
        Map<String, Object> patListConf = getPatListQueryViewConfig("ALL");
        res.put("patQueryConf", patListConf);
        //获取患者列表表格的表头
        String tableHead = Config.getCIV_PATIENT_COLUMN("ALL");
        res.put("tableHead", tableHead);
        //获取点击查看跳转的目的URL页面
        String defaultPage = Config.getCIV_DEFAULT_PAGE("ALL");
        res.put("defaultPage", defaultPage);
        //获取患者列表表格的表头
        String listVisitType = Config.getCIV_PATIENT_LIST_VISIT_TYPE("ALL");
        res.put("listVisitType", listVisitType);

        return res;
    }


    /**
     * 患者列表查询条件显示配置
     *
     * @return
     */
    private Map<String, Object> getPatListQueryViewConfig(String oid) {
        Map<String, Object> confgiMap = new HashMap<String, Object>();
        Map<String, String> mapDefault = new HashMap<String, String>();
        mapDefault.put("name", "患者ID");
        mapDefault.put("type", "input");
        mapDefault.put("defaultValue", "");
        confgiMap.put("pid", mapDefault);

        mapDefault = new HashMap<String, String>();
        mapDefault.put("name", "就诊号");
        mapDefault.put("type", "input");
        mapDefault.put("defaultValue", "");
        confgiMap.put("visit_no", mapDefault);

        mapDefault = new HashMap<String, String>();
        mapDefault.put("name", "患者姓名");
        mapDefault.put("type", "input");
        mapDefault.put("defaultValue", "");
        confgiMap.put("name", mapDefault);

        mapDefault = new HashMap<String, String>();
        mapDefault.put("name", "身份证号");
        mapDefault.put("type", "input");
        mapDefault.put("defaultValue", "");
        confgiMap.put("pno", mapDefault);

        mapDefault = new HashMap<String, String>();
        mapDefault.put("name", "就诊类型");
        mapDefault.put("type", "select");
        mapDefault.put("defaultValue", "");
        confgiMap.put("visit_type", mapDefault);

        mapDefault = new HashMap<String, String>();
        mapDefault.put("name", "就诊科室");
        mapDefault.put("type", "select");
        mapDefault.put("defaultValue", "");
        confgiMap.put("visit_dept", mapDefault);

        mapDefault = new HashMap<String, String>();
        mapDefault.put("name", "就诊日期");
        mapDefault.put("type", "date");
        mapDefault.put("defaultValue", "");
        confgiMap.put("visit_date", mapDefault);

        mapDefault = new HashMap<String, String>();
        mapDefault.put("name", "出院日期");
        mapDefault.put("type", "date");
        mapDefault.put("defaultValue", "");
        confgiMap.put("outVisit_date", mapDefault);

/*		mapDefault = new HashMap<String, String>();
		mapDefault.put("name", "病区");
		mapDefault.put("type", "select");
		mapDefault.put("defaultValue", "");
		confgiMap.put("district_admission_to_name", mapDefault);*/


        String configStr = Config.getPATIENT_LIST_QUERY_VIEW(oid);
        if (StringUtils.isNotBlank(configStr)) {
            String[] configs = configStr.split(";");
            for (String viewItem : configs) {
                if (StringUtils.isNotBlank(viewItem) && viewItem.contains("=")) {
                    String[] viewItems = viewItem.split("=");
                    Map<String, String> mapOld = (Map<String, String>) confgiMap.get(viewItems[0]);
                    if (null == mapOld) {
                        mapOld = new HashMap<String, String>();
                        confgiMap.put(viewItems[0], mapOld);
                    }
                    String[] values = viewItems[1].split(",");
                    if (null == values) {
                        continue;
                    }
                    mapOld.put("name", values[0]);
                    if (values.length > 1) {
                        mapOld.put("type", values[1]);
                    }
                    if (values.length > 2) {
                        mapOld.put("defaultValue", values[2]);
                    } else {
                        mapOld.put("defaultValue", "");
                    }
                    if (values.length > 3) {
                        mapOld.put("defaultName", values[3]);
                    }
                    if (values.length > 4) {
                        /**
                         * 2024-11-14添加配置选项值
                         * 复选框 eg：yqmc=院区,select2,,,nq+南区|bq+北区|xq+西区|dq+东区;
                         */
                        String select2Value = values[4];

                        Map<String, Object> map = new HashMap<>();
                        String[] parts = select2Value.split("\\|");
                        for (String part : parts) {
                            String[] keyValue = part.split("\\+");
                            if (keyValue.length == 2) {
                                String key = keyValue[0];
                                String value = keyValue[1];
                                map.put(key, value);
                            }
                        }
                        com.alibaba.fastjson.JSONObject jsonObject = new com.alibaba.fastjson.JSONObject(map);
                        mapOld.put("value", jsonObject.toString());
                    }
                    confgiMap.put(viewItems[0], mapOld);
                    //confgiMap.put(viewItems[0], viewItems[1]);
                }
            }
        }
        return confgiMap;
    }

    /**
     * 获取当前视图配置
     *
     * @return
     */
    @Override
    public Map<String, Object> getCurrentConfig(String oid) {
        Map<String, Object> res = new HashMap<String, Object>();
        //模板配置
        String pageModule = Config.getCIV_CURRENT_CONFIG(oid);
        res.put("pageModule", pageModule);
        //全局设置
        List<SysConfig> list = getSysConfig(oid);
        res.put("sysConfig", list);
        //医嘱状态
        List<String> orderStatus = Config.getCIV_ORDERSTATUS(oid);
        res.put("orderStatus", orderStatus);
        //检验详情表格表头配置
        res.put("inspectHead", Config.getCIV_LAB_REPORT_DETAIL_HEAD(oid));
        Map<String, Object> orderConfig = new HashMap<>();
        Map<String, Object> headers = new HashMap<>();
        headers.put("jm", OrderShowConfigVo.getCurrentJmShowConfig(oid));
        headers.put("kf", OrderShowConfigVo.getCurrentKfShowConfig(oid));
        orderConfig.put("headers", headers);
        res.put("order_config", orderConfig);
        return res;
    }


    /**
     * 获取就诊试图配置
     *
     * @param userCode
     * @return
     */
    @Override
    public Map<String, Object> getVisitConfig(String oid, String userCode) {
        Map<String, Object> res = new HashMap<String, Object>();
        //检验详情表格表头配置
        res.put("inspectHead", Config.getCIV_LAB_REPORT_DETAIL_HEAD(oid));
        //就诊试图科室筛选
        String deptShow = Config.getVisitDeptSelectConfig(oid);
        res.put("deptShow", deptShow);
        //右侧菜单项
        List<Map<String, Object>> list = getPowerConfigByVisit(oid, userCode);
        res.put("visitShow", list);

        //获取医嘱状态名称列表
        res.put("orderStatus", Config.getCIV_ORDERSTATUS(oid));
        //是否显示右侧菜单数量 20241029因为目前是全局设置 故将oid改成ALL
        res.put("tabNum", getSysConfigByType("ALL", "CivVisit_CalcNum"));
        //是否有手术列表项
        res.put("operListConfig", Config.getSHOW_OPER_LIST(oid));
        //手术记录的tabs项
        res.put("operTabConfig", operService.getAnesthesiaConfig(oid));
        // 护理表格头部设置
        res.put("nurseTableHeadConfig", Config.getCIV_NURSE_TABLE_HEAD(oid));
        //费用表格头部设置
        //        List<String> feeConfigList = new ArrayList<String>();
//        feeConfigList.add(ConfigCache.getCache(oid, "FEE_TABLE_OUT_CHARGE_CONFIG"));
//        feeConfigList.add(ConfigCache.getCache(oid, "FEE_TABLE_IN_CHARGE_CONFIG"));
        List<Object> feeConfigListResult = new ArrayList<>();
        List<Map<String, Object>> feeConfigList = new ArrayList<>();
//        feeConfigList = ((List) JSONObject.parse(ConfigCache.getCache(oid, "FEE_TABLE_OUT_CHARGE_CONFIG")));
//        feeConfigList.add((List) JSONObject.parse(ConfigCache.getCache(oid, "FEE_TABLE_IN_CHARGE_CONFIG")));
        feeConfigListResult.add((List) JSONObject.parse(ConfigCache.getCache(oid, "FEE_TABLE_OUT_CHARGE_CONFIG")));
        feeConfigListResult.add((List) JSONObject.parse(ConfigCache.getCache(oid, "FEE_TABLE_IN_CHARGE_CONFIG")));
        res.put("feeTableHeadcConfig", feeConfigListResult);
//        res.put("feeTableHeadcConfig", feeConfigList);
        //临川用血表格头部设置
        List<Object> bloodHeadConfig = new ArrayList<>();
        bloodHeadConfig.add(JSONObject.parse(ConfigCache.getCache(oid, "BLOOD_HEAD_CONFIG")));//申请表头
        bloodHeadConfig.add((List) JSONObject.parse(ConfigCache.getCache(oid, "BLOOD_PREPARE_HEAD_CONFIG")));//配发表头
        bloodHeadConfig.add((List) JSONObject.parse(ConfigCache.getCache(oid, "BLOOD_BLOU_HEAD_CONFIG")));//执行表头
        bloodHeadConfig.add((List) JSONObject.parse(ConfigCache.getCache(oid, "BLOOD_BLOU_PATROL_HEAD_CONFIG")));//完成表头
        res.put("bloodHeadConfig", bloodHeadConfig);
        //检查检验是否显示超时
        String isOverTimeLab = ConfigCache.getCache(oid, "LAB_OVER_TIME");
        String isOverTimeExam = ConfigCache.getCache(oid, "EXAM_OVER_TIME");
        res.put("lab_over_time", "true".equals(isOverTimeLab) ? "1" : "0");
        res.put("exam_over_time", "true".equals(isOverTimeExam) ? "1" : "0");
        //是否使用病例章节模板
        String emrDgHtml = Config.getIS_EMR_DG_HTML(oid);
        res.put("visit_emr_dg", "true".equals(emrDgHtml) ? "1" : "0");
        //就诊试图是否显示病区
        String visitBq = Config.getIS_SHOW_VISIT_CARD_BQ(oid);
        res.put("visit_bq", "true".equals(visitBq) ? "1" : "0");
        //检查检验类一直是否显示超时
        boolean examOverTime = Config.getEXAM_OVER_TIME(oid);
        res.put("exam_over_time", examOverTime);
        boolean labOverTime = Config.getLAB_OVER_TIME(oid);
        res.put("lab_over_time", labOverTime);
        //末次就诊栏是否跟随就诊视图的变化而变化
        String scnnar_show_with_visit = Config.getSCNNAR_SHOW_WITH_VISIT(oid);
        res.put("visit_scnnar_show_config", scnnar_show_with_visit);
        //就诊试图 医嘱显示列配置
//        String orderConfig = Config.getVISIT_ORDER_ALL_SHOW_CONFIG(oid);
//        res.put("order_config", orderConfig);

        //就诊试图 医嘱显示列配置
        ResultVo<List<Map<String, Object>>> allResultVo = OrderShowConfigVo.buildAllShowConfigResultVo(oid);
        res.put("order_ALL_config", allResultVo);
        ResultVo<List<Map<String, Object>>> kfResultVo = OrderShowConfigVo.buildKfShowConfigResultVo(oid);
        res.put("order_KF_config", kfResultVo);
        ResultVo<List<Map<String, Object>>> jmResultVo = OrderShowConfigVo.buildJmShowConfigResultVo(oid);
        res.put("order_JM_config", jmResultVo);
        ResultVo<List<Map<String, Object>>> qtResultVo = OrderShowConfigVo.buildQtShowConfigResultVo(oid);
        res.put("order_QT_config", qtResultVo);
        ResultVo<List<Map<String, Object>>> labResultVo = OrderShowConfigVo.buildLabShowConfigResultVo(oid);
        res.put("order_LAB_config", labResultVo);
        ResultVo<List<Map<String, Object>>> examResultVo = OrderShowConfigVo.buildExamShowConfigResultVo(oid);
        res.put("order_EXAM_config", examResultVo);
        ResultVo<List<Map<String, Object>>> operResultVo = OrderShowConfigVo.buildOperShowConfigResultVo(oid);
        res.put("order_OPER_config", operResultVo);
        ResultVo<List<Map<String, Object>>> otherResultVo = OrderShowConfigVo.buildOtherShowConfigResultVo(oid);
        res.put("order_OTHER_config", otherResultVo);
        return res;
    }

    /**
     * 获取分类视图配置
     *
     * @param userCode
     * @return
     */
    @Override
    public Map<String, Object> getCatagoryConfig(String userCode, String patientId, String visitId) {
        Map<String, Object> res = new HashMap<String, Object>();
        res.put("inspectHead", Config.getCIV_LAB_REPORT_DETAIL_HEAD("ALL"));
        String config = Config.getSHOW_OPER_LIST("ALL");
        res.put("operListConfig", config);
        //若未配置外部url,获取手术麻醉单配置
        List<Map<String, String>> list = operService.getAnesthesiaConfig("ALL");
        res.put("operTabConfig", list);
        //护理单表头
        List<Map<String, String>> rs = Config.getCIV_NURSE_TABLE_HEAD("ALL");
        res.put("nurseHeadConfig", rs);
        //费用明细展示表头
//        List<String> feeConfigList = new ArrayList<String>();
//        feeConfigList.add(ConfigCache.getCache(oid, "FEE_TABLE_OUT_CHARGE_CONFIG"));
//        feeConfigList.add(ConfigCache.getCache(oid, "FEE_TABLE_IN_CHARGE_CONFIG"));
        List<Object> feeConfigListResult = new ArrayList<>();
        List<Map<String, Object>> feeConfigList = new ArrayList<>();
//        feeConfigList = ((List) JSONObject.parse(ConfigCache.getCache(oid, "FEE_TABLE_OUT_CHARGE_CONFIG")));
//        feeConfigList.add((List) JSONObject.parse(ConfigCache.getCache(oid, "FEE_TABLE_IN_CHARGE_CONFIG")));
        feeConfigListResult.add((List) JSONObject.parse(ConfigCache.getCache("ALL", "FEE_TABLE_OUT_CHARGE_CONFIG")));
        feeConfigListResult.add((List) JSONObject.parse(ConfigCache.getCache("ALL", "FEE_TABLE_IN_CHARGE_CONFIG")));
        res.put("feeHeadConfig", feeConfigListResult);
        //若配置外部url,获取配置的外部url
        // 医嘱显示列配置
//        String orderConfig = Config.getVISIT_ORDER_ALL_SHOW_CONFIG(oid);
//        res.put("order_config", orderConfig);
        // 医嘱显示列配置

        ResultVo<List<Map<String, Object>>> kfResultVo = OrderShowConfigVo.buildKfShowConfigResultVo("ALL");
        res.put("order_KF_config", kfResultVo);
        ResultVo<List<Map<String, Object>>> jmResultVo = OrderShowConfigVo.buildJmShowConfigResultVo("ALL");
        res.put("order_JM_config", jmResultVo);
        ResultVo<List<Map<String, Object>>> qtResultVo = OrderShowConfigVo.buildQtShowConfigResultVo("ALL");
        res.put("order_QT_config", qtResultVo);
        ResultVo<List<Map<String, Object>>> allResultVo = OrderShowConfigVo.buildAllShowConfigResultVo("ALL");
        res.put("order_ALL_config", allResultVo);

        return res;
    }

    @Override
    public String getUserName(String oid, String userCode) {
        String name = powerDao.selectUserByUserCode(oid, userCode);
        return name;
    }


    /**
     * @param configCode
     * @return
     */
    @Override
    public boolean deleteSysHideConfig(String configCode,String fields) {
        int i = powerDao.deleteSysHideConfig(configCode);
        int j = powerDao.updateSysConfigSubDict(fields,"");
        return i > 0 && j > 0;
    }

    /**
     * @param configName
     * @param configValue
     * @param fields
     * @return
     */
    @Override
    public boolean insertSysHideConfig( String configName, String configValue, String fields, String maskRuleCode, String maskRuleName) {
        int i = 0;
        int j = 0;
        i = powerDao.insertSysHideConfig(configName, configValue, fields, maskRuleCode, maskRuleName);
        j = powerDao.updateSysConfigSubDict(fields,"add");
        return i > 0 && j > 0;
    }

    @Override
    public List<Map<String, String>> getOidConfigByUserCode(String userCode) {
        List<Map<String, String>> orgResultList = new ArrayList<>();

        List<TreeNodeVo> hospitalOid = configService.getHospitalOid();
        for (TreeNodeVo treeNodeVo : hospitalOid) {
            List<NameAndCodeVo> children = treeNodeVo.getChildren();
            for (NameAndCodeVo child : children) {
                String oid = child.getCode();
                String orgName = child.getName();
                String value = "1";
                Map<String, Object> powerConfigByUser = getPowerConfigByUser(oid, userCode);
                if (powerConfigByUser.isEmpty()) {
                    value = "0";
                }
                Map<String, String> tmpMap = new HashMap<>();
                tmpMap.put("code", oid);
                tmpMap.put("name", orgName);
                tmpMap.put("value", value);
                orgResultList.add(tmpMap);
            }
        }

        return orgResultList;
    }

    @Override
    public List<Map<String, String>> getOidConfigByDeptCode(String deptCode) {
        List<Map<String, String>> orgResultList = new ArrayList<>();
        List<TreeNodeVo> hospitalOid = configService.getHospitalOid();
        for (TreeNodeVo treeNodeVo : hospitalOid) {
            List<NameAndCodeVo> children = treeNodeVo.getChildren();
            for (NameAndCodeVo child : children) {
                String oid = child.getCode();
                String orgName = child.getName();
                String value = "1";
                Map<String, Object> powerConfigByDept = getPowerConfigByDept(oid, deptCode);
                if (powerConfigByDept.isEmpty()) {
                    value = "0";
                }
                Map<String, String> tmpMap = new HashMap<>();
                tmpMap.put("code", oid);
                tmpMap.put("name", orgName);
                tmpMap.put("value", value);
                orgResultList.add(tmpMap);
            }
        }


        return orgResultList;
    }

    @Override
    public List<NameAndCodeVo> getVisitStatusDict() {
        String json = ConfigCache.getCache("ALL", "VISIT_STATUS_DICT");
        List<NameAndCodeVo> dictList = new ArrayList<>();
        try {
            List<NameAndCodeVo> vos = objectMapper.readValue(json, new TypeReference<List<NameAndCodeVo>>() {
            });
            dictList.addAll(vos);
        } catch (JsonProcessingException e) {
            throw new RuntimeException(e);
        }
        return dictList;
    }

    @Override
    public String getSingleloginUser(String oid,String jhTicket) {
        log.info("oid="+oid+";jhTicket="+jhTicket);
        String url = Config.getSingleloginUser(oid);
        log.info("url="+url);
        String userCode = "";
        if(StringUtils.isNotBlank(url)){
            if(HdrConstantEnum.HOSPITAL_JDF_SZ.getCode().equals(oid)){
                log.info(HdrConstantEnum.HOSPITAL_JDF_SZ.getCode()+"调用");
                try {
                    url = url + "&ticket="+jhTicket;
                    URL apiUrl = new URL(url);
                    HttpURLConnection connection = (HttpURLConnection) apiUrl.openConnection();
                    connection.setRequestMethod("GET");

                    int responseCode = connection.getResponseCode();
                    log.info("responseCode="+responseCode);
                    if (responseCode == HttpURLConnection.HTTP_OK) {
                        BufferedReader reader = new BufferedReader(new InputStreamReader(connection.getInputStream(),"GB2312"));
                        String inputLine;
                        StringBuffer response = new StringBuffer();
                        while ((inputLine = reader.readLine()) != null) {
                            response.append(inputLine);
                        }
                        reader.close();
                        log.info("调用结果："+response.toString());
                        if(response.toString().contains("<cas:user>")){
                            int beginIndex = response.indexOf("<cas:user>") + "<cas:user>".length();
                            int endIndex = response.indexOf("</cas:user>");
                            userCode = response.toString().substring(beginIndex,endIndex);
                            log.info("截取的userCode：" + userCode);
                        }
                    }
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }else if( HdrConstantEnum.HOSPITAL_JDF_CD.getCode().equals(oid)){
                log.info(HdrConstantEnum.HOSPITAL_JDF_CD.getCode()+"调用");
                try {
                    URL apiUrl = new URL(url);
                    HttpURLConnection con = (HttpURLConnection) apiUrl.openConnection();
                    con.setRequestMethod("POST");
                    con.setDoOutput(true);
                    OutputStream os = con.getOutputStream();
                    jhTicket = "ticket=" + jhTicket;
                    os.write(jhTicket.getBytes());
                    os.flush();
                    os.close();

                    int responseCode = con.getResponseCode();
                    log.info("responseCode="+responseCode);
                    if (responseCode == HttpURLConnection.HTTP_OK) {
                        BufferedReader ins = new BufferedReader(new InputStreamReader(con.getInputStream()));
                        String inputLine;
                        StringBuffer response = new StringBuffer();

                        while ((inputLine = ins.readLine()) != null) {
                            response.append(inputLine);
                        }
                        ins.close();
                        log.info("调用结果："+response.toString());
                        if(response.toString().contains("<cas:user>")){
                            int beginIndex = response.indexOf("<cas:user>") + "<cas:user>".length();
                            int endIndex = response.indexOf("</cas:user>");
                            userCode = response.toString().substring(beginIndex,endIndex);
                            log.info("截取的userCode：" + userCode);
                        }
                    }
                }catch (Exception e){
                    e.printStackTrace();
                }
            }//else if("H34017300026".equals(oid)){
//                try {
//                    URL apiUrl = new URL(url);
//
//                    String data = "<soapenv:Envelope xmlns:soapenv=\"http://schemas.xmlsoap.org/soap/envelope/\" xmlns:ser=\"http://service.ocl.hdr.goodwill.com/\">\n" +
//                            "   <soapenv:Header/>\n" +
//                            "   <soapenv:Body>\n" +
//                            "      <ser:operOCL>\n" +
//                            "         <oid>"+oid+"</oid><patientid>"+pid+"</patientid>\n" +
//                            "         <visitid>"+vid+"</visitid><orderno>"+orderNo+"</orderno>\n" +
//                            "      </ser:operOCL>\n" +
//                            "   </soapenv:Body>\n" +
//                            "</soapenv:Envelope>";
//
//                    userCode = wsConnection(url, data);
//
//                } catch (Exception e) {
//                    e.printStackTrace();
//                }
//            }
        }
        return userCode;
    }
    @Override
    public Map<String, String> updateVipPatientDataRight(String oid, String userCodes,String configValue,String deptCode) {
        // TODO Auto-generated method stub
        //所有院区oid
        String result = "";
        Map<String, String> map = new HashMap<String, String>();
        if(StringUtils.isNotBlank(deptCode)){
            if (!updatePowerConfigByType(oid, deptCode, "VIP", configValue, false)){
                result = result + "VIP权限设置失败。";
            }
        }else if(StringUtils.isNotBlank(userCodes)){
            String[] users = userCodes.split("/");
            for (int i = 0; i < users.length; i++) {
                List<Map<String, String>> configs = new ArrayList<Map<String, String>>();
                configs = powerDao.getVipConfig(oid, users[i], true);
                if (configs.size() == 0) {
                    String deptCodenew = powerDao.selectDeptByUser(oid, users[i]);
//				insertPowerConfigByUser(users[i], deptCode);
                    //上面一行注释掉了，原来第一次设置权限的时候是从科室里面取权限，不是页面设置的权限，导致第一次配置的权限不生效
                    //原本的设计是第一次是初始化的
                    DateFormat bf = new SimpleDateFormat("yyyy-MM-dd E a HH:mm:ss");
                    String date = bf.format(new Date());
                    if (!powerDao.insertPowerConfigByUser(oid, users[i], deptCodenew, "VIP", "0", date)){
                        result = result + "VIP权限设置失败。";
                    }

                } else {
                    if (!updatePowerConfigByType(oid, userCodes, "VIP", configValue, true)){
                        result = result + "VIP权限设置失败。";
                    }
                }
            }
        }
        if (!"".equals(result)) {
            map.put("result", "0");
            map.put("msg", result);
        } else {
            map.put("result", "1");
        }
        return map;
    }

    /**
     * 获取脱敏规则
     */
    @Override
    public  Map<String, List<Map<String, Object>>> getMaskRules(String oid) {
        Map<String, List<Map<String, Object>>> result = new HashMap<>();
        List<Map<String, Object>> list=powerDao.queryMaskRules(oid);
        for(Map<String, Object> map:list){
            if ("random".equals(map.get("configcode"))||"encryt".equals(map.get("configcode"))) {
                String paramsStr = (String) map.get("params");
                Map<String, String> paramsMap = new HashMap<>();
                if (StringUtils.isNotBlank(paramsStr)) {
                    String[] paramsPairs = paramsStr.split(";");
                    for (String pair : paramsPairs) {
                        String[] keyValue = pair.split(",");
                        if (keyValue.length == 2) {
                            paramsMap.put(keyValue[0].trim(), keyValue[1].trim());
                        }
                    }
                }
                map.put("params",paramsMap);
            }
        }
        result.put("result", list);
        return result;
    }

    @Override
    public Map<String, Object> getSysHideConfigByUser(String oid, String userCode) {
        Map<String, Object> rs = new HashMap<String, Object>();

        //获取全部已配置脱敏字段
        QueryWrapper<SysConfigSubDict> fileAllQueryWrapper = new QueryWrapper<>();
        fileAllQueryWrapper.eq("oid", oid);
        fileAllQueryWrapper.eq("is_add", "1");
        List<SysConfigSubDict> filedAllList = sysConfigSubDictMapper.selectList(fileAllQueryWrapper);

        //获取userCode配置的脱敏字段
        QueryWrapper<SysFiledUser> filedUserQueryWrapper = new QueryWrapper<>();
        filedUserQueryWrapper.eq("user_code", userCode);
        List<SysFiledUser> filedUserList = sysFiledUserMapper.selectList(filedUserQueryWrapper);

        List<String> fieldCodes=new ArrayList<>();
        if(filedUserList!=null && filedUserList.size()>0){
            fieldCodes=Arrays.asList(filedUserList.get(0).getFiledCode().split("/"));
        }

        List<Map<String, String>> filedList = new ArrayList<>();
        for (SysConfigSubDict sub : filedAllList) {
            Map<String, String> map = new HashMap<>();
            map.put("code", sub.getFiledCode());
            map.put("name", sub.getFiledName());
            if(fieldCodes.size()>0){
                boolean exists = fieldCodes.stream().anyMatch(fieldCode -> fieldCode.equals(sub.getFiledCode()));
                if (exists) {
                    map.put("value", "1");
                } else {
                    map.put("value", "0");
                }
            }else{
                map.put("value", "0");
            }
            filedList.add(map);
        }
        rs.put("result", filedList);

        return rs;
    }

    /**
     * 20241105 由于目前用户脱敏不分院区，故oid固定设置为ALL
     * @param userList eg:[{"oid":"42661889.5","userCode":"test_operator/emr"}]
     * @param dataMasking
     * @return
     */
    @Override
    public boolean insertFiledUser(String userList, String dataMasking) {
        boolean flag = true;
        ObjectMapper objectMapper = new ObjectMapper();
        try {
            JsonNode rootNode = objectMapper.readTree(userList);
            List<Map<String, String>> list = new ArrayList<>();

            Iterator<JsonNode> elements = rootNode.elements();
            while (elements.hasNext()) {
                JsonNode node = elements.next();
                Map<String, String> map = new HashMap<>();
                Iterator<Map.Entry<String, JsonNode>> fields = node.fields();
                while (fields.hasNext()) {
                    Map.Entry<String, JsonNode> field = fields.next();
                    map.put(field.getKey(), field.getValue().asText());
                }
                list.add(map);
            }

            for (Map<String, String> map : list) {
                //String oid = map.get("oid");
                String oid = "ALL";
                String userCode = map.get("userCode");
                if(StringUtils.isNotBlank(userCode)) {
                    insertFiledUser(oid, userCode, dataMasking);
                }
            }
        } catch (IOException e) {
            flag = false;
            e.printStackTrace();
        }
        return flag;
    }

    /**
     * @param oid
     * @param userCode
     * @param dataMasking
     * @return
     */
    @Override
    public boolean insertFiledUser(String oid, String userCode, String dataMasking) {
        if ("ALL".equalsIgnoreCase(userCode)) {
            List<Map<String, String>> userList = this.powerDao.getUserOrRoleList("", "", "");
            userCode = userList.stream().map(userMap -> userMap.get("usercode")).collect(Collectors.joining("/"));
        }

        boolean flag = true;
        try {
            //批量删除
            powerDao.deleteFiledByUsers(oid, Arrays.asList(userCode.split("/")));
            //按用户逐一保存映射关系
            String[] users = userCode.split("/");
            for (String user : users) {
                powerDao.insertFiledUser(oid, user, dataMasking);
            }
        } catch (Exception e) {
            flag = false;
            e.printStackTrace();
        }
        return flag;
    }

    /**
     * 获取当前用户脱敏字段配置信息
     */
    @Override
    public Map<String, Map<String, Object>> getMaskConfigForCurrentUser(){
        Map<String, Map<String, Object>> fieldMap = new HashMap<>();

        //获取当前用户配置的脱敏字段
        UserEntity user = SecurityCommonUtil.getCurrentLoginUser();
        QueryWrapper<SysFiledUser> wrapper = new QueryWrapper<>();
        wrapper.eq("user_code", user.getUsercode());
        List<SysFiledUser> filedUserList = sysFiledUserMapper.selectList(wrapper);
        if(filedUserList.size()==0){
            return fieldMap;
        }
        List<String> fieldCodeList=new ArrayList<>();
        if(filedUserList!=null && filedUserList.size()>0){
            SysFiledUser entity=filedUserList.get(0);
            fieldCodeList=Arrays.asList(entity.getFiledCode().split("/"));
        }

        //筛选全部有效脱敏字段配置
        List<Map<String, Object>> list = this.getInfoHiddenField("", "");
        list=list.stream().filter(map -> map.containsKey("inuse") && "1".equals(map.get("inuse").toString())).collect(Collectors.toList());

        //获取当前用户脱敏字段配置信息
        for (Map<String, Object> map : list) {
            String[] fieldsArray = map.get("orm_fileds").toString().split(",");
            Set<String> fieldsSet = Arrays.stream(fieldsArray).map(String::trim).collect(Collectors.toSet());
            fieldCodeList.stream()
                    .filter(item -> fieldsSet.contains(String.valueOf(item).trim()))
                    .forEach(item -> {
                        fieldMap.put(String.valueOf(item).trim(), map);
                    });
        }
        return fieldMap;
    }

    /**
     * 根据脱敏配置 进行字段脱敏
     */
    @Override
    public String maskFiledValue(String fieldValue,Map<String, Object> ruleMap){
        String vaule="";
        if (StringUtils.isNotBlank(fieldValue)) {
            String maskRuleCode = String.valueOf(ruleMap.get("maskRuleCode"));
            String ruleValue = String.valueOf(ruleMap.get("value"));

            if ("random".equals(maskRuleCode)) {
                vaule=Utils.getRandomByType(fieldValue.length(), StringUtils.isBlank(ruleValue) ? "alpnum" : ruleValue);
            } else if ("fixdata".equals(maskRuleCode)) {
                vaule=ruleValue;
            } else if ("encryt".equals(maskRuleCode)) {
                if ("Des".equals(ruleValue)) {
                    try {
                        vaule = encryptService.DesEncrypt(ruleValue);
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                } else if ("Base64".equals(ruleValue)) {
                    vaule = encryptService.EhrBase64Encode(ruleValue);
                } else if ("Md5".equals(ruleValue)) {
                    vaule = encryptService.gpMd5Encrypt(ruleValue);
                } else {
                    vaule = encryptService.gpMd5Encrypt(ruleValue);
                }
            } else {
                //否则按老方式无效化脱敏，即*代替
                vaule= Utils.encrypt(fieldValue, ruleValue);
            }
        }
        return vaule;
    }

    /**
     * 20250116 第三方单点登录对接调用接口
     *
     * @param oid
     * @param param
     * @return
     */
    @Override
    public String getSingleloginUserByOther(String oid, String param) {
        String result = "";
        String ssoConfig = ConfigCache.getCache(oid, "SSO_CONFIG");
        String url = ssoConfig + "?" + param;
        log.info("url====：" + url);
        String s = WsUtil.requestByGet(url);

        String returnConfig = ConfigCache.getCache(oid, "SSO_RETURN_FORMAT_CONFIG");
        if (StringUtils.isBlank(returnConfig)) {
            System.out.printf("请配置单点登录用户名获取方式【SSO_RETURN_FORMAT_CONFIG】，值示例：如果返回值格式为{\"result\":\"{\"userCode\":\"admin\"}\"},则配置result:userCode;如果返回值格式为{\"userCode\":\"admin\"}\"},则配置userCode。");
            return result;
        }

        JSONObject jsonObject = JSONObject.parseObject(s);
        if (jsonObject.getIntValue("code") == 200) {
            String[] arr = returnConfig.split(":");
            int len = arr.length;
            if (len == 1) {
                result = jsonObject.get(arr[0]).toString();
            } else {
                for (int i = 0; i < len; i++) {
                    if (i < len - 1) {
                        jsonObject = jsonObject.getJSONObject("result");
                    } else {
                        result = jsonObject.get(arr[len - 1]).toString();
                    }
                }
            }
        }
        log.info("reponseCode=" + jsonObject.getIntValue("code") + ";result=" + result);
        return result;
    }

}
