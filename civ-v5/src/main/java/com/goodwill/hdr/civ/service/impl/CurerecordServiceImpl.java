package com.goodwill.hdr.civ.service.impl;


import com.goodwill.hdr.civ.enums.HdrTableEnum;
import com.goodwill.hdr.civ.service.CurerecordService;
import com.goodwill.hdr.core.orm.MatchType;
import com.goodwill.hdr.core.orm.Page;
import com.goodwill.hdr.core.utils.ApplicationException;
import com.goodwill.hdr.hbase.bo.PropertyFilter;
import com.goodwill.hdr.hbase.dto.responseVo.PageResultVo;
import com.goodwill.hdr.hbase.dto.responseVo.ResultVo;
import com.goodwill.hdr.hbaseQueryClient.builder.PageRequestBuilder;
import com.goodwill.hdr.hbaseQueryClient.service.HbaseQueryClient;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * 治疗记录serviceimplements
 *
 * <AUTHOR>
 */
@Service
public class CurerecordServiceImpl implements CurerecordService {
    protected Logger logger = LoggerFactory.getLogger(getClass());
    private final HbaseQueryClient hbaseQueryClient;

    public CurerecordServiceImpl(HbaseQueryClient hbaseQueryClient) {
        this.hbaseQueryClient = hbaseQueryClient;
    }

    @Override
    public Page<Map<String, String>> getPageView(String oid, String patientId,String visitId,
                                                 String orderBy, String orderDir, int pageNo, int pageSize) {
        if (StringUtils.isBlank(orderBy)) {
            orderBy = "BDATE";
        }
        if (StringUtils.isBlank(orderDir)) {
            orderDir = "desc";
        }
        if (pageNo == 0) {
            pageNo = 1;
        }
        if (pageSize == 0) {
            pageSize = 10;
        }
        Page<Map<String, String>> page = new Page<Map<String, String>>();
        page.setPageNo(pageNo);
        page.setPageSize(pageSize);
        page.setOrderBy(orderBy);
        page.setOrderDir(orderDir);
        List<PropertyFilter> Filters = new ArrayList<PropertyFilter>();
        List<Map<String, String>> list = new ArrayList<Map<String, String>>();
        try {
            ResultVo<PageResultVo<Map<String, String>>> resultVo = hbaseQueryClient.getPageByCondition(
                    PageRequestBuilder.init()
                            .tableName(HdrTableEnum.HDR_TWFUN_CUREMST.getCode())
                            .patientId(patientId)
                            .oid(oid)
                            .visitId(visitId)
                            .visitTypeCode("")
                            .filters(Filters)
                            .pageNo(0)
                            .pageSize(0)
                            .orderBy("")
                            .desc()
                            .column()
                            .build());
            if(resultVo.isSuccess()){
                list = resultVo.getContent().getResult();
            }
//            list = hbaseDao.findConditionByPatient(
//                    HdrTableEnum.HDR_TWFUN_CUREMST.getCode(), oid, patientId,
//                    Filters);
            if (list.size() == 0) {
                return page;
            }
        } catch (Exception e) {
            logger.error("查询Hbase数据库有误。 ", e);
            throw new ApplicationException("查询病理报表出错。" + e.getCause());
        }
        // 按照BDATE降序
        list = sortMapList(list, "BDATE");
        // 下面是分页方法
        List<Map<String, String>> result = new ArrayList<Map<String, String>>();
        int i = 1;
        for (Map<String, String> item : list) {
            if (i > (page.getPageNo() - 1) * page.getPageSize()
                    && i <= page.getPageNo() * page.getPageSize()) {
                result.add(item);
            }
            i++;
        }
        page.setTotalCount(i - 1);
        page.setResult(result);
        page = getResultPage(page, 1);
        return page;
    }

    @Override
    public Page<Map<String, String>> getPageViewForDetail(String oid, String patientId,String visitId,
                                                          String orderCode, String orderBy, String orderDir, int pageNo,
                                                          int pageSize) {
        if (StringUtils.isEmpty(orderBy)) {
            orderBy = "ACTDATE";
        }
        if (StringUtils.isEmpty(orderDir)) {
            orderDir = "asc";
        }
        if (pageNo == 0) {
            pageNo = 1;
        }
        if (pageSize == 0) {
            pageSize = 4;
        }
        Page<Map<String, String>> page = new Page<Map<String, String>>();
        page.setPageNo(pageNo);
        page.setPageSize(pageSize);
        page.setOrderBy(orderBy);
        page.setOrderDir(orderDir);
        List<PropertyFilter> Filters = new ArrayList<PropertyFilter>();
        Filters.add(new PropertyFilter("ORDER_CODE", MatchType.EQ
                .getOperation(), orderCode));
        Filters.add(new PropertyFilter("GBACT", MatchType.EQ
                .getOperation(), "*"));
        List<Map<String, String>> list = new ArrayList<Map<String, String>>();
        ResultVo<PageResultVo<Map<String, String>>> resultVo = hbaseQueryClient.getPageByCondition(
                PageRequestBuilder.init()
                        .tableName(HdrTableEnum.HDR_TWFUN_CURESUB.getCode())
                        .patientId(patientId)
                        .oid(oid)
                        .visitId(visitId)
                        .visitTypeCode("")
                        .filters(Filters)
                        .pageNo(0)
                        .pageSize(0)
                        .orderBy("")
                        .desc()
                        .column()
                        .build());
        if(resultVo.isSuccess()){
            list = resultVo.getContent().getResult();
        }
//        list = hbaseDao.findConditionByPatient(
//                HdrTableEnum.HDR_TWFUN_CURESUB.getCode(), oid, patientId, Filters);
        // 按照BDATE降序
        list = sortMapList(list, "ACTDATE");
        // 下面是分页方法
        List<Map<String, String>> result = new ArrayList<Map<String, String>>();
        int i = 1;
        for (Map<String, String> item : list) {
            if (i > (page.getPageNo() - 1) * page.getPageSize()
                    && i <= page.getPageNo() * page.getPageSize()) {
                result.add(item);
            }
            i++;
        }
        page.setTotalCount(i - 1);
        page.setResult(result);
        page = getResultPage(page, 2);
        return page;
    }

    @Override
    public Page<Map<String, String>> getHDReportPageView(
            Map<String, String> queryMap, String outPatientId, String orderBy, String orderDir,
            int pageNo, int pageSize) {
        if (StringUtils.isEmpty(orderBy)) {
            orderBy = "DIALYSIS_DATE";
        }
        if (StringUtils.isEmpty(orderDir)) {
            orderDir = "desc";
        }
        if (pageNo == 0) {
            pageNo = 1;
        }
        if (pageSize == 0) {
            pageSize = 10;
        }
        Page<Map<String, String>> page = new Page<Map<String, String>>();
        page.setPageNo(pageNo);
        page.setPageSize(pageSize);
        page.setOrderBy(orderBy);
        page.setOrderDir(orderDir);

        String patientId = (String) queryMap.get("patient_id");
        String visitType = (String) queryMap.get("visit_type");
        String visitOrder = (String) queryMap.get("visit_order");
        String oid = (String) queryMap.get("oid");

        List<PropertyFilter> Filters = new ArrayList<PropertyFilter>();
//        if (StringUtils.isNotBlank(visitOrder)) {
//            PropertyFilter filter1 = null;
//            filter1 = new PropertyFilter();
//            filter1.setMatchType("=");
//            filter1.setPropertyName("VISIT_ID");
//            filter1.setPropertyValue(visitOrder);
//            Filters.add(filter1);
//        }
        if (StringUtils.isNotBlank(visitType)) {
            PropertyFilter filter1 = new PropertyFilter();
            visitType = "INPV".equals(visitType) ? "02" : "01";
            filter1.setMatchType("=");
            filter1.setPropertyName("VISIT_TYPE_CODE");
            filter1.setPropertyValue(visitType);
            Filters.add(filter1);
        }
        List<Map<String, String>> list = new ArrayList<Map<String, String>>();
        try {
            ResultVo<PageResultVo<Map<String, String>>> resultVo = hbaseQueryClient.getPageByCondition(
                    PageRequestBuilder.init()
                            .tableName(HdrTableEnum.HDR_HEMODIALYSIS.getCode())
                            .patientId(patientId)
                            .oid(oid)
                            .visitId(visitOrder)
                            .visitTypeCode(visitType)
                            .filters(Filters)
                            .pageNo(0)
                            .pageSize(0)
                            .orderBy("")
                            .desc()
                            .column("REPORT_TIME", "DIALYSIS_MODE", "DIALYSIS_RESULT", "REPORT_DOCTOR_NAME", "REPORT_NURSE_NAME", "PACS_URL", "ORG_NO", "ORG_NAME")
                            .build());
            if(resultVo.isSuccess()){
                list = resultVo.getContent().getResult();
            }
//            list = hbaseDao.findConditionByPatient(
//                    HdrTableEnum.HDR_HEMODIALYSIS.getCode(), oid, patientId,
//                    Filters, new String[]{"REPORT_TIME", "DIALYSIS_MODE", "DIALYSIS_RESULT", "REPORT_DOCTOR_NAME", "REPORT_NURSE_NAME", "PACS_URL", "ORG_NO", "ORG_NAME"});
            if (list.size() == 0) {
                return page;
            }
        } catch (Exception e) {
            logger.error("查询Hbase数据库有误。 ", e);
            throw new ApplicationException("查询表出错。" + e.getCause());
        }
        if (StringUtils.isNotBlank(outPatientId)) {
            String[] outPatInfo = outPatientId.split(",");
            for (String pid_vids : outPatInfo) {
                String[] pid_vid = pid_vids.split("\\|");
                if (!patientId.equals(pid_vid[1]) && !visitType.equals(pid_vid[0])) {
                    List<PropertyFilter> filters2 = new ArrayList<PropertyFilter>();
                    if (StringUtils.isNotBlank(pid_vid[0])) {
                        String visit_type = "INPV".equals(pid_vid[0]) ? "02" : "01";
                        PropertyFilter filter2 = new PropertyFilter();
                        filter2.setMatchType("=");
                        filter2.setPropertyName("VISIT_TYPE_CODE");
                        filter2.setPropertyValue(visit_type);
                        filters2.add(filter2);
                    }
                    List<Map<String, String>> otherList = new ArrayList<>();
//                    List<Map<String, String>> otherList = hbaseDao.findConditionByPatient(
//                            HdrTableEnum.HDR_HEMODIALYSIS.getCode(), pid_vid[2], pid_vid[1],
//                            filters2, new String[]{"REPORT_TIME", "DIALYSIS_MODE", "DIALYSIS_RESULT", "REPORT_DOCTOR_NAME", "REPORT_NURSE_NAME", "PACS_URL", "ORG_NO", "ORG_NAME"});
                    ResultVo<PageResultVo<Map<String, String>>> resultVo = hbaseQueryClient.getPageByCondition(
                            PageRequestBuilder.init()
                                    .tableName(HdrTableEnum.HDR_HEMODIALYSIS.getCode())
                                    .patientId(patientId)
                                    .oid(oid)
                                    .visitId(pid_vid[3])
                                    .visitTypeCode(visitType)
                                    .filters(Filters)
                                    .pageNo(0)
                                    .pageSize(0)
                                    .orderBy("")
                                    .desc()
                                    .column("REPORT_TIME", "DIALYSIS_MODE", "DIALYSIS_RESULT", "REPORT_DOCTOR_NAME", "REPORT_NURSE_NAME", "PACS_URL", "ORG_NO", "ORG_NAME")
                                    .build());
                    if(resultVo.isSuccess()){
                        otherList = resultVo.getContent().getResult();
                    }
                    if (list.size() != 0) {
                        list.addAll(otherList);
                    }
                }
            }
        }

        // 按照BDATE降序
        list = sortMapList(list, "DIALYSIS_DATE");
        // 下面是分页方法
        List<Map<String, String>> result = new ArrayList<Map<String, String>>();
        int i = 1;
        for (Map<String, String> item : list) {
            if (i > (page.getPageNo() - 1) * page.getPageSize()
                    && i <= page.getPageNo() * page.getPageSize()) {
                result.add(item);
            }
            i++;
        }
        page.setTotalCount(i - 1);
        page.setResult(result);
        page = getResultPage(page, 3);
        return page;
    }

    @Override
    public void getHDReportNum( Map<String, Object> resultMap, String outPatientId) {
        int num = 0;


        if (StringUtils.isNotBlank(outPatientId)) {
            String[] pats = outPatientId.split(",");
            for (int i = 0; i < pats.length; i++) {
                if (StringUtils.isNotBlank(pats[i])) {
                    String[] pat = pats[i].split("\\|");
                    num = num + getReportsByPat(pat[2], pat[1], pat[0], pat[3]);
                }
            }
        }
        resultMap.put("num", num);
    }

    /**
     * @param patientId 患者编号
     * @return 返回类型：int
     * @Description 方法描述: 根据患者ID查询血透报告总数
     */
    @Override
    public int getReportsByPat(String oid, String patientId, String visitType, String visitId) {
        List<PropertyFilter> filters = new ArrayList<PropertyFilter>();
        filters.add(new PropertyFilter("VISIT_TYPE_CODE",  MatchType.EQ.getOperation(), visitType));
//        if (StringUtils.isNotBlank(visitId)) {
//            filters.add(new PropertyFilter("VISIT_ID", "STRING", MatchType.EQ.getOperation(), visitId));
//        }
        List<Map<String, String>> outpage = new ArrayList<>();
        ResultVo<PageResultVo<Map<String, String>>> resultVo = hbaseQueryClient.getPageByCondition(
                PageRequestBuilder.init()
                        .tableName(HdrTableEnum.HDR_HEMODIALYSIS.getCode())
                        .patientId(patientId)
                        .oid(oid)
                        .visitId(visitId)
                        .visitTypeCode(visitType)
                        .filters(filters)
                        .pageNo(0)
                        .pageSize(0)
                        .orderBy("")
                        .desc()
                        .column("REPORT_TIME", "DIALYSIS_MODE", "DIALYSIS_RESULT", "REPORT_DOCTOR_NAME", "REPORT_NURSE_NAME", "PACS_URL")
                        .build());
        if(resultVo.isSuccess()){
            outpage = resultVo.getContent().getResult();
        }
//        List<Map<String, String>> outpage = hbaseDao.findConditionByPatient(HdrTableEnum.HDR_HEMODIALYSIS.getCode(), oid,
//                patientId, filters, "REPORT_TIME", "DIALYSIS_MODE", "DIALYSIS_RESULT", "REPORT_DOCTOR_NAME", "REPORT_NURSE_NAME", "PACS_URL");
        return outpage.size();
    }


    @Override
    public Map<String, String> getHDReportDetails(String code) {
        return null;
    }

    /**
     * 中间对应map jsp页面需要的key为key 数据库中查询到的列名为value
     *
     * @return
     */
    private Map<String, String> mapMapRelation(int flag) {
        Map<String, String> resultMap = new HashMap<String, String>();
        if (flag == 1) {
            // 医嘱时间
            resultMap.put("bddate", "BDATE");
            // 医生名称
            resultMap.put("dr_name", "DR_NAME");
            // 医嘱项目
            resultMap.put("order_name", "ORDER_NAME");
            // 总次数
            resultMap.put("exam_item_name", "QTY");
            // 天数
            resultMap.put("nal", "NAL");
            // 结束标记
            resultMap.put("gbend", "GBEND");
            // 备注
            resultMap.put("remark", "REMARK");
            // 医嘱编码
            resultMap.put("ordercode", "ORDER_CODE");
        } else if (flag == 2) {
            // 操作日期
            resultMap.put("actdate", "ACTDATE");
            // 操作人
            resultMap.put("use_name", "USE_NAME");
            // 用法
            resultMap.put("dos_name", "DOS_NAME");
            // 单次数量
            resultMap.put("realqty", "REALQTY");
            // 日次数
            resultMap.put("gbdiv", "GBDIV");
            // 备注
            resultMap.put("remark", "REMARK");
        } else if (flag == 3) {
            // 主键
            resultMap.put("code", "ROWKEY");
            // 透析模式
            resultMap.put("dialysis_mode", "DIALYSIS_MODE");
            // 血透日期
            resultMap.put("report_time", "REPORT_TIME");
            // 透析情况
            resultMap.put("dialysis_result", "DIALYSIS_RESULT");
            // 制定处方医生
            resultMap.put("report_doctor_name", "REPORT_DOCTOR_NAME");
            // 确认处方护士
            resultMap.put("report_nurse_name", "REPORT_NURSE_NAME");
            // PDF地址
            resultMap.put("pacs_url", "PACS_URL");
            //医疗机构标识
            resultMap.put("orgNo", "ORG_NO");
            //医疗机构名称
            resultMap.put("orgName", "ORG_NAME");
        }
        return resultMap;
    }

    /**
     * 将Page中，数据库字段与jsp页面的key值对应
     *
     * @param page
     * @return
     */
    private Page<Map<String, String>> getResultPage(
            Page<Map<String, String>> page, int flag) {
        List<Map<String, String>> initList = page.getResult();
        if (initList == null || initList.size() == 0) {
            return page;
        }
        // 存放page中的result
        List<Map<String, String>> dataResultList = new ArrayList<Map<String, String>>();
        // 中间key-value对应map
        Map<String, String> relaMap = mapMapRelation(flag);
        // 取得页面所需key值的set集合
        Set<String> relaKeySet = relaMap.keySet();
        for (Map<String, String> dataMap : initList) {
            // 存放jsp页面所需的key值和value值
            Map<String, String> resultMap = new HashMap<String, String>();
            for (String relaKey : relaKeySet) {
                String relaValue = relaMap.get(relaKey);
                String newValue = dataMap.get(relaValue);
//                if (relaValue.equals("BDATE")) {
//                    newValue = newValue.substring(0, 10);
//                }
                resultMap.put(relaKey, newValue);
            }
            dataResultList.add(resultMap);
        }
        page.setResult(dataResultList);

        return page;
    }

    /**
     * @param list
     * @return
     * @Description 方法描述:对一系列Map按照时间进行升序
     */
    public static List<Map<String, String>> sortMapList(
            List<Map<String, String>> list, final String sortBy) {
        List<Map<String, String>> result = new LinkedList<Map<String, String>>(
                list);
        if (list.size() > 1) {
            Collections.sort(result, new Comparator<Map<String, String>>() {
                @Override
                public int compare(Map<String, String> o1,
                                   Map<String, String> o2) {
                    String s1 = o2.get(sortBy);
                    String s2 = o1.get(sortBy);
                    if (StringUtils.isBlank(s1)) {
                        s1 = "1900-01-01";
                    }
                    if (StringUtils.isBlank(s2)) {
                        s2 = "1900-01-01";
                    }
                    return (s1).compareTo(s2);
                }
            });
        }
        return result;
    }

}
