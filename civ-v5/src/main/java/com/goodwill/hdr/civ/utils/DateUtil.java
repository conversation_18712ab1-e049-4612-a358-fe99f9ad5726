package com.goodwill.hdr.civ.utils;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Calendar;
import java.util.Date;

/**
 * <AUTHOR>
 * @Description 类描述：
 * @modify 修改记录：
 */
public class DateUtil {



    /**
     * 获取年月日时间
     *
     * @return
     */
    public static String getDateStringByTime(String time) {
        if (null == time) {
            return "";
        }
        return time.substring(0, 10);
    }

    /**


    /**
     * 获取传入时间的分钟
     *
     * @param date
     * @return
     */
    public static int getMinute(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        return calendar.get(Calendar.MINUTE);
    }

    /**
     * 获取当前时间 yyyy-MM-dd HH:mm:ss
     * @return
     */
    public static String getCurrentTime() {
        LocalDateTime now = LocalDateTime.now();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        String formattedDate = now.format(formatter);
        return formattedDate;
    }

    /**
     * 获取当前时间 yyyy-MM-dd HH:mm:ss.SSS
     * @return
     */
    public static String getCurrentTime2MS() {
        LocalDateTime now = LocalDateTime.now();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss.SSS");
        String formattedDate = now.format(formatter);
        return formattedDate;
    }

}
