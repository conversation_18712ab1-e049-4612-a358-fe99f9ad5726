package com.goodwill.hdr.civ.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.io.Serializable;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2022-03-03
 */
@TableName("civ_sys_config")
public class SysConfig implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 设置名称
     */
    @TableField("configname")
    private String configName;

    /**
     * 设置编码
     */
    @TableField("configcode")
    private String configCode;

    /**
     * 值
     */
    @TableField("configvalue")
    private String configValue;

    /**
     * 值类型
     */
    @TableField("valuetype")
    private String valueType;

    /**
     * 值域范围
     */
    @TableField("valuerange")
    private String valueRange;

    /**
     * 说明
     */
    @TableField("configdesc")
    private String configDesc;

    /**
     * 排序
     */
    private Integer sort;

    /**
     * 最后修改时间
     */
    @TableField("lastupdatetime")
    private String lastUpdateTime;

    private String oid;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }


    public String getOid() {
        return oid;
    }

    public void setOid(String oid) {
        this.oid = oid;
    }

    public String getConfigName() {
        return configName;
    }

    public void setConfigName(String configName) {
        this.configName = configName;
    }

    public String getConfigCode() {
        return configCode;
    }

    public void setConfigCode(String configCode) {
        this.configCode = configCode;
    }

    public String getConfigValue() {
        return configValue;
    }

    public void setConfigValue(String configValue) {
        this.configValue = configValue;
    }

    public String getValueType() {
        return valueType;
    }

    public void setValueType(String valueType) {
        this.valueType = valueType;
    }

    public String getValueRange() {
        return valueRange;
    }

    public void setValueRange(String valueRange) {
        this.valueRange = valueRange;
    }

    public String getConfigDesc() {
        return configDesc;
    }

    public void setConfigDesc(String configDesc) {
        this.configDesc = configDesc;
    }

    public Integer getSort() {
        return sort;
    }

    public void setSort(Integer sort) {
        this.sort = sort;
    }

    public String getLastUpdateTime() {
        return lastUpdateTime;
    }

    public void setLastUpdateTime(String lastUpdateTime) {
        this.lastUpdateTime = lastUpdateTime;
    }

    @Override
    public String toString() {
        return "SysConfig{" +
                "id=" + id +
                ", configName='" + configName + '\'' +
                ", configCode='" + configCode + '\'' +
                ", configValue='" + configValue + '\'' +
                ", valueType='" + valueType + '\'' +
                ", valueRange='" + valueRange + '\'' +
                ", configDesc='" + configDesc + '\'' +
                ", sort=" + sort +
                ", lastUpdateTime='" + lastUpdateTime + '\'' +
                ", oid='" + oid + '\'' +
                '}';
    }
}
