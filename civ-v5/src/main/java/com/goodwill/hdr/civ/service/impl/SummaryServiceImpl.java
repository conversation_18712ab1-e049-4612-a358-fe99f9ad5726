package com.goodwill.hdr.civ.service.impl;


import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.goodwill.hdr.civ.config.Config;
import com.goodwill.hdr.civ.config.ConfigCache;
import com.goodwill.hdr.civ.enums.HdrConstantEnum;
import com.goodwill.hdr.civ.enums.HdrTableEnum;
import com.goodwill.hdr.civ.service.PowerService;
import com.goodwill.hdr.civ.service.SummaryService;
import com.goodwill.hdr.civ.utils.ColumnUtil;
import com.goodwill.hdr.civ.utils.ListPage;
import com.goodwill.hdr.civ.utils.Utils;
import com.goodwill.hdr.core.orm.MatchType;
import com.goodwill.hdr.core.orm.Page;
import com.goodwill.hdr.hbase.bo.PropertyFilter;
import com.goodwill.hdr.hbase.dto.responseVo.PageResultVo;
import com.goodwill.hdr.hbase.dto.responseVo.ResultVo;
import com.goodwill.hdr.hbaseQueryClient.builder.PageRequestBuilder;
import com.goodwill.hdr.hbaseQueryClient.service.HbaseQueryClient;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.text.DecimalFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.Duration;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Service
public class SummaryServiceImpl implements SummaryService {


    private static final Logger log = LoggerFactory.getLogger(SummaryServiceImpl.class);
    private final PowerService powerService;
    private final HbaseQueryClient hbaseQueryClient;
    private final ObjectMapper objectMapper;

    public SummaryServiceImpl(PowerService powerService, HbaseQueryClient hbaseQueryClient, ObjectMapper objectMapper) {
        this.powerService = powerService;
        this.hbaseQueryClient = hbaseQueryClient;
        this.objectMapper = objectMapper;
    }

    @Override
    public List<String> getInpSummary(String oid, String patId, String visitId) {
        //封装数据
        List<String> result = new ArrayList<String>();
        List<Map<String, String>> fieldslist = Config.getSummaryFields(oid);
        //页面查询条件
        List<PropertyFilter> filters = new ArrayList<PropertyFilter>();
        String[] fields = new String[fieldslist.size()];
        int count = 0;
        for (Map<String, String> map : fieldslist) {
            if (!"-".equals(map.get("code"))) {
                fields[count] = map.get("code");
            } else {
                fields[count] = "";
            }
            count++;
        }
        List<Map<String, String>> list = new ArrayList<>();
        ResultVo<PageResultVo<Map<String, String>>> resultVo = hbaseQueryClient.getPageByCondition(
                PageRequestBuilder.init()
                        .tableName(HdrTableEnum.HDR_INP_SUMMARY.getCode())
                        .patientId(patId)
                        .oid(oid)
                        .visitId(visitId)
                        .visitTypeCode("")
                        .filters(filters)
                        .pageNo(0)
                        .pageSize(0)
                        .orderBy("")
                        .desc()
                        .column(fields)
                        .build());
        if (resultVo.isSuccess()) {
            list = resultVo.getContent().getResult();
        }
//        List<Map<String, String>> list = hbaseDao.findConditionByPatientVisitId(HdrTableEnum.HDR_INP_SUMMARY.getCode(),oid,
//                patId, visitId, filters, fields);
        List<Map<String, String>> infoHidden = powerService.getInfoHidden(list);
        //List<Map<String, String>> infoHidden = powerService.getInfoHiddenByMaskRule(list);
        //病案首页信息
        //未找到数据，中断执行
        Map<String, String> map = new HashMap<String, String>();
        if (!infoHidden.isEmpty()) {
            map = infoHidden.get(0);
        }
        //信息脱敏

        for (Map<String, String> field : fieldslist) {
            if ("ORG_NAME".equalsIgnoreCase(field.get("code"))) {
                result.add(field.get("name"));
                result.add(Config.getConfigValue(oid, "org_name"));
                continue;
            }
            if ("ORG_OID".equalsIgnoreCase(field.get("code"))) {
                result.add(field.get("name"));
                result.add(Config.getConfigValue(oid, "org_oid"));
                continue;
            }
            if ("-".equals(field.get("code"))) {
                result.add("-");
                result.add("");
                continue;
            }
            if ("IN_HOSPITAL_DAYS".equals(field.get("code"))) {
                String days = "";
                DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
                if (StringUtils.isNotBlank(map.get(field.get("code")))) {
                    days = map.get(field.get("code"));
                } else {
                    String admissionTimeString = map.get("ADMISSION_TIME");
                    if (StringUtils.isBlank(admissionTimeString)) {
                        continue;
                    }
                    LocalDateTime admissionTime = LocalDateTime.parse(admissionTimeString, dateTimeFormatter);
                    //String dischargeTime = map.get("DISCHARGE_TIME");
                    LocalDateTime dischargeTimeDate;
                    if (StringUtils.isNotBlank(map.get("DISCHARGE_TIME"))) {
                        dischargeTimeDate = LocalDateTime.parse(map.get("DISCHARGE_TIME"), dateTimeFormatter);
                    }
                    else{
                        dischargeTimeDate = LocalDateTime.now();
                    }

                    days = Duration.between(admissionTime, dischargeTimeDate).toDays() + "";

                }
                result.add(field.get("name"));
                result.add(days);
                continue;
            }
            if ("AUTOPSY_INDICATOR".equals(field.get("code"))) {
                result.add(field.get("name"));
                String autopsy = map.get("AUTOPSY_INDICATOR");
                if ("true".equals(autopsy)) {
                    result.add("是");
                } else if ("false".equals(autopsy)) {
                    result.add("否");
                } else {
                    result.add(map.get("AUTOPSY_INDICATOR"));
                }
                continue;
            }
//            if ("AUTOPSY_INDICATOR".equals(field.get("code"))) {
//                String autopsy = map.get("AUTOPSY_INDICATOR");
//                result.add(autopsy != null ? (Boolean.parseBoolean(autopsy) ? "是" : "否") : autopsy);
//                continue;
//            }
            result.add(field.get("name"));
            result.add(StringUtils.isNotBlank(map.get(field.get("code"))) ? map.get(field.get("code")) : "");
        }
        return result;
    }

    @Override
    public List<String> getInpSummaryBefore(String oid, String patId, String visitId) {
        //封装数据
        List<String> result = new ArrayList<String>();
        List<Map<String, String>> fieldslist = Config.getSummaryFields(oid);
        //页面查询条件
        List<PropertyFilter> filters = new ArrayList<PropertyFilter>();
        String[] fields = new String[fieldslist.size()];
        int count = 0;
        for (Map<String, String> map : fieldslist) {
            if (!"-".equals(map.get("code"))) {
                fields[count] = map.get("code");
            } else {
                fields[count] = "";
            }
            count++;
        }
        List<Map<String, String>> list = new ArrayList<>();
        ResultVo<PageResultVo<Map<String, String>>> resultVo = hbaseQueryClient.getPageByCondition(
                PageRequestBuilder.init()
                        .tableName("HDR_INP_SUMMARY_BEFORE")
                        .patientId(patId)
                        .oid(oid)
                        .visitId(visitId)
                        .visitTypeCode("")
                        .filters(filters)
                        .pageNo(0)
                        .pageSize(0)
                        .orderBy("")
                        .desc()
                        .column(fields)
                        .build());
        if (resultVo.isSuccess()) {
            list = resultVo.getContent().getResult();
        }
//        List<Map<String, String>> list = hbaseDao.findConditionByPatientVisitId(HdrTableEnum.HDR_INP_SUMMARY.getCode(),oid,
//                patId, visitId, filters, fields);
        List<Map<String, String>> infoHidden = powerService.getInfoHidden(list);
        //病案首页信息
        //未找到数据，中断执行
        Map<String, String> map = new HashMap<String, String>();
        if (!infoHidden.isEmpty()) {
            map = infoHidden.get(0);
        }
        //信息脱敏

        for (Map<String, String> field : fieldslist) {
            if ("ORG_NAME".equals(field.get("code"))) {
                result.add(field.get("name"));
                result.add(Config.getConfigValue(oid, "org_name"));
                continue;
            }
            if ("ORG_OID".equals(field.get("code"))) {
                result.add(field.get("name"));
                result.add(Config.getConfigValue(oid, "org_oid"));
                continue;
            }
            if ("-".equals(field.get("code"))) {
                result.add("-");
                result.add("");
                continue;
            }
            if ("IN_HOSPITAL_DAYS".equals(field.get("code"))) {

                DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
                String days = "";
                if (StringUtils.isNotBlank(map.get(field.get("code")))) {
                    days = map.get(field.get("code"));
                } else {
                    String admissionTimeString = map.get("ADMISSION_TIME");
                    if (StringUtils.isBlank(admissionTimeString)) {
                        continue;
                    }
                    LocalDateTime admissionTime = LocalDateTime.parse(admissionTimeString, dateTimeFormatter);
                    String dischargeTime = map.get("DISCHARGE_TIME");
                    LocalDateTime dischargeTimeDate = LocalDateTime.parse(dischargeTime, dateTimeFormatter);
                    if (StringUtils.isBlank(dischargeTime)) {
                        dischargeTimeDate = LocalDateTime.now();
                    }

                    days = Duration.between(admissionTime, dischargeTimeDate).toDays() + "";
                }
                result.add(field.get("name"));
                result.add(days);
                continue;
            }
            if ("AUTOPSY_INDICATOR".equals(field.get("code"))) {
                result.add(field.get("name"));
                String autopsy = map.get("AUTOPSY_INDICATOR");
                if ("true".equals(autopsy)) {
                    result.add("是");
                } else if ("false".equals(autopsy)) {
                    result.add("否");
                } else {
                    result.add(map.get("AUTOPSY_INDICATOR"));
                }
                continue;
            }
//            if ("AUTOPSY_INDICATOR".equals(field.get("code"))) {
//                String autopsy = map.get("AUTOPSY_INDICATOR");
//                result.add(autopsy != null ? (Boolean.parseBoolean(autopsy) ? "是" : "否") : autopsy);
//                continue;
//            }
            result.add(field.get("name"));
            result.add(StringUtils.isNotBlank(map.get(field.get("code"))) ? map.get(field.get("code")) : "");
        }
        return result;
    }

    @Override
    public List<String> getInpSummaryInfo(String oid, String patId, String visitId) {
        //封装数据
        List<String> result = new ArrayList<String>();
        List<Map<String, String>> fieldslist = Config.getSummaryInfoFields(oid);
        //页面查询条件
        List<PropertyFilter> filters = new ArrayList<PropertyFilter>();
        String[] fields = new String[fieldslist.size()];
        int count = 0;
        for (Map<String, String> map : fieldslist) {
            if (!"-".equals(map.get("code"))) {
                fields[count] = map.get("code");
            } else {
                fields[count] = "";
            }
            count++;
        }
        List<Map<String, String>> list = new ArrayList<>();
        ResultVo<PageResultVo<Map<String, String>>> resultVo = hbaseQueryClient.getPageByCondition(
                PageRequestBuilder.init()
                        .tableName(HdrTableEnum.HDR_INP_SUMMARY.getCode())
                        .patientId(patId)
                        .oid(oid)
                        .visitId(visitId)
                        .visitTypeCode("")
                        .filters(filters)
                        .pageNo(0)
                        .pageSize(0)
                        .orderBy("")
                        .desc()
                        .column(fields)
                        .build());
        if (resultVo.isSuccess()) {
            list = resultVo.getContent().getResult();
        }
//        List<Map<String, String>> list = hbaseDao.findConditionByPatientVisitId(HdrTableEnum.HDR_INP_SUMMARY.getCode(),oid,
//                patId, visitId, filters, fields);
        //病案首页信息
        //未找到数据，中断执行
        Map<String, String> map = new HashMap<String, String>();
        if (list.size() > 0) {
            map = list.get(0);
        }
        //新增脱敏
        List<Map<String, String>> listTemp = new ArrayList<Map<String, String>>();
        listTemp.add(map);
//        powerService.getInfoHidden(oid,listTemp);
        for (int i = 0; i < fieldslist.size(); i++) {
            Map<String, String> field = fieldslist.get(i);
            if ("AUTOPSY_INDICATOR".equals(field.get("code"))) {
                result.add(field.get("name"));
                String autopsy = map.get("AUTOPSY_INDICATOR");
                if ("true".equals(autopsy)) {
                    result.add("是");
                } else if ("false".equals(autopsy)) {
                    result.add("否");
                } else {
                    result.add(map.get("AUTOPSY_INDICATOR"));
                }
                continue;
            }
//            if ("AUTOPSY_INDICATOR".equals(field.get("code"))) {
//                String autopsy = map.get("AUTOPSY_INDICATOR");
//                result.add(autopsy != null ? (Boolean.parseBoolean(autopsy) ? "是" : "否") : autopsy);
//                continue;
//            }
            result.add(field.get("name"));
            result.add(StringUtils.isNotBlank(map.get(field.get("code"))) ? map.get(field.get("code")) : "");
        }
        return result;
    }

    @Override
    public List<String> getInpSummaryBeforeInfo(String oid, String patId, String visitId) {
        //封装数据
        List<String> result = new ArrayList<String>();
        List<Map<String, String>> fieldslist = Config.getSummaryInfoFields(oid);
        //页面查询条件
        List<PropertyFilter> filters = new ArrayList<PropertyFilter>();
        String[] fields = new String[fieldslist.size()];
        int count = 0;
        for (Map<String, String> map : fieldslist) {
            if (!"-".equals(map.get("code"))) {
                fields[count] = map.get("code");
            } else {
                fields[count] = "";
            }
            count++;
        }
        List<Map<String, String>> list = new ArrayList<>();
        ResultVo<PageResultVo<Map<String, String>>> resultVo = hbaseQueryClient.getPageByCondition(
                PageRequestBuilder.init()
                        .tableName("HDR_INP_SUMMARY_BEFORE")
                        .patientId(patId)
                        .oid(oid)
                        .visitId(visitId)
                        .visitTypeCode("")
                        .filters(filters)
                        .pageNo(0)
                        .pageSize(0)
                        .orderBy("")
                        .desc()
                        .column(fields)
                        .build());
        if (resultVo.isSuccess()) {
            list = resultVo.getContent().getResult();
        }
//        List<Map<String, String>> list = hbaseDao.findConditionByPatientVisitId(HdrTableEnum.HDR_INP_SUMMARY.getCode(),oid,
//                patId, visitId, filters, fields);
        //病案首页信息
        //未找到数据，中断执行
        Map<String, String> map = new HashMap<String, String>();
        if (list.size() > 0) {
            map = list.get(0);
        }
        //新增脱敏
        List<Map<String, String>> listTemp = new ArrayList<Map<String, String>>();
        listTemp.add(map);
//        powerService.getInfoHidden(oid,listTemp);
        for (int i = 0; i < fieldslist.size(); i++) {
            Map<String, String> field = fieldslist.get(i);
            if ("AUTOPSY_INDICATOR".equals(field.get("code"))) {
                result.add(field.get("name"));
                String autopsy = map.get("AUTOPSY_INDICATOR");
                if ("true".equals(autopsy)) {
                    result.add("是");
                } else if ("false".equals(autopsy)) {
                    result.add("否");
                } else {
                    result.add(map.get("AUTOPSY_INDICATOR"));
                }
                continue;
            }
//            if ("AUTOPSY_INDICATOR".equals(field.get("code"))) {
//                String autopsy = map.get("AUTOPSY_INDICATOR");
//                result.add(autopsy != null ? (Boolean.parseBoolean(autopsy) ? "是" : "否") : autopsy);
//                continue;
//            }
            result.add(field.get("name"));
            result.add(StringUtils.isNotBlank(map.get(field.get("code"))) ? map.get(field.get("code")) : "");
        }
        return result;
    }

    @Override
    public Map<String, Object> getInpSummaryDiag(String oid, String patId, String visitId) {
        Map<String, Object> result = new HashMap<String, Object>();
        List<Map<String, String>> list = new ArrayList<>();
        String[] columns={"DIAGNOSIS_TYPE_NAME", "DIAGNOSIS_NUM", "DIAGNOSIS_SUB_NUM", "DIAGNOSIS_CODE",
                "DIAGNOSIS_NAME", "DIAGNOSIS_TIME", "DIAGNOSIS_DOCTOR_NAME", "DIAGNOSIS_DESC",
                "DIAGNOSIS_PART", "CATALOG_TIME", "DIAGNOSIS_DEPT_NAME", "TREAT_RESULT_NAME", "TREAT_DAYS",
                "ADM_CONDITION_NAME"};
        ResultVo<PageResultVo<Map<String, String>>> resultVo = hbaseQueryClient.getPageByCondition(
                PageRequestBuilder.init()
                        .tableName(HdrTableEnum.HDR_INP_SUMMARY_DIAG.getCode())
                        .patientId(patId)
                        .oid(oid)
                        .visitId(visitId)
                        .visitTypeCode("")
                        .filters(new ArrayList<PropertyFilter>())
                        .pageNo(0)
                        .pageSize(0)
                        .orderBy("")
                        .desc()
                        .column(columns)
                        .build());
        if (resultVo.isSuccess()) {
            list = resultVo.getContent().getResult();
        }
//        List<Map<String, String>> list = hbaseDao.findConditionByPatientVisitId(
//                HdrTableEnum.HDR_INP_SUMMARY_DIAG.getCode(), oid,patId, visitId, new ArrayList<PropertyFilter>(),
//                new String[]{"DIAGNOSIS_TYPE_NAME", "DIAGNOSIS_NUM", "DIAGNOSIS_SUB_NUM", "DIAGNOSIS_CODE",
//                        "DIAGNOSIS_NAME", "DIAGNOSIS_TIME", "DIAGNOSIS_DOCTOR_NAME", "DIAGNOSIS_DESC",
//                        "DIAGNOSIS_PART", "CATALOG_TIME", "DIAGNOSIS_DEPT_NAME", "TREAT_RESULT_NAME", "TREAT_DAYS",
//                        "ADM_CONDITION_NAME"});
        //首页诊断
        List<Map<String, String>> diags = new ArrayList<Map<String, String>>();
        //未找到，中断执行
        if (list.size() == 0) {
            result.put("diags", diags);
            return result;
        }

        Utils.sortListMulti(list, new String[]{"DIAGNOSIS_TYPE_CODE", "DIAGNOSIS_TIME"}, new String[]{"asc", "asc"});
        if(HdrConstantEnum.HOSPITAL_SJT.getCode().equals(oid) || HdrConstantEnum.HOSPITAL_BJYA.getCode().equals(oid)){
            log.info("按DIAGNOSIS_NUM排序");
            list = Utils.sortNumStringList(list, "DIAGNOSIS_NUM", "asc");
        }

        for (Map<String, String> map : list) {
            Map<String, String> diag = new HashMap<String, String>();
            //是否主诊断
            if ("1".equals(map.get("DIAGNOSIS_NUM"))) {
                diag.put("mainFlag", "是");
            } else {
                diag.put("mainFlag", "否");
            }
            //诊断时间
            Utils.checkAndPutToMap(diag, "diagnosisTime",  map.get("DIAGNOSIS_TIME"), "-",
                    false);
            //入院病情
            Utils.checkAndPutToMap(diag, "admConditionName", map.get("ADM_CONDITION_NAME"), "无", false);
            //字段映射
            ColumnUtil.convertMapping(diag, map, columns);

            diags.add(diag);
        }
        result.put("diags", diags);

        return result;
    }

    @Override
    public Map<String, Object> getInpSummaryBeforeDiag(String oid, String patId, String visitId) {
        Map<String, Object> result = new HashMap<String, Object>();
        List<Map<String, String>> list = new ArrayList<>();
        ResultVo<PageResultVo<Map<String, String>>> resultVo = hbaseQueryClient.getPageByCondition(
                PageRequestBuilder.init()
                        .tableName("HDR_INP_SUMMARY_DIAG_BEFORE")
                        .patientId(patId)
                        .oid(oid)
                        .visitId(visitId)
                        .visitTypeCode("")
                        .filters(new ArrayList<PropertyFilter>())
                        .pageNo(0)
                        .pageSize(0)
                        .orderBy("")
                        .desc()
                        .column("DIAGNOSIS_TYPE_NAME", "DIAGNOSIS_NUM", "DIAGNOSIS_SUB_NUM", "DIAGNOSIS_CODE",
                                "DIAGNOSIS_NAME", "DIAGNOSIS_TIME", "DIAGNOSIS_DOCTOR_NAME", "DIAGNOSIS_DESC",
                                "DIAGNOSIS_PART", "CATALOG_TIME", "DIAGNOSIS_DEPT_NAME", "TREAT_RESULT_NAME", "TREAT_DAYS",
                                "ADM_CONDITION_NAME")
                        .build());
        if (resultVo.isSuccess()) {
            list = resultVo.getContent().getResult();
        }
//        List<Map<String, String>> list = hbaseDao.findConditionByPatientVisitId(
//                HdrTableEnum.HDR_INP_SUMMARY_DIAG.getCode(), oid,patId, visitId, new ArrayList<PropertyFilter>(),
//                new String[]{"DIAGNOSIS_TYPE_NAME", "DIAGNOSIS_NUM", "DIAGNOSIS_SUB_NUM", "DIAGNOSIS_CODE",
//                        "DIAGNOSIS_NAME", "DIAGNOSIS_TIME", "DIAGNOSIS_DOCTOR_NAME", "DIAGNOSIS_DESC",
//                        "DIAGNOSIS_PART", "CATALOG_TIME", "DIAGNOSIS_DEPT_NAME", "TREAT_RESULT_NAME", "TREAT_DAYS",
//                        "ADM_CONDITION_NAME"});
        //首页诊断
        List<Map<String, String>> diags = new ArrayList<Map<String, String>>();
        //未找到，中断执行
        if (list.size() == 0) {
            result.put("diags", diags);
            return result;
        }
//		List<Map<String, String>> listSort = Utils.sortList(list, "DIAGNOSIS_NUM", "asc");
        Utils.sortListMulti(list, new String[]{"DIAGNOSIS_TYPE_CODE", "DIAGNOSIS_TIME"}, new String[]{"asc", "asc"});
        for (Map<String, String> map : list) {
            Map<String, String> diag = new HashMap<String, String>();
            //是否主诊断
            if ("1".equals(map.get("DIAGNOSIS_NUM"))) {
                diag.put("mainFlag", "是");
            } else {
                diag.put("mainFlag", "否");
            }
            //诊断时间
            Utils.checkAndPutToMap(diag, "diagnosisTime", Utils.getDate("yyyy-MM-dd", map.get("DIAGNOSIS_TIME")), "-",
                    false);
            //入院病情
            Utils.checkAndPutToMap(diag, "admConditionName", map.get("ADM_CONDITION_NAME"), "无", false);
            //字段映射
            ColumnUtil.convertMapping(diag, map, new String[]{"DIAGNOSIS_TYPE_NAME", "DIAGNOSIS_NAME",
                    "DIAGNOSIS_CODE", "TREAT_RESULT_NAME"});

            diags.add(diag);
        }
        result.put("diags", diags);

        return result;
    }

    @Override
    public Map<String, Object> getInpSummaryOperation(String oid, String patId, String visitId) {
        //封装数据
        Map<String, Object> result = new HashMap<String, Object>();
        List<Map<String, String>> list = new ArrayList<>();
        ResultVo<PageResultVo<Map<String, String>>> resultVo = hbaseQueryClient.getPageByCondition(
                PageRequestBuilder.init()
                        .tableName(HdrTableEnum.HDR_INP_SUMMARY_OPER.getCode())
                        .patientId(patId)
                        .oid(oid)
                        .visitId(visitId)
                        .visitTypeCode("")
                        .filters(new ArrayList<PropertyFilter>())
                        .pageNo(0)
                        .pageSize(0)
                        .orderBy("")
                        .desc()
                        .column("DIAGNOSIS_CODE", "OPER_APPLY_NO", "OPER_NO", "OPERATION_NAME", "OPERATION_DESC",
                                "OPERATION_DATE", "CATALOG_TIME", "OPERATION_GRADE_NAME", "SURGEN_CODE", "SURGEN_NAME",
                                "FIRST_ASSISTANT_NAME", "SECOND_ASSISTANT_NAME", "ANESTHESIA_METHOD_NAME",
                                "ANESTHESIA_DOCTOR_NAME", "ASA_GRADE_NAME", "WOUND_GRADE_NAME", "HEALING_GRADE_NAME",
                                "OPERATION_CODE")
                        .build());
        if (resultVo.isSuccess()) {
            list = resultVo.getContent().getResult();
        }
//        List<Map<String, String>> list = hbaseDao.findConditionByPatientVisitId(
//                HdrTableEnum.HDR_INP_SUMMARY_OPER.getCode(), oid,patId, visitId, new ArrayList<PropertyFilter>(),
//                new String[]{"DIAGNOSIS_CODE", "OPER_APPLY_NO", "OPER_NO", "OPERATION_NAME", "OPERATION_DESC",
//                        "OPERATION_DATE", "CATALOG_TIME", "OPERATION_GRADE_NAME", "SURGEN_CODE", "SURGEN_NAME",
//                        "FIRST_ASSISTANT_NAME", "SECOND_ASSISTANT_NAME", "ANESTHESIA_METHOD_NAME",
//                        "ANESTHESIA_DOCTOR_NAME", "ASA_GRADE_NAME", "WOUND_GRADE_NAME", "HEALING_GRADE_NAME",
//                        "OPERATION_CODE"});
        //首页手术
        List<Map<String, String>> operations = new ArrayList<Map<String, String>>();
        //未找到，中断执行
        if (list.size() == 0) {
            result.put("operations", operations);
            return result;
        }
        List<Map<String, String>> listSort = Utils.sortList(list, "OPER_NO", "asc");
        for (Map<String, String> map : listSort) {
            Map<String, String> operation = new HashMap<String, String>();
            Utils.checkAndPutToMap(operation, "operationDate", Utils.getDate("yyyy-MM-dd HH:mm:ss", map.get("OPERATION_DATE")),
                    "-", false); //手术日期
            //拼接  切口等级/愈合等级
            String wound = map.get("WOUND_GRADE_NAME"); //切口等级
            String heal = map.get("HEALING_GRADE_NAME"); //愈合等级
            if (StringUtils.isNotBlank(wound) && StringUtils.isNotBlank(heal)) {
                operation.put("woundHeal", wound + "/" + heal);
            } else if (StringUtils.isNotBlank(wound) && StringUtils.isBlank(heal)) {
                operation.put("woundHeal", wound + "/");
            } else if (StringUtils.isBlank(wound) && StringUtils.isNotBlank(heal)) {
                operation.put("woundHeal", "/" + heal);
            } else {
                operation.put("woundHeal", "-");
            }
            //字段映射
            ColumnUtil.convertMapping(operation, map, new String[]{"OPERATION_CODE", "OPERATION_GRADE_NAME",
                    "OPERATION_NAME", "SURGEN_NAME", "FIRST_ASSISTANT_NAME", "SECOND_ASSISTANT_NAME",
                    "ANESTHESIA_METHOD_NAME", "ANESTHESIA_DOCTOR_NAME"});
            operations.add(operation);
        }
        result.put("operations", operations);
        return result;
    }

    @Override
    public Map<String, Object> getInpSummaryBeforeOperation(String oid, String patId, String visitId) {
        //封装数据
        Map<String, Object> result = new HashMap<String, Object>();
        List<Map<String, String>> list = new ArrayList<>();
        ResultVo<PageResultVo<Map<String, String>>> resultVo = hbaseQueryClient.getPageByCondition(
                PageRequestBuilder.init()
                        .tableName("HDR_INP_SUMMARY_OPER_BEFORE")
                        .patientId(patId)
                        .oid(oid)
                        .visitId(visitId)
                        .visitTypeCode("")
                        .filters(new ArrayList<PropertyFilter>())
                        .pageNo(0)
                        .pageSize(0)
                        .orderBy("")
                        .desc()
                        .column("DIAGNOSIS_CODE", "OPER_APPLY_NO", "OPER_NO", "OPERATION_NAME", "OPERATION_DESC",
                                "OPERATION_DATE", "CATALOG_TIME", "OPERATION_GRADE_NAME", "SURGEN_CODE", "SURGEN_NAME",
                                "FIRST_ASSISTANT_NAME", "SECOND_ASSISTANT_NAME", "ANESTHESIA_METHOD_NAME",
                                "ANESTHESIA_DOCTOR_NAME", "ASA_GRADE_NAME", "WOUND_GRADE_NAME", "HEALING_GRADE_NAME",
                                "OPERATION_CODE")
                        .build());
        if (resultVo.isSuccess()) {
            list = resultVo.getContent().getResult();
        }
//        List<Map<String, String>> list = hbaseDao.findConditionByPatientVisitId(
//                HdrTableEnum.HDR_INP_SUMMARY_OPER.getCode(), oid,patId, visitId, new ArrayList<PropertyFilter>(),
//                new String[]{"DIAGNOSIS_CODE", "OPER_APPLY_NO", "OPER_NO", "OPERATION_NAME", "OPERATION_DESC",
//                        "OPERATION_DATE", "CATALOG_TIME", "OPERATION_GRADE_NAME", "SURGEN_CODE", "SURGEN_NAME",
//                        "FIRST_ASSISTANT_NAME", "SECOND_ASSISTANT_NAME", "ANESTHESIA_METHOD_NAME",
//                        "ANESTHESIA_DOCTOR_NAME", "ASA_GRADE_NAME", "WOUND_GRADE_NAME", "HEALING_GRADE_NAME",
//                        "OPERATION_CODE"});
        //首页手术
        List<Map<String, String>> operations = new ArrayList<Map<String, String>>();
        //未找到，中断执行
        if (list.size() == 0) {
            result.put("operations", operations);
            return result;
        }
        List<Map<String, String>> listSort = Utils.sortList(list, "OPER_NO", "asc");
        for (Map<String, String> map : listSort) {
            Map<String, String> operation = new HashMap<String, String>();
            Utils.checkAndPutToMap(operation, "operationDate", Utils.getDate("yyyy-MM-dd hh:mm:ss", map.get("OPERATION_DATE")),
                    "-", false); //手术日期
            //拼接  切口等级/愈合等级
            String wound = map.get("WOUND_GRADE_NAME"); //切口等级
            String heal = map.get("HEALING_GRADE_NAME"); //愈合等级
            if (StringUtils.isNotBlank(wound) && StringUtils.isNotBlank(heal)) {
                operation.put("woundHeal", wound + "/" + heal);
            } else if (StringUtils.isNotBlank(wound) && StringUtils.isBlank(heal)) {
                operation.put("woundHeal", wound + "/");
            } else if (StringUtils.isBlank(wound) && StringUtils.isNotBlank(heal)) {
                operation.put("woundHeal", "/" + heal);
            } else {
                operation.put("woundHeal", "-");
            }
            //字段映射
            ColumnUtil.convertMapping(operation, map, new String[]{"OPERATION_CODE", "OPERATION_GRADE_NAME",
                    "OPERATION_NAME", "SURGEN_NAME", "FIRST_ASSISTANT_NAME", "SECOND_ASSISTANT_NAME",
                    "ANESTHESIA_METHOD_NAME", "ANESTHESIA_DOCTOR_NAME"});
            operations.add(operation);
        }
        result.put("operations", operations);
        return result;
    }

    @Override
    public List<Map<String, String>> getOutSummary(String oid, String patId, String visitId) {
        //GHHX 泰兴 挂号序号 字段
        List<Map<String, String>> outVisits = new ArrayList<>();
        ResultVo<PageResultVo<Map<String, String>>> resultVo = hbaseQueryClient.getPageByCondition(
                PageRequestBuilder.init()
                        .tableName(HdrTableEnum.HDR_OUT_VISIT.getCode())
                        .patientId(patId)
                        .oid(oid)
                        .visitId(visitId)
                        .visitTypeCode("")
                        .filters(new ArrayList<PropertyFilter>())
                        .pageNo(0)
                        .pageSize(0)
                        .orderBy("")
                        .desc()
                        .column("VISIT_ID", "REG_CATEGORY_NAME", "REG_TYPE_NAME", "VISIT_DEPT_NAME", "VISIT_DOCTOR_NAME",
                                "REGISTING_TIME", "VISIT_TIME", "EMERGENCY_VISIT_IND", "FIRSTV_INDICATOR", "PERSON_NAME",
                                "DATE_OF_BIRTH", "ID_CARD_NO", "SEX_NAME", "REG_CATEGORY_NAME", "CHARGE_TYPE_NAME",
                                "SEPARATE_TIME", "VISIT_CONSALT_ROOM", "EMERGENCY_VISIT_IND", "MARITAL_STATUS_NAME",
                                "OCCUPATION_NAME", "MAILING_ADDRESS", "PHONE_NUMBER", "AGE_VALUE", "GHHX")
                        .build());
        if (resultVo.isSuccess()) {
            outVisits = resultVo.getContent().getResult();
        }
//        List<Map<String, String>> outVisits = hbaseDao.findConditionByPatientVisitId(
//                HdrTableEnum.HDR_OUT_VISIT.getCode(),oid, patId, visitId, new ArrayList<PropertyFilter>(), new String[]{
//                        "VISIT_ID", "REG_CATEGORY_NAME", "REG_TYPE_NAME", "VISIT_DEPT_NAME", "VISIT_DOCTOR_NAME",
//                        "REGISTING_TIME", "VISIT_TIME", "EMERGENCY_VISIT_IND", "FIRSTV_INDICATOR", "PERSON_NAME",
//                        "DATE_OF_BIRTH", "ID_CARD_NO", "SEX_NAME", "REG_CATEGORY_NAME", "CHARGE_TYPE_NAME",
//                        "SEPARATE_TIME", "VISIT_CONSALT_ROOM", "EMERGENCY_VISIT_IND", "MARITAL_STATUS_NAME",
//                        "OCCUPATION_NAME", "MAILING_ADDRESS", "PHONE_NUMBER", "AGE_VALUE", "GHHX"});
        return outVisits;
    }

    @Override
    public List<Map<String, String>> getOutSummaryDiag(String oid, String patId, String visitId) {
        List<Map<String, String>> list = new ArrayList<>();
        ResultVo<PageResultVo<Map<String, String>>> resultVo = hbaseQueryClient.getPageByCondition(
                PageRequestBuilder.init()
                        .tableName(HdrTableEnum.HDR_OUT_VISIT_DIAG.getCode())
                        .patientId(patId)
                        .oid(oid)
                        .visitId(visitId)
                        .visitTypeCode("")
                        .filters(new ArrayList<PropertyFilter>())
                        .pageNo(0)
                        .pageSize(0)
                        .orderBy("")
                        .desc()
                        .column("DIAGNOSIS_NUM", "DIAGNOSIS_SUB_NUM", "DIAGNOSIS_CODE", "DIAGNOSIS_NAME",
                                "DIAGNOSIS_TIME", "DIAGNOSIS_DOCTOR_NAME", "DIAGNOSIS_DESC", "DIAGNOSIS_PART", "ICD_CODE")
                        .build());
        if (resultVo.isSuccess()) {
            list = resultVo.getContent().getResult();
        }
//        List<Map<String, String>> list = hbaseDao.findConditionByPatientVisitId(
//                HdrTableEnum.HDR_OUT_VISIT_DIAG.getCode(), oid,patId, visitId, new ArrayList<PropertyFilter>(),
//                new String[]{"DIAGNOSIS_NUM", "DIAGNOSIS_SUB_NUM", "DIAGNOSIS_CODE", "DIAGNOSIS_NAME",
//                        "DIAGNOSIS_TIME", "DIAGNOSIS_DOCTOR_NAME", "DIAGNOSIS_DESC", "DIAGNOSIS_PART", "ICD_CODE"});
        List<Map<String, String>> listSort = Utils.sortList(list, "DIAGNOSIS_NUM", "asc");
        return listSort;
    }

    /**
     * 费用明细table配置
     *
     * @param
     * @param visitType
     * @return
     */
    @Override
    public String getPatientFeeTable(String oid, String visitType) {
        String configName = "FEE_TABLE_IN_CHARGE_CONFIG";
        if ("OUTPV".equals(visitType)) {
            configName = "FEE_TABLE_OUT_CHARGE_CONFIG";
        }
        String feeTableConfig = ConfigCache.getCache(oid, configName);
        return feeTableConfig;
    }

    /**
     * 费用明细类型
     *
     * @param patientId
     * @param visitId
     * @param visitType
     * @return
     */
    @Override
    public Page<Map<String, String>> getPatientInpSummaryFeeTypes(String oid, String patientId, String visitId, String visitType, int pageNo, int pageSize) {
        if (pageNo == 0) {
            pageNo = 1;
        }
        if (pageSize == 0) {
            pageSize = 10;
        }
        Page<Map<String, String>> page = new Page<Map<String, String>>();
        page.setPageNo(pageNo);
        page.setPageSize(pageSize);
        String tableName = HdrTableEnum.HDR_IN_CHARGE.getCode();
        if ("INPV".equals(visitType)) {
            visitType = "02";
        } else if ("OUTPV".equals(visitType)) {
            visitType = "01";
            tableName = HdrTableEnum.HDR_OUT_CHARGE.getCode();
        }
        //过滤条件
        List<PropertyFilter> filters = new ArrayList<PropertyFilter>();
        if (StringUtils.isNotBlank(visitType)) {
            filters.add(new PropertyFilter("VISIT_TYPE_CODE", MatchType.EQ.getOperation(), visitType));
        }
        List<Map<String, String>> list = new ArrayList<>();
        ResultVo<PageResultVo<Map<String, String>>> resultVo = hbaseQueryClient.getPageByCondition(
                PageRequestBuilder.init()
                        .tableName(tableName)
                        .patientId(patientId)
                        .oid(oid)
                        .visitId(visitId)
                        .visitTypeCode(visitType)
                        .filters(new ArrayList<PropertyFilter>())
                        .pageNo(0)
                        .pageSize(0)
                        .orderBy("")
                        .desc()
                        .column("CHARGE_CLASS_CODE", "CHARGE_CLASS_NAME")
                        .build());
        if (resultVo.isSuccess()) {
            list = resultVo.getContent().getResult();
        }
//        List<Map<String, String>> list = hbaseDao.findConditionByPatientVisitId(tableName,oid,
//                patientId, visitId, filters, new String[]{"BILL_ITEM_CODE", "BILL_ITEM_NAME"});
        List<Map<String, String>> typeList = new ArrayList<Map<String, String>>();
        Map<String, String> mapAll = new HashMap<String, String>();
        mapAll.put("type_code", "all");
        mapAll.put("type_name", "全部");
        typeList.add(mapAll);
        for1:
        for (Map<String, String> map : list) {
            for2:
            for (Map<String, String> mapTemp : typeList) {
                //CHARGE_CLASS_CODE
                if (null != map.get("CHARGE_CLASS_NAME") && map.get("CHARGE_CLASS_NAME").equals(mapTemp.get("type_name"))) {
                    continue for1;
                }
            }
            Map<String, String> mapRes = new HashMap<String, String>();
            mapRes.put("type_code", map.get("CHARGE_CLASS_CODE"));
            mapRes.put("type_name", map.get("CHARGE_CLASS_NAME"));
            typeList.add(mapRes);
        }
        page.setTotalCount(typeList.size());
        ListPage<Map<String, String>> listPage = new ListPage<Map<String, String>>(typeList, pageNo, pageSize);
        page.setResult(listPage.getPagedList());
        return page;
    }

    /**
     * 获取费用明细数据
     *
     * @param patientId
     * @param visitId
     * @param visitType
     * @param pageNo
     * @param pageSize
     * @return
     */
    public Page<Map<String, String>> getPatientInpSummaryFeeData(String oid, String patientId, String visitId, String visitType, String feeType, int pageNo, int pageSize) {
        Page<Map<String, String>> page = new Page<Map<String, String>>();
        page.setPageNo(1);
        page.setPageSize(1000000);
        //先试用visit_type获取表头配置
        String config = getPatientFeeTable(oid, visitType);// ConfigCache.getCache("FEE_TABLE_CONFIG");
        String tableName = HdrTableEnum.HDR_IN_CHARGE.getCode();
        if ("INPV".equals(visitType)) {
            visitType = "02";
        } else if ("OUTPV".equals(visitType) || "EMPV".equals(visitType)) {
            visitType = "01";
            tableName = HdrTableEnum.HDR_OUT_CHARGE.getCode();
        }
        //过滤条件
        List<PropertyFilter> filters = new ArrayList<PropertyFilter>();
        if (StringUtils.isNotBlank(visitType)) {
            filters.add(new PropertyFilter("VISIT_TYPE_CODE", MatchType.EQ.getOperation(), visitType));
        }
        if (StringUtils.isNotBlank(feeType) && !"all".equals(feeType)) {
            filters.add(new PropertyFilter("CHARGE_CLASS_CODE", MatchType.EQ.getOperation(), feeType));
        }
//		new String[]{"CHARGE_ITEM_NAME","CHARGE_ITEM_CODE","CHARGE_CLASS_CODE","CHARGE_CLASS_NAME","CHARGE_CLASS","CHARGE_ITEM"
//				,"CHARGE_TIME","CHARGE_ITEM_PRICE","CHARGE_AMOUNT_VALUE","CHARGE_FEE","PRESC_DOCTOR_NAME","ORDER_DOCTOR"}
//        page = hbaseDao.findPageConditionByPatientVisitId(tableName, oid, patientId, visitId, page,
//                filters, new String[]{});

        ResultVo<PageResultVo<Map<String, String>>> resultVo = hbaseQueryClient.getPageByCondition(
                PageRequestBuilder.init()
                        .tableName(tableName)
                        .patientId(patientId)
                        .oid(oid)
                        .visitId(visitId)
                        .visitTypeCode(visitType)
                        .filters(filters)
                        .pageNo(0)
                        .pageSize(0)
                        .orderBy("")
                        .desc()
                        .column(new String[]{})
                        .build());
        if (resultVo.isSuccess()) {
            page.setResult(resultVo.getContent().getResult());
            page.setTotalCount(resultVo.getContent().getTotal());
        }
        Page<Map<String, String>> page2 = new Page<Map<String, String>>();
        page2.setPageNo(pageNo);
        page2.setPageSize(pageSize);
        page2.setTotalCount(page.getTotalCount());
        page2.setTotalPages(page.getTotalPages());
        page2.setResult(page.getResult().subList(getStartPageNum(pageNo, pageSize), getEndPageNum(pageNo, pageSize, page.getTotalCount())));
        page = page2;
        List<Map<String, String>> feeConfigList=new ArrayList<>();
        try {
            feeConfigList  = objectMapper.readValue(config, new TypeReference<List<Map<String, String>>>() {
            });
        } catch (JsonProcessingException e) {
           log.error("解析费用明细表头失败",e);
        }

        List<Map<String, String>> result = new ArrayList<Map<String, String>>();
        for (Map<String, String> map : page.getResult()) {
            Map<String, String> mapTemp = new HashMap<String, String>();
            for ( Map<String, String> feeConfig : feeConfigList) {
                if (!"true".equals(feeConfig.get("hidden"))) {
                    if (StringUtils.isNotBlank(feeConfig.get("name"))) {
                        mapTemp.put(feeConfig.get("name"), map.get(feeConfig.get("column")));
                    }
                }
            }
            result.add(mapTemp);
        }
        page.setResult(result);
        return page;
    }

    @Override
    public Map<String, Object> getInpSummaryFee(String oid, String patId, String visitId) {
        Map<String, Object> result = new HashMap<String, Object>();
        String[] columns = new String[]{"COUNT_CHARGE_FEE", "CHARGE_FEE1", "CHARGE_FEE2",
                "CHARGE_FEE3", "CHARGE_FEE4", "CHARGE_FEE5", "CHARGE_FEE6", "CHARGE_FEE7", "CHARGE_FEE8",
                "CHARGE_FEE9", "CHARGE_FEE10", "CHARGE_FEE11", "CHARGE_FEE12", "CHARGE_FEE13", "CHARGE_FEE14",
                "CHARGE_FEE15", "CHARGE_FEE16", "CHARGE_FEE17", "CHARGE_FEE18", "CHARGE_FEE19", "CHARGE_FEE20",
                "CHARGE_FEE21", "CHARGE_FEE22", "CHARGE_FEE23", "CHARGE_FEE24", "CHARGE_FEE25", "CHARGE_FEE26",
                "CHARGE_FEE27", "CHARGE_FEE28", "CHARGE_FEE29", "CHARGE_FEE30", "CHARGE_FEE31", "CHARGE_FEE32",
                "CHARGE_FEE33", "CHARGE_FEE34", "CHARGE_FEE35", "CHARGE_FEE36", "CHARGE_FEE37", "CHARGE_FEE38",
                "CHARGE_FEE39", "CHARGE_FEE40","CHARGE_FEE41","CHARGE_FEE42","CHARGE_FEE8_1","CHARGE_FEE8_2","CHARGE_FEE8_3",
                "CHARGE_FEE8_4","CHARGE_FEE8_5","CHARGE_FEE8_6","CHARGE_FEE43","CHARGE_FEE43_1","CHARGE_FEE43_2"};
        List<String> columnsList = new ArrayList<String>(Arrays.asList(columns));
        //获取配置文件的字段
        String columnConfig = Config.getVISIT_SUMMARY_FEE_COLUMN(oid);
        List<Map<String, String>> columnMap = new ArrayList<Map<String, String>>();
        if (StringUtils.isNotBlank(columnConfig)) {
            String columnes[] = columnConfig.split(";");
            for (String column : columnes) {
                // info[0]大类  info[1] 字段名  info[2] 字段名称
                String info[] = column.split(",");
                Map<String, String> map = new HashMap<String, String>();
                map.put("main_class", info[0]);
                map.put("field_code", info[1]);
                map.put("field_name", info[2]);
                columnMap.add(map);
                //添加到查询字段里
                columnsList.add(info[1]);
            }
        }
        //过滤条件
        List<PropertyFilter> filters = new ArrayList<PropertyFilter>();
        //查询病案首页 仅取费用相关字段
        List<Map<String, String>> list = new ArrayList<>();
        ResultVo<PageResultVo<Map<String, String>>> resultVo = hbaseQueryClient.getPageByCondition(
                PageRequestBuilder.init()
                        .tableName(HdrTableEnum.HDR_INP_SUMMARY.getCode())
                        .patientId(patId)
                        .oid(oid)
                        .visitId(visitId)
                        .visitTypeCode("")
                        .filters(filters)
                        .pageNo(0)
                        .pageSize(0)
                        .orderBy("")
                        .desc()
                        .column(columnsList.toArray(new String[]{}))
                        .build());
        if (resultVo.isSuccess()) {
            list = resultVo.getContent().getResult();
        }
//        List<Map<String, String>> list = hbaseDao.findConditionByPatientVisitId(HdrTableEnum.HDR_INP_SUMMARY.getCode(),oid,
//                patId, visitId, filters, columnsList.toArray(new String[]{}));
        //存储分类后费用信息
        List<Map<String, Object>> feeList = new ArrayList<Map<String, Object>>();
        //未找到，终止执行
        if (list.size() == 0) {
            result.put("total", "0.00"); //总费用
            result.put("selfExpense", "0.00"); //自付费用
            result.put("data", feeList);
            return result;
        }
        Map<String, String> map = list.get(0);
        //综合医疗服务类
        Map<String, Object> map1 = new HashMap<String, Object>();
        List<Map<String, Object>> map1List = new ArrayList<Map<String, Object>>();
        Map<String, Object> map11 = new HashMap<String, Object>();
        map11.put("name", "一般医疗服务费");
        map11.put("cost", calCost(map.get("CHARGE_FEE2"), map.get("CHARGE_FEE27"), map.get("CHARGE_FEE28")));
        map1List.add(map11);
        Map<String, Object> map12 = new HashMap<String, Object>();
        map12.put("name", "一般治疗操作费");
        map12.put(
                "cost",
                calCost(map.get("CHARGE_FEE9"), map.get("CHARGE_FEE22"), map.get("CHARGE_FEE14"),
                        map.get("CHARGE_FEE29")));
        map1List.add(map12);
        Map<String, Object> map13 = new HashMap<String, Object>();
        map13.put("name", "护理费");
        map13.put("cost", calCost(map.get("CHARGE_FEE15")));
        map1List.add(map13);
        Map<String, Object> map14 = new HashMap<String, Object>();
        map14.put("name", "其他费用");
        map14.put("cost", calCost(map.get("CHARGE_FEE39")));
        map1List.add(map14);
        map1.put("name", "综合医疗服务类");
        map1.put("综合医疗服务类", "综合医疗服务类");
        map1.put("data", map1List);
        feeList.add(map1);

        //诊断类
        Map<String, Object> map2 = new HashMap<String, Object>();
        List<Map<String, Object>> map2List = new ArrayList<Map<String, Object>>();
        Map<String, Object> map21 = new HashMap<String, Object>();
        map21.put("name", "临床诊断项目费");
        map21.put("cost", calCost(map.get("CHARGE_FEE3")));
        map2List.add(map21);
        Map<String, Object> map22 = new HashMap<String, Object>();
        map22.put("name", "影像学诊断费");
        map22.put("cost", calCost(map.get("CHARGE_FEE16"), map.get("CHARGE_FEE18"), map.get("CHARGE_FEE19")));
        map2List.add(map22);
        Map<String, Object> map23 = new HashMap<String, Object>();
        map23.put("name", "实验室诊断费");
        map23.put("cost", calCost(map.get("CHARGE_FEE20")));
        map2List.add(map23);
        Map<String, Object> map24 = new HashMap<String, Object>();
        map24.put("name", "病理诊断费");
        map24.put("cost", calCost(map.get("CHARGE_FEE21")));
        map2List.add(map24);
        map2.put("name", "诊断类");
        map2.put("诊断类", "诊断类");
        map2.put("data", map2List);
        feeList.add(map2);

        //治疗类
        Map<String, Object> map3 = new HashMap<String, Object>();
        List<Map<String, Object>> map3List = new ArrayList<Map<String, Object>>();
        Map<String, Object> map31 = new HashMap<String, Object>();
        map31.put("name", "非手术治疗项目费");
        map31.put("cost", calCost(map.get("CHARGE_FEE6"), map.get("CHARGE_FEE10")));
        map3List.add(map31);
        Map<String, Object> map32 = new HashMap<String, Object>();
        map32.put("name", "∟临床物理治疗费");
        map32.put("cost", calCost(map.get("CHARGE_FEE4"), map.get("CHARGE_FEE17")));
        map3List.add(map32);
        Map<String, Object> map33 = new HashMap<String, Object>();
        map33.put("name", "手术治疗费");
        map33.put("cost", calCost(map.get("CHARGE_FEE41")));
        map3List.add(map33);
        Map<String, Object> map34 = new HashMap<String, Object>();
        map34.put("name", "∟手术费");
        map34.put("cost", calCost(map.get("CHARGE_FEE5"), map.get("CHARGE_FEE11"), map.get("CHARGE_FEE13")));
        map3List.add(map34);
        Map<String, Object> map35 = new HashMap<String, Object>();
        map35.put("name", "∟麻醉费");
        map35.put("cost", calCost(map.get("CHARGE_FEE12")));
        map3List.add(map35);
        map3.put("name", "治疗类");
        map3.put("治疗类", "治疗类");
        map3.put("data", map3List);
        feeList.add(map3);

        //康复类
        Map<String, Object> map4 = new HashMap<String, Object>();
        List<Map<String, Object>> map4List = new ArrayList<Map<String, Object>>();
        Map<String, Object> map41 = new HashMap<String, Object>();
        map41.put("name", "康复费");
        map41.put("cost", calCost(map.get("CHARGE_FEE7")));
        map4List.add(map41);
        map4.put("name", "康复类");
        map4.put("康复类", "康复类");
        map4.put("data", map4List);
        feeList.add(map4);

        //中医类
        Map<String, Object> map5 = new HashMap<String, Object>();
        List<Map<String, Object>> map5List = new ArrayList<Map<String, Object>>();
        Map<String, Object> map50 = new HashMap<String, Object>();
        map50.put("name", "中医诊断");
        map50.put("cost", calCost(map.get("CHARGE_FEE42")));
        map5List.add(map50);
        Map<String, Object> map51 = new HashMap<String, Object>();
        map51.put("name", "中医治疗费");
        map51.put("cost", calCost(map.get("CHARGE_FEE8")));
        map5List.add(map51);
        Map<String, Object> map52 = new HashMap<String, Object>();
        map52.put("name", "∟中医外治");
        map52.put("cost", calCost(map.get("CHARGE_FEE8_1")));
        map5List.add(map52);
        Map<String, Object> map53 = new HashMap<String, Object>();
        map53.put("name", "∟中医骨伤");
        map53.put("cost", calCost(map.get("CHARGE_FEE8_2")));
        map5List.add(map53);
        Map<String, Object> map54 = new HashMap<String, Object>();
        map54.put("name", "∟针刺与灸法");
        map54.put("cost", calCost(map.get("CHARGE_FEE8_3")));
        map5List.add(map54);
        Map<String, Object> map55 = new HashMap<String, Object>();
        map55.put("name", "∟中医推拿治疗");
        map55.put("cost", calCost(map.get("CHARGE_FEE8_4")));
        map5List.add(map55);
        Map<String, Object> map56 = new HashMap<String, Object>();
        map56.put("name", "∟中医肛肠治疗");
        map56.put("cost", calCost(map.get("CHARGE_FEE8_5")));
        map5List.add(map56);
        Map<String, Object> map57 = new HashMap<String, Object>();
        map57.put("name", "∟中医特殊治疗");
        map57.put("cost", calCost(map.get("CHARGE_FEE8_6")));
        map5List.add(map57);
        Map<String, Object> map58 = new HashMap<String, Object>();
        map58.put("name", "中医其他");
        map58.put("cost", calCost(map.get("CHARGE_FEE43")));
        map5List.add(map58);
        Map<String, Object> map59 = new HashMap<String, Object>();
        map59.put("name", "∟中药特殊调配加工");
        map59.put("cost", calCost(map.get("CHARGE_FEE43_1")));
        map5List.add(map59);
        Map<String, Object> map510 = new HashMap<String, Object>();
        map510.put("name", "∟辩证施善");
        map510.put("cost", calCost(map.get("CHARGE_FEE43_2")));
        map5List.add(map510);
        map5.put("name", "中医类");
        map5.put("中医类", "中医类");
        map5.put("data", map5List);
        feeList.add(map5);

        //西药类
        Map<String, Object> map6 = new HashMap<String, Object>();
        List<Map<String, Object>> map6List = new ArrayList<Map<String, Object>>();
        Map<String, Object> map61 = new HashMap<String, Object>();
        map61.put("name", "西药费");
        map61.put("cost", calCost(map.get("CHARGE_FEE31")));
        map6List.add(map61);
        Map<String, Object> map62 = new HashMap<String, Object>();
        map62.put("name", "抗菌药物费用");
        map62.put("cost", calCost(map.get("CHARGE_FEE32")));
        map6List.add(map62);
        map6.put("name", "西药类");
        map6.put("西药类", "西药类");
        map6.put("data", map6List);
        feeList.add(map6);

        //中药类
        Map<String, Object> map7 = new HashMap<String, Object>();
        List<Map<String, Object>> map7List = new ArrayList<Map<String, Object>>();
        Map<String, Object> map71 = new HashMap<String, Object>();
        map71.put("name", "中成药费");
        map71.put("cost", calCost(map.get("CHARGE_FEE37")));
        map7List.add(map71);
        Map<String, Object> map72 = new HashMap<String, Object>();
        map72.put("name", "中草药费");
        map72.put("cost", calCost(map.get("CHARGE_FEE38")));
        map7List.add(map72);
        map7.put("name", "中药类");
        map7.put("中药类", "中药类");
        map7.put("data", map7List);
        feeList.add(map7);

        //血液和血液制品类
        Map<String, Object> map8 = new HashMap<String, Object>();
        List<Map<String, Object>> map8List = new ArrayList<Map<String, Object>>();
        Map<String, Object> map81 = new HashMap<String, Object>();
        map81.put("name", "血费");
        map81.put("cost", calCost(map.get("CHARGE_FEE30")));
        map8List.add(map81);
        Map<String, Object> map82 = new HashMap<String, Object>();
        map82.put("name", "白蛋白类制品费");
        map82.put("cost", calCost(map.get("CHARGE_FEE33")));
        map8List.add(map82);
        Map<String, Object> map83 = new HashMap<String, Object>();
        map83.put("name", "球蛋白类制品费");
        map83.put("cost", calCost(map.get("CHARGE_FEE34")));
        map8List.add(map83);
        Map<String, Object> map84 = new HashMap<String, Object>();
        map84.put("name", "凝血因子类制品费");
        map84.put("cost", calCost(map.get("CHARGE_FEE35")));
        map8List.add(map84);
        Map<String, Object> map85 = new HashMap<String, Object>();
        map85.put("name", "细胞因子类制品费");
        map85.put("cost", calCost(map.get("CHARGE_FEE36")));
        map8List.add(map85);
        map8.put("name", "血液和血液制品类");
        map8.put("血液和血液制品类", "血液和血液制品类");
        map8.put("data", map8List);
        feeList.add(map8);

        //耗材类
        Map<String, Object> map9 = new HashMap<String, Object>();
        List<Map<String, Object>> map9List = new ArrayList<Map<String, Object>>();
        Map<String, Object> map91 = new HashMap<String, Object>();
        map91.put("name", "治疗用一次性医用材料费");
        map91.put("cost", calCost(map.get("CHARGE_FEE23")));
        map9List.add(map91);
        Map<String, Object> map92 = new HashMap<String, Object>();
        map92.put("name", "手术用一次性医用材料费");
        map92.put("cost", calCost(map.get("CHARGE_FEE24"), map.get("CHARGE_FEE25")));
        map9List.add(map92);
        Map<String, Object> map93 = new HashMap<String, Object>();
        map93.put("name", "检查用一次性医用材料费");
        map93.put("cost", calCost(map.get("CHARGE_FEE26")));
        map9List.add(map93);
        map9.put("name", "耗材类");
        map9.put("耗材类", "耗材类");
        map9.put("data", map9List);
        feeList.add(map9);

        //其他类
        Map<String, Object> map10 = new HashMap<>();
        List<Map<String, Object>> map10List = new ArrayList<>();
        Map<String, Object> map101 = new HashMap<String, Object>();
        map101.put("name", "其他费用");
        map101.put("cost", calCost(map.get("CHARGE_FEE40")));
        map10List.add(map101);
        map10.put("name", "其他类");
        map10.put("其他类", "其他类");
        map10.put("data", map10List);
        feeList.add(map10);

        for (Map<String, String> mapTemp : columnMap) {
            String mainClass = mapTemp.get("main_class");
            for (Map<String, Object> feeMap : feeList) {
                String main_class = (String) feeMap.get(mainClass);
                if (mainClass.equals(main_class)) {
                    Map<String, Object> mapConfig = new HashMap<String, Object>();
                    mapConfig.put("name", mapTemp.get("field_name"));
                    mapConfig.put("cost", calCost(map.get(mapTemp.get("field_code"))));
                    List<Map<String, Object>> mapData = (List<Map<String, Object>>) feeMap.get("data");
                    mapData.add(mapConfig);
                    break;
                }
            }
        }


        result.put("total", map.get("COUNT_CHARGE_FEE")); //总费用
        result.put("selfExpense", map.get("CHARGE_FEE1")); //自付费用
        result.put("data", feeList);
        return result;
    }

    /**
     * @param feeStrings
     * @return 费用字符串
     * @Description 方法描述: 计算费用，保留两位小数
     */
    private String calCost(String... feeStrings) {
        double total = 0.00;
        if (null != feeStrings && feeStrings.length > 0) {
            for (String fee : feeStrings) {
                if (StringUtils.isNotBlank(fee)) {
                    total = total + Double.parseDouble(fee);
                }
            }
        }
        //保留两位小数
        DecimalFormat df = new DecimalFormat("#0.00");
        return df.format(total);
    }

    @Override
    public Map<String, Object> getPatientOutpSummary(String oid, String patientId, String visitId) {
        Map<String, Object> result = new HashMap<String, Object>();
        //封装数据
        List<String> outVisitResultList = new ArrayList<String>();
        //诊断信息
        List<Map<String, String>> diags = new ArrayList<Map<String, String>>();
        //获取门诊患者就诊信息配置字段
        List<Map<String, String>> fieldslist = Config.getOutpSummaryFields(oid);
        //页面查询条件
        List<PropertyFilter> filters = new ArrayList<PropertyFilter>();
        String[] fields = new String[fieldslist.size()];
        int count = 0;
        for (Map<String, String> map : fieldslist) {
            if (!"-".equals(map.get("code"))) {
                fields[count] = map.get("code");
            } else {
                fields[count] = "";
            }
            count++;
        }
        List<Map<String, String>> outVisitList = new ArrayList<>();
        ResultVo<PageResultVo<Map<String, String>>> resultVo = hbaseQueryClient.getPageByCondition(
                PageRequestBuilder.init()
                        .tableName(HdrTableEnum.HDR_OUT_VISIT.getCode())
                        .patientId(patientId)
                        .oid(oid)
                        .visitId(visitId)
                        .visitTypeCode("")
                        .filters(filters)
                        .pageNo(0)
                        .pageSize(0)
                        .orderBy("")
                        .desc()
                        .column(fields)
                        .build());
        if (resultVo.isSuccess()) {
            outVisitList = resultVo.getContent().getResult();
        }
//        List<Map<String, String>> outVisitList = hbaseDao.findConditionByPatientVisitId(HdrTableEnum.HDR_OUT_VISIT.getCode(),oid,
//                patientId, visitId, filters, fields);
        //信息脱敏
        List<Map<String, String>> infoHidden = powerService.getInfoHidden(outVisitList);
        //List<Map<String, String>> infoHidden = powerService.getInfoHiddenByMaskRule(outVisitList);
        //未找到数据，中断执行
        Map<String, String> outVisitMap = new HashMap<String, String>();
        if (infoHidden.isEmpty()) {
            result.put("summary", outVisitResultList);
            result.put("diags", diags);
            return result;

        } else {
            outVisitMap = infoHidden.get(0);
        }

        for (Map<String, String> field : fieldslist) {
            //处理年龄
            if ("AGE_VALUE".equals(field.get("code"))) {
                String year = "";
                if (StringUtils.isNotBlank(outVisitMap.get(field.get("code")))) {
                    year = outVisitMap.get(field.get("code"));
                } else {
                    if (StringUtils.isNotBlank(outVisitMap.get("DATE_OF_BIRTH")) && StringUtils.isNotBlank(outVisitMap.get("VISIT_TIME"))) {
                        String birthday = outVisitMap.get("DATE_OF_BIRTH");
                        String ADMISSION_TIME = outVisitMap.get("VISIT_TIME");
                        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
                        Date date1 = null;
                        Date date2 = null;
                        try {
                            date1 = sdf.parse(ADMISSION_TIME.split(" ")[0]);
                            date2 = sdf.parse(birthday);
                        } catch (ParseException e) {
                            e.printStackTrace();
                        }
                        year = String.valueOf((date1.getTime() - date2.getTime()) / 24 / 3600 / 1000 / 365);
                    }
                }
                outVisitResultList.add(field.get("name"));
                outVisitResultList.add(year);
                continue;
            }
            //急诊标识
            if ("EMERGENCY_VISIT_IND".equals(field.get("code"))) {
                if ("true".equals(outVisitMap.get("EMERGENCY_VISIT_IND"))) {
                    outVisitResultList.add(field.get("name"));
                    outVisitResultList.add("是");
                } else {
                    outVisitResultList.add(field.get("name"));
                    outVisitResultList.add("否");
                }
                continue;
            }
            //初诊标识
            if ("FIRSTV_INDICATOR".equals(field.get("code"))) {
                if ("true".equals(outVisitMap.get("FIRSTV_INDICATOR"))) {
                    outVisitResultList.add(field.get("name"));
                    outVisitResultList.add("是");
                } else {
                    outVisitResultList.add(field.get("name"));
                    outVisitResultList.add("否");
                }
                continue;
            }
            outVisitResultList.add(field.get("name"));
            outVisitResultList.add(StringUtils.isNotBlank(outVisitMap.get(field.get("code"))) ? outVisitMap.get(field.get("code")) : "");
        }
        result.put("summary", outVisitResultList);

        //获取门诊诊断
        List<Map<String, String>> diagList = getOutSummaryDiag(oid, patientId, visitId);
        for (Map<String, String> map : diagList) {
            Map<String, String> diag = new HashMap<String, String>();
            ColumnUtil.convertMapping(diag, map, new String[]{"DIAGNOSIS_CODE", "DIAGNOSIS_NAME", "DIAGNOSIS_NUM",
                    "DIAGNOSIS_TIME", "DIAGNOSIS_DOCTOR_NAME"});
            diags.add(diag);
        }
        result.put("diags", diags);

        return result;
    }

    @Override
    public Map<String, String> getPatientInfo(String oid, String patientId, String visitType, String visitId) {
        Map<String, String> infos = new HashMap<String, String>();
        infos.put("PATIENT_ID", patientId);
        List<PropertyFilter> filters = new ArrayList<PropertyFilter>();
        filters.add(new PropertyFilter("VISIT_TYPE_CODE", MatchType.EQ.getOperation(), visitType));
        //可能得到两条记录  门诊患者信息 和 住院患者信息
        List<Map<String, String>> indexs = new ArrayList<>();
        ResultVo<PageResultVo<Map<String, String>>> resultVo = hbaseQueryClient.getPageByCondition(
                PageRequestBuilder.init()
                        .tableName(HdrTableEnum.HDR_EMR_CONTENT.getCode())
                        .patientId(patientId)
                        .oid(oid)
                        .visitId(visitId)
                        .visitTypeCode(visitType)
                        .filters(filters)
                        .pageNo(0)
                        .pageSize(0)
                        .orderBy("")
                        .desc()
                        .column("EID", "PERSON_NAME", "SEX_NAME", "DATE_OF_BIRTH", "INP_NO",
                                "OUTP_NO", "IN_PATIENT_ID", "OUT_PATIENT_ID", "NATIONALITY_NAME", "POSTCODE", "NEXT_OF_KIN",
                                "RELATIONSHIP_NAME", "NEXT_OF_KIN_PHONE")
                        .build());
        if (resultVo.isSuccess()) {
            indexs = resultVo.getContent().getResult();
        }
//        List<Map<String, String>> indexs = hbaseDao.findConditionByPatient(HdrTableEnum.HDR_PATIENT.getCode(),oid,
//                patientId, filters, new String[]{"EID", "PERSON_NAME", "SEX_NAME", "DATE_OF_BIRTH", "INP_NO",
//                        "OUTP_NO", "IN_PATIENT_ID", "OUT_PATIENT_ID", "NATIONALITY_NAME", "POSTCODE", "NEXT_OF_KIN",
//                        "RELATIONSHIP_NAME", "NEXT_OF_KIN_PHONE"});
        //未找到患者，返回空的map
        if (indexs.size() == 0) {
            return infos;
        }
        //循环遍历患者信息  记录需要的字段
        for (Map<String, String> one : indexs) {
            Utils.checkAndPutToMap(infos, "PERSON_NAME", one.get("PERSON_NAME"), "-", false); //患者名
            Utils.checkAndPutToMap(infos, "SEX_NAME", one.get("SEX_NAME"), "-", false); //性别
            Utils.checkAndPutToMap(infos, "EID", one.get("EID"), "-", false); //主索引
            Utils.checkAndPutToMap(infos, "INP_NO", one.get("INP_NO"), "住院号未知", false); //住院号
            Utils.checkAndPutToMap(infos, "OUTP_NO", one.get("OUT_NO"), "门诊号未知", false); //门诊号
            Utils.checkAndPutToMap(infos, "IN_PATIENT_ID", one.get("IN_PATIENT_ID"), "-", false); //住院患者标识
            Utils.checkAndPutToMap(infos, "OUT_PATIENT_ID", one.get("OUT_PATIENT_ID"), "-", false); //门诊患者标识
            Utils.checkAndPutToMap(infos, "DATE_OF_BIRTH", one.get("DATE_OF_BIRTH"), "-", false); //出生日期
            Utils.checkAndPutToMap(infos, "NATIONALITY_NAME", one.get("NATIONALITY_NAME"), "-", false); //民族
            Utils.checkAndPutToMap(infos, "POSTCODE", one.get("POSTCODE"), "-", false); //邮编
            Utils.checkAndPutToMap(infos, "NEXT_OF_KIN", one.get("NEXT_OF_KIN"), "-", false); //联系人姓名
            Utils.checkAndPutToMap(infos, "RELATIONSHIP_NAME", one.get("RELATIONSHIP_NAME"), "-", false); //与患者关系
            Utils.checkAndPutToMap(infos, "NEXT_OF_KIN_PHONE", one.get("NEXT_OF_KIN_PHONE"), "-", false); //联系人电话
        }
        //处理出生日期
        String birthday = infos.get("DATE_OF_BIRTH");
        if (!"-".equals(birthday)) {
            infos.put("DATE_OF_BIRTH",  birthday);
        }
        return infos;
    }

    public int getStartPageNum(int pageNo, int pageSize) {
        return (pageNo - 1) * pageSize;
    }

    public int getEndPageNum(int pageNo, int pageSize, long totalPage) {
        if ((pageNo * pageSize) > totalPage) {
            return (int) (totalPage);
        } else {
            return ((pageNo * pageSize));
        }
    }

    @Override
    public Map<String, Object> getPatientOutpSummaryYouAn(String oid, String patientId, String visitId) {
        Map<String, Object> result = new HashMap<String, Object>();
        String visitType = "";
        List<PropertyFilter> filters = new ArrayList<PropertyFilter>();
//        filters.add(new PropertyFilter("VISIT_ID",MatchType.EQ.getOperation(), visitId));
        // 患者信息和就诊信息查询开始
        List<Map<String, String>> list = new ArrayList<>();
        List<Map<String, String>> summaryOutpFieldsList = Config.getSummaryOutpFields(oid,"CIV_VISIT_OUTP_SUNNARY_PATIENT");
        List<Map<String, String>> civVisitOutpSunnaryInfoList = Config.getSummaryOutpFields(oid, "CIV_VISIT_OUTP_SUNNARY_INFO");
        List<Map<String, String>> mergedList = Stream.concat(summaryOutpFieldsList.stream(), civVisitOutpSunnaryInfoList.stream()).collect(Collectors.toList());
        String[] columnArray1 = new String[mergedList.size()];
        for (int i = 0; i < mergedList.size(); i++) {
            Map<String, String> map = mergedList.get(i);
            if (StringUtils.isNotBlank(map.get("code"))) {
                columnArray1[i] = map.get("code");
            }else {
                columnArray1[i] = "";
            }
        }
        ResultVo<PageResultVo<Map<String, String>>> resultVo1 = hbaseQueryClient.getPageByCondition(
                PageRequestBuilder.init()
                        .tableName(HdrTableEnum.JH_PATIENT_BASIC_INFORMATION_VIEW.getCode())
                        .patientId(patientId)
                        .oid(oid)
                        .visitId(visitId)
                        .visitTypeCode(visitType)
                        .filters(filters)
                        .pageNo(0)
                        .pageSize(0)
                        .orderBy("")
                        .desc()
                        .column(columnArray1)
                        .build());
        if (resultVo1.isSuccess()) {
            list = resultVo1.getContent().getResult();
        }
        list = powerService.getInfoHidden(list);
        Map<String, String> map = new HashMap<>();
        if (list.size()>0) {
            map = list.get(0);
        }
        List<String> patientList = new ArrayList<>();
        for (Map<String, String> outpField : summaryOutpFieldsList) {
            patientList.add(outpField.get("name"));
            patientList.add(StringUtils.isNotBlank(map.get(outpField.get("code"))) ? map.get(outpField.get("code")) : "");
        }
        result.put("patient",patientList);
        List<String> patientVisitList = new ArrayList<>();
        for (Map<String, String> outpVisitField : civVisitOutpSunnaryInfoList) {
            patientVisitList.add(outpVisitField.get("name"));
            patientVisitList.add(StringUtils.isNotBlank(map.get(outpVisitField.get("code"))) ? map.get(outpVisitField.get("code")) : "");
        }
        result.put("patientVisit",patientVisitList);
        // 患者信息和就诊信息查询结束
        // 诊断查询开始
        list.clear();
        String[] columnDiag = Config.getSummaryOutpDiagFields(oid).split(",");
        ResultVo<PageResultVo<Map<String, String>>> resultVo2 = hbaseQueryClient.getPageByCondition(
                PageRequestBuilder.init()
                        .tableName(HdrTableEnum.JH_DIAGNOSTIC_INFORMATION_VIEW.getCode())
                        .patientId(patientId)
                        .oid(oid)
                        .visitId(visitId)
                        .visitTypeCode(visitType)
                        .filters(filters)
                        .pageNo(0)
                        .pageSize(0)
                        .orderBy("")
                        .desc()
                        .column(columnDiag)
                        .build());
        if (resultVo2.isSuccess()) {
            list = resultVo2.getContent().getResult();
        }
        if (list.size()>0) {
            List<Map<String, Object>> patientDiagList = new ArrayList<>();
            List<Map<String, String>> patientDiagPalList = new ArrayList<>();
            List<Map<String, String>> patientDiagOthList = new ArrayList<>();
            for (Map<String, String> diag : list) {
                if ("1".equals(diag.get("ismain"))) {
                    patientDiagPalList.add(diag);
                }else {
                    patientDiagOthList.add(diag);
                }
            }
            Map<String, Object> palMap = new HashMap<>();
            palMap.put("pal",patientDiagPalList);
            Map<String, Object> othMap = new HashMap<>();
            othMap.put("oth",patientDiagOthList);
            patientDiagList.add(palMap);
            patientDiagList.add(othMap);
            result.put("patientDiag", patientDiagList);
        }else {
            result.put("patientDiag", "");
        }
        // 诊断查询结束
        // 手术查询开始
        list.clear();
        String[] columnOper = Config.getSummaryOutpOperFields(oid).split(",");
        ResultVo<PageResultVo<Map<String, String>>> resultVo3 = hbaseQueryClient.getPageByCondition(
                PageRequestBuilder.init()
                        .tableName(HdrTableEnum.JH_SURGICAL_INFORMATION_VIEW.getCode())
                        .patientId(patientId)
                        .oid(oid)
                        .visitId(visitId)
                        .visitTypeCode(visitType)
                        .filters(filters)
                        .pageNo(0)
                        .pageSize(0)
                        .orderBy("")
                        .desc()
                        .column(columnOper)
                        .build());
        if (resultVo3.isSuccess()) {
            list = resultVo3.getContent().getResult();
        }
        List<Map<String, String>> operList = new ArrayList<>();
        if (list.size()>0) {
            for (Map<String, String> stringStringMap : list) {
                operList.add(stringStringMap);
            }
            result.put("patientOper", operList);
        }else {
            result.put("patientOper", operList);
        }
        // 手术查询结束
        // 费用查询开始
        list.clear();
        map.clear();
        String summaryOutpFeeDisplayFields = Config.getSummaryOutpFeeDisplayFields(oid);
        JSONArray jsonArray = JSONObject.parseArray(summaryOutpFeeDisplayFields);
        Map<String, String> merMap = new HashMap<>();
        for (int i = 0; i < jsonArray.size(); i++) {
            JSONArray dataJson = jsonArray.getJSONObject(i).getJSONArray("data");
            for (int i1 = 0; i1 < dataJson.size(); i1++) {
                Iterator<String> iterator = dataJson.getJSONObject(i1).keySet().iterator();
                if (iterator.hasNext()) {
                    String key = iterator.next();
                    String value = dataJson.getJSONObject(i1).getString(key);
                    merMap.put(key,value);
                }
            }
        }
        String[] columnArray2 = new ArrayList<>(merMap.values()).toArray(new String[0]);
        ResultVo<PageResultVo<Map<String, String>>> resultVo4 = hbaseQueryClient.getPageByCondition(
                PageRequestBuilder.init()
                        .tableName(HdrTableEnum.JH_COST_INFORMATION_VIEW.getCode())
                        .patientId(patientId)
                        .oid(oid)
                        .visitId(visitId)
                        .visitTypeCode(visitType)
                        .filters(filters)
                        .pageNo(0)
                        .pageSize(0)
                        .orderBy("")
                        .desc()
                        .column(columnArray2)
                        .build());
        if (resultVo4.isSuccess()) {
            list = resultVo4.getContent().getResult();
        }
        if (list.size()>0) {
            map = list.get(0);
        }
        List<Map<String, Object>> feeList = new ArrayList<>();
        for (int i = 0; i < jsonArray.size(); i++) {
            Map<String, Object> configMap = new HashMap<>();
            String name = jsonArray.getJSONObject(i).getString("name");
            configMap.put("name", name);
            JSONArray dataJson = jsonArray.getJSONObject(i).getJSONArray("data");
            Map<String, String> dataMap = new HashMap<>();
            for (int i1 = 0; i1 < dataJson.size(); i1++) {
                Iterator<String> iterator = dataJson.getJSONObject(i1).keySet().iterator();
                if (iterator.hasNext()) {
                    String key = iterator.next();
                    String value = dataJson.getJSONObject(i1).getString(key);
                    dataMap.put(key,StringUtils.isNotBlank(map.get(value)) ? map.get(value) : "");
                }
            }
            configMap.put("data",dataMap);
            feeList.add(configMap);
        }
        result.put("patientFee", feeList);
        // 费用查询结束
        return result;
    }

}
