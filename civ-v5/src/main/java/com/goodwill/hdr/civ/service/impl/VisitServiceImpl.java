package com.goodwill.hdr.civ.service.impl;

import cn.hutool.core.codec.Base64;
import cn.hutool.core.util.ArrayUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.goodwill.hdr.civ.config.Config;
import com.goodwill.hdr.civ.config.ConfigCache;
import com.goodwill.hdr.civ.entity.SysConfig;
import com.goodwill.hdr.civ.enums.HdrTableEnum;
import com.goodwill.hdr.civ.mapper.SecurityDeptUserMapper;
import com.goodwill.hdr.civ.mapper.SysConfigMapper;
import com.goodwill.hdr.civ.service.PowerService;
import com.goodwill.hdr.civ.service.VisitService;
import com.goodwill.hdr.civ.utils.CivUtils;
import com.goodwill.hdr.civ.utils.UpvUtil;
import com.goodwill.hdr.civ.utils.Utils;
import com.goodwill.hdr.civ.vo.SolrVo;
import com.goodwill.hdr.core.orm.MatchType;
import com.goodwill.hdr.hbase.bo.PropertyFilter;
import com.goodwill.hdr.hbase.dto.responseVo.PageResultVo;
import com.goodwill.hdr.hbase.dto.responseVo.ResultVo;
import com.goodwill.hdr.hbaseQueryClient.builder.PageRequestBuilder;
import com.goodwill.hdr.hbaseQueryClient.service.HbaseQueryClient;
import com.goodwill.hdr.rest.client.enums.JhdcpServerCode;
import com.goodwill.hdr.rest.client.transmission.JhdcpHttpSender;
import com.goodwill.hdr.rest.client.wrapper.JhdcpQueryWrapper;
import com.goodwill.hdr.rest.client.wrapper.imp.JhdcpQueryWrapperImp;
import com.goodwill.hdr.security.priority.vo.QueryParamVO;
import com.goodwill.hdr.security.priority.vo.SecurityDeptUserVO;
import com.goodwill.hdr.security.utils.SecurityCommonUtil;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.Map.Entry;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;


@Service
public class VisitServiceImpl implements VisitService {

    private static Logger logger = LoggerFactory.getLogger(VisitService.class);


    @Autowired
    private SysConfigMapper sysConfigMapper;


    @Autowired
    private PowerService powerService;


    @Autowired
    private SecurityDeptUserMapper securityDeptUserMapper;

    @Autowired
    JhdcpHttpSender jhdcpHttpSender;

    private final HbaseQueryClient hbaseQueryClient;


    public VisitServiceImpl(HbaseQueryClient hbaseQueryClient) {
        this.hbaseQueryClient = hbaseQueryClient;
    }

    @Override
    public Map<String, String> getInfoFromPatient(String oid, String patientId, String visitType) {
        Map<String, String> infos = new HashMap<String, String>();
        infos.put("PATIENT_ID", patientId);
        List<PropertyFilter> filters = new ArrayList<PropertyFilter>();
        if (StringUtils.isNotBlank(visitType)) {
            filters.add(new PropertyFilter("VISIT_TYPE_CODE", MatchType.EQ.getOperation(), visitType));
        }
        List<Map<String, String>> indexs = new ArrayList<>();
        //可能得到两条记录  门诊患者信息 和 住院患者信息
//        Page<Map<String, String>> indexs = hbaseDao.findPageConditionByPatient(HdrTableEnum.HDR_PATIENT.getCode(), oid,
//                patientId, page, filters, new String[]{"EID", "PERSON_NAME", "SEX_NAME", "DATE_OF_BIRTH", "INP_NO",
//                        "OUTP_NO", "IN_PATIENT_ID", "OUT_PATIENT_ID", "OID", "GLOBLE_ID", "ID_CARD_NO"});
        ResultVo<PageResultVo<Map<String, String>>> resultVo = hbaseQueryClient.getPageByCondition(
                PageRequestBuilder.init()
                        .tableName(HdrTableEnum.HDR_PATIENT.getCode())
                        .patientId(patientId)
                        .oid(oid)
                        .visitId("")
                        .visitTypeCode("02")
                        .filters(filters)
                        .pageNo(1)
                        .pageSize(10)
                        .orderBy("")
                        .desc()
                        .column("EID", "PERSON_NAME", "SEX_NAME", "DATE_OF_BIRTH", "INP_NO",
                                "OUTP_NO", "IN_PATIENT_ID", "OUT_PATIENT_ID", "OID", "GLOBLE_ID", "ID_CARD_NO")
                        .build());
        if (resultVo.isSuccess()) {
            indexs = resultVo.getContent().getResult();
        }
        System.out.println("*****查询到的患者信息条数为：" + indexs.size());
        if (indexs.size() == 0) { //未找到患者
            infos.put("STATUS", "0");
            return infos;
        }

        //20250328 增加脱敏
        List<Map<String, String>> infoHidden = powerService.getInfoHiddenByMaskRule(indexs);
        //循环遍历患者信息  记录需要的字段
        for (Map<String, String> one : infoHidden) {
            Utils.checkAndPutToMap(infos, "PERSON_NAME", one.get("PERSON_NAME"), "", false);
            Utils.checkAndPutToMap(infos, "SEX_NAME", one.get("SEX_NAME"), "", false);
            Utils.checkAndPutToMap(infos, "EID", one.get("EID"), "", false);
            Utils.checkAndPutToMap(infos, "INP_NO", one.get("INP_NO"), "住院号未知", false);
            Utils.checkAndPutToMap(infos, "OUTP_NO", one.get("OUT_NO"), "门诊号未知", false);
            Utils.checkAndPutToMap(infos, "IN_PATIENT_ID", one.get("IN_PATIENT_ID"), "", false);
            Utils.checkAndPutToMap(infos, "OUT_PATIENT_ID", one.get("OUT_PATIENT_ID"), "", false);
            Utils.checkAndPutToMap(infos, "DATE_OF_BIRTH", one.get("DATE_OF_BIRTH"), "", false);
            Utils.checkAndPutToMap(infos, "OID", one.get("OID"), "", false);
            Utils.checkAndPutToMap(infos, "ID_CARD_NO", one.get("ID_CARD_NO"), "", false);
        }
        //处理出生日期
        String birthday = infos.get("DATE_OF_BIRTH");
        if (StringUtils.isNotBlank(birthday)) {
            infos.put("DATE_OF_BIRTH", birthday);
        }
        return infos;
    }

    /**
     * outPid 的业务意义就是所有就诊坐标的集合
     *
     * @param oid
     * @param initOid
     * @param patientId 患者编号
     * @param visitType 就诊类型
     * @return
     */
    @Override
    public Map<String, Object> getOutOrInPatientId(String oid, String initOid, String patientId, String visitType, String visitDate, String visitDept, String timeStart, String timeEnd, String deptType) {
        Map<String, Object> result = new HashMap<String, Object>();

        List<PropertyFilter> filters = new ArrayList<PropertyFilter>();
        if (StringUtils.isNotBlank(visitType)) {
            filters.add(new PropertyFilter("VISIT_TYPE_CODE", MatchType.EQ.getOperation(), visitType));
        }
        ResultVo<PageResultVo<Map<String, String>>> resultVo = hbaseQueryClient.getPageByCondition(
                PageRequestBuilder.init()
                        .tableName(HdrTableEnum.HDR_PATIENT.getCode())
                        .patientId(patientId)
                        .oid(initOid)
                        .visitId("")
                        .visitTypeCode(visitType)
                        .filters(filters)
                        .pageNo(0)
                        .pageSize(0)
                        .orderBy("")
                        .desc()
                        .column("ID_CARD_NO", "EID")
                        .build());
        List<Map<String, String>> indexs = new ArrayList<>();


        if (resultVo.isSuccess()) {
            indexs = resultVo.getContent().getResult();
        }

        String idCardNo = "";
        String eid = "";

        if (indexs != null && !indexs.isEmpty()) {
            for (Map<String, String> one : indexs) {
                if (StringUtils.isNotBlank(idCardNo) && StringUtils.isNotBlank(eid)) {
                    break;
                }

                if (StringUtils.isBlank(idCardNo) && StringUtils.isNotBlank(one.get("ID_CARD_NO"))) {
                    idCardNo = one.get("ID_CARD_NO");
                }
                if (StringUtils.isBlank(eid) && StringUtils.isNotBlank(one.get("EID"))) {
                    eid = one.get("EID");
                }
            }
        }
        //获取主索引
        boolean status = false;
        Set<String> strings;
        DateTimeFormatter df2 = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        if (StringUtils.isNotBlank(visitDate)) {
            timeStart = Utils.calStartDate(visitDate);
            strings = queryPatientCoordinates(oid, idCardNo, patientId, visitType, timeStart, LocalDate.now().format(df2), visitDept, eid);
        } else {
            strings = queryPatientCoordinates(oid, idCardNo, patientId, visitType, timeStart, timeEnd, visitDept, eid);
        }

        Set<String> pids = new HashSet<>();
        Set<String> oids = new HashSet<>();
        //筛选院区数据 全部院区或者根据下拉框的某个院区返回固定院区患者数据

        for (String s : strings) {
            String[] split = s.split("\\|");
            oids.add(split[2]);
            if (!oid.equalsIgnoreCase("ALL")) {
                if (oid.equals(split[2])) {
                    pids.add(s);
                }
            } else {
                pids = strings;
            }
        }
        if (StringUtils.isNotBlank(deptType)) {
            switch (deptType) {
                case "00":
                    pids.removeIf(a -> a.split("\\|")[0].contains("02") || a.split("\\|")[0].contains("01"));
                    break;
                case "02":
                    pids.removeIf(a -> a.split("\\|")[0].contains("00") || a.split("\\|")[0].contains("01"));
                    break;
                case "01":
                    pids.removeIf(a -> a.split("\\|")[0].contains("00") || a.split("\\|")[0].contains("02"));
                    break;
            }
        }
        List<String> idList = new ArrayList<>(pids);

        idList.sort((id1, id2) -> {
            String[] split1 = id1.split("\\|");
            String time1 = "0000-00-00 00:00:00";
            if (split1.length == 5 && StringUtils.isNotBlank(split1[4])) {
                time1 = split1[4];
            }
            String[] split2 = id2.split("\\|");
            String time2 = "0000-00-00 00:00:00";
            if (split2.length == 5 && StringUtils.isNotBlank(split2[4])) {
                time2 = split2[4];
            }
            return time2.compareTo(time1);
        });

        status = true;
        result.put("status", status);
        String ids = StringUtils.join(idList.toArray(), ",");
        result.put("ids", ids);
        result.put("oids", StringUtils.join(oids.toArray(), ","));
        result.put("eid", eid);
        return result;

    }

    private Set<String> queryPatientCoordinates(String oid, String idCardNo, String patientId, String visitType, String timeStart, String timeEnd, String visitDept, String eid) {
        JhdcpQueryWrapperImp queryWrapper = new JhdcpQueryWrapperImp(JhdcpServerCode.ALL_VISIT.getCode(), Integer.valueOf(1), Integer.valueOf(10000000));
        Set<String> patientCoordinates = new HashSet<>(16);

       /* QueryParamVO queryParamVO = new QueryParamVO();
        String userCode = SecurityCommonUtil.getCurrentLoginUser().getUsercode();
        queryParamVO.setUserCode(userCode);
        List<SecurityDeptUserVO> securityDeptUserList = this.securityDeptUserMapper.queryAuthronizedDeptList(queryParamVO);
        List<String> deptList = new ArrayList<>();
        for (SecurityDeptUserVO securityDeptUserVO : securityDeptUserList)
            deptList.add(securityDeptUserVO.getDeptcode());*/

        String userCode = SecurityCommonUtil.getCurrentLoginUser().getUsercode();
        List<String> pkDeptList = securityDeptUserMapper.selectpkDeptByUserCode(userCode);
        List<Map<String, String>> deptCNList = this.securityDeptUserMapper.selectDeptByOid(oid,"'"+String.join("','",pkDeptList)+"'");
        List<String> deptList = deptCNList.stream().map(array -> array.get("deptCode")).collect(Collectors.toList());

        if (ArrayUtil.containsIgnoreCase(deptList.<CharSequence>toArray((CharSequence[]) new String[0]), "all")) {
            deptList.clear();
        } else {
            if (StringUtils.isNotBlank(visitDept) && !visitDept.equals("allDept")) {
                queryWrapper.eq("SC_VISIT_DEPT", visitDept);
            }else {
                queryWrapper.in("SC_VISIT_DEPT", deptList.<String>toArray(new String[0]));
            }
        }
        boolean vipflag = false;
        if (!Config.getCiv_Admin(oid).equals(userCode)) {
            List<Map<String, String>> vipMap = new ArrayList<>();
            vipMap = this.powerService.getVipConfigByUser(oid, userCode);
            String vip = "0";
            vip = (String) ((Map) vipMap.get(0)).get("configValue");
            if (vip.equals("1"))
                vipflag = true;
        }
        if (vipflag) {
            queryWrapper.eq("VIP_IND", "1");
        }
        if (StringUtils.isNotBlank(oid) && !"ALL".equalsIgnoreCase(oid))
            queryWrapper.eq("ORG_CODE", oid);
        if (StringUtils.isNotBlank(eid)) {
            queryWrapper.eq("EID", eid);
        } else if (StringUtils.isNotBlank(idCardNo)) {
            queryWrapper.eq("ID_CARD_NO", idCardNo);
        } else {
            queryWrapper.eq("HIS_PAT_ID", patientId);
        }
        if (StringUtils.isNotBlank(timeEnd)) {
            queryWrapper.le("VISIT_TIME", timeEnd + " 23:59:59");
        }
        if (StringUtils.isNotBlank(timeStart)) {
            queryWrapper.ge("VISIT_TIME", timeStart + " 00:00:00");
        }
        /*if (StringUtils.isNotBlank(visitDept) && !visitDept.equals("allDept")) {
            queryWrapper.eq("SC_OUT_DEPT", visitDept);
        }*/
        //添加就诊条件 eg；visit_status|in|1,2;visit_type|in|A,D
        String visitFilter = ConfigCache.getCache(oid, "CIV_VISIT_FILTER");
        if (StringUtils.isNotBlank(visitFilter)) {
            String[] arr1 = visitFilter.split(";");
            for(String str:arr1) {
                String[] arr2 = str.split("\\|");
                queryWrapper.in(arr2[0], arr2[2]);
            }
        }
        //剔除体检数据
        queryWrapper.notIn("SD_VISIT_TYPE_CODE", "03");

        String dataPageJson = this.jhdcpHttpSender.getDataPageJson((JhdcpQueryWrapper) queryWrapper);
        SolrVo solrVo = JSON.parseObject(dataPageJson, SolrVo.class);
        List<Map<String, String>> solrData = solrVo.getData();
        for (Map<String, String> doc : solrData) {
            String pid = doc.get("HIS_PAT_ID");
            idCardNo = doc.get("ID_CARD_NO");
            String oidTmp = doc.get("ORG_CODE");
            String visitId = doc.get("VISIT_NUM");
            String visitTypeCode = doc.get("SD_VISIT_TYPE_CODE");
            String visitTime = doc.get("VISIT_TIME");
            if (StringUtils.isBlank(pid) || StringUtils.isBlank(visitId) || StringUtils.isBlank(oidTmp)
                    || StringUtils.isBlank(visitTypeCode)) {
                continue;
            }
            if (StringUtils.isBlank(visitTime)) {
                visitTime = "0000-00-00 00:00:00";
            }
            /*if (visitTypeCode.equals("03")) {
                patientCoordinates.add(visitTypeCode + "|" + idCardNo + "|" + oidTmp + "|" + visitId + "|" + visitTime);
                continue;
            }*/
            patientCoordinates.add(visitTypeCode + "|" + pid + "|" + oidTmp + "|" + visitId + "|" + visitTime);
        }
        return patientCoordinates;
    }

    private String getIdCardNoOrEid(String oid, String patientId, String visitType, String type) {


        List<PropertyFilter> filters = new ArrayList<PropertyFilter>();
        if (StringUtils.isNotBlank(visitType)) {
            filters.add(new PropertyFilter("VISIT_TYPE_CODE", MatchType.EQ.getOperation(), visitType));
        }
        ResultVo<PageResultVo<Map<String, String>>> resultVo = hbaseQueryClient.getPageByCondition(
                PageRequestBuilder.init()
                        .tableName(HdrTableEnum.HDR_PATIENT.getCode())
                        .patientId(patientId)
                        .oid(oid)
                        .visitId("")
                        .visitTypeCode(visitType)
                        .filters(filters)
                        .pageNo(0)
                        .pageSize(0)
                        .orderBy("")
                        .desc()
                        .column("ID_CARD_NO", "EID")
                        .build());
        List<Map<String, String>> indexs = new ArrayList<>();
//        List<Map<String, String>> indexs = hbaseDao.findConditionByPatient(HdrTableEnum.HDR_PATIENT.getCode(), oid,
//                patientId, filters, "ID_CARD_NO","EID");

        if (resultVo.isSuccess()) {
            indexs = resultVo.getContent().getResult();
        }
        if (indexs == null || indexs.size() == 0) { //未找到患者
            return "";
        }
        String idCardNo = indexs.get(0).get("ID_CARD_NO");
        String eid = indexs.get(0).get("EID");
        if ("idCardNo".equals(type) && StringUtils.isNotBlank(idCardNo)) {
            return idCardNo;
        } else if ("eid".equals(type) && StringUtils.isNotBlank(eid)) {
            return eid;
        }
        return "";
    }


    /**
     * 将某个患者iD的诊断信息转化为map
     *
     * @param diags
     * @return
     */
    public Map<String, Object> transDiagInfoToMap(List<Map<String, String>> diags) {
        Map<String, Object> diagsMap = new HashMap<>();
        for (Map<String, String> map : diags) {
            String visitId=map.get("VISIT_ID");
            if(StringUtils.isNotBlank(visitId)){
                diagsMap.put(visitId.trim(), map);
            }
        }
        return diagsMap;
    }

    /**
     * @param visits      存储就诊列表的集合
     * @param patientId   患者编号
     * @param visitType   就诊类型
     * @param visitStatus
     * @return 返回类型： void
     * @Description 方法描述: 根据患者编号和就诊类型查询该类型下的就诊列表，并将数据填充到visits
     */
    private void getVisitsAndAddToVisits(List<Map<String, String>> visits, String oid, String patientId, String visitType, String visitId,
                                         String startDate, String endDate, String visitDept, String visitStatus) {
        List<PropertyFilter> filters = new ArrayList<PropertyFilter>();
        /* 为了提高查询效率 减少无效查询  故将改代码迁移到具体if模块中
        Map<String, Object> emrDiagsMap = new HashMap<>();
        Map<String, Object> inpDiagsMap = new HashMap<>();
        //首先获取次patientid下的所有诊断，防止循环查询诊断数据
       if ("02".equals(visitType)) {
            List<Map<String, String>> emrDiags = this.getPatMainDiagInpbyConfig(oid, patientId, "");//未出院  病历诊断
            List<Map<String, String>> inpDiags = this.getPatInpDiag(oid, patientId, visitId);//出院  病案诊断
            //处理成map格式
            emrDiagsMap = transDiagInfoToMap(emrDiags);
            inpDiagsMap = transDiagInfoToMap(inpDiags);
        } else if ("01".equals(visitType)) {
            List<Map<String, String>> outpDiags = this.getPatDiagOutp(oid, patientId, visitId, "");
            inpDiagsMap = transDiagInfoToMap(outpDiags);
        } else if ("00".equals(visitType)) {
            List<Map<String, String>> outpDiags = this.getPatDiagEmp(oid, patientId, visitId, "");
            inpDiagsMap = transDiagInfoToMap(outpDiags);
        }*/

        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        if (StringUtils.isNotBlank(endDate)) {
            LocalDate date = LocalDate.parse(endDate, formatter);
            LocalDate plusDays = date.plusDays(1);
            endDate = plusDays.format(formatter);
        }

        //住院
        if ("02".equals(visitType)) {
            //时间条件
            if (StringUtils.isNotBlank(startDate)) {
                filters.add(new PropertyFilter("ADMISSION_TIME", MatchType.GE.getOperation(), startDate + " 00:00:00"));
            }
            if (StringUtils.isNotBlank(endDate)) {
                filters.add(new PropertyFilter("ADMISSION_TIME", MatchType.LE.getOperation(), endDate + " 23:59:59"));
            }
            if (StringUtils.isNotBlank(visitStatus)) {
                filters.add(new PropertyFilter("VISIT_STATUS_CODE", MatchType.IN.getOperation(), visitStatus));
            }

            //			filters.add(new PropertyFilter("TRANS_NO", "STRING", MatchType.EQ.getOperation(), "0")); //仅取入出院，不取转科
            String civInVisitFilter= Config.getCIV_IN_VISIT_FILTER(oid);
            if (StringUtils.isNotBlank(civInVisitFilter)) {
                strToFilter(civInVisitFilter, filters, ";");
            }
            List<Map<String, String>> patAdts = new ArrayList<>();
            filters.add(new PropertyFilter("VISIT_TYPE_CODE", MatchType.EQ.getOperation(), "02"));
            ResultVo<PageResultVo<Map<String, String>>> resultVo = hbaseQueryClient.getPageByCondition(
                    PageRequestBuilder.init()
                            .tableName(HdrTableEnum.HDR_PAT_ADT.getCode())
                            .patientId(patientId)
                            .oid(oid)
                            .visitId(visitId)
                            .visitTypeCode("")
                            .filters(filters)
                            .pageNo(0)
                            .pageSize(0)
                            .orderBy("TRANS_NO")
                            .asc()
                            .column("DEPT_DISCHARGE_FROM_CODE", "DEPT_DISCHARGE_FROM_NAME", "ADMISSION_TIME", "VISIT_NO",
                                    "DISCHARGE_TIME", "VISIT_ID", "DEPT_ADMISSION_TO_NAME", "DEPT_ADMISSION_TO_CODE", "INP_NO",
                                    "IN_PATIENT_ID", "TRANS_NO", "CURR_DEPT_NAME", "CURR_DEPT_CODE", "DISTRICT_ADMISSION_TO_NAME", "CURR_DISTRICT_NAME", "VISIT_STATUS_CODE", "VISIT_STATUS_NAME", "OID", "ORG_NO", "ORG_NAME")
                            .build());

            if (resultVo.isSuccess()) {
                patAdts = resultVo.getContent().getResult();
            }
            if (patAdts.isEmpty()) {
                return;
            }
            Utils.sortListMulti(patAdts, new String[]{"TRANS_NO"}, new String[]{"asc"});
            //存在转科的只取最后一次转科信息
            patAdts = deleteMultiVisit(patAdts);
            Map<String, String> adt = patAdts.get(0);
            Map<String, String> covert = new HashMap<String, String>();
            if ("0".equals(adt.get("TRANS_NO"))) {
                covert.put("START_DEPT_NAME", adt.get("DEPT_ADMISSION_TO_NAME")); //入院科室
                covert.put("START_DEPT_CODE", adt.get("DEPT_ADMISSION_TO_CODE"));
                covert.put("DISTRICT_ADMISSION_TO_NAME", adt.get("DISTRICT_ADMISSION_TO_NAME")); //入院病区
            } else {
                covert.put("START_DEPT_NAME", adt.get("CURR_DEPT_NAME")); //当前科室
                covert.put("START_DEPT_CODE", adt.get("CURR_DEPT_CODE"));
                covert.put("DISTRICT_ADMISSION_TO_NAME", adt.get("CURR_DISTRICT_NAME")); //转科病区
            }

            if (StringUtils.isNotBlank(visitDept) && !"allDept".equalsIgnoreCase(visitDept)) {
                if (!visitDept.equals(covert.get("START_DEPT_CODE"))) {
                    return;
                }
            }

//                covert.put("END_DEPT_CODE", adt.get("DEPT_DISCHARGE_FROM_CODE")); //出院科室
//                covert.put("END_DEPT_NAME", adt.get("DEPT_DISCHARGE_FROM_NAME"));
            covert.put("OID", adt.get("OID"));

            if (StringUtils.isNotBlank(adt.get("VISIT_STATUS_NAME"))) {
                covert.put("VISIT_STATUS_NAME", adt.get("VISIT_STATUS_NAME"));
            }

            covert.put("START_TIME", adt.get("ADMISSION_TIME")); //入院时间
            covert.put("END_TIME", adt.get("DISCHARGE_TIME")); //出院时间
            covert.put("VISIT_ID", StringUtils.isNotBlank(adt.get("VISIT_ID"))?adt.get("VISIT_ID").trim():""); //就诊次数
//                covert.put("DISTRICT_ADMISSION_TO_NAME", adt.get("DISTRICT_ADMISSION_TO_NAME")); //病区

            covert.put("ORG_NO", adt.get("ORG_NO")); //医疗机构标识
            covert.put("ORG_NAME", adt.get("ORG_NAME")); //医疗机构名称
            //查询本次住院诊断  主诊断
            visitId = StringUtils.isNotBlank(adt.get("VISIT_ID"))?adt.get("VISIT_ID").trim():"";

            List<Map<String, String>> inpDiags = new ArrayList<Map<String, String>>();
            if (StringUtils.isBlank(adt.get("DISCHARGE_TIME"))) {
                List<Map<String, String>> emrDiagsList = this.getPatMainDiagInpbyConfig(oid, patientId, "");//未出院  病历诊断
                Map<String, Object> emrDiagsMap = transDiagInfoToMap(emrDiagsList);
                if (!emrDiagsMap.isEmpty()) {
                    Map<String, String> mapTmp = (Map<String, String>) emrDiagsMap.get(visitId);
                    if (null != mapTmp) {
                        inpDiags.add(mapTmp);
                    }
                }
            } else {
//					inpDiags = this.getPatInpDiag(patientId, visitId);
                List<Map<String, String>> inpDiagsList = this.getPatInpDiag(oid, patientId, visitId);//出院  病案诊断
                Map<String, Object> inpDiagsMap = transDiagInfoToMap(inpDiagsList);
                if (!inpDiagsMap.isEmpty()) {
                    Map<String, String> mapTmp = (Map<String, String>) inpDiagsMap.get(visitId);
                    if (null != mapTmp) {
                        inpDiags.add(mapTmp);
                    }
                }
            }
            if (inpDiags.size() > 0) {
                covert.put("DIAGNOSIS_CODE", inpDiags.get(0).get("DIAGNOSIS_CODE")); //诊断编码
                covert.put("DIAGNOSIS_NAME", inpDiags.get(0).get("DIAGNOSIS_NAME")); //诊断名称
            } else {
                covert.put("DIAGNOSIS_CODE", "");
                covert.put("DIAGNOSIS_NAME", "");
            }
            covert.put("INP_NO", adt.get("INP_NO"));
            covert.put("VISIT_TYPE", "INPV");
            covert.put("NOW_PATIENT", adt.get("IN_PATIENT_ID"));//区分传入的患者编号和通过EID关联出来的患者编号
            covert.put("VISIT_NO", adt.get("VISIT_NO"));
            visits.add(covert);

        } else {
            //时间条件
            if (StringUtils.isNotBlank(startDate)) {
                filters.add(new PropertyFilter("VISIT_TIME", MatchType.GE.getOperation(), startDate + " 00:00:00"));
            }
            if (StringUtils.isNotBlank(endDate)) {

                filters.add(new PropertyFilter("VISIT_TIME", MatchType.LE.getOperation(), endDate + " 23:59:59"));
            }
            if ("00".equals(visitType)) {
                filters.add(new PropertyFilter("EMERGENCY_VISIT_IND", MatchType.EQ.getOperation(), "true"));
            }
            if (StringUtils.isNotBlank(visitDept) && !"allDept".equalsIgnoreCase(visitDept)) {
                filters.add(new PropertyFilter("VISIT_DEPT_CODE", MatchType.EQ.getOperation(), visitDept));
            }
            if (StringUtils.isNotBlank(visitStatus)) {
                filters.add(new PropertyFilter("VISIT_STATUS_CODE", MatchType.IN.getOperation(), visitStatus));
            }

            String civOutVisitFilter= Config.getCIV_OUT_VISIT_FILTER(oid);
            if (StringUtils.isNotBlank(civOutVisitFilter)) {
                strToFilter(civOutVisitFilter, filters, ";");
            }
            List<Map<String, String>> outVisits = new ArrayList<>();

            ResultVo<PageResultVo<Map<String, String>>> resultVo = hbaseQueryClient.getPageByCondition(
                    PageRequestBuilder.init()
                            .tableName(HdrTableEnum.HDR_OUT_VISIT.getCode())
                            .patientId(patientId)
                            .oid(oid)
                            .visitId(visitId)
                            .visitTypeCode("01")
                            .filters(filters)
                            .pageNo(0)
                            .pageSize(0)
                            .orderBy("")
                            .desc()
                            .column("REGISTING_TIME", "VISIT_TIME", "VISIT_ID", "VISIT_DEPT_NAME",
                                    "VISIT_DEPT_CODE", "VISIT_FLAG", "OUT_PATIENT_ID", "EMERGENCY_VISIT_IND", "OUTP_NO", "VISIT_STATUS_CODE", "VISIT_STATUS_NAME", "OID", "ORG_NO", "ORG_NAME")
                            .build());
            if (resultVo.isSuccess()) {
                outVisits = resultVo.getContent().getResult();
            }
            if (outVisits.isEmpty()) {
                return;
            }
            Map<String, String> outVisit = outVisits.get(0);
            Map<String, String> covert = new HashMap<String, String>();
            if (StringUtils.isNotBlank(outVisit.get("VISIT_STATUS_NAME"))) {
                covert.put("VISIT_STATUS_NAME", outVisit.get("VISIT_STATUS_NAME"));
            }
            covert.put("START_DEPT_NAME", outVisit.get("VISIT_DEPT_NAME")); //就诊科室
            covert.put("START_DEPT_CODE", outVisit.get("VISIT_DEPT_CODE"));
            covert.put("START_TIME", outVisit.get("VISIT_TIME")); //就诊时间
            covert.put("VISIT_ID", outVisit.get("VISIT_ID").trim()); //就诊次数
            covert.put("VISIT_NO", outVisit.get("OUTP_NO")); //就诊号

            covert.put("OID", outVisit.get("OID")); //医院OID
            covert.put("ORG_NO", outVisit.get("ORG_NO")); //医疗机构标识
            covert.put("ORG_NAME", outVisit.get("ORG_NAME")); //医疗机构名称
            //查询本次门诊诊断
            visitId = outVisit.get("VISIT_ID").trim();
//				List<Map<String, String>> outpDiags = this.getPatDiagOutp(patientId, visitId, "");


            List<Map<String, String>> outpDiagsList=new ArrayList<>();
            Map<String, Object> inpDiagsMap = new HashMap<>();
            if ("01".equals(visitType)) {
                outpDiagsList = this.getPatDiagOutp(oid, patientId, visitId, "");
                inpDiagsMap = transDiagInfoToMap(outpDiagsList);
            } else if ("00".equals(visitType)) {
                outpDiagsList = this.getPatDiagEmp(oid, patientId, visitId, "");
                inpDiagsMap = transDiagInfoToMap(outpDiagsList);
            }

            List<Map<String, String>> outpDiags = new ArrayList<Map<String, String>>();
            if (!inpDiagsMap.isEmpty()) {
                Map<String, String> mapTmp = (Map<String, String>) inpDiagsMap.get(visitId);
                if (null != mapTmp) {
                    outpDiags.add(mapTmp);
                }
            }
            if (!outpDiags.isEmpty()) {
                covert.put("DIAGNOSIS_CODE", outpDiags.get(0).get("DIAGNOSIS_CODE"));
                covert.put("DIAGNOSIS_NAME", outpDiags.get(0).get("DIAGNOSIS_NAME"));
            } else {
                covert.put("DIAGNOSIS_CODE", "");
                covert.put("DIAGNOSIS_NAME", "");
            }
            //若就诊时间没有，用挂号时间代替
            String registing_time = outVisit.get("REGISTING_TIME");
            if (StringUtils.isBlank(covert.get("START_TIME"))) {
                covert.put("START_TIME", registing_time);
            }

            //区分门诊和急诊
            if ("true".equals(outVisit.get("EMERGENCY_VISIT_IND"))) {
                covert.put("VISIT_TYPE", "EMPV");
            } else {
                covert.put("VISIT_TYPE", "OUTPV");
            }
            covert.put("NOW_PATIENT", outVisit.get("OUT_PATIENT_ID"));

            visits.add(covert);
        }
    }

    /**
     * @param
     * @param
     * @param visitDept
     * @return
     * @Description 方法描述: 通过EID或PATIENT_ID查询患者所有的就诊信息，优先EID
     */
    private List<Map<String, String>> getAllVisits(List<String> pidlist, String visitDept, String startDate, String endDate, String visitStatus) {
        //就诊列表
        List<Map<String, String>> visits = new ArrayList<Map<String, String>>();
        //根据上述筛选后的患者编号和就诊类型   获取所有就诊列表
        for (String pid : pidlist) {
            String[] vtpid = pid.split("\\|");
            getVisitsAndAddToVisits(visits, vtpid[2], vtpid[1], vtpid[0], vtpid[3], startDate, endDate, visitDept, visitStatus);
        }
        return visits;
    }

    public ConcurrentHashMap<String, String> getAllPids(String idCardNo, String patientId, String oid, String visitType, String deptType, String eid, String startDate, String endDate, String visitStatus) {
        //key:就诊类型|门诊和住院患者编号    value:就诊类型
        ConcurrentHashMap<String, String> pidInpNo = new ConcurrentHashMap<String, String>();
        //获取当前用户授权科室
        /*QueryParamVO queryParamVO = new QueryParamVO();
        String userCode = SecurityCommonUtil.getCurrentLoginUser().getUsercode();
        queryParamVO.setUserCode(userCode);
        List<SecurityDeptUserVO> securityDeptUserList = securityDeptUserMapper.queryAuthronizedDeptList(queryParamVO);
        List<String> deptList = new ArrayList<>();
        for (SecurityDeptUserVO securityDeptUserVO : securityDeptUserList) {
            deptList.add(securityDeptUserVO.getDeptcode());
        }*/

        String userCode = SecurityCommonUtil.getCurrentLoginUser().getUsercode();
        List<String> pkDeptList = securityDeptUserMapper.selectpkDeptByUserCode(userCode);
        List<Map<String, String>> deptCNList = this.securityDeptUserMapper.selectDeptByOid(oid,"'"+String.join("','",pkDeptList)+"'");
        List<String> deptList = deptCNList.stream().map(array -> array.get("deptCode")).collect(Collectors.toList());
        String deptStr = StringUtils.join(deptList.toArray(), ",");

        if (ArrayUtil.containsIgnoreCase(deptList.toArray(new String[deptList.size()]), "all")) {
            deptList.clear();
            deptStr = null;
        }
        //根据主索引查询 Solr 处理门诊患者标识和住院患者标识不一致情况


        //查询中台solr

        JhdcpQueryWrapperImp queryWrapper = new JhdcpQueryWrapperImp(JhdcpServerCode.PATIENT_INFO.getCode(), 1, 100);
        if (StringUtils.isNotBlank(oid) && !"all".equalsIgnoreCase(oid)) {
            queryWrapper.eq("ORG_CODE", oid);
        }
        if (StringUtils.isNotBlank(eid) && !"-".equals(eid)) {
            queryWrapper.eq("EMPI", eid);
        } else if (StringUtils.isNotBlank(idCardNo) && !"-".equals(idCardNo)) {
            queryWrapper.eq("ID_CARD_NO", idCardNo);
        } else {
            queryWrapper.eq("HIS_PAT_ID", patientId);
        }
        String dataPageJson = jhdcpHttpSender.getDataPageJson(queryWrapper);
        SolrVo solrVo = JSON.parseObject(dataPageJson, SolrVo.class);
        List<Map<String, String>> docList = solrVo.getData();
        for (Map<String, String> doc : docList) {
            String pid = (String) doc.get("HIS_PAT_ID");
            String oidTmp = (String) doc.get("ORG_CODE");
            String inpNo = (String) doc.get("INP_NO");
            String outpNo = (String) doc.get("OUTP_NO");
            String visitId = (String) doc.get("VISIT_NUM");

            if (StringUtils.isNotBlank(pid) && StringUtils.isNotBlank(inpNo)) {
                List<PropertyFilter> filters = new ArrayList<PropertyFilter>();
                //时间条件
                if (StringUtils.isNotBlank(startDate)) {
                    filters.add(new PropertyFilter("ADMISSION_TIME", MatchType.GE.getOperation(), startDate + " 00:00:00"));
                }
                if (StringUtils.isNotBlank(endDate)) {
                    filters.add(new PropertyFilter("ADMISSION_TIME", MatchType.LE.getOperation(), endDate + " 23:59:59"));
                }
                if (StringUtils.isNotBlank(visitStatus)) {
                    filters.add(new PropertyFilter("VISIT_STATUS_CODE", MatchType.EQ.getOperation(), visitStatus));
                }
                if (StringUtils.isNotBlank(deptStr)) {
                    filters.add(new PropertyFilter("DEPT_ADMISSION_TO_CODE", MatchType.IN.getOperation(), deptStr));
                }
                //filters.add(new PropertyFilter("DEPT_ADMISSION_TO_CODE", "STRING", MatchType.IN.getOperation(), deptStr));
                List<Map<String, String>> inVisits = new ArrayList<>();
                ResultVo<PageResultVo<Map<String, String>>> resultVo = hbaseQueryClient.getPageByCondition(
                        PageRequestBuilder.init()
                                .tableName(HdrTableEnum.HDR_PAT_ADT.getCode())
                                .patientId(patientId)
                                .oid(oid)
                                .visitId(visitId)
                                .visitTypeCode("02")
                                .filters(filters)
                                .pageNo(0)
                                .pageSize(0)
                                .orderBy("")
                                .desc()
                                .column("OID", "VISIT_ID", "IN_PATIENT_ID",
                                        "OUT_PATIENT_ID", "VISIT_TYPE_CODE",
                                        "DEPT_ADMISSION_TO_CODE", "DEPT_ADMISSION_TO_NAME", "INP_NO")
                                .build());
//                    List<Map<String, String>> inVisits = hbaseDao.findConditionByPatient(
//                            HdrTableEnum.HDR_PAT_ADT.getCode(), oidTmp, pid, filters,
//                            "OID", "VISIT_ID", "IN_PATIENT_ID",
//                            "OUT_PATIENT_ID", "VISIT_TYPE_CODE",
//                            "DEPT_ADMISSION_TO_CODE", "DEPT_ADMISSION_TO_NAME", "INP_NO");
                if (resultVo.isSuccess()) {
                    inVisits = resultVo.getContent().getResult();
                }
                if (inVisits.size() > 0) {
                    for (Map<String, String> inVisit : inVisits) {
                        //String visitId = inVisit.get("VISIT_ID");
                        pidInpNo.put("02" + "|" + pid + "|" + oidTmp + "|" + visitId, "02" + "|" + pid + "|" + oidTmp + "|" + visitId);
                    }
                }
            }
            if (StringUtils.isNotBlank(pid) && StringUtils.isNotBlank(outpNo)) {
                List<PropertyFilter> filters = new ArrayList<PropertyFilter>();
                if (StringUtils.isNotBlank(deptStr)) {
                    filters.add(new PropertyFilter("VISIT_DEPT_CODE", MatchType.IN.getOperation(), deptStr));
                }
                //filters.add(new PropertyFilter("VISIT_DEPT_CODE", "STRING", MatchType.IN.getOperation(), deptStr));
                //时间条件
                if (StringUtils.isNotBlank(startDate)) {
                    filters.add(new PropertyFilter("VISIT_TIME", MatchType.GE.getOperation(), startDate + " 00:00:00"));
                }
                if (StringUtils.isNotBlank(endDate)) {

                    filters.add(new PropertyFilter("VISIT_TIME", MatchType.LE.getOperation(), endDate + " 23:59:59"));
                }
                if (StringUtils.isNotBlank(visitStatus)) {
                    filters.add(new PropertyFilter("VISIT_STATUS_CODE", MatchType.EQ.getOperation(), visitStatus));
                }
                List<Map<String, String>> outVisits = new ArrayList<>();
                ResultVo<PageResultVo<Map<String, String>>> resultVo = hbaseQueryClient.getPageByCondition(
                        PageRequestBuilder.init()
                                .tableName(HdrTableEnum.HDR_OUT_VISIT.getCode())
                                .patientId(patientId)
                                .oid(oid)
                                .visitId(visitId)
                                .visitTypeCode("01")
                                .filters(filters)
                                .pageNo(0)
                                .pageSize(0)
                                .orderBy("")
                                .desc()
                                .column("OID", "VISIT_ID", "VISIT_DEPT_NAME", "OUTP_NO", "IN_PATIENT_ID",
                                        "OUT_PATIENT_ID", "VISIT_DEPT_CODE", "VISIT_TYPE_CODE", "PERSON_NAME")
                                .build());
                if (resultVo.isSuccess()) {
                    outVisits = resultVo.getContent().getResult();
                }
//                    List<Map<String, String>> outVisits = hbaseDao.findConditionByPatient(
//                            HdrTableEnum.HDR_OUT_VISIT.getCode(), oidTmp, pid, filters,
//                            "OID", "VISIT_ID", "VISIT_DEPT_NAME", "OUTP_NO", "IN_PATIENT_ID",
//                            "OUT_PATIENT_ID", "VISIT_DEPT_CODE", "VISIT_TYPE_CODE", "PERSON_NAME");
                if (outVisits.size() > 0) {
                    for (Map<String, String> outVisit : outVisits) {
                        //String visitId = outVisit.get("VISIT_ID");
                        String visitTypeCode = outVisit.get("VISIT_TYPE_CODE");
                        pidInpNo.put(visitTypeCode + "|" + pid + "|" + oidTmp + "|" + visitId, visitTypeCode + "|" + pid + "|" + oidTmp + "|" + visitId);
                    }
                }
            }
        }


        return pidInpNo;
    }


    @Override
    public String getFinalInVisit(String oid, String patientId, String outPatientId) {
        // TODO Auto-generated method stub
        List<PropertyFilter> filters = new ArrayList<PropertyFilter>();
        //仅查询入出院记录
        filters.add(new PropertyFilter("TRANS_NO", MatchType.EQ.getOperation(), "0"));
        List<Map<String, String>> patAdts = new ArrayList<>();
//        ResultVo<PageResultVo<Map<String, String>>> resultVo = hbaseQueryClient.getPageByCondition(
//                PageRequestBuilder.init()
//                        .tableName(HdrTableEnum.HDR_PAT_ADT.getCode())
//                        .patientId(patientId)
//                        .oid(oid)
//                        .visitId(visitId)
//                        .visitTypeCode("02")
//                        .filters(filters)
//                        .pageNo(0)
//                        .pageSize(0)
//                        .orderBy("")
//                        .desc()
//                        .column("VISIT_ID", "ADMISSION_TIME", "PERSON_NAME", "SEX_NAME",
//                                "DATE_OF_BIRTH", "CURR_BED_LABEL", "CURR_DEPT_NAME", "ADMISSION_TIME", "INP_NO")
//                        .build());
////        List<Map<String, String>> patAdts = hbaseDao.findConditionByPatient(HdrTableEnum.HDR_PAT_ADT.getCode(), oid,
////                patientId, filters, new String[]{"VISIT_ID", "ADMISSION_TIME", "PERSON_NAME", "SEX_NAME",
////                        "DATE_OF_BIRTH", "CURR_BED_LABEL", "CURR_DEPT_NAME", "ADMISSION_TIME", "INP_NO"});
//        if(resultVo.isSuccess()){
//            patAdts = resultVo.getContent().getResult();
//        }
        List<String> pats = new ArrayList<String>();
        if (StringUtils.isNotBlank(outPatientId)) {
            String[] ids = outPatientId.split(",");
            for (int i = 0; i < ids.length; i++) {
                pats.add(ids[i]);
            }
        }
        for (String pat : pats) {
            String[] info = pat.split("\\|");
            String type = info[0];
            String oidTmp = info[2];
            String id = info[1];
            String visitId = info[3];
            //关联患者号查询
            List<Map<String, String>> otherPatAdts = new ArrayList<>();
            ResultVo<PageResultVo<Map<String, String>>> otherresultVo = hbaseQueryClient.getPageByCondition(
                    PageRequestBuilder.init()
                            .tableName(HdrTableEnum.HDR_PAT_ADT.getCode())
                            .patientId(id)
                            .oid(oidTmp)
                            .visitId(visitId)
                            .visitTypeCode("02")
                            .filters(filters)
                            .pageNo(0)
                            .pageSize(0)
                            .orderBy("")
                            .desc()
                            .column("VISIT_ID", "ADMISSION_TIME", "PERSON_NAME", "SEX_NAME",
                                    "DATE_OF_BIRTH", "CURR_BED_LABEL", "CURR_DEPT_NAME", "ADMISSION_TIME", "INP_NO")
                            .build());
            if (otherresultVo.isSuccess()) {
                otherPatAdts = otherresultVo.getContent().getResult();
            }
//                List<Map<String, String>> otherPatAdts = hbaseDao.findConditionByPatient(
//                        HdrTableEnum.HDR_PAT_ADT.getCode(), oid, pid, filters,
//                        new String[]{"VISIT_ID", "ADMISSION_TIME", "PERSON_NAME", "SEX_NAME", "DATE_OF_BIRTH",
//                                "CURR_BED_LABEL", "CURR_DEPT_NAME", "ADMISSION_TIME", "INP_NO"});
            //合并
            patAdts.addAll(otherPatAdts);

        }
        //筛选末次住院信息，记录必要字段
        String result = "";
        String admissionTimeString = "";
        for (Map<String, String> map : patAdts) {
            String visitId = map.get("VISIT_ID").trim() == null ? "" : map.get("VISIT_ID").trim();
            String adtString = map.get("ADMISSION_TIME") == null ? "" : map.get("ADMISSION_TIME");
            if (adtString.compareTo(admissionTimeString) > 0) {
                admissionTimeString = adtString;
                result = visitId;
            }
        }
        return result;
    }


    @Override
    public Map<String, String> getPatientInFinalVisit(String this_oid, String oid, String patientId, String outPatientId, String visitType) {
        Map<String, String> rs = new HashMap<String, String>();

        List<String> pats = new ArrayList<String>();
//        if (StringUtils.isNotBlank(this_oid) && this_oid.equals(oid) || this_oid.equals("ALL")) {
//            pats.add(visitType + "|" + patientId + "|" + oid);
//        }
        if (StringUtils.isNotBlank(outPatientId)) {
            String[] ids = outPatientId.split(",");
            for (int i = 0; i < ids.length; i++) {
                pats.add(ids[i]);
            }
        }
        List<Map<String, String>> visitList = new ArrayList<Map<String, String>>();

        for (String pat : pats) {
            String[] info = pat.split("\\|");
            String type = info[0];
            String oidTmp = info[2];
            String id = info[1];
            String visitId = info[3];
            List<PropertyFilter> filters = new ArrayList<PropertyFilter>();
            if ("02".equals(type)) {
                filters.add(new PropertyFilter("TRANS_NO", MatchType.EQ.getOperation(), "0")); //仅查询入出院记录
                //filters.add(new PropertyFilter("VISIT_ID", MatchType.EQ.getOperation(), visitId)); //仅查询入出院记录
                List<Map<String, String>> inVisits = new ArrayList<>();
                ResultVo<PageResultVo<Map<String, String>>> resultVo = hbaseQueryClient.getPageByCondition(
                        PageRequestBuilder.init()
                                .tableName(HdrTableEnum.HDR_PAT_ADT.getCode())
                                .patientId(id)
                                .oid(oid)
                                .visitId(visitId)
                                .visitTypeCode("02")
                                .filters(filters)
                                .pageNo(0)
                                .pageSize(0)
                                .orderBy("")
                                .desc()
                                .column("VISIT_ID", "ADMISSION_TIME", "PERSON_NAME", "SEX_NAME", "DATE_OF_BIRTH",
                                        "VISIT_TYPE_CODE", "CURR_BED_LABEL", "CURR_DEPT_NAME", "CURR_DEPT_CODE",
                                        "ADMISSION_TIME", "INP_NO", "OID")
                                .build());
                if (resultVo.isSuccess()) {
                    inVisits = resultVo.getContent().getResult();
                }
//                List<Map<String, String>> inVisits = hbaseDao.findConditionByPatient(
//                        HdrTableEnum.HDR_PAT_ADT.getCode(), oidTmp, id, filters,
//                        new String[]{"VISIT_ID", "ADMISSION_TIME", "PERSON_NAME", "SEX_NAME", "DATE_OF_BIRTH",
//                                "VISIT_TYPE_CODE", "CURR_BED_LABEL", "CURR_DEPT_NAME", "CURR_DEPT_CODE",
//                                "ADMISSION_TIME", "INP_NO", "OID"});
                for (Map<String, String> map : inVisits) {
                    map.put("VISIT_TYPE_CODE", "02");
                    map.put("PATIENT_ID", id);
                    visitList.add(map);
                }
            } else {
                continue;
            }
        }

        String visitTimeString = "";
        String patString = "";
        String result = "";
        String resultOid = "";
        for (Map<String, String> map : visitList) {
            String visitId = map.get("VISIT_ID").trim() == null ? "" : map.get("VISIT_ID").trim();
            String adtString = map.get("ADMISSION_TIME") == null ? "" : map.get("ADMISSION_TIME");
            if (adtString.compareTo(visitTimeString) > 0) {
                visitTimeString = adtString;
                result = visitId;
                patString = map.get("PATIENT_ID");
                resultOid = map.get("OID");
            }
        }

        if (StringUtils.isNotBlank(patString) && StringUtils.isNotBlank(result)) {
            rs.put("patientId", patString);
            rs.put("visitId", result);
            rs.put("oid", resultOid);
        }

        return rs;
    }

    /**
     * 通过pid，vid,visitType获取就诊信息
     *
     * @param patientId 患者编号
     * @param
     * @param visitType 就诊类型
     * @return
     */
    public Map<String, String> getPatientInfo(String oid, String patientId, String visitId, String visitType) {
        List<PropertyFilter> filters = new ArrayList<PropertyFilter>();
        List<Map<String, String>> visitList = new ArrayList<Map<String, String>>();
        if ("02".equals(visitType)) {
            //仅查询入出院记录
            filters.add(new PropertyFilter("TRANS_NO", MatchType.EQ.getOperation(), "0"));
            filters.add(new PropertyFilter("VISIT_TYPE_CODE", MatchType.EQ.getOperation(), visitType));
//			filters.add(new PropertyFilter("VISIT_ID", PropertyType.STRING.name(),
//					MatchType.EQ.getOperation(), visitId));
            List<Map<String, String>> inVisits = new ArrayList<>();
            ResultVo<PageResultVo<Map<String, String>>> resultVo = hbaseQueryClient.getPageByCondition(
                    PageRequestBuilder.init()
                            .tableName(HdrTableEnum.HDR_PAT_ADT.getCode())
                            .patientId(patientId)
                            .oid(oid)
                            .visitId(visitId)
                            .visitTypeCode(visitType)
                            .filters(filters)
                            .pageNo(0)
                            .pageSize(0)
                            .orderBy("")
                            .desc()
                            .column("VISIT_ID", "ADMISSION_TIME", "PERSON_NAME", "SEX_NAME", "IN_PATIENT_ID",
                                    "OUT_PATIENT_ID", "DATE_OF_BIRTH", "VISIT_TYPE_CODE", "CURR_BED_LABEL","BED_LABEL",
                                    "DEPT_ADMISSION_TO_CODE", "DEPT_ADMISSION_TO_NAME", "CURR_DEPT_NAME", "CURR_DEPT_CODE",
                                    "ADMISSION_TIME", "INP_NO", "TRANS_NO", "DISTRICT_ADMISSION_TO_NAME", "DISCHARGE_TIME", "ORG_NAME", "ORG_NO", "CASE_NO")
                            .build());
            if (resultVo.isSuccess()) {
                inVisits = resultVo.getContent().getResult();
            }
//            List<Map<String, String>> inVisits = hbaseDao.findConditionByPatientVisitId(
//                    HdrTableEnum.HDR_PAT_ADT.getCode(), oid, patientId, visitId, filters,
//                    new String[]{"VISIT_ID", "ADMISSION_TIME", "PERSON_NAME", "SEX_NAME", "IN_PATIENT_ID",
//                            "OUT_PATIENT_ID", "DATE_OF_BIRTH", "VISIT_TYPE_CODE", "CURR_BED_LABEL",
//                            "DEPT_ADMISSION_TO_CODE", "DEPT_ADMISSION_TO_NAME", "CURR_DEPT_NAME", "CURR_DEPT_CODE",
//                            "ADMISSION_TIME", "INP_NO", "TRANS_NO", "DISTRICT_ADMISSION_TO_NAME", "DISCHARGE_TIME", "ORG_NAME", "ORG_NO"});
            for (Map<String, String> map : inVisits) {
                map.put("VISIT_TYPE_CODE", "02");
                map.put("PATIENT_ID", patientId);
                visitList.add(map);
            }
        } else {

            if (StringUtils.isNotBlank(Config.getCIV_OUT_VISIT_FILTER(oid))) {
                strToFilter(Config.getCIV_OUT_VISIT_FILTER(oid), filters, ";");
            }
            List<Map<String, String>> outVisits = new ArrayList<>();
            ResultVo<PageResultVo<Map<String, String>>> resultVo = hbaseQueryClient.getPageByCondition(
                    PageRequestBuilder.init()
                            .tableName(HdrTableEnum.HDR_OUT_VISIT.getCode())
                            .patientId(patientId)
                            .oid(oid)
                            .visitId(visitId)
                            .visitTypeCode(visitType)
                            .filters(filters)
                            .pageNo(0)
                            .pageSize(0)
                            .orderBy("")
                            .desc()
                            .column("VISIT_TIME", "VISIT_ID", "VISIT_DEPT_NAME", "OUTP_NO", "IN_PATIENT_ID",
                                    "OUT_PATIENT_ID", "VISIT_DEPT_CODE", "VISIT_TYPE_CODE", "PERSON_NAME", "SEX_NAME",
                                    "DATE_OF_BIRTH", "VISIT_FLAG", "ORG_NAME", "ORG_NO")
                            .build());
            if (resultVo.isSuccess()) {
                outVisits = resultVo.getContent().getResult();
            }
//            List<Map<String, String>> outVisits = hbaseDao.findConditionByPatient(
//                    HdrTableEnum.HDR_OUT_VISIT.getCode(), oid, patientId, filters,
//                    new String[]{"VISIT_TIME", "VISIT_ID", "VISIT_DEPT_NAME", "OUTP_NO", "IN_PATIENT_ID",
//                            "OUT_PATIENT_ID", "VISIT_DEPT_CODE", "VISIT_TYPE_CODE", "PERSON_NAME", "SEX_NAME",
//                            "DATE_OF_BIRTH", "VISIT_FLAG", "ORG_NAME", "ORG_NO"});
            for (Map<String, String> map : outVisits) {
                map.put("VISIT_TYPE_CODE", "01");
                map.put("PATIENT_ID", patientId);
                visitList.add(map);
            }
        }

        //就诊去重：只留下转科情况的最后一次转科记录
        visitList = deleteMultiVisit(visitList);
        //新增脱敏
//        powerService.getInfoHidden(oid, visitList);
        String dischargeTime = "";
        Map<String, String> resMap = new HashMap<String, String>();
        for (Map<String, String> map : visitList) {
            dischargeTime = map.get("DISCHARGE_TIME");
            if ("02".equals(visitType)) {
                Utils.checkAndPutToMap(resMap, "admission_time", map.get("ADMISSION_TIME"), "-", false); //就诊时间
                Utils.checkAndPutToMap(resMap, "stay_dept_name", map.get("DEPT_ADMISSION_TO_NAME"), "-", false); //科室
                Utils.checkAndPutToMap(resMap, "district_admission_to_name", map.get("DISTRICT_ADMISSION_TO_NAME"), "-", false); //病区
                Utils.checkAndPutToMap(resMap, "patient_id", patientId, "-", false); //患者编号
                Utils.checkAndPutToMap(resMap, "inp_no", map.get("INP_NO"), "-", false); //住院号
                Utils.checkAndPutToMap(resMap, "orgName", map.get("ORG_NAME"), "-", false); //机构名称
                Utils.checkAndPutToMap(resMap, "orgNo", map.get("ORG_NO"), "-", false); //机构名称
                Utils.checkAndPutToMap(resMap, "caseNo", map.get("CASE_NO"), "-", false); //病案号
                //处理转科信息
                if ("0".equals(map.get("TRANS_NO"))) {
                    Utils.checkAndPutToMap(resMap, "stay_bed", map.get("BED_LABEL"), "-", false); //床号
                } else {
                    Utils.checkAndPutToMap(resMap, "stay_bed", map.get("CURR_BED_LABEL"), "-", false); //床号
                }
            } else {
                Utils.checkAndPutToMap(resMap, "admission_time", map.get("VISIT_TIME"), "-", false); //就诊时间
                Utils.checkAndPutToMap(resMap, "stay_dept_name", map.get("VISIT_DEPT_NAME"), "-", false); //科室
                Utils.checkAndPutToMap(resMap, "patient_id", patientId, "-", false); //患者编号
                Utils.checkAndPutToMap(resMap, "inp_no", map.get("OUTP_NO"), "-", false); //住院号
                Utils.checkAndPutToMap(resMap, "orgName", map.get("ORG_NAME"), "-", false); //机构名称
                Utils.checkAndPutToMap(resMap, "orgNo", map.get("ORG_NO"), "-", false); //机构名称
            }
        }
        //末次住院主诊断

        List<Map<String, String>> patDiagList = new ArrayList<Map<String, String>>();
        if ("02".equals(visitType)) {
            if (StringUtils.isBlank(dischargeTime)) {
                patDiagList = getPatMainDiagInpbyConfig(oid, patientId, visitId);
            } else {
                patDiagList = getPatInpDiag(oid, patientId, visitId);
            }
        } else {
            patDiagList = getPatDiagOutp(oid, patientId, visitId, "1");
        }
        if (patDiagList.size() > 0) {
            Utils.checkAndPutToMap(resMap, "diagnosis_code", patDiagList.get(0).get("DIAGNOSIS_CODE"), "-", false);
            Utils.checkAndPutToMap(resMap, "diagnosis_name", patDiagList.get(0).get("DIAGNOSIS_NAME"), "-", false);
        } else {
            resMap.put("diagnosis_code", "-");
            resMap.put("diagnosis_name", "-");
        }
        resMap.put("visit_type_code", visitType);
        return resMap;
    }

    @Override
    public Map<String, String> getCurrentPatientInfo(String oid, String ids) {
        List<Map<String, String>> visitList = new ArrayList<>();
        String[] pats = ids.split(",");
        String lastId = pats[0];
        String[] split = lastId.split("\\|");
        String visitType = split[0];
        //就诊类型为急诊在HDR_OUT_VISIT里为01，急诊标识true
        if (visitType.equals("00")) {
            visitType = "01";
        }
        String patientId = split[1];
        String lastoid = split[2];
        String visitId = split[3];
        List<PropertyFilter> filters = new ArrayList<>();
        if (visitType.equals("02")) {
            if (StringUtils.isNotBlank(Config.getCIV_IN_VISIT_FILTER(oid)))
                strToFilter(Config.getCIV_IN_VISIT_FILTER(oid), filters, ";");
            List<Map<String, String>> inVisits = new ArrayList<>();
            ResultVo<PageResultVo<Map<String, String>>> resultVo = this.hbaseQueryClient.getPageByCondition(
                    PageRequestBuilder.init()
                            .tableName(HdrTableEnum.HDR_PAT_ADT.getCode())
                            .patientId(patientId)
                            .oid(lastoid)
                            .visitId(visitId)
                            .visitTypeCode(visitType)
                            .filters(filters)
                            .pageNo(0)
                            .pageSize(0)
                            .orderBy("")
                            .desc()
                            .column(new String[]{
                                    "VISIT_ID", "ADMISSION_TIME", "PERSON_NAME", "SEX_NAME", "IN_PATIENT_ID", "OUT_PATIENT_ID", "DATE_OF_BIRTH", "VISIT_TYPE_CODE", "CURR_BED_LABEL", "DEPT_ADMISSION_TO_CODE",
                                    "DEPT_ADMISSION_TO_NAME", "CURR_DEPT_NAME", "CURR_DEPT_CODE", "BED_LABEL", "ADMISSION_TIME", "INP_NO", "TRANS_NO", "DISTRICT_ADMISSION_TO_NAME", "DISCHARGE_TIME", "ORG_NO",
                                    "ORG_NAME", "ID_CARD_NO", "EID", "CASE_NO"}).build());
            if (resultVo.isSuccess())
                inVisits = resultVo.getContent().getResult();
            if (inVisits != null && !inVisits.isEmpty())
                for (Map<String, String> map1 : inVisits) {
                    map1.put("VISIT_TYPE_CODE", "02");
                    map1.put("PATIENT_ID", patientId);
                    map1.put("OID", oid);
                    visitList.add(map1);
                }
        } else if (visitType.equals("01")) {
            if (StringUtils.isNotBlank(Config.getCIV_OUT_VISIT_FILTER(oid)))
                strToFilter(Config.getCIV_OUT_VISIT_FILTER(oid), filters, ";");
            List<Map<String, String>> outVisits = new ArrayList<>();
            ResultVo<PageResultVo<Map<String, String>>> resultVo = this.hbaseQueryClient.getPageByCondition(
                    PageRequestBuilder.init()
                            .tableName(HdrTableEnum.HDR_OUT_VISIT.getCode())
                            .patientId(patientId)
                            .oid(lastoid)
                            .visitId(visitId)
                            .visitTypeCode(visitType)
                            .filters(filters)
                            .pageNo(0)
                            .pageSize(0)
                            .orderBy("")
                            .desc()
                            .column(new String[]{
                                    "VISIT_TIME", "VISIT_ID", "VISIT_DEPT_NAME", "OUTP_NO", "IN_PATIENT_ID", "OUT_PATIENT_ID", "VISIT_DEPT_CODE", "VISIT_TYPE_CODE", "PERSON_NAME", "SEX_NAME",
                                    "DATE_OF_BIRTH", "VISIT_FLAG", "ORG_NO", "ORG_NAME", "ID_CARD_NO", "EID"}).build());
            if (resultVo.isSuccess())
                outVisits = resultVo.getContent().getResult();
            if (outVisits != null && !outVisits.isEmpty())
                for (Map<String, String> map1 : outVisits) {
                    map1.put("VISIT_TYPE_CODE", "01");
                    map1.put("PATIENT_ID", patientId);
                    map1.put("OID", oid);
                    visitList.add(map1);
                }
        }
        //统计患者就诊总次数
        Map<String, String> map = new HashMap<String, String>();
        int count = pats.length;
        map.put("visit_total", String.valueOf(count));
        //获取当前院区就诊次数
        Map<String, Long> countMap = Arrays.stream(pats).map(part -> part.split("\\|")[2]).collect(Collectors.groupingBy(s -> s, Collectors.counting()));
        long curCount = countMap.getOrDefault(oid, 0L);
        map.put("current_visit_total", String.valueOf(curCount));

        ///====
        visitList = deleteMultiVisit(visitList);
        //筛选末次住院信息，记录必要字段
        String resultString = "";
        String visitTimeString = "";
        String currBedLabelString = "";
        String currDeptNameString = "";
        String currDeptCodeString = "";
        String pearsonNameString = "";
        String sexNameString = "";
        String dateOfBirthString = "";
        String pNoString = "";
        String visitTypeCode = "";
        String districtName = "";
        String dischargeTime = "";
        String idCardNo = "";
        String oidLast = "";
        String orgNo = "";
        String orgName = "";
        String eid = "";
        String caseNo = "";
        //新增患者门诊住院无数据时patString为空问题。
        String patString = "";//patientId;
        String idCardNoEncrypt = "";
        //新增脱敏

        if (visitList != null && !visitList.isEmpty()) {
//            提前处理身份证号，提供未加密版本
            Optional<Map<String, String>> optionalMap = visitList.stream().filter(m -> StringUtils.isNotBlank(m.get("ID_CARD_NO"))).findAny();

            if (optionalMap.isPresent()) {
                idCardNo = optionalMap.get().get("ID_CARD_NO");
            }

            //List<Map<String, String>> infoHidden = powerService.getInfoHidden(visitList);
            List<Map<String, String>> infoHidden = powerService.getInfoHiddenByMaskRule(visitList);
            for (Map<String, String> maps : infoHidden) {

                String visitTtpeString = maps.get("VISIT_TYPE_CODE");
                String adtString = "";
                idCardNoEncrypt = maps.get("ID_CARD_NO");
                eid = maps.get("EID");
                if ("02".equals(visitTtpeString)) {
                    //map.get("VISIT_ID");
                    //String visitId = map.get("VISIT_ID").trim();
                    adtString = maps.get("ADMISSION_TIME") == null ? "" : maps.get("ADMISSION_TIME");
                    if (adtString.compareTo(visitTimeString) > 0) {
                        visitTypeCode = maps.get("VISIT_TYPE_CODE");
                        patString = maps.get("PATIENT_ID");
                        visitTimeString = adtString;
                        resultString = maps.get("VISIT_ID");
                        oidLast = maps.get("OID");
                        //处理一下存在转科的情况
                        if ("0".equals(maps.get("TRANS_NO"))) {
                            currDeptNameString = maps.get("DEPT_ADMISSION_TO_NAME");
                            currDeptCodeString = maps.get("DEPT_ADMISSION_TO_CODE");
                            currBedLabelString = maps.get("BED_LABEL");
                        } else {
                            currBedLabelString = maps.get("CURR_BED_LABEL");
                            currDeptNameString = maps.get("CURR_DEPT_NAME");
                            currDeptCodeString = maps.get("CURR_DEPT_CODE");
                            if (StringUtils.isBlank(currDeptNameString) && StringUtils.isBlank(currDeptCodeString)) {
                                currDeptNameString = maps.get("DEPT_ADMISSION_TO_NAME");
                                currDeptCodeString = maps.get("DEPT_ADMISSION_TO_CODE");
                                currBedLabelString = maps.get("BED_LABEL");
                            }
                        }
                        pearsonNameString = maps.get("PERSON_NAME");
                        sexNameString = maps.get("SEX_NAME");
                        dateOfBirthString = maps.get("DATE_OF_BIRTH");
                        pNoString = maps.get("INP_NO");
                        districtName = maps.get("DISTRICT_ADMISSION_TO_NAME");
                        dischargeTime = maps.get("DISCHARGE_TIME");
                        orgNo = maps.get("ORG_NO");
                        orgName = maps.get("ORG_NAME");
                        caseNo = maps.get("CASE_NO");
                    }
                } else {
                    //String visitId = map.get("VISIT_ID").trim();
//                    String adtString = map.get("VISIT_TIME") == null ? "" : map.get("VISIT_TIME");
                    adtString = maps.get("VISIT_TIME") == null ? "" : maps.get("VISIT_TIME");
                    eid = maps.get("EID");
                    if (adtString.compareTo(visitTimeString) > 0) {
                        visitTypeCode = maps.get("VISIT_TYPE_CODE");
                        patString = maps.get("PATIENT_ID");
                        visitTimeString = adtString;
                        resultString = maps.get("VISIT_ID");
                        oidLast = maps.get("OID");
                        currBedLabelString = "";
                        currDeptNameString = maps.get("VISIT_DEPT_NAME");
                        currDeptCodeString = maps.get("VISIT_DEPT_CODE");
                        pearsonNameString = maps.get("PERSON_NAME");
                        sexNameString = maps.get("SEX_NAME");
                        dateOfBirthString = maps.get("DATE_OF_BIRTH");
                        pNoString = maps.get("OUTP_NO");
                        orgNo = maps.get("ORG_NO");
                        orgName = maps.get("ORG_NAME");
                    }
                }
            }
        }

        //20250328
        Map<String, String> patientMap=getInfoFromPatient( oid,  patientId,  visitType);
        pearsonNameString=StringUtils.isBlank(pearsonNameString)?patientMap.get("PERSON_NAME"):pearsonNameString;
        sexNameString=StringUtils.isBlank(sexNameString)?patientMap.get("SEX_NAME"):sexNameString;
        dateOfBirthString=StringUtils.isBlank(dateOfBirthString)?patientMap.get("DATE_OF_BIRTH"):dateOfBirthString;
        eid=StringUtils.isBlank(eid)?patientMap.get("EID"):eid;

        Utils.checkAndPutToMap(map, "id_card_no", idCardNoEncrypt, "-", false); //身份证号
        Utils.checkAndPutToMap(map, "idCardNo", idCardNo, "-", false); //身份证号
        if ("02".equals(visitTypeCode)) {
            Utils.checkAndPutToMap(map, "eid", eid, "-", false); //就诊次数
            Utils.checkAndPutToMap(map, "visit_id", resultString, "-", false); //就诊次数
            Utils.checkAndPutToMap(map, "admission_time", visitTimeString, "-", false); //就诊时间
            Utils.checkAndPutToMap(map, "stay_bed", currBedLabelString, "-", false); //床号
            Utils.checkAndPutToMap(map, "stay_dept_name", currDeptNameString, "-", false); //科室
            Utils.checkAndPutToMap(map, "stay_dept_code", currDeptCodeString, "-", false); //科室
            Utils.checkAndPutToMap(map, "patient_id", patientId, "-", false); //患者编号
            Utils.checkAndPutToMap(map, "inp_no", pNoString, "-", false); //住院号
            Utils.checkAndPutToMap(map, "person_name", pearsonNameString, "-", false); //患者名
            Utils.checkAndPutToMap(map, "sex_name", sexNameString, "-", false); //性别
            Utils.checkAndPutToMap(map, "date_of_birth", dateOfBirthString, "-", false); //出生日期
            Utils.checkAndPutToMap(map, "visit_type_code", visitTypeCode, "-", false); //出生日期
            Utils.checkAndPutToMap(map, "district_admission_to_name", districtName, "-", false); //出生日期
            Utils.checkAndPutToMap(map, "orgNo", orgNo, "-", false); //医疗机构标识
            Utils.checkAndPutToMap(map, "orgName", orgName, "-", false); //医疗机构名称
            Utils.checkAndPutToMap(map, "oidLast", oidLast, "-", false);
            Utils.checkAndPutToMap(map, "case_no", caseNo, "-", false);
        } else {
            Utils.checkAndPutToMap(map, "eid", eid, "-", false);
            Utils.checkAndPutToMap(map, "visit_id", resultString, "-", false); //就诊次数
            Utils.checkAndPutToMap(map, "admission_time", visitTimeString, "-", false); //就诊时间
            Utils.checkAndPutToMap(map, "stay_dept_name", currDeptNameString, "-", false); //科室
            Utils.checkAndPutToMap(map, "stay_dept_code", currDeptCodeString, "-", false); //科室
            Utils.checkAndPutToMap(map, "patient_id", patientId, "-", false); //患者编号
            Utils.checkAndPutToMap(map, "inp_no", pNoString, "-", false); //住院号
            Utils.checkAndPutToMap(map, "person_name", pearsonNameString, "-", false); //患者名
            Utils.checkAndPutToMap(map, "sex_name", sexNameString, "-", false); //性别
            Utils.checkAndPutToMap(map, "date_of_birth", dateOfBirthString, "-", false); //出生日期
            Utils.checkAndPutToMap(map, "visit_type_code", visitTypeCode, "-", false); //出生日期
            Utils.checkAndPutToMap(map, "orgNo", orgNo, "-", false); //医疗机构标识
            Utils.checkAndPutToMap(map, "orgName", orgName, "-", false); //医疗机构名称
            Utils.checkAndPutToMap(map, "oidLast", oidLast, "-", false);
        }

        //末次住院主诊断

        List<Map<String, String>> patDiagList = new ArrayList<Map<String, String>>();
        if ("02".equals(visitTypeCode)) {
            if (StringUtils.isBlank(dischargeTime)) {
                patDiagList = getPatMainDiagInpbyConfig(oidLast, patString, resultString);
            } else {
                patDiagList = getPatInpDiag(oidLast, patString, resultString);
                if (patDiagList.size() == 0) {//这里再处理一下，如果出院了病案诊断表里面没有数据，再从病历诊断里面取数据，存在出院后归档前补充患者就诊信息的情况
                    patDiagList = getPatMainDiagInpbyConfig(oidLast, patString, resultString);
                }
            }
        } else {
            patDiagList = getPatDiagOutp(oidLast, patString, resultString, "1");
        }
        if (patDiagList != null && patDiagList.size() > 0) {
            Utils.checkAndPutToMap(map, "diagnosis_code", patDiagList.get(0).get("DIAGNOSIS_CODE"), "-", false);
            Utils.checkAndPutToMap(map, "diagnosis_name", patDiagList.get(0).get("DIAGNOSIS_NAME"), "-", false);
        } else {
            map.put("diagnosis_code", "-");
            map.put("diagnosis_name", "-");
        }


        //统计患者过敏次数
        long allergy = 0;
        Map<String, String> result = new HashMap<>();
        QueryWrapper<SysConfig> wrapper = new QueryWrapper<>();
        wrapper.eq("oid", oid).eq("configcode", "StartUse_Allergy");
        List<SysConfig> sysConfigs = sysConfigMapper.selectList(wrapper);
        for (SysConfig sysConfig : sysConfigs) {
            result.put("configName", sysConfig.getConfigName());//
            result.put("configCode", sysConfig.getConfigCode());//
            result.put("configValue", sysConfig.getConfigValue());//
            result.put("configDesc", sysConfig.getConfigDesc());//
            result.put("sort", sysConfig.getSort().toString());//
            break;
        }
//        Map<String, String> result = powerDao.getSysConfigByConfigCode(oid, "StartUse_Allergy");
        if ("1".equals(result.get("configValue"))) {
            List<Map<String, String>> allergyList = new ArrayList<Map<String, String>>();
            List<PropertyFilter> allergyfilters = new ArrayList<PropertyFilter>();
            List<Map<String, String>> allergys = new ArrayList<>();
            filters.add(new PropertyFilter("VISIT_TYPE_CODE", MatchType.EQ.getOperation(), visitType));
            ResultVo<PageResultVo<Map<String, String>>> resultVo = hbaseQueryClient.getPageByCondition(
                    PageRequestBuilder.init()
                            .tableName("HDR_ALLERGY")
                            .patientId(patientId)
                            .oid(oid)
                            .visitId(resultString)
                            .visitTypeCode(visitType)
                            .filters(allergyfilters)
                            .pageNo(0)
                            .pageSize(0)
                            .orderBy("")
                            .desc()
                            .column("ALLERGY_CATEGORY_CODE")
                            .build());
            if (resultVo.isSuccess()) {
                allergys = resultVo.getContent().getResult();
            }

//                List<Map<String, String>> allergys = hbaseDao.findConditionByPatient("HDR_ALLERGY", oidTemp, id, filters,
//                        "ALLERGY_CATEGORY_CODE");
            allergyList.addAll(allergys);

            if (allergyList != null && allergyList.size() > 0) {
                allergy = allergyList.size();
            }

        }
        map.put("allergy_count", String.valueOf(allergy));
        return map;
    }


    @Override
    public List<Map<String, String>> getPatMainDiagInpbyConfig(String oid, String patId, String visitId) {
        List<Map<String, String>> list = new ArrayList<Map<String, String>>();
        String tableName = "HDR_EMR_CONTENT_DIAG";
        List<PropertyFilter> filters = new ArrayList<>();
        //TODO 增加配置读取患者主诊断
        CivUtils.strToFilter(Config.getCIV_PAT_MAINDIAG_INP_FILTER(oid), filters, ";");
        ResultVo<PageResultVo<Map<String, String>>> resultVo = hbaseQueryClient.getPageByCondition(
                PageRequestBuilder.init()
                        .tableName(tableName)
                        .patientId(patId)
                        .oid(oid)
                        .visitId(visitId)
                        .visitTypeCode("")
                        .filters(filters)
                        .pageNo(0)
                        .pageSize(0)
                        .orderBy("")
                        .desc()
                        .column("DIAGNOSIS_CODE", "DIAGNOSIS_NAME", "DIAGNOSIS_TIME", "DIAGNOSIS_DOCTOR_CODE",
                                "DIAGNOSIS_DOCTOR_NAME", "DIAGNOSIS_DESC", "VISIT_ID")
                        .build());
        if (resultVo.isSuccess()) {
            list = resultVo.getContent().getResult();
        }
        return list;
    }

    public List<Map<String, String>> getPatInpDiag(String oid, String patId, String visitId) {
        List<Map<String, String>> list = new ArrayList<>();
        //获取患者住院诊断:初步诊断、主诊断
        String tableName = "HDR_INP_SUMMARY_DIAG";
        List<PropertyFilter> filters = PageRequestBuilder.FilterBuilder.init().build();
        CivUtils.strToFilter(Config.getCIV_PAT_MAINDIAG_INP_FILTER(oid), filters, ";");
//        List<Map<String, String>> list = hbaseDao.findConditionByPatientVisitId(tableName, oid, patId, visitId, filters, new String[]{
//                "DIAGNOSIS_CODE", "DIAGNOSIS_NAME", "DIAGNOSIS_TIME", "DIAGNOSIS_DESC", "DIAGNOSIS_NUM", "DIAGNOSIS_TYPE_NAME", "VISIT_ID"});
        ResultVo<PageResultVo<Map<String, String>>> resultVo = hbaseQueryClient.getPageByCondition(
                PageRequestBuilder.init()
                        .tableName(tableName)
                        .patientId(patId)
                        .oid(oid)
                        .visitId(visitId)
                        .visitTypeCode("")
                        .filters(filters)
                        .pageNo(0)
                        .pageSize(0)
                        .orderBy("")
                        .desc()
                        .column("DIAGNOSIS_CODE", "DIAGNOSIS_NAME", "DIAGNOSIS_TIME", "DIAGNOSIS_DESC", "DIAGNOSIS_NUM", "DIAGNOSIS_TYPE_NAME", "VISIT_ID")
                        .build());
        if (resultVo.isSuccess()) {
            list = resultVo.getContent().getResult();
        }
        return list;
    }

    @Override
    public List<Map<String, String>> getPatDiagOutp(String oid, String patId, String visitId, String isMain) {
        List<Map<String, String>> list = new ArrayList<Map<String, String>>();
        //过滤条件
        List<PropertyFilter> filters = new ArrayList<PropertyFilter>();
        if ("1".equals(isMain)) { //主诊断
            filters = PageRequestBuilder.FilterBuilder.init().eq("MAIN_FLAG", "1").build();
            //filters.add(new PropertyFilter("MAIN_FLAG", "STRING", MatchType.EQ.getOperation(), "1"));
        } else if ("2".equals(isMain)) { //非主诊断
            filters = PageRequestBuilder.FilterBuilder.init().notEq("MAIN_FLAG", "1").build();
            //filters.add(new PropertyFilter("MAIN_FLAG", "STRING", MatchType.NE.getOperation(), "1"));
        }
//        List<Map<String, String>> list = hbaseDao.findConditionByPatientVisitId(
//                HdrTableEnum.HDR_OUT_VISIT_DIAG.getCode(), oid, patId, visitId, filters,
//                new String[]{"DIAGNOSIS_CODE", "DIAGNOSIS_NAME", "DIAGNOSIS_TIME", "DIAGNOSIS_DOCTOR_CODE",
//                        "DIAGNOSIS_DOCTOR_NAME", "DIAGNOSIS_DESC", "VISIT_ID"});
        ResultVo<PageResultVo<Map<String, String>>> resultVo = hbaseQueryClient.getPageByCondition(
                PageRequestBuilder.init()
                        .tableName(HdrTableEnum.HDR_OUT_VISIT_DIAG.getCode())
                        .patientId(patId)
                        .oid(oid)
                        .visitId(visitId)
                        .visitTypeCode("")
                        .filters(filters)
                        .pageNo(0)
                        .pageSize(0)
                        .orderBy("DIAGNOSIS_TIME")
                        .desc()
                        .column("DIAGNOSIS_CODE", "DIAGNOSIS_NAME", "DIAGNOSIS_TIME", "DIAGNOSIS_DOCTOR_CODE",
                                "DIAGNOSIS_DOCTOR_NAME", "DIAGNOSIS_DESC", "VISIT_ID")
                        .build());
//        if (list != null && list.size() > 0) {
//            //诊断时间降序
////            Utils.sortListByDate(list, "DIAGNOSIS_TIME", Sort.DESC);
//            Utils.sortListDescByDate(list, "DIAGNOSIS_TIME");
//        }
        if (resultVo.isSuccess()) {
            list = resultVo.getContent().getResult();
        }
        return list;
    }

    @Override
    public List<Map<String, String>> getPatDiagEmp(String oid, String patId, String visitId, String isMain) {
        List<Map<String, String>> list = new ArrayList<Map<String, String>>();
        //过滤条件
        List<PropertyFilter> filters = new ArrayList<PropertyFilter>();
        if ("1".equals(isMain)) { //主诊断
            filters = PageRequestBuilder.FilterBuilder.init().eq("MAIN_FLAG", "1").build();
            //filters.add(new PropertyFilter("MAIN_FLAG", "STRING", MatchType.EQ.getOperation(), "1"));
        } else if ("2".equals(isMain)) { //非主诊断
            filters = PageRequestBuilder.FilterBuilder.init().notEq("MAIN_FLAG", "1").build();
            //filters.add(new PropertyFilter("MAIN_FLAG", "STRING", MatchType.NE.getOperation(), "1"));
        }
        //createPropertyFilter("EMERGENCY_VISIT_IND", "true", MatchType.EQ.getOperation(), filters);
        //filters.add(new PropertyFilter("EMERGENCY_VISIT_IND", "STRING", MatchType.EQ.getOperation(), "true"));
//        List<Map<String, String>> list = hbaseDao.findConditionByPatientVisitId(
//                HdrTableEnum.HDR_OUT_VISIT_DIAG.getCode(), oid, patId, visitId, filters,
//                new String[]{"DIAGNOSIS_CODE", "DIAGNOSIS_NAME", "DIAGNOSIS_TIME", "DIAGNOSIS_DOCTOR_CODE",
//                        "DIAGNOSIS_DOCTOR_NAME", "DIAGNOSIS_DESC", "VISIT_ID"});
        ResultVo<PageResultVo<Map<String, String>>> resultVo = hbaseQueryClient.getPageByCondition(
                PageRequestBuilder.init()
                        .tableName(HdrTableEnum.HDR_OUT_VISIT_DIAG.getCode())
                        .patientId(patId)
                        .oid(oid)
                        .visitId(visitId)
                        .visitTypeCode("")
                        .filters(filters)
                        .pageNo(0)
                        .pageSize(0)
                        .orderBy("DIAGNOSIS_TIME")
                        .desc()
                        .column("DIAGNOSIS_CODE", "DIAGNOSIS_NAME", "DIAGNOSIS_TIME", "DIAGNOSIS_DOCTOR_CODE",
                                "DIAGNOSIS_DOCTOR_NAME", "DIAGNOSIS_DESC", "VISIT_ID")
                        .build());
//        if (list != null && list.size() > 0) {
//            //诊断时间降序
////            Utils.sortListByDate(list, "DIAGNOSIS_TIME", Sort.DESC);
//            Utils.sortListDescByDate(list, "DIAGNOSIS_TIME");
//        }
        if (resultVo.isSuccess()) {
            list = resultVo.getContent().getResult();
        }
        return list;
    }

    /**
     * @param
     * @param visitId   就诊次数
     * @param visitType 就诊类型  01|02
     * @return
     * @Description 方法描述: 判断患者某次就诊是否有过敏反应
     */
    private Map<String, String> getAllergys(List<String> pids, String visitId, String visitType) {
        Map<String, String> res = new HashMap<String, String>();
        List<PropertyFilter> filters = new ArrayList<PropertyFilter>();
//        if (StringUtils.isNotBlank(visitId)) {
//            filters.add(new PropertyFilter("VISIT_ID", MatchType.EQ.getOperation(), visitId));
//        }
        if (StringUtils.isNotBlank(visitType)) {
            filters.add(new PropertyFilter("VISIT_TYPE_CODE", MatchType.EQ.getOperation(), visitType));
        }

        //20250704 pids根据pid、oid去重
        Set<String> uniquePids = new HashSet<>();
        for (String value: pids) {
            String[] values = value.split("\\|");
            uniquePids.add(values[1]+"|"+values[2]);
        }
        logger.info("uniquePids.........."+uniquePids);

        List<Map<String, String>> list = new ArrayList<Map<String, String>>();
        for (String patientId : uniquePids) {
            String[] split = patientId.split("\\|");
            if (StringUtils.isNotBlank(patientId)) {
                List<Map<String, String>> allergys = new ArrayList<>();
                ResultVo<PageResultVo<Map<String, String>>> resultVo = hbaseQueryClient.getPageByCondition(
                        PageRequestBuilder.init()
                                .tableName("HDR_ALLERGY")
                                .patientId(split[0])
                                .oid(split[1])
                                .visitId(visitId)
                                .visitTypeCode(visitType)
                                .filters(filters)
                                .pageNo(0)
                                .pageSize(0)
                                .orderBy("")
                                .desc()
                                .column("ALLERGY_CATEGORY_CODE", "VISIT_ID", "IN_PATIENT_ID", "OID")
                                .build());
                if (resultVo.isSuccess()) {
                    allergys = resultVo.getContent().getResult();
                }
                list.addAll(allergys);
            }
        }
        //将结果按pid+vid拼接处理
        for (Map<String, String> map : list) {
            res.put(map.get("OID") + "|" + map.get("IN_PATIENT_ID") + "|" + map.get("VISIT_ID").trim(), "true");
        }
        return res;
    }

    /**
     * 字符查询传转换为filter查询对象
     *
     * @param filterString 查询字符串 column|in|080,079,004,035,088,134
     * @param filters
     * @param strSplit     分隔每个查询条件的分隔符
     */
    private static void strToFilter(String filterString, List<PropertyFilter> filters, String strSplit) {
        //将配置字符串转换为查询
        if (StringUtils.isNotBlank(filterString)) {
            String[] filterStrings = filterString.split(strSplit);
            for (String filterItemString : filterStrings) {
                String[] tempString = filterItemString.split("\\|");
                createPropertyFilter(tempString[0], tempString[2], tempString[1], filters);
            }
        }
    }

    /**
     * 创建Filter
     *
     * @param columnName
     * @param keyword
     * @param filters
     */
    public static void createPropertyFilter(String columnName, String keyword, String MatchType,
                                            List<PropertyFilter> filters) {
        if (StringUtils.isNotBlank(keyword)) {
            PropertyFilter filter1 = new PropertyFilter();
            filter1.setMatchType(MatchType);
            filter1.setPropertyName(columnName);
            filter1.setPropertyValue(keyword);
            //filter1.setPropertyType("STRING");
            filters.add(filter1);
        }
    }

    /**
     * 存在转科的情况下去重
     *
     * @param list
     */
    public List<Map<String, String>> deleteMultiVisit(List<Map<String, String>> list) {
        Map<String, Map<String, String>> map = new HashMap<String, Map<String, String>>();
        List<Map<String, String>> restult = new ArrayList<Map<String, String>>();
        if (list != null && list.size() > 0) {
            for (Map<String, String> mapTemp : list) {
                String oid = mapTemp.get("OID");
                String vid = mapTemp.get("VISIT_ID");
                String vtypeCode = mapTemp.get("VISIT_TYPE_CODE");
                String pid = StringUtils.isNotBlank(mapTemp.get("IN_PATIENT_ID")) ? mapTemp.get("IN_PATIENT_ID")
                        : mapTemp.get("OUT_PATIENT_ID");
                if (null == map.get(oid + "|" + pid + "|" + vid + "|" + vtypeCode)) {
                    map.put(oid + "|" + pid + "|" + vid + "|" + vtypeCode, mapTemp);
                } else {
                    String transNo = mapTemp.get("TRANS_NO");
                    String transNoOld = map.get(oid + "|" + pid + "|" + vid + "|" + vtypeCode).get("TRANS_NO");
                    int trans_no = StringUtils.isNotBlank(transNo) ? Integer.parseInt(transNo) : 0;
                    int trans_no_old = StringUtils.isNotBlank(transNoOld) ? Integer.parseInt(transNoOld) : 0;
                    if (trans_no_old < trans_no) {
                        map.put(oid + "|" + pid + "|" + vid + "|" + vtypeCode, mapTemp);
                    }
                }
            }
        }
        if (map != null && map.size() > 0) {
            for (String key : map.keySet()) {
                Map<String, String> mapTemp = map.get(key);
                restult.add(mapTemp);
            }
        }

        return restult;
    }

    /**
     * 根据身份证号+患者姓名关联免登陆跳转地址
     *
     * @param id_crad_no
     * @param
     * @param
     * @return
     */
    @Override
    public Map<String, String> getPatientinfo(String oid, String id_crad_no) {
        Map<String, String> result = new HashMap<String, String>();
        //检索solr，根据身份证号关联患者末次就诊信息(缺一不可)
        if (StringUtils.isBlank(id_crad_no)) {
            result.put("status", "0");
            return result;
        }
        String url = "";


        //查询中台solr
        if (StringUtils.isNotBlank(id_crad_no)) {
            //查询基本信息solr查出患者唯一业务号
            JhdcpQueryWrapperImp queryWrapper = new JhdcpQueryWrapperImp(JhdcpServerCode.PATIENT_INFO.getCode(), 1, 10);
            queryWrapper.eq("ID_CARD_NO", id_crad_no);
            String dataPageJson = jhdcpHttpSender.getDataPageJson(queryWrapper);
            SolrVo solrVo = JSON.parseObject(dataPageJson, SolrVo.class);
            List<Map<String, String>> data = solrVo.getData();
            for (Map<String, String> map : data) {
                String hisPatId = map.get("HIS_PAT_ID");
                if (StringUtils.isBlank(hisPatId)) {
                    continue;
                }
                String inpNo = map.get("INP_NO");
                String outpNo = map.get("OUTP_NO");
                String serverCode;
                String visitType;
                if (StringUtils.isNotBlank(inpNo)) {
                    serverCode = JhdcpServerCode.ALL_VISIT.getCode();
                    visitType = "02";
                } else if (StringUtils.isNotBlank(outpNo)) {
                    serverCode = JhdcpServerCode.ALL_VISIT.getCode();
                    visitType = "01";
                } else {
                    continue;
                }
                //查询住院
                JhdcpQueryWrapperImp queryWrapperInVisit = new JhdcpQueryWrapperImp(serverCode, 1, 10);
                queryWrapperInVisit.eq("HIS_PAT_ID", hisPatId);
                dataPageJson = jhdcpHttpSender.getDataPageJson(queryWrapperInVisit);
                solrVo = JSON.parseObject(dataPageJson, SolrVo.class);
                List<Map<String, String>> docList = solrVo.getData();
                //住院有数据取住院信息
                if (docList.size() > 0) {
                    Map<String, String> docMap = docList.get(0);
                    String visitId = docMap.get("VISIT_NUM");
                    oid = docMap.get("ORG_CODE");
                    hisPatId = docMap.get("HIS_PAT_ID");
                    try {
                        url = Config.getCIV_DEFAULT_PAGE(oid) + "?visit_id=" + Base64.encode(visitId) + "&visit_type="
                                + Base64.encode(visitType) + "&patient_id=" + Base64.encode(hisPatId) + "&oid=" + Base64.encode(oid) + "&way=en";
                        result.put("status", "1");
                    } catch (Exception e) {
                        e.printStackTrace();
                        throw new RuntimeException("通过身份证号查询到的患者号,就诊次,就诊类型存在空值");
                    }
                    result.put("URL", url);
                }
            }

        }

        if (result.isEmpty()) {
            result.put("status", "0");
        }
        return result;
    }

    @Override
    public Map<String, String> getRpcUrlByInpNo(String oid, String inpNo) {

        String serverCode = JhdcpServerCode.ALL_VISIT.getCode();
        String visitType = "02";
        String cloumn = "INP_NO";
        return queryServerForUrl(oid, inpNo, serverCode, visitType, cloumn);
    }

    private Map<String, String> queryServerForUrl(String oid, String num, String serverCode, String visitType, String cloumn) {
        JhdcpQueryWrapperImp queryWrapper = new JhdcpQueryWrapperImp(serverCode, 1, 10);
        queryWrapper.eq(cloumn, num);
        String dataPageJson = jhdcpHttpSender.getDataPageJson(queryWrapper);
        SolrVo solrVo = JSON.parseObject(dataPageJson, SolrVo.class);
        List<Map<String, String>> data = solrVo.getData();
        String url = "";
        Map<String, String> result = new HashMap<>();
        if (data.size() > 0) {
            Map<String, String> docMap = data.get(0);

            String visitId = docMap.get("VISIT_NUM");
            String hisPatId = docMap.get("HIS_PAT_ID");
            logger.info("通过" + cloumn + ":" + num + "查询到patientId:" + hisPatId + ",visitId:" + visitId);
            url = Config.getCIV_DEFAULT_PAGE(oid) + "?visit_id=" + Base64.encode(visitId) + "&visit_type="
                    + Base64.encode(visitType) + "&patient_id=" + Base64.encode(hisPatId) + "&oid=" + Base64.encode(oid) + "&way=en";

        } else {
            logger.error("通过" + cloumn + ":" + num + "未查询到患者信息");
            result.put("status", "0");
        }
        result.put("URL", url);
        return result;
    }

    @Override
    public Map<String, String> getRpcUrlByVisitNo(String oid, String visitNo) {
        String serverCode = JhdcpServerCode.OUT_VISIT.getCode();
        String visitType = "01";
        String cloumn = "OUTP_NO";
        return queryServerForUrl(oid, visitNo, serverCode, visitType, cloumn);
    }

    @Override
    public Map<String, Object> getPatientVisitsList(String oid, String outPatientId, String visitDept, String deptType, String visitStatus, String timeStart, String timeEnd, String visitDate) {
        Map<String, Object> results = new HashMap<String, Object>();
        List<String> pidlist = new ArrayList<>();
        if (StringUtils.isNotBlank(outPatientId)) {
            String[] pats = outPatientId.split(",");
            pidlist.addAll(Arrays.asList(pats));
        }
        logger.info("pidlist长度:{}", pidlist.size());
        if (StringUtils.isNotBlank(deptType)) {
            switch (deptType) {
                case "00":
                    pidlist.removeIf(a -> a.split("\\|")[0].contains("02") || a.split("\\|")[0].contains("01"));
                    break;
                case "02": {
                    pidlist.removeIf(a -> a.split("\\|")[0].contains("00") || a.split("\\|")[0].contains("01"));
                    break;
                }
                case "01": {
                    pidlist.removeIf(a -> a.split("\\|")[0].contains("00") || a.split("\\|")[0].contains("02"));
                    break;
                }
            }
        }
        DateTimeFormatter df2 = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        List<Map<String, String>> visits = new ArrayList<>();
        if (StringUtils.isNotBlank(visitDate)) {
            timeStart = Utils.calStartDate(visitDate);
            visits = getAllVisits(pidlist, visitDept, timeStart, LocalDate.now().format(df2), visitStatus);
        } else {
            visits = getAllVisits(pidlist, visitDept, timeStart, timeEnd, visitStatus);
        }

        logger.info("visits长度:{}", visits.size());
        Map<String, String> allergyMap = getAllergys(pidlist, "", "");
        List<Map<String, String>> resultList = new ArrayList<>();
        for (Map<String, String> visit : visits) {
            Map<String, String> nowVisitMap = new HashMap<String, String>();
            String tmpVisitType = visit.get("VISIT_TYPE");
            if ("OUTPV".equals(tmpVisitType)) {
                nowVisitMap.put("type_code", "OUTPV");
                nowVisitMap.put("type", "门诊");
            } else if ("INPV".equals(tmpVisitType)) {
                nowVisitMap.put("type_code", "INPV");
                nowVisitMap.put("type", "住院");
            } else if ("EMPV".equals(tmpVisitType)) {
                nowVisitMap.put("type_code", "EMPV");
                nowVisitMap.put("type", "急诊");
            }
            nowVisitMap.put("allergy", allergyMap.get(visit.get("OID") + "|" + visit.get("NOW_PATIENT") + "|" + visit.get("VISIT_ID").trim()));
            String startTime = visit.get("START_TIME");
            String endTime = visit.get("END_TIME");
            if (StringUtils.isNotBlank(startTime)) {
                nowVisitMap.put("start_time", startTime);
            } else {
                nowVisitMap.put("start_time", "-");
            }
            if (StringUtils.isNotBlank(endTime)) {
                nowVisitMap.put("end_time", endTime);
            } else {
                nowVisitMap.put("end_time", "-");
            }
            Utils.checkAndPutToMap(nowVisitMap, "visit_id", visit.get("VISIT_ID").trim(), "-", false);
            Utils.checkAndPutToMap(nowVisitMap, "now_patient", visit.get("NOW_PATIENT"), "-", false);
            Utils.checkAndPutToMap(nowVisitMap, "inp_no", visit.get("INP_NO"), "-", false);
            Utils.checkAndPutToMap(nowVisitMap, "diagnosis_code", visit.get("DIAGNOSIS_CODE"), "", false);
            if (StringUtils.isNotBlank(Config.getIS_SHOW_VISIT_STATUS(oid)) && "true".equals(Config.getIS_SHOW_VISIT_STATUS(oid))
                    && StringUtils.isNotBlank(visit.get("VISIT_STATUS_NAME"))) {
                nowVisitMap.put("visit_status", visit.get("VISIT_STATUS_NAME"));
            }
            Utils.checkAndPutToMap(nowVisitMap, "diagnosis_name", visit.get("DIAGNOSIS_NAME"), "", false);
            Utils.checkAndPutToMap(nowVisitMap, "visit_no", visit.get("VISIT_NO"), "", false);
            Utils.checkAndPutToMap(nowVisitMap, "district_admission_to_name", visit.get("DISTRICT_ADMISSION_TO_NAME"), "", false);
            Utils.checkAndPutToMap(nowVisitMap, "oid", visit.get("OID"), "", false);
            Utils.checkAndPutToMap(nowVisitMap, "orgName", visit.get("ORG_NAME"), "", false);
            nowVisitMap.put("dept_code", visit.get("START_DEPT_CODE"));
            nowVisitMap.put("dept_name", visit.get("START_DEPT_NAME"));
            resultList.add(nowVisitMap);
        }
        //将上述条件筛选过的就诊数据  按时间排序
//        List<Map<String, String>> sortedVisits = UpvUtil.sortMapList(resultList, "start_time");
        List<Map<String, String>> sortedVisits = UpvUtil.sortMapList(resultList, "start_time").stream().distinct().collect(Collectors.toList());
        results.put("visit_list", sortedVisits);
        return results;
    }


    @Override
    public Map<String, Object> getPatientVisitDeptList(String oid, String ids, String visitStatus, String visitDept, String timeStart, String timeEnd, String visitDate, String patientId, String visitType) {
        //结果数据
        Map<String, Object> results = new HashMap<String, Object>();
        List<String> pidlist = new ArrayList<>();
        if (StringUtils.isNotBlank(ids)) {
            String[] pats = ids.split(",");
            pidlist.addAll(Arrays.asList(pats));
        }
        List<Map<String, String>> visits = new ArrayList<>();
        DateTimeFormatter df1 = DateTimeFormatter.ofPattern("yyyyMMdd");
        DateTimeFormatter df2 = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        if (StringUtils.isNotBlank(visitDate)) {
            timeStart = Utils.calStartDate(visitDate);
            visits = getAllVisits(pidlist, "", timeStart, LocalDate.now().format(df2), visitStatus);
            if (StringUtils.isBlank(timeStart)) {
                results.put("return_time", "-" + LocalDate.now().format(df1));
            } else {
                results.put("return_time", LocalDate.parse(timeStart, df2).format(df1) + "-" + LocalDate.now().format(df1));
            }
        } else {
            visits = getAllVisits(pidlist, "", timeStart, timeEnd, visitStatus);
            results.put("return_time", LocalDate.parse(timeStart, df2).format(df1) + "-" + LocalDate.parse(timeEnd, df2).format(df1));
        }
        //科室信息   科室编码->科室名称
        Map<String, String> deptMap = new HashMap<String, String>();

        for (Map<String, String> visit : visits) {
            //默认取出入院科室
            String deptName = visit.get("START_DEPT_NAME");
            String deptCode = visit.get("START_DEPT_CODE");

            if (StringUtils.isNotBlank(deptCode) && StringUtils.isNotBlank(deptName)) {
                //保存科室  去除重复
                if (!deptMap.containsKey(deptCode)) {
                    deptMap.put(deptCode, deptName);
                }
            }

        }


        //科室列表
        List<Map<String, String>> deptList = new LinkedList<Map<String, String>>();
        //遍历科室  统计各科室的就诊次数
        int tm = 0; //门诊总次数
        int tz = 0; //住院总次数
        int tj = 0; //急诊总次数
        int i = 0; //计数器，用于科室排序
        for (Entry<String, String> entry : deptMap.entrySet()) {
            i++;
            Map<String, String> map = new HashMap<String, String>();
            //标记当前选中的科室
            if (entry.getKey().equals(visitDept)) {
                map.put("now_dept", "true");
            }
            map.put("name", entry.getValue()); //科室名
            map.put("code", entry.getKey()); //科室编码
            //统计属于当前科室的  门诊 住院  急诊次数
            int m = 0; //门诊次数
            int z = 0; //住院次数
            int j = 0; //急诊次数
            for (Map<String, String> v : visits) {
                String deptCode = v.get("START_DEPT_CODE");
                String tmpVisitType = v.get("VISIT_TYPE");
                if (StringUtils.isNotBlank(deptCode) && deptCode.equals(entry.getKey())) {
                    if ("OUTPV".equals(tmpVisitType)) {
                        m++;
                    } else if ("INPV".equals(tmpVisitType)) {
                        z++;
                    } else if ("EMPV".equals(tmpVisitType)) {
                        j++;
                    }
                }
            }
            tm = tm + m;
            tz = tz + z;
            tj = tj + j;
            map.put("outpv", String.valueOf(m)); //门诊
            map.put("inpv", String.valueOf(z)); //住院
            map.put("emv", String.valueOf(j)); //急诊
            map.put("no", String.valueOf(i)); //序号
            if (!deptList.contains(map)) {
                deptList.add(map);
            }
        }
        //全部科室就诊次统计
        Map<String, String> tmap = new HashMap<String, String>();
        tmap.put("name", "全部科室");
        tmap.put("code", "allDept");
        tmap.put("outpv", String.valueOf(tm));
        tmap.put("inpv", String.valueOf(tz));
        tmap.put("emv", String.valueOf(tj));
        tmap.put("no", String.valueOf(deptMap.size() + 1));
        if ("allDept".equals(visitDept)) {
            tmap.put("now_dept", "true");
        }
        //序号降序排列，保证全部科室始终位于第一位
        Utils.sortListMulti(deptList, new String[]{"no"}, new String[]{"desc"});
        List<Map<String, String>> deList = new ArrayList<Map<String, String>>();
        deList.add(tmap);
        deList.addAll(deptList);
        //保存科室列表
        results.put("dept_list", deList);
        return results;
    }

}
