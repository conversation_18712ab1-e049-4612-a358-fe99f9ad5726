package com.goodwill.hdr.civ.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.goodwill.hdr.civ.entity.HdrEmrDgHtml;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @description 针对表【hdr_emr_dg_html】的数据库操作Mapper
 * @createDate 2022-03-17 14:34:06
 * @Entity com.goodwill.hdr.civ.entity.HdrEmrDgHtml
 */
@Mapper
public interface HdrEmrDgHtmlMapper extends BaseMapper<HdrEmrDgHtml> {
    List<Map<String, String>> getEmrDgHtml(@Param("mrClassCode") String mrClassCode);
}




