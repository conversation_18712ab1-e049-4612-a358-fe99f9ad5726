package com.goodwill.hdr.civ.dao;

import com.goodwill.hdr.civ.entity.LogRecord;

import java.util.List;
import java.util.Map;


public interface LogMonitorDao {
    /**
     * @param logRecord
     * @return
     * @Description 保存日志监控数据
     */
    int insertMonitorLog(LogRecord logRecord);

    /**
     * @return
     * @Description 查询日志监控数据
     */
    public List<Map<String, String>> queryMonitorLog(String oid, String deptCode, String userCode,
                                                     String visitPageCode, String beginDate, String endDate);

    /**
     * @param oid
     * @param userName
     * @param deptCode
     * @return
     * @Description 获取用户（医生）列表
     */
    public List<Map<String, String>> queryDoctorsByDept(String oid, String userName, String deptCode);

    /**
     * 查询所有监控日志
     *
     * @return
     */
    public List<Map<String, Object>> queryAllMonitorLog();

    /**
     * @param oid
     * @param deptCode
     * @param beginDate
     * @param endDate
     * @return
     */
    public List<Long> getClassifyMonitorLog(String oid, String deptCode, String beginDate, String endDate, String flag, String pageCode, List<String> xList);

    /**
     * @param oid
     * @param flag
     * @param deptCode
     * @param userCode
     * @param days
     * @return
     */
    public List<Long> getClassifyByTimeMonitorLog(String oid, String flag, String deptCode, String userCode, String days, String dateNow, String pageCode, List<String> list);

    /**
     * 扇形图
     *
     * @return
     */
    public List<Map<String, Object>> getSectorData();

    /**
     * top 5
     *
     * @param groupField
     * @return
     */
    public List<Map<String, Object>> getTopFiveLogData(String groupField, String groupName, String pageCodeField);

//    /**
//     * 获取医生
//     * @return
//     */
//    public  List<String> getSecurityUser();
//
//    /**
//     * 获取科室
//     * @return
//     */
//    public  List<String> getDept();
}
