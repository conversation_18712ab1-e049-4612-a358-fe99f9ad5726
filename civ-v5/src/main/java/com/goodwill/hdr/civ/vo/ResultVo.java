package com.goodwill.hdr.civ.vo;

public class ResultVo<T> {
    private int code;
    private String msg;
    private T content;

    public ResultVo() {
    }

    private ResultVo(int code, String msg, T content) {
        this.code = code;
        this.msg = msg;
        this.content = content;
    }

    public static <T> ResultVo<T> success(T content) {
        return new ResultVo<>(1, "", content);
    }

    public static <T> ResultVo<T> error(String msg, T defaultContent) {
        return new ResultVo<>(0, msg, defaultContent);
    }

    public static <T> ResultVo<T> error(String msg) {
        return new ResultVo<>(0, msg, null);
    }

    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    public T getContent() {
        return content;
    }

    public void setContent(T content) {
        this.content = content;
    }
}
