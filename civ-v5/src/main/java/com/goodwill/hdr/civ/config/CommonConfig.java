package com.goodwill.hdr.civ.config;

import org.apache.commons.lang.StringUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Description 类描述：
 * @modify 修改记录：
 */
public class CommonConfig {
    //配置文件
//    private static final String FILE_NAME = "common-config.properties";
//    private static final String project_site = ConfigCache.getCache(oid,"PROJECT_SITE");

    public static String getProject_site(String oid) {
        return ConfigCache.getCache(oid, "PROJECT_SITE");
    }

    public static String getLinkType(String oid, String sysCode) {
        String linkType = ConfigCache.getCache(oid, ConfigCache.getCache(oid, "PROJECT_SITE") + "_" + sysCode + "_LINKTYPE");
        return StringUtils.isNotBlank(linkType) ? linkType : "";
    }

    private static String getParam_num(String oid, String sysCode) {
        String param_num = ConfigCache.getCache(oid, ConfigCache.getCache(oid, "PROJECT_SITE") + "_" + sysCode + "_PARAM_NUM");
        return StringUtils.isNotBlank(param_num) ? param_num : "0";
    }

    public static String getURL(String oid, String sysCode) {
        String URL = ConfigCache.getCache(oid, ConfigCache.getCache(oid, "PROJECT_SITE") + "_" + sysCode + "_URL");
        return StringUtils.isNotBlank(URL) ? URL : "";
    }

    public static List<String> getParams(String oid, String sysCode) {
        List<String> params = new ArrayList<String>();
        if (StringUtils.isNotBlank(ConfigCache.getCache(oid, "PROJECT_SITE"))) {
            if (StringUtils.isNotBlank(getParam_num(oid, sysCode)) && (!"0".equals(getParam_num(oid, sysCode)))) {
                for (int i = 1; i <= Integer.valueOf(getParam_num(oid, sysCode)); i++) {
                    String field = ConfigCache.getCache(oid, ConfigCache.getCache(oid, "PROJECT_SITE") + "_" + sysCode + "_PARAMETER_FIELD" + i);
                    params.add(field.trim());
                }
            }
        }
        return params;
    }

    public static Map<String, String> getParam_configs(String oid, String sysCode) {
        Map<String, String> param_configs = new HashMap<String, String>();
        if (StringUtils.isNotBlank(ConfigCache.getCache(oid, "PROJECT_SITE"))) {
            for (int i = 1; i <= Integer.valueOf(getParam_num(oid, sysCode)); i++) {
                String field = ConfigCache.getCache(oid, ConfigCache.getCache(oid, "PROJECT_SITE") + "_" + sysCode + "_PARAMETER_FIELD" + i);
                String config = ConfigCache.getCache(oid, ConfigCache.getCache(oid, "PROJECT_SITE") + "_" + sysCode + "_PARAMETER_FIELD_CONFIG" + i);
                param_configs.put(field.trim(), config.trim());
            }
        }
        return param_configs;
    }

}
