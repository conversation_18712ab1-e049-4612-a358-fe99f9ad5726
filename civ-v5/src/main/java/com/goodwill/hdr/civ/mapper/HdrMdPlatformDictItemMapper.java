package com.goodwill.hdr.civ.mapper;

import com.goodwill.hdr.civ.entity.HdrMdPlatformDictItem;
import com.goodwill.hdr.web.core.mapper.HdrBaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;

import java.util.List;

@Mapper
public interface HdrMdPlatformDictItemMapper extends HdrBaseMapper<HdrMdPlatformDictItem> {

    @Select("select match_codes from hdr_md_platform_dict_item where dict_code = 'DrugGroup' and data_status='ENB' and code in($code)")
    List<Object> getDrugInCodes(String code);

    @Select("select match_codes from hdr_md_platform_dict_item where dict_code = 'DrugGroup' and data_status='ENB' and code=#{code}")
    List<Object> getDrugEqCodes(String code);
}
