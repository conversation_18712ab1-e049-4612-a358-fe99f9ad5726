package com.goodwill.hdr.civ.vo;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.goodwill.hdr.civ.config.ConfigCache;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.util.*;

/**
 * 医嘱显示列默认配置
 */
@Component
public class OrderShowConfigVo implements ApplicationContextAware {
    private static final Logger log = LoggerFactory.getLogger(OrderShowConfigVo.class);

    private static final List<Map<String, Object>> all = new ArrayList<>();
    private static final List<Map<String, Object>> kf = new ArrayList<>();
    private static final List<Map<String, Object>> jm = new ArrayList<>();
    private static final List<Map<String, Object>> qt = new ArrayList<>();
    private static final List<Map<String, Object>> lab = new ArrayList<>();
    private static final List<Map<String, Object>> exam = new ArrayList<>();
    private static final List<Map<String, Object>> oper = new ArrayList<>();
    private static final List<Map<String, Object>> other = new ArrayList<>();

    private static final List<Map<String, Object>> currentJm = new ArrayList<>();
    private static final List<Map<String, Object>> currentKf = new ArrayList<>();

    private static ObjectMapper objectMapper;


    static {
        paddingList(all, "医嘱性质", "orderPropertiesName", "ORDER_PROPERTIES_NAME");
        paddingList(all, "医嘱名称", "orderItemName", "ORDER_ITEM_NAME");
        paddingList(all, "开立时间", "orderTime", "ORDER_TIME", "false", "true");
        paddingList(all, "医嘱编号", "orderNo", "ORDER_NO");
        paddingList(all, "医嘱类别", "orderClassName", "ORDER_CLASS_NAME");
        paddingList(all, "医嘱状态", "orderStatus", "ORDER_STATUS_NAME");
        paddingList(all, "就诊类型", "visitType", "VISIT_TYPE_CODE", "true");
    }

    static {
        paddingList(kf, "医嘱性质", "orderPropertiesName", "ORDER_PROPERTIES_NAME");
        paddingList(kf, "医嘱名称", "orderItemName", "ORDER_ITEM_NAME");
        paddingList(kf, "剂量", "dosageValue", "TOTAL_DOSAGE_VALUE");
        paddingList(kf, "单位", "dosageUnit", "TOTAL_DOSAGE_UNIT");
        paddingList(kf, "频率", "frequencyName", "FREQUENCY_NAME");
        paddingList(kf, "医嘱类型", "orderClassName", "ORDER_CLASS_NAME");
        paddingList(kf, "开立人", "orderDoctorName", "ORDER_DOCTOR_NAME");
        paddingList(kf, "开立时间", "orderTime", "ORDER_TIME", "false", "true");
        paddingList(kf, "医嘱编号", "orderNo", "ORDER_NO");
        paddingList(kf, "医嘱状态", "orderStatus", "ORDER_STATUS_NAME");
        paddingList(kf, "就诊类型", "visitType", "VISIT_TYPE_CODE", "true");
    }

    static {
        paddingList(jm, "组标志", "parentOrderNo", "PARENT_ORDER_NO");
        paddingList(jm, "医嘱性质", "orderPropertiesName", "ORDER_PROPERTIES_NAME");
        paddingList(jm, "剂量", "dosageValue", "TOTAL_DOSAGE_VALUE");
        paddingList(jm, "单位", "dosageUnit", "TOTAL_DOSAGE_UNIT");
        paddingList(jm, "频率", "frequencyName", "FREQUENCY_NAME");
        paddingList(jm, "医嘱类型", "orderClassName", "ORDER_CLASS_NAME");
        paddingList(jm, "开立人", "orderDoctorName", "ORDER_DOCTOR_NAME");
        paddingList(jm, "开立时间", "orderTime", "ORDER_TIME", "false", "true");
        paddingList(jm, "医嘱编号", "orderNo", "ORDER_NO");
        paddingList(jm, "医嘱状态", "orderStatus", "ORDER_STATUS_NAME");
        paddingList(jm, "就诊类型", "visitType", "VISIT_TYPE_CODE", "true");
    }

    static {
        paddingList(qt, "医嘱性质", "orderPropertiesName", "ORDER_PROPERTIES_NAME");
        paddingList(qt, "医嘱名称", "orderItemName", "ORDER_ITEM_NAME");
        paddingList(qt, "剂量", "dosageValue", "TOTAL_DOSAGE_VALUE");
        paddingList(qt, "单位", "dosageUnit", "TOTAL_DOSAGE_UNIT");
        paddingList(qt, "频率", "frequencyName", "FREQUENCY_NAME");
        paddingList(qt, "医嘱类型", "orderClassName", "ORDER_CLASS_NAME");
        paddingList(qt, "开立人", "orderDoctorName", "ORDER_DOCTOR_NAME");
        paddingList(qt, "开立时间", "orderTime", "ORDER_TIME", "false", "true");
        paddingList(qt, "医嘱编号", "orderNo", "ORDER_NO");
        paddingList(qt, "医嘱状态", "orderStatus", "ORDER_STATUS_NAME");
        paddingList(qt, "就诊类型", "visitType", "VISIT_TYPE_CODE", "true");
    }

    static {
        paddingList(lab, "医嘱性质", "orderPropertiesName", "ORDER_PROPERTIES_NAME");
        paddingList(lab, "医嘱名称", "orderItemName", "ORDER_ITEM_NAME");
        paddingList(lab, "医嘱类型", "orderClassName", "ORDER_CLASS_NAME");
        paddingList(lab, "开立人", "orderDoctorName", "ORDER_DOCTOR_NAME");
        paddingList(lab, "开立时间", "orderTime", "ORDER_TIME", "false", "true");
        paddingList(lab, "医嘱编号", "orderNo", "ORDER_NO");
        paddingList(lab, "当前状态", "reportStatus", "REPORT_STATUS");
        paddingList(lab, "就诊类型", "visitType", "VISIT_TYPE_CODE", "true");
        paddingList(lab, "报告状态", "reportStatus", "REPORT_STATUS");
        paddingList(jm, "就诊类型", "visitType", "VISIT_TYPE_CODE", "true");
    }

    static {
        paddingList(exam, "医嘱性质", "orderPropertiesName", "ORDER_PROPERTIES_NAME");
        paddingList(exam, "医嘱名称", "orderItemName", "ORDER_ITEM_NAME");
        paddingList(exam, "医嘱类型", "orderClassName", "ORDER_CLASS_NAME");
        paddingList(exam, "开立人", "orderDoctorName", "ORDER_DOCTOR_NAME");
        paddingList(exam, "开立时间", "orderTime", "ORDER_TIME", "false", "true");
        paddingList(exam, "医嘱编号", "orderNo", "ORDER_NO");
        paddingList(exam, "当前状态", "reportStatus", "REPORT_STATUS");
        paddingList(exam, "就诊类型", "visitType", "VISIT_TYPE_CODE", "true");
        paddingList(exam, "报告状态", "reportStatus", "REPORT_STATUS");
        paddingList(jm, "就诊类型", "visitType", "VISIT_TYPE_CODE", "true");
    }

    static {
        paddingList(oper, "手术名称", "operationName", "OPERATION_NAME");
        paddingList(oper, "开立人", "orderDoctorName", "ORDER_DOCTOR_NAME");
        paddingList(oper, "开立时间", "orderTime", "ORDER_TIME", "false", "true");
        paddingList(oper, "术前诊断", "diagBeforeOperationName", "DIAG_BEFORE_OPERATION_NAME");
        paddingList(oper, "施术者", "planOperDoctorName", "PLAN_OPER_DOCTOR_NAME");
        paddingList(oper, "手术日期", "applyOperTime", "PLAN_OPER_TIME");
        paddingList(oper, "医嘱编号", "orderNo", "ORDER_NO");
        paddingList(jm, "就诊类型", "visitType", "VISIT_TYPE_CODE", "true");

    }

    static {
        paddingList(other, "医嘱性质", "orderPropertiesName", "ORDER_PROPERTIES_NAME");
        paddingList(other, "医嘱名称", "orderItemName", "ORDER_ITEM_NAME");
        paddingList(other, "开立时间", "orderTime", "ORDER_TIME", "false", "true");
        paddingList(other, "频率", "frequencyName", "FREQUENCY_NAME");
        paddingList(other, "医嘱编号", "orderNo", "ORDER_NO");
        paddingList(other, "医嘱类别", "orderClassName", "ORDER_CLASS_NAME");
        paddingList(other, "医嘱状态", "orderStatus", "ORDER_STATUS_NAME");
        paddingList(jm, "就诊类型", "visitType", "VISIT_TYPE_CODE", "true");
    }

    static {
        paddingList(currentKf, "医嘱性质", "orderPropertiesName", "ORDER_PROPERTIES_NAME");
        paddingList(currentKf, "医嘱名称", "orderItemName", "ORDER_ITEM_NAME");
        paddingList(currentKf, "频率", "frequencyName", "FREQUENCY_NAME");
        paddingList(currentKf, "开立人", "orderDoctorName", "ORDER_DOCTOR_NAME");
        paddingList(currentKf, "开立时间", "orderTime", "ORDER_TIME", "false", "true");
        paddingList(currentKf, "医嘱状态", "orderStatus", "ORDER_STATUS_NAME");
        paddingList(currentKf, "医嘱编号", "orderNo", "ORDER_NO");
        paddingList(jm, "就诊类型", "visitType", "VISIT_TYPE_CODE", "true");
    }

    static {
        paddingList(currentJm, "医嘱性质", "orderPropertiesName", "ORDER_PROPERTIES_NAME");
        paddingList(currentJm, "医嘱名称", "orderItemName", "ORDER_ITEM_NAME");
        paddingList(currentJm, "频率", "frequencyName", "FREQUENCY_NAME");
        paddingList(currentJm, "开立人", "orderDoctorName", "ORDER_DOCTOR_NAME");
        paddingList(currentJm, "开立时间", "orderTime", "ORDER_TIME", "false", "true");
        paddingList(currentJm, "医嘱状态", "orderStatus", "ORDER_STATUS_NAME");
        paddingList(currentJm, "医嘱编号", "orderNo", "ORDER_NO");
        paddingList(jm, "就诊类型", "visitType", "VISIT_TYPE_CODE", "true");
    }


    private static void paddingList(List<Map<String, Object>> list, String display, String name, String field) {

        paddingList(list, display, name, field, "", "");

    }

    private static void paddingList(List<Map<String, Object>> list, String display, String name, String field, String hidden) {

        paddingList(list, display, name, field, hidden, "");

    }

    private static void paddingList(List<Map<String, Object>> list, String display, String name, String field, String hidden, String order) {

        Map<String, Object> tmpMap = new HashMap<>();
        tmpMap.put("display", display);
        tmpMap.put("name", name);
        tmpMap.put("field", field);
        if (StringUtils.isNotBlank(hidden)) {
            tmpMap.put("hidden", Boolean.valueOf(hidden));
        }
        if (StringUtils.isNotBlank(order)) {
            tmpMap.put("order", Boolean.valueOf(order));
        }
        list.add(tmpMap);

    }

    public static List<Map<String, Object>> getAllShowConfig(String oid) {
        String allShowConfig = ConfigCache.getCache(oid, "ORDER_ALL_SHOW_CONFIG");
        return readJson(allShowConfig).orElse(all);
    }

    public static List<Map<String, Object>> getKfShowConfig(String oid) {
        String allShowConfig = ConfigCache.getCache(oid, "ORDER_KF_SHOW_CONFIG");
        return readJson(allShowConfig).orElse(kf);
    }

    public static List<Map<String, Object>> getJmShowConfig(String oid) {
        String allShowConfig = ConfigCache.getCache(oid, "ORDER_JM_SHOW_CONFIG");
        return readJson(allShowConfig).orElse(jm);
    }

    public static List<Map<String, Object>> getQtShowConfig(String oid) {
        String allShowConfig = ConfigCache.getCache(oid, "ORDER_QT_SHOW_CONFIG");
        return readJson(allShowConfig).orElse(qt);
    }

    public static List<Map<String, Object>> getLabShowConfig(String oid) {
        String allShowConfig = ConfigCache.getCache(oid, "ORDER_LAB_SHOW_CONFIG");
        return readJson(allShowConfig).orElse(lab);
    }

    public static List<Map<String, Object>> getExamShowConfig(String oid) {
        String allShowConfig = ConfigCache.getCache(oid, "ORDER_EXAM_SHOW_CONFIG");
        return readJson(allShowConfig).orElse(exam);
    }

    public static List<Map<String, Object>> getOperShowConfig(String oid) {
        String allShowConfig = ConfigCache.getCache(oid, "ORDER_OPER_SHOW_CONFIG");
        return readJson(allShowConfig).orElse(oper);
    }

    public static List<Map<String, Object>> getOtherShowConfig(String oid) {
        String allShowConfig = ConfigCache.getCache(oid, "ORDER_OTHER_SHOW_CONFIG");
        return readJson(allShowConfig).orElse(other);
    }

    public static List<Map<String, Object>> getCurrentJmShowConfig(String oid) {
        String allShowConfig = ConfigCache.getCache(oid, "ORDER_CURRENT_JM_SHOW_CONFIG");
        return readJson(allShowConfig).orElse(currentJm);
    }

    public static List<Map<String, Object>> getCurrentKfShowConfig(String oid) {
        String allShowConfig = ConfigCache.getCache(oid, "ORDER_CURRENT_KF_SHOW_CONFIG");
        return readJson(allShowConfig).orElse(currentKf);
    }

    private static Optional<List<Map<String, Object>>> readJson(String showConfig) {
        List<Map<String, Object>> jsonList = null;
        if (StringUtils.isNotBlank(showConfig)) {
            try {
                jsonList = objectMapper.readValue(showConfig, new TypeReference<List<Map<String, Object>>>() {
                });
            } catch (IOException e) {
                log.error("解析{}失败", showConfig);
            }
        }
        return Optional.ofNullable(jsonList);
    }

    public static ResultVo<List<Map<String, Object>>> buildAllShowConfigResultVo(String oid) {

        String allShowConfig = ConfigCache.getCache(oid, "ORDER_ALL_SHOW_CONFIG");
        return buildResultVo("ORDER_ALL_SHOW_CONFIG", allShowConfig, all);
    }

    public static ResultVo<List<Map<String, Object>>> buildKfShowConfigResultVo(String oid) {
        String allShowConfig = ConfigCache.getCache(oid, "ORDER_KF_SHOW_CONFIG");
        return buildResultVo("ORDER_KF_SHOW_CONFIG", allShowConfig, kf);
    }

    public static ResultVo<List<Map<String, Object>>> buildJmShowConfigResultVo(String oid) {
        String allShowConfig = ConfigCache.getCache(oid, "ORDER_JM_SHOW_CONFIG");
        return buildResultVo("ORDER_JM_SHOW_CONFIG", allShowConfig, jm);
    }

    public static ResultVo<List<Map<String, Object>>> buildQtShowConfigResultVo(String oid) {
        String allShowConfig = ConfigCache.getCache(oid, "ORDER_QT_SHOW_CONFIG");
        return buildResultVo("ORDER_QT_SHOW_CONFIG", allShowConfig, qt);
    }

    public static ResultVo<List<Map<String, Object>>> buildLabShowConfigResultVo(String oid) {
        String allShowConfig = ConfigCache.getCache(oid, "ORDER_LAB_SHOW_CONFIG");
        return buildResultVo("ORDER_LAB_SHOW_CONFIG", allShowConfig, lab);
    }

    public static ResultVo<List<Map<String, Object>>> buildExamShowConfigResultVo(String oid) {
        String allShowConfig = ConfigCache.getCache(oid, "ORDER_EXAM_SHOW_CONFIG");
        return buildResultVo("ORDER_EXAM_SHOW_CONFIG", allShowConfig, exam);
    }

    public static ResultVo<List<Map<String, Object>>> buildOperShowConfigResultVo(String oid) {
        String allShowConfig = ConfigCache.getCache(oid, "ORDER_OPER_SHOW_CONFIG");
        return buildResultVo("ORDER_OPER_SHOW_CONFIG", allShowConfig, oper);
    }

    public static ResultVo<List<Map<String, Object>>> buildOtherShowConfigResultVo(String oid) {
        String allShowConfig = ConfigCache.getCache(oid, "ORDER_OTHER_SHOW_CONFIG");
        return buildResultVo("ORDER_OTHER_SHOW_CONFIG", allShowConfig, other);
    }


    private static ResultVo<List<Map<String, Object>>> buildResultVo(String configCode, String showConfig, List<Map<String, Object>> defaultList) {
        Optional<List<Map<String, Object>>> optionalList = Optional.empty();
        if (StringUtils.isNotBlank(showConfig)) {
            try {
                List<Map<String, Object>> jsonList = objectMapper.readValue(showConfig, new TypeReference<List<Map<String, Object>>>() {
                });
                optionalList = Optional.of(jsonList);
                return ResultVo.success(optionalList.get());
            } catch (IOException e) {
                return ResultVo.error(configCode + "配置错误,采用默认配置", optionalList.orElse(defaultList));
            }
        }
        return ResultVo.success(defaultList);
    }


    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        objectMapper = applicationContext.getBean(ObjectMapper.class);
    }


}
