package com.goodwill.hdr.civ.service;



import com.goodwill.hdr.core.orm.Page;

import java.util.Map;

/**
 * 治疗记录Service
 *
 * <AUTHOR>
 */
public interface CurerecordService {
    /**
     * 获取治疗记录主表
     *
     * @param patientId
     * @param orderBy
     * @param orderDir
     * @param pageNo
     * @param pageSize
     * @return
     */
    public Page<Map<String, String>> getPageView(String oid, String patientId, String visitId,
                                                 String orderBy, String orderDir, int pageNo, int pageSize);

    public Page<Map<String, String>> getPageViewForDetail(String oid, String patientId,String visitId,
                                                          String orderCode, String orderBy, String orderDir, int pageNo,
                                                          int pageSize);

    public Page<Map<String, String>> getHDReportPageView(
            Map<String, String> queryMap, String outPatientId, String orderBy, String orderDir,
            int pageNo, int pageSize);

    public void getHDReportNum( Map<String, Object> map, String outPatientId);

    public int getReportsByPat(String oid, String patientId, String visitType, String visitId);

    public Map<String, String> getHDReportDetails(String code);

}
