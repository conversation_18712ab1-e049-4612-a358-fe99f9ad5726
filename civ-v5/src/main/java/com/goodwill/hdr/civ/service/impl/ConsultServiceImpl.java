package com.goodwill.hdr.civ.service.impl;

import com.goodwill.hdr.civ.enums.HdrTableEnum;
import com.goodwill.hdr.civ.service.ConsultService;
import com.goodwill.hdr.hbase.dto.responseVo.PageResultVo;
import com.goodwill.hdr.hbase.dto.responseVo.ResultVo;
import com.goodwill.hdr.hbaseQueryClient.builder.PageRequestBuilder;
import com.goodwill.hdr.hbaseQueryClient.service.HbaseQueryClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;

/**
 * @PROJECT_NAME: civ-5.2
 * @DESCRIPTION:
 * @USER: xiaohuya
 * @DATE: 28/07/2025 17:41
 */

@Service
public class ConsultServiceImpl implements ConsultService {
    protected Logger logger = LoggerFactory.getLogger(getClass());
    private final HbaseQueryClient hbaseQueryClient;

    public ConsultServiceImpl(HbaseQueryClient hbaseQueryClient) {
        this.hbaseQueryClient = hbaseQueryClient;
    }
    @Override
    public List<Map<String, String>> getConsultList(String oid, String patientId, String visitId, String visitType) {
        ResultVo<PageResultVo<Map<String, String>>> resultVo = hbaseQueryClient.getPageByCondition(
                PageRequestBuilder.init()
                        .tableName(HdrTableEnum.HDR_TWFUN_CUREMST.getCode())
                        .patientId(patientId)
                        .oid(oid)
                        .visitId(visitId)
                        .visitTypeCode("")
                        .filters(new ArrayList<>())
                        .pageNo(0)
                        .pageSize(0)
                        .orderBy("")
                        .desc()
                        .column()
                        .build());
        if(resultVo.isSuccess()){
            return   resultVo.getContent().getResult();
        }
        return Collections.emptyList();
    }

    @Override
    public Map<String, String> getConsultDetails(String oid, String patientId, String visitId, String visitType, String consultNo) {
        ResultVo<PageResultVo<Map<String, String>>> resultVo = hbaseQueryClient.getPageByCondition(
                PageRequestBuilder.init()
                        .tableName(HdrTableEnum.HDR_TWFUN_CUREMST.getCode())
                        .patientId(patientId)
                        .oid(oid)
                        .visitId(visitId)
                        .visitTypeCode("")
                        .filters(new ArrayList<>())
                        .pageNo(0)
                        .pageSize(0)
                        .orderBy("")
                        .desc()
                        .column()
                        .build());
        if(resultVo.isSuccess()){
            return   resultVo.getContent().getResult().get(0);
        }
        return Collections.emptyMap();
    }
}
