package com.goodwill.hdr.civ.vo;

import com.goodwill.hdr.hbase.bo.PropertyFilter;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class YXPT_ConfigVo {
    private String moduleName;
    private String linkType;
    private String desKey;
    private String url;
    private String doctorId;
    private String doctorName;
    /**
     * key: column
     * value: param
     */
    private Map<String, String> paramMap = new HashMap<>();
    private List<queryHbaseConfig> queryList = new ArrayList<>();

    public String getModuleName() {
        return moduleName;
    }

    public void setModuleName(String moduleName) {
        this.moduleName = moduleName;
    }

    public String getLinkType() {
        return linkType;
    }

    public void setLinkType(String linkType) {
        this.linkType = linkType;
    }

    public String getDesKey() {
        return desKey;
    }

    public void setDesKey(String desKey) {
        this.desKey = desKey;
    }

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public String getDoctorId() {
        return doctorId;
    }

    public void setDoctorId(String doctorId) {
        this.doctorId = doctorId;
    }

    public String getDoctorName() {
        return doctorName;
    }

    public void setDoctorName(String doctorName) {
        this.doctorName = doctorName;
    }

    public Map<String, String> getParamMap() {
        return paramMap;
    }

    public void setParamMap(Map<String, String> paramMap) {
        this.paramMap = paramMap;
    }

    public List<queryHbaseConfig> getQueryList() {
        return queryList;
    }

    public void setQueryList(List<queryHbaseConfig> queryList) {
        this.queryList = queryList;
    }

    public static class queryHbaseConfig {
        private String tableName;
        private List<PropertyFilter> conditionList;
        private List<String> columnList;

        public String getTableName() {
            return tableName;
        }

        public void setTableName(String tableName) {
            this.tableName = tableName;
        }

        public List<PropertyFilter> getConditionList() {
            return conditionList;
        }

        public void setConditionList(List<PropertyFilter> conditionList) {
            this.conditionList = conditionList;
        }

        public List<String> getColumnList() {
            return columnList;
        }

        public void setColumnList(List<String> columnList) {
            this.columnList = columnList;
        }

        @Override
        public String toString() {
            final StringBuilder sb = new StringBuilder();
            sb.append("{")
                    .append("\"tableName\":").append(tableName)
                    .append(", \"conditionList\":").append(conditionList)
                    .append(", \"columnList\":").append(columnList)
                    .append('}');
            return sb.toString();
        }
    }


}
