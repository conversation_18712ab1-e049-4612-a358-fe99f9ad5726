package com.goodwill.hdr.civ.utils;

import com.goodwill.hdr.civ.config.Config;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.*;
import java.net.HttpURLConnection;
import java.net.ProtocolException;
import java.net.URL;

/**
 * <AUTHOR>
 * @Description 类描述：webservice 工具类
 * @Date 2018年10月22日
 * @modify 修改记录：
 */
public class WSUtils {

    public static void main(String[] args) {
//		String result = wsCall("http://localhost:8080/hdr-upv/ws/remoteWS?wsdl",
//				"http://webservice.upv2.goodwill.com/", "wsTest", "001474148700");
//		System.out.println(result);
        "aaa".contains(null);
    }

    private static Logger logger = LoggerFactory.getLogger(WSUtils.class);

    /**
     * @param pid     患者编号
     * @param vid     就诊次数
     * @param orderNo 医嘱号
     * @return 返回类型： String
     * @return
     * @Description 方法描述: 药品闭环调用
     */
    public static String wsDrugOCL(String oid, String pid, String vid, String orderNo) {

        String result = "";
        if (StringUtils.isBlank(Config.getOCLWSDL(oid))) {
            return result;
        }
        try {
            URL url = new URL(Config.getOCLWSDL(oid));

//			--------------

            /**
             *
             * <soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/" xmlns:ser="http://service.ocl.hdr.goodwill.com/">
             *    <soapenv:Header/>
             *    <soapenv:Body>
             *       <ser:drugOCL>
             *          <!--Optional:-->
             *          <oid>12532500432105148J</oid>
             *          <!--Optional:-->
             *          <patientid>11748026</patientid>
             *          <!--Optional:-->
             *          <visitid>1542390</visitid>
             *          <!--Optional:-->
             *          <orderno>39116458</orderno>
             *       </ser:drugOCL>
             *    </soapenv:Body>
             * </soapenv:Envelope>
             *

             *
             */





                    String data = "<soapenv:Envelope xmlns:soapenv=\"http://schemas.xmlsoap.org/soap/envelope/\" xmlns:ser=\"http://service.ocl.hdr.goodwill.com/\">" +
                            "<soapenv:Header/>" +
                            "<soapenv:Body>" +
                            "<ser:drugOCL>" +
                            "<oid>" +
                            oid +
                            "</oid>" +
                            "<patientid>" +
                            pid +
                            "</patientid>" +
                            "<visitid>" +
                            vid +
                            "</visitid>" +
                            "<orderno>" +
                            orderNo +
                            "</orderno>" +
                            "</ser:drugOCL>" +
                            "</soapenv:Body>" +
                            "</soapenv:Envelope>";


            result = wsConnection(url, data);

        } catch (Exception e) {
            e.printStackTrace();
        }
        if (StringUtils.isNotBlank(result)) {
            result = result.replace("&quot;", "\"");
        }
        return result;
    }

    /**
     * @param pid     患者编号
     * @param vid     就诊次数
     * @param orderNo 医嘱号
     * @return 返回类型： String
     * @return
     * @Description 方法描述: 检验闭环调用
     */
    public static String wsLabOCL(String oid, String pid, String vid, String orderNo) {

        String result = "";
        if (StringUtils.isBlank(Config.getOCLWSDL(oid))) {
            return result;
        }
        try {
            URL url = new URL(Config.getOCLWSDL(oid));

            String data = "<soapenv:Envelope xmlns:soapenv=\"http://schemas.xmlsoap.org/soap/envelope/\" xmlns:ser=\"http://service.ocl.hdr.goodwill.com/\">" +
                    "<soapenv:Header/>" +
                    "<soapenv:Body>" +
                    "<ser:labOCL>" +
                    "<oid>" +
                    oid +
                    "</oid>" +
                    "<patientid>" +
                    pid +
                    "</patientid>" +
                    "<visitid>" +
                    vid +
                    "</visitid>" +
                    "<orderno>" +
                    orderNo +
                    "</orderno>" +
                    "</ser:labOCL>" +
                    "</soapenv:Body>" +
                    "</soapenv:Envelope>";


            result = wsConnection(url, data);

        } catch (Exception e) {
            e.printStackTrace();
        }
        if (StringUtils.isNotBlank(result)) {
            result = result.replace("&quot;", "\"");
        }
        return result;
    }

    /**
     * @param pid     患者编号
     * @param vid     就诊次数
     * @param orderNo 医嘱号
     * @return 返回类型： String
     * @return
     * @Description 方法描述: 检查闭环调用
     */
    public static String wsExamOCL(String oid, String pid, String vid, String orderNo) {

        String result = "";
        if (StringUtils.isBlank(Config.getOCLWSDL(oid))) {
            return result;
        }
        try {
            URL url = new URL(Config.getOCLWSDL(oid));

            String data = "<soapenv:Envelope xmlns:soapenv=\"http://schemas.xmlsoap.org/soap/envelope/\" xmlns:ser=\"http://service.ocl.hdr.goodwill.com/\">" +
                    "<soapenv:Header/>" +
                    "<soapenv:Body>" +
                    "<ser:examOCL>" +
                    "<oid>" +
                    oid +
                    "</oid>" +
                    "<patientid>" +
                    pid +
                    "</patientid>" +
                    "<visitid>" +
                    vid +
                    "</visitid>" +
                    "<orderno>" +
                    orderNo +
                    "</orderno>" +
                    "</ser:examOCL>" +
                    "</soapenv:Body>" +
                    "</soapenv:Envelope>";


            result = wsConnection(url, data);

        } catch (Exception e) {
            e.printStackTrace();
        }
        if (StringUtils.isNotBlank(result)) {
            result = result.replace("&quot;", "\"");
        }
        return result;
    }

    /**
     * @param pid     患者编号
     * @param vid     就诊次数
     * @param orderNo 医嘱号
     * @return 返回类型： String
     * @return
     * @Description 方法描述: 手术闭环调用
     */
    public static String wsOperOCL(String oid, String pid, String vid, String orderNo) {

        String result = "";
        if (StringUtils.isBlank(Config.getOCLWSDL(oid))) {
            return result;
        }
        try {
            URL url = new URL(Config.getOCLWSDL(oid));
            String data = "<soapenv:Envelope xmlns:soapenv=\"http://schemas.xmlsoap.org/soap/envelope/\" xmlns:ser=\"http://service.ocl.hdr.goodwill.com/\">" +
                    "<soapenv:Header/>" +
                    "<soapenv:Body>" +
                    "<ser:operOCL>" +
                    "<oid>" +
                    oid +
                    "</oid>" +
                    "<patientid>" +
                    pid +
                    "</patientid>" +
                    "<visitid>" +
                    vid +
                    "</visitid>" +
                    "<orderno>" +
                    orderNo +
                    "</orderno>" +
                    "</ser:operOCL>" +
                    "</soapenv:Body>" +
                    "</soapenv:Envelope>";


            result = wsConnection(url, data);

        } catch (Exception e) {
            e.printStackTrace();
        }
        if (StringUtils.isNotBlank(result)) {
            result = result.replace("&quot;", "\"");
        }
        return result;
    }

    /**
     * @param pid     患者编号
     * @param vid     就诊次数
     * @param applyNo 输血申请号
     * @return 返回类型： String
     * @return
     * @Description 方法描述: 输血闭环调用
     */
    public static String wsBloodOCL(String oid, String pid, String vid, String applyNo) {

        String result = "";
        if (StringUtils.isBlank(Config.getOCLWSDL(oid))) {
            return result;
        }
        try {
            URL url = new URL(Config.getOCLWSDL(oid));
            String data = "<soapenv:Envelope xmlns:soapenv=\"http://schemas.xmlsoap.org/soap/envelope/\" xmlns:ser=\"http://service.ocl.hdr.goodwill.com/\">" +
                    "<soapenv:Header/>" +
                    "<soapenv:Body>" +
                    "<ser:bloodOCL>" +
                    "<oid>" +
                    oid +
                    "</oid>" +
                    "<patientid>" +
                    pid +
                    "</patientid>" +
                    "<visitid>" +
                    vid +
                    "</visitid>" +
                    "<applyno>" +
                    applyNo +
                    "</applyno>" +
                    "</ser:bloodOCL>" +
                    "</soapenv:Body>" +
                    "</soapenv:Envelope>";


            result = wsConnection(url, data);

        } catch (Exception e) {
            e.printStackTrace();
        }
        if (StringUtils.isNotBlank(result)) {
            result = result.replace("&quot;", "\"");
        }
        return result;
    }

    /**
     * @param url
     * @param data1
     * @return 返回类型： String
     * @return
     * @throws IOException
     * @throws ProtocolException
     * @throws UnsupportedEncodingException
     * @Description 方法描述: WS xml方式调用
     */
    private static String wsConnection(URL url, String data1) throws IOException, ProtocolException,
            UnsupportedEncodingException {
        String result;
        HttpURLConnection conn = (HttpURLConnection) url.openConnection();
        conn.setRequestMethod("POST");
        conn.setDoOutput(true);
        conn.setDoInput(true);
        conn.setRequestProperty("Content-Type", "text/xml;charset=utf-8");
        OutputStream outputStream = conn.getOutputStream();
        outputStream.write(data1.getBytes("utf-8"));
        outputStream.flush();
        //int responseCode = conn.getResponseCode();
        //if (responseCode == 200) {
        InputStream inputStream = conn.getInputStream();
        BufferedReader br = new BufferedReader(new InputStreamReader(inputStream, "UTF-8"));
        StringBuilder sb = new StringBuilder();
        String temp = null;
        while ((temp = br.readLine()) != null) {
            sb.append(temp);
        }
        inputStream.close();
        String returnSoap = sb.toString();
        returnSoap = returnSoap.replace("&lt;", "<");
        returnSoap = returnSoap.replace("&gt;", ">");
        String beginTag = "<return>";
        String endTag = "</return>";
        int beginIndex = returnSoap.indexOf(beginTag);
        int endIndex = returnSoap.indexOf(endTag);
        result = returnSoap.substring(beginIndex + beginTag.length(), endIndex);
        return result;
    }

//	/**
//	 * @Description
//	 * 方法描述: WS 动态客户端方式调用接口
//	 * @return 返回类型： String
//	 * @param wsdl WSDL文件地址
//	 * @param namespace 命名空间
//	 * @param method 接口方法名
//	 * @param params 参数列表
//	 * @return
//	 */
//	public static String wsCall(String wsdl, String namespace, String method, Object... params) {
//		//创建动态的客户端工厂
//		JaxWsDynamicClientFactory factory = JaxWsDynamicClientFactory.newInstance();
//		//根据服务地址创建客户端
//		Client client = factory.createClient(wsdl);
//		Object[] result = {};
//		QName qName;
//		try {
//			qName = new QName(namespace, method);
//			result = client.invoke(qName, params);
//		} catch (Exception e) {
//			e.printStackTrace();
//		}
//		return (String) result[0];
//	}


    /**
     * @param url
     * @param data
     * @return 返回类型： String
     * @return
     * @throws IOException
     * @throws ProtocolException
     * @throws UnsupportedEncodingException
     * @Description 方法描述: WS xml方式调用
     */
    public static String wsConnectionData(URL url, String data)
            throws IOException, ProtocolException, UnsupportedEncodingException {
        logger.info("入参："+data);
        String result;
        HttpURLConnection conn = (HttpURLConnection) url.openConnection();
        conn.setRequestMethod("POST");
        conn.setDoOutput(true);
        conn.setDoInput(true);
        conn.setRequestProperty("soapAction", "http://tempuri.org/GetStrEncrypt");
        conn.setRequestProperty("Content-Type", "text/xml;charset=utf-8");
        OutputStream outputStream = conn.getOutputStream();
        outputStream.write(data.getBytes("utf-8"));
        outputStream.flush();
        //int responseCode = conn.getResponseCode();
        //if (responseCode == 200) {
        InputStream inputStream = conn.getInputStream();
        BufferedReader br = new BufferedReader(new InputStreamReader(inputStream, "UTF-8"));
        StringBuilder sb = new StringBuilder();
        String temp = null;
        while ((temp = br.readLine()) != null) {
            sb.append(temp);
        }
        inputStream.close();
        String returnSoap = sb.toString();
        returnSoap = returnSoap.replace("&lt;", "<");
        returnSoap = returnSoap.replace("&gt;", ">");
        String beginTag = "<GetStrEncryptResult>";
        String endTag = "</GetStrEncryptResult>";
        int beginIndex = returnSoap.indexOf(beginTag);
        int endIndex = returnSoap.indexOf(endTag);
        result = returnSoap.substring(beginIndex + beginTag.length(), endIndex);
        return result;
    }

    /**
     * 获取纸质病历地址
     *
     * @param id_card_no
     * @return
     */
    public static String getWSData(String userCode, String WSMedicalRecordURL, String id_card_no, String discharge_time) {
        URL wsurl;
        String res = null;
        String data = "";
        try {
            String time = StringUtils.isBlank(discharge_time) ? "" : "|" + discharge_time;
            wsurl = new URL(WSMedicalRecordURL);
            data = "<soapenv:Envelope xmlns:soapenv='http://schemas.xmlsoap.org/soap/envelope/' xmlns:tem='http://tempuri.org/'><soapenv:Header/><soapenv:Body><tem:GetStrEncrypt><tem:str>" + userCode + "|0|"
                    + id_card_no + time
                    + "</tem:str><tem:key>zjey_1_2_3_zjey</tem:key></tem:GetStrEncrypt></soapenv:Body></soapenv:Envelope>";
            System.out.println("=======data:" + data);
            res = wsConnectionData(wsurl, data);
            System.out.println("=======result:" + res);
        } catch (Exception e) {
            logger.error("=======data:" + data);
            e.printStackTrace();

        }
        return res;
    }

    /**
     * 获取纸质病历地址
     *
     * @param id_card_no
     * @return
     */
    public static String getWSData2(String userCode, String WSMedicalRecordURL, String id_card_no, String discharge_time) {
        URL wsurl;
        String res = null;
        String data = "";
        try {
            String time = StringUtils.isBlank(discharge_time) ? "" : "|" + discharge_time;
            wsurl = new URL(WSMedicalRecordURL);
            data = "<soap:Envelope xmlns:soap=\"http://www.w3.org/2003/05/soap-envelope\" xmlns:tem=\"http://tempuri.org/\">\n" +
                    "   <soap:Header/>\n" +
                    "   <soap:Body>\n" +
                    "      <tem:GetStrEncrypt>\n" +
                    "         <!--Optional:-->\n" +
                    "         <tem:str>" + userCode + "|0|" + id_card_no + "</tem:str>\n" +
                    "         <!--Optional:-->\n" +
                    "         <tem:key>zjey_1_2_3_zjey</tem:key>\n" +
                    "      </tem:GetStrEncrypt>\n" +
                    "   </soap:Body>\n" +
                    "</soap:Envelope>";
            System.out.println("=======data:" + data);
            res = wsConnectionData(wsurl, data);
            System.out.println("=======result:" + res);
        } catch (Exception e) {
            logger.error("=======data:" + data);
            e.printStackTrace();

        }
        return res;
    }

    public static String wsConnectionCommon(String urlStr, String data) {
        logger.info("url:::" + urlStr);
        logger.info("data:::" + data);
        String result = "";
        HttpURLConnection conn = null;
        try {
            URL url = new URL(urlStr);
            conn = (HttpURLConnection) url.openConnection();
            conn.setRequestMethod("POST");
            conn.setDoOutput(true);
            conn.setDoInput(true);
            conn.setRequestProperty("Content-Type", "text/xml;charset=utf-8");
            OutputStream outputStream = conn.getOutputStream();
            outputStream.write(data.getBytes("utf-8"));
            outputStream.flush();
            InputStream inputStream = conn.getInputStream();
            BufferedReader br = new BufferedReader(new InputStreamReader(inputStream, "UTF-8"));
            StringBuilder sb = new StringBuilder();
            String temp = null;
            while ((temp = br.readLine()) != null) {
                sb.append(temp);
            }
            inputStream.close();
            result = sb.toString();
            result = result.replace("&lt;", "<").replace("&gt;", ">");
        } catch (IOException e) {
            e.printStackTrace();
        }
        logger.info("接口返回结果result:::" + result);
        return result;
    }

}
