package com.goodwill.hdr.civ.config;


import com.goodwill.hdr.civ.entity.Config;
import com.goodwill.hdr.civ.utils.ConfigUtil;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @Description 类描述：
 * @modify 修改记录：
 */
@Service
public class ConfigCache {

    /**
     * 获取缓存
     *
     * @param key
     * @return
     */
    public static String getCache(String oid, String key) {
        //先从缓存里面取数据
        String value = "";

        List<Config> list = ConfigUtil.getConfigBycode(oid, key);
        if (list == null || list.isEmpty()) {
            return value;
        }

        value = list.get(0).getConfigValue();
        if (null == value) {
            value = "";
        }

        return value;
    }


}
