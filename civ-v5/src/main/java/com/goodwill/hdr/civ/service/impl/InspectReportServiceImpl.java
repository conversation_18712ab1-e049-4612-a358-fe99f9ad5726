package com.goodwill.hdr.civ.service.impl;


import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.goodwill.hdr.civ.config.Config;
import com.goodwill.hdr.civ.config.ConfigCache;
import com.goodwill.hdr.civ.entity.LabResult;
import com.goodwill.hdr.civ.enums.HdrTableEnum;
import com.goodwill.hdr.civ.service.InspectReportService;
import com.goodwill.hdr.civ.service.SpecialtyViewPowerService;
import com.goodwill.hdr.civ.utils.CivUtils;
import com.goodwill.hdr.civ.utils.ColumnUtil;
import com.goodwill.hdr.civ.utils.ListPage;
import com.goodwill.hdr.civ.utils.Utils;
import com.goodwill.hdr.civ.vo.InspectVoForMedicalTechologies;
import com.goodwill.hdr.core.orm.MatchType;
import com.goodwill.hdr.core.orm.Page;
import com.goodwill.hdr.core.utils.ApplicationException;
import com.goodwill.hdr.hbase.bo.PropertyFilter;
import com.goodwill.hdr.hbase.dto.responseVo.PageResultVo;
import com.goodwill.hdr.hbase.dto.responseVo.ResultVo;
import com.goodwill.hdr.hbaseQueryClient.builder.PageRequestBuilder;
import com.goodwill.hdr.hbaseQueryClient.service.HbaseQueryClient;
import com.goodwill.hdr.security.utils.SecurityCommonUtil;
import com.goodwill.hdr.web.common.vo.ResultVO;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;
import com.goodwill.hdr.civ.vo.InspectionReportTabVo;
import com.goodwill.hdr.civ.vo.KeyValueDto;

@Service
public class InspectReportServiceImpl implements InspectReportService {

    protected Logger logger = LoggerFactory.getLogger(getClass());

    @Autowired
    private SpecialtyViewPowerService specialtyViewPowerService;
    private final HbaseQueryClient hbaseQueryClient;

    @Autowired
    ObjectMapper objectMapper;

    public InspectReportServiceImpl(HbaseQueryClient hbaseQueryClient) {
        this.hbaseQueryClient = hbaseQueryClient;
    }


    @Override
    public Page<Map<String, String>> getInspectReportList(String oid, String patientId, String visitId, String visitType,
                                                          String orderBy, String orderDir, int pageNo, int pageSize,String tabCode) {
        //分页 排序
        Page<Map<String, String>> page = new Page<Map<String, String>>();
        //分页
        boolean pageable = true;
        if (pageNo == 0 || pageSize == 0) {
            pageable = false;
        } else {
            page.setPageNo(pageNo);
            page.setPageSize(pageSize);
        }
        //排序
        if (StringUtils.isBlank(orderBy) || StringUtils.isBlank(orderDir)) {
            page.setOrderBy("REPORT_TIME");
            page.setOrderDir("desc");
        } else {
            page.setOrderBy(orderBy);
            page.setOrderDir(orderDir);
        }

        String labCode = null;
        List<InspectionReportTabVo> list = getReportType(oid);
        //List<Map<String, String>> inspectionReportTabList = getInspectionReportTab(oid,patientId,visitId,visitType,"",list);
        for (InspectionReportTabVo i : list) {
            if (tabCode.equals("ALL") || StringUtils.isBlank(tabCode)) {
                labCode = "ALL";
                break;
            }
            if (tabCode.equals(i.getTabCode())) {
                labCode =i.getLabCode();
                break;
            }
        }
        if (labCode != null) {
            try {
                if ("OUTPV".equalsIgnoreCase(visitType)) {
                    page = getOutpvLabReports(page, oid, patientId, visitId, pageable, labCode);
                } else if ("INPV".equalsIgnoreCase(visitType)) {
                    page = getInpvLabReports(page, oid, patientId, visitId, pageable, labCode);
                }
//            Utils.sortListByDate(page.getResult(), page.getOrderBy(), page.getOrderDir()); //排序
                Utils.sortListDescByDate(page.getResult(), page.getOrderBy());
            } catch (Exception e) {
                logger.error("查询Hbase数据库表 HDR_LAB_REPORT 有误！ ", e);
                throw new ApplicationException("查询检验报告表出错！" + e.getCause());
            }
        }
        if (page.getTotalCount() <= 0) {
            return page;
        }
        //字段映射
        List<Map<String, String>> reports = new ArrayList<Map<String, String>>();
        for (Map<String, String> map : page) {
            Map<String, String> report = new HashMap<String, String>();
            ColumnUtil.convertMapping(report, map, "LAB_ITEM_NAME", "REPORT_TIME","LAB_ITEM_CODE",
                    "REPORT_NO", "HIGH", "LOW", "HH", "LL", "OID","WYHR_REPORT_NO");
            reports.add(report);
        }
        //重置分页
        page.setResult(reports);
        return page;

    }

    /**
     * @param page      分页对象
     * @param patientId 患者编号
     * @param visitId   就诊次
     * @param pageable  是否分页
     * @return 分页对象
     * @Description 方法描述: 某次门诊的检验报告
     */
    private Page<Map<String, String>> getOutpvLabReports(Page<Map<String, String>> page, String oid, String patientId,
                                                         String visitId, boolean pageable, String labCode) {
        List<Map<String, String>> labs = new ArrayList<Map<String, String>>();
        //通过分页优化查询速度：
        int pageSize = page.getPageSize();
        int pageNo = page.getPageNo();
        page.setPageNo(1);
        page.setPageSize(100000);
        //优先 根据vid查询
        List<PropertyFilter> filters = new ArrayList<PropertyFilter>();
//        if (StringUtils.isNotBlank(visitId)) {
//            filters.add(new PropertyFilter("VISIT_ID", "STRING", MatchType.EQ.getOperation(), visitId));
//        }
        filters.add(new PropertyFilter("VISIT_TYPE_CODE", MatchType.EQ.getOperation(), "01"));

        if (StringUtils.isNotBlank(labCode) && !labCode.equals("ALL")) {
            filters.add(new PropertyFilter("LAB_TYPE_CODE", MatchType.IN.getOperation(), labCode));
        }

        //获取过滤条件配置
        String inspect_report_config_value = Config.getINSPECT_REPORT_CONFIG_VALUE(oid);
        if (!"null".equals(inspect_report_config_value)) {
            //按;号截取多个过滤条件
            String[] split = inspect_report_config_value.split(";");
            if (split != null && split.length > 0) {
                for (String value : split) {
                    //验证配置格式是否标准
                    if ((value.contains(","))) {
                        String[] split1 = value.split(",");
                        filters.add(new PropertyFilter(split1[0], split1[1],
                                split1[2].replace("/", ",")));
                    }
                }
            }
        }
        //分页判断
        if (pageable) {
            String orderBy = page.getOrderBy();
            String orderDir = page.getOrderDir();
            ResultVo<PageResultVo<Map<String, String>>> resultVo = hbaseQueryClient.getPageByCondition(
                    PageRequestBuilder.init()
                            .tableName("HDR_LAB_REPORT")
                            .patientId(patientId)
                            .oid(oid)
                            .visitId(visitId)
                            .visitTypeCode("01")
                            .filters(filters)
                            .pageNo(0)
                            .pageSize(0)
                            .orderBy("REPORT_TIME")
                            .desc()
                            .column("LAB_ITEM_NAME", "REPORT_TIME", "REPORT_NO", "OID", "LAB_ITEM_CODE","WYHR_REPORT_NO")
                            .build());
//            page = hbaseDao.findPageConditionByPatient(HdrTableEnum.HDR_LAB_REPORT.getCode(), oid, patientId, page, filters,
//                    "LAB_ITEM_NAME", "REPORT_TIME", "REPORT_NO", "OID","LAB_ITEM_CODE");
            if(resultVo.isSuccess()){
                page.setResult(resultVo.getContent().getResult());
                page.setTotalCount(resultVo.getContent().getTotal());
            }
            //Utils.sortListByDate(page.getResult(), orderBy,orderDir);
            //分页
            ListPage<Map<String, String>> listPage = new ListPage<Map<String, String>>(page.getResult(), pageNo, pageSize);
            labs = listPage.getPagedList();
            page.setResult(labs);
            page.setPageNo(pageNo);
            page.setPageSize(pageSize);
        } else {
            ResultVo<PageResultVo<Map<String, String>>> resultVo = hbaseQueryClient.getPageByCondition(
                    PageRequestBuilder.init()
                            .tableName("HDR_LAB_REPORT")
                            .patientId(patientId)
                            .oid(oid)
                            .visitId(visitId)
                            .visitTypeCode("01")
                            .filters(filters)
                            .pageNo(0)
                            .pageSize(0)
                            .orderBy("REPORT_TIME")
                            .desc()
                            .column("LAB_ITEM_NAME", "REPORT_TIME", "REPORT_NO", "OID","LAB_ITEM_CODE","WYHR_REPORT_NO")
                            .build());
//            page = hbaseDao.findPageConditionByPatient(HdrTableEnum.HDR_LAB_REPORT.getCode(), oid, patientId, page, filters,
//                    "LAB_ITEM_NAME", "REPORT_TIME", "REPORT_NO", "OID","LAB_ITEM_CODE");
            if(resultVo.isSuccess()){
                labs = resultVo.getContent().getResult();
            }
//            labs = hbaseDao.findConditionByPatient(HdrTableEnum.HDR_LAB_REPORT.getCode(), oid, patientId, filters,
//                    "LAB_ITEM_NAME", "REPORT_TIME", "REPORT_NO", "OID","LAB_ITEM_CODE");
            //Utils.sortListByDate(labs, page.getOrderBy(), page.getOrderDir()); //排序
            page.setTotalCount(labs.size());
            page.setResult(labs);
        }

        //TODO 若缺失就诊次，可根据CivUtils中的其他条件筛选

        //上述均未查询到结果，则认为未查询到数据，终止执行
        if (page.getTotalCount() <= 0) {
            return page;
        }
        //统计每份检验报告下的异常的检验细项
        for (Map<String, String> lab : page) {
            Map<String, Object> m = getInspectReportYC(oid, patientId, visitId,lab.get("REPORT_NO"));
            lab.put("HIGH", m.get("high").toString());
            lab.put("LOW", m.get("low").toString());
            lab.put("HH", m.get("HH").toString());
            lab.put("LL", m.get("LL").toString());
        }
        return page;
    }

    /**
     * @param page      分页对象
     * @param patientId 患者编号
     * @param visitId   就诊次
     * @param pageable  是否分页
     * @return 分页对象
     * @Description 方法描述: 某次住院的检验报告
     */
    private Page<Map<String, String>> getInpvLabReports(Page<Map<String, String>> page, String oid, String patientId,
                                                        String visitId, boolean pageable,String labCode) {
        List<Map<String, String>> labs = new ArrayList<Map<String, String>>();
        int pageSize = page.getPageSize();
        int pageNo = page.getPageNo();
        page.setPageNo(1);
        page.setPageSize(100000);
        //优先  根据vid查询
        List<PropertyFilter> filters = new ArrayList<PropertyFilter>();
//        if (StringUtils.isNotBlank(visitId)) {
//            filters.add(new PropertyFilter("VISIT_ID", "STRING", MatchType.EQ.getOperation(), visitId));
//        }
        filters.add(new PropertyFilter("VISIT_TYPE_CODE",  MatchType.EQ.getOperation(), "02"));
        if (StringUtils.isNotBlank(labCode) && !labCode.equals("ALL")) {
            filters.add(new PropertyFilter("LAB_TYPE_CODE", MatchType.IN.getOperation(), labCode));
        }

        String inspect_report_config_value = Config.getINSPECT_REPORT_CONFIG_VALUE(oid);
        if (!"null".equals(inspect_report_config_value)) {
            //按;号截取多个过滤条件
            String[] split = inspect_report_config_value.split(";");
            if (split != null && split.length > 0) {
                for (String value : split) {
                    //验证配置格式是否标准
                    if ((value.contains(","))) {
                        String[] split1 = value.split(",");
                        filters.add(new PropertyFilter(split1[0], split1[1],
                                split1[2].replace("/", ",")));
                    }
                }
            }
        }
        if (pageable) {

            ResultVo<PageResultVo<Map<String, String>>> resultVo = hbaseQueryClient.getPageByCondition(
                    PageRequestBuilder.init()
                            .tableName("HDR_LAB_REPORT")
                            .patientId(patientId)
                            .oid(oid)
                            .visitId(visitId)
                            .visitTypeCode("02")
                            .filters(filters)
                            .pageNo(0)
                            .pageSize(0)
                            .orderBy("REPORT_TIME")
                            .desc()
                            .column("LAB_ITEM_NAME", "REPORT_TIME", "REPORT_NO", "OID", "LAB_ITEM_CODE","WYHR_REPORT_NO")
                            .build());
            if(resultVo.isSuccess()){
                page.setResult(resultVo.getContent().getResult());
                page.setTotalCount(resultVo.getContent().getTotal());
            }
//            page = hbaseDao.findPageConditionByPatient(HdrTableEnum.HDR_LAB_REPORT.getCode(), oid, patientId, page, filters,
//                    "LAB_ITEM_NAME", "REPORT_TIME", "REPORT_NO", "OID","LAB_ITEM_CODE");
            //分页
            ListPage<Map<String, String>> listPage = new ListPage<Map<String, String>>(page.getResult(), pageNo, pageSize);
            labs = listPage.getPagedList();
            page.setResult(labs);
            page.setPageSize(pageSize);
            page.setPageNo(pageNo);
        } else {
            ResultVo<PageResultVo<Map<String, String>>> resultVo = hbaseQueryClient.getPageByCondition(
                    PageRequestBuilder.init()
                            .tableName("HDR_LAB_REPORT")
                            .patientId(patientId)
                            .oid(oid)
                            .visitId(visitId)
                            .visitTypeCode("02")
                            .filters(filters)
                            .pageNo(0)
                            .pageSize(0)
                            .orderBy("REPORT_TIME")
                            .desc()
                            .column("LAB_ITEM_NAME", "REPORT_TIME", "REPORT_NO", "OID","LAB_ITEM_CODE","WYHR_REPORT_NO")
                            .build());
            if(resultVo.isSuccess()){
                labs = resultVo.getContent().getResult();
            }
//            labs = hbaseDao.findConditionByPatient(HdrTableEnum.HDR_LAB_REPORT.getCode(), oid, patientId, filters,
//                    "LAB_ITEM_NAME", "REPORT_TIME", "REPORT_NO", "OID","LAB_ITEM_CODE");
            Utils.sortListByDate(labs, page.getOrderBy(), page.getOrderDir()); //排序
            page.setTotalCount(labs.size());
            page.setResult(labs);
        }

        //根据vid未找到，再根据入院出院时间查询
//        if (page.getTotalCount() <= 0) {
//            page = CivUtils.getInpLabReports(page, oid, patientId, visitId, pageable);
//            //分页
//            ListPage<Map<String, String>> listPage = new ListPage<Map<String, String>>(page.getResult(), pageNo, pageSize);
//            labs = listPage.getPagedList();
//            page.setResult(labs);
//            page.setPageSize(pageSize);
//            page.setPageNo(pageNo);
//        }

        //以上方式均未找到，终止执行
        if (page.getTotalCount() <= 0) {
            return page;
        }

        //统计每份检验报告下的异常检验细项
        for (Map<String, String> lab : page) {
            Map<String, Object> m = getInspectReportYC(oid, patientId,visitId, lab.get("REPORT_NO"));
            lab.put("HIGH", m.get("high").toString());
            lab.put("LOW", m.get("low").toString());
            lab.put("HH", m.get("HH").toString());
            lab.put("LL", m.get("LL").toString());
        }
        return page;
    }

    @Override
    public Map<String, Object> getInspectReportDetails(String oid, String patientId, String visitId,String reportNo, int pageNo, int pageSize,
                                                       String show,String tabCode,String wyhrReportNo) {
        Map<String, Object> result = new HashMap<String, Object>();
        //当前检验报告
        List<PropertyFilter> filter1 = new ArrayList<PropertyFilter>();
        filter1.add(new PropertyFilter("REPORT_NO", MatchType.EQ.getOperation(), reportNo));
        List<Map<String, String>> labs = new ArrayList<>();

        String[] columns={"LAB_TYPE_NAME", "SAMPLE_NO", "SAMPLE_TIME", "SPECIMAN_TYPE_NAME", "SPECIMAN_TYPE_CODE",
                "LAB_PERFORMED_TIME", "LAB_ITEM_NAME", "REPORT_TIME", "APPLY_NO", "APPLY_DOCTOR_NAME", "APPLY_DEPT_NAME",
                "APPLY_TIME", "NOTE","LAB_ITEM_CODE","LAB_DEVICE_NAME","LAB_URL",
                "LAB_MICRO_GROWTH","LAB_MICRO_COMMENT","LAB_MICRO_TCT","LAB_MICRO_CIR","REPORT_DOCTOR_NAME",
                "SPEC_CONFIRM_TIME","PERFORMED_DOCTOR_NAME","REPORT_CONFIRMER_NAME","REPORT_CONFIRM_TIME","SAMPLE_NURSE_NAME",
                "LAB_MICRO_BACTERIA_CODE","LAB_MICRO_BACTERIA_NAME","URL_CLASS","PACS_IMG"};
        ResultVo<PageResultVo<Map<String, String>>> resultVo = hbaseQueryClient.getPageByCondition(
                PageRequestBuilder.init()
                        .tableName("HDR_LAB_REPORT")
                        .patientId(patientId)
                        .oid(oid)
                        .visitId(visitId)
                        .visitTypeCode("")
                        .filters(filter1)
                        .pageNo(0)
                        .pageSize(0)
                        .orderBy("REPORT_TIME")
                        .desc()
                        .column(columns)
                        .build());
        if(resultVo.isSuccess()){
            labs = resultVo.getContent().getResult();
        }

        //未找到报告，终止执行
        if (labs.size() == 0) {
            return result;
        }
        Map<String, String> lab = labs.get(0);
        Map<String, String> labMap = new HashMap<String, String>();

        //字段映射
        ColumnUtil.convertMapping(labMap, lab, columns);
        labMap.put("labUrl", lab.get("LAB_URL"));
        labMap.put("urls",getLabReportUrl(oid,lab,patientId,visitId));
        result.put("lab", labMap);

        String  head=Config.getCIV_LAB_REPORT_DETAIL_HEAD_TAB(oid,tabCode);
        result.put("head", head);
        //检验细项列表
        Page<Map<String, String>> page = getInspectSubItems(oid, patientId,visitId, reportNo, pageNo, pageSize, show);
        //存储检验细项
        result.put("items", page.getResult());

        //20250224 添加微生物培养信息
        addWSWMessage(result, oid, tabCode, patientId, visitId, reportNo, labMap, head);

        //20240805 添加外院检验结果互认
        addLabReportDeatilsForWY(result, tabCode, oid, patientId, wyhrReportNo, pageNo, pageSize,show);

        return result;
    }

    @Override
    public Page<Map<String, String>> getInspectSubItems(String oid, String patientId,String visitId, String reportNo, int pageNo, int pageSize,
                                                        String show) {
        //分页
        Page<Map<String, String>> page = new Page<Map<String, String>>();
        boolean pageable = true;
        if (pageNo == 0 || pageSize == 0) {
            pageable = false;
        } else {
            //HBASE基础查询服务无法先排序再分页，故先查所有的数据，再排序分页。
            page.setPageNo(1);
            page.setPageSize(100000);
        }
        //当前检验报告下的  检验细项
        List<PropertyFilter> filters = new ArrayList<PropertyFilter>();
        filters.add(new PropertyFilter("REPORT_NO", MatchType.EQ.getOperation(), reportNo));
        //分页判断   Config.getCIV_LAB_REPORT_DETAIL_FIELD()
        // "LAB_SUB_ITEM_CODE", "LAB_SUB_ITEM_NAME", "RANGE", "LAB_RESULT_UNIT", "LAB_RESULT_VALUE",
        //					"LAB_QUAL_RESULT", "RESULT_STATUS_CODE","TEST_NO"
        if (pageable) {
            ResultVo<PageResultVo<Map<String, String>>> resultVo = hbaseQueryClient.getPageByCondition(
                    PageRequestBuilder.init()
                            .tableName("HDR_LAB_REPORT_DETAIL")
                            .patientId(patientId)
                            .oid(oid)
                            .visitId(visitId)
                            .visitTypeCode("")
                            .filters(filters)
                            .pageNo(0)
                            .pageSize(0)
                            .orderBy("")
                            .desc()
                            .column(Config.getCIV_LAB_REPORT_DETAIL_FIELD(oid))
                            .build());

            if(resultVo.isSuccess()){
                page.setTotalCount(resultVo.getContent().getTotal());
                page.setResult(resultVo.getContent().getResult());
            }
//            page = hbaseDao.findPageConditionByPatient(HdrTableEnum.HDR_LAB_REPORT_DETAIL.getCode(), oid, patientId, page,
//                    filters, Config.getCIV_LAB_REPORT_DETAIL_FIELD(oid));
        } else {
            List<Map<String, String>> subLabs = new ArrayList<>();
            ResultVo<PageResultVo<Map<String, String>>> resultVo = hbaseQueryClient.getPageByCondition(
                    PageRequestBuilder.init()
                            .tableName("HDR_LAB_REPORT_DETAIL")
                            .patientId(patientId)
                            .oid(oid)
                            .visitId(visitId)
                            .visitTypeCode("")
                            .filters(filters)
                            .pageNo(0)
                            .pageSize(0)
                            .orderBy("")
                            .desc()
                            .column(Config.getCIV_LAB_REPORT_DETAIL_FIELD(oid))
                            .build());

            if(resultVo.isSuccess()){
                subLabs = resultVo.getContent().getResult();
            }
//            List<Map<String, String>> subLabs = hbaseDao.findConditionByPatient(
//                    HdrTableEnum.HDR_LAB_REPORT_DETAIL.getCode(), oid, patientId, filters, Config.getCIV_LAB_REPORT_DETAIL_FIELD(oid));
            page.setResult(subLabs);
            page.setTotalCount(subLabs.size());
        }
        //未找到细项，终止执行
        if (page.getTotalCount() <= 0) {
            return page;
        }
        //排序
        List<Map<String, String>> list = page.getResult();

        list = Utils.sortNumStringList(list, Config.getHDR_LAB_REPORT_DETAIL_SORT(oid), "asc");
        page.setResult(list);
        //分页
//        if (pageable) {
//            page.setPageNo(pageNo);
//            page.setPageSize(pageSize);
//            page.setTotalCount(page.getTotalCount());
//            ListPage<Map<String, String>> listPage = new ListPage<Map<String, String>>(page.getResult(), pageNo, pageSize);
//            page.setResult(listPage.getPagedList());
//        }

        //处理检验细项
        List<Map<String, String>> items = new ArrayList<Map<String, String>>();
        for (Map<String, String> map : page) {
            Map<String, String> item = new HashMap<String, String>();
            //字段映射
            ColumnUtil.convertMapping(item, map, Config.getCIV_LAB_REPORT_DETAIL_FIELD(oid));
            //检验结果
            if (StringUtils.isNotBlank(map.get("LAB_RESULT_VALUE")) && !"-".equals(map.get("LAB_RESULT_VALUE"))) {
                String labResult = map.get("LAB_RESULT_VALUE"); //定量
                item.put("labResultValue", labResult);
                if (Utils.isNumber(labResult)) {
                    item.put("showLine", "true"); //定量结果存在且为数字，显示趋势图
                }
            } else {
                Utils.checkAndPutToMap(item, "labResultValue", map.get("LAB_QUAL_RESULT"), "结果未知", true); //定性
            }
            //异常项判断
            boolean ex = false;
            String status = map.get("RESULT_STATUS_CODE");
            if (StringUtils.isNotBlank(status)) {
                if ("H".equals(status)) {
                    item.put("status", "high");
                    ex = true;
                } else if ("L".equals(status)) {
                    item.put("status", "low");
                    ex = true;
                } else if ("HH".equals(status)) {
                    item.put("status", "HH");
                    ex = true;
                } else if ("LL".equals(status)) {
                    item.put("status", "LL");
                    ex = true;
                }
                //仅保存异常项
                if ("1".equals(show) && ex) {
                    items.add(item);
                }
            }
            //保存所有检验项
            if (!"1".equals(show)) {
                items.add(item);
            }
        }
        //重置分页
        page.setResult(items);
        return page;
    }
    @Override
    public List<Map<String, Object>> getInspectReportDetailSubItems(String oid, String patientId, String visitId,String reportNo, int pageNo, int pageSize,
                                                                    List<String> reportTime,String labItemCode) {
        Page<Map<String, String>> page = new Page<Map<String, String>>();
        List<Map<String, Object>> items = new ArrayList<Map<String, Object>>();
        String[] fieldColumns=Config.getCIV_LAB_REPORT_DETAIL_FIELD(oid);

        boolean pageable = true;
        if (pageNo == 0 || pageSize == 0) {
            pageable = false;
        } else {
            page.setPageNo(1);
            page.setPageSize(100000);
        }
        List<PropertyFilter> filters = new ArrayList<PropertyFilter>();
        filters.add(new PropertyFilter("REPORT_NO", MatchType.EQ.getOperation(), reportNo));

        ResultVo<PageResultVo<Map<String, String>>> resultVo=new ResultVo<>();
        if (pageable) {
            resultVo = hbaseQueryClient.getPageByCondition(
                    PageRequestBuilder.init()
                            .tableName("HDR_LAB_REPORT_DETAIL")
                            .patientId(patientId)
                            .oid(oid)
                            .visitId(visitId)
                            .visitTypeCode("")
                            .filters(filters)
                            .pageNo(0)
                            .pageSize(0)
                            .orderBy("")
                            .desc()
                            .column(fieldColumns)
                            .build());
        } else {
            resultVo = hbaseQueryClient.getPageByCondition(
                    PageRequestBuilder.init()
                            .tableName("HDR_LAB_REPORT_DETAIL")
                            .patientId(patientId)
                            .oid(oid)
                            .visitId(visitId)
                            .visitTypeCode("")
                            .filters(filters)
                            .pageNo(0)
                            .pageSize(0)
                            .orderBy("")
                            .desc()
                            .column(fieldColumns)
                            .build());
        }

        List<Map<String, String>> subLabs = new ArrayList<>();
        if(resultVo.isSuccess()){
            subLabs=resultVo.getContent().getResult();
            //排序
            subLabs = Utils.sortNumStringList(subLabs, Config.getHDR_LAB_REPORT_DETAIL_SORT(oid), "asc");
        }
        
        if (subLabs==null || subLabs.size()==0) {
            return items;
        }
       
        //处理检验细项
        for (Map<String, String> map : subLabs) {
            Map<String, Object> item = new HashMap<>();
            //字段映射
            Map<String, String> temp = new HashMap<>();
            ColumnUtil.convertMapping(temp, map, Config.getCIV_LAB_REPORT_DETAIL_FIELD(oid));
            //添加结果
            item.putAll(temp);
            String labSubItemCode = map.get("LAB_SUB_ITEM_CODE");
            logger.info("=========检验明细编码："+labSubItemCode);
            List<PropertyFilter> filters2 = new ArrayList<PropertyFilter>();
            String labvalue = null;
            String labItemReportNo = null;
            List<LabResult> labResults = new ArrayList<>();
            for(int i = 0; i < reportTime.size(); i++){
                LabResult labResult = new LabResult();
                List<PropertyFilter> filters1 = new ArrayList<PropertyFilter>();
                filters1.add(new PropertyFilter("LAB_ITEM_CODE", MatchType.EQ.getOperation(), labItemCode));
                LocalDate localDate = LocalDate.parse(reportTime.get(i), DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
                filters1.add(new PropertyFilter("REPORT_TIME", MatchType.LE.getOperation(), localDate +" 23:59:59" ));
                filters1.add(new PropertyFilter("REPORT_TIME", MatchType.GE.getOperation(), localDate +" 00:00:00" ));
                List<Map<String, String>> Labs = new ArrayList<>();
                ResultVo<PageResultVo<Map<String, String>>> resultVo0 = hbaseQueryClient.getPageByCondition(
                        PageRequestBuilder.init()
                                .tableName("HDR_LAB_REPORT")
                                .patientId(patientId)
                                .oid(oid)
                                .visitId(visitId)
                                .visitTypeCode("")
                                .filters(filters1)
                                .pageNo(0)
                                .pageSize(0)
                                .orderBy("")
                                .desc()
                                .column("REPORT_NO")
                                .build());

                if(resultVo0.isSuccess()){
                    Labs = resultVo0.getContent().getResult();
                }

                filters1.clear();
                Map<String, String> map1 = new HashMap<>();
                if(Labs.size() > 0 && Labs != null){
                    for(int j = 0; j < Labs.size(); j++){
                        if(i == 0){
                            labItemReportNo = reportNo;
                        }else if( ! reportNo.equals(Labs.get(j).get("REPORT_NO"))){
                            labItemReportNo =Labs.get(j).get("REPORT_NO");
                        }
                        Page<Map<String, String>> lsPage = new Page<Map<String, String>>();
                        logger.info("=========报告号："+labItemReportNo);
                        filters2.add(new PropertyFilter("LAB_SUB_ITEM_CODE", MatchType.EQ.getOperation(), labSubItemCode));
                        filters2.add(new PropertyFilter("REPORT_NO", MatchType.EQ.getOperation(), labItemReportNo));
                        ResultVo<PageResultVo<Map<String, String>>> resultVo1 = hbaseQueryClient.getPageByCondition(
                                PageRequestBuilder.init()
                                        .tableName("HDR_LAB_REPORT_DETAIL")
                                        .patientId(patientId)
                                        .oid(oid)
                                        .visitId(visitId)
                                        .visitTypeCode("")
                                        .filters(filters2)
                                        .pageNo(0)
                                        .pageSize(0)
                                        .orderBy("")
                                        .desc()
                                        .column("LAB_RESULT_VALUE","LAB_QUAL_RESULT","RESULT_STATUS_CODE")
                                        .build());

                        if(resultVo1.isSuccess()){
                            lsPage.setResult(resultVo1.getContent().getResult());
                        }
//                        lsPage = hbaseDao.findPageConditionByPatient(HdrTableEnum.HDR_LAB_REPORT_DETAIL.getCode(), oid, patientId, lsPage,
//                                filters2, "LAB_RESULT_VALUE","LAB_QUAL_RESULT","RESULT_STATUS_CODE");
                        filters2.clear();
                        if(lsPage.getResult().size() > 0 && lsPage.getResult() != null ){
                            map1 = lsPage.getResult().get(0);
                        }
                    }
                }
                String status = map1.get("RESULT_STATUS_CODE");
                labvalue = StringUtils.isNotBlank(map1.get("LAB_RESULT_VALUE")) ? map1.get("LAB_RESULT_VALUE") : map1.get("LAB_QUAL_RESULT");
                logger.info("=========检验结果："+labvalue);
                labResult.setValue(labvalue);
                if (StringUtils.isNotBlank(status)) {
                    if ("H".equals(status)) {
                        labResult.setStatus("high");
                    } else if ("L".equals(status)) {
                        labResult.setStatus("low");
                    } else if ("HH".equals(status)) {
                        labResult.setStatus("HH");
                    } else if ("LL".equals(status)) {
                        labResult.setStatus("LL");

                    }
                }else {
                    labResult.setStatus("");
                }
                labResults.add(labResult);
            }
            item.put("labResultValue", labResults);
            for(LabResult l : labResults){
                if (StringUtils.isNotBlank(l.getValue())) {
                    if (Utils.isNumber(l.getValue())) {
                        item.put("showLine", "true"); //定量结果存在且为数字，显示趋势图
                    }
                }
            }
            items.add(item);
        }

        return items;
    }

    public Map<String, Object> getInspectReportYC(String oid, String patientId, String visitId,String reportNo) {
        //封装数据
        Map<String, Object> result = new HashMap<String, Object>();
//        try {
            //查询当前检验报告下的  检验细项列表
            List<PropertyFilter> filter2 = new ArrayList<PropertyFilter>();
            filter2.add(new PropertyFilter("REPORT_NO", MatchType.EQ.getOperation(), reportNo));
            List<Map<String, String>> subLabs = new ArrayList<>();
            ResultVo<PageResultVo<Map<String, String>>> resultVo = hbaseQueryClient.getPageByCondition(
                    PageRequestBuilder.init()
                            .tableName("HDR_LAB_REPORT_DETAIL")
                            .patientId(patientId)
                            .oid(oid)
                            .visitId(visitId)
                            .visitTypeCode("")
                            .filters(filter2)
                            .pageNo(0)
                            .pageSize(0)
                            .orderBy("")
                            .desc()
                            .column("REPORT_NO",
                                    "RESULT_STATUS_CODE")
                            .build());
            if(resultVo.isSuccess()){
                subLabs = resultVo.getContent().getResult();
            }
//            List<Map<String, String>> subLabs = hbaseDao.findConditionByPatient(
//                    HdrTableEnum.HDR_LAB_REPORT_DETAIL.getCode(), oid, patientId, filter2, new String[]{"REPORT_NO",
//                            "RESULT_STATUS_CODE"});
            //处理检验细项
            int top = 0;
            int low = 0;
            int HH = 0;
            int LL = 0;
            for (Map<String, String> map : subLabs) {
                //异常项判断
                String status = map.get("RESULT_STATUS_CODE");
                if (StringUtils.isNotBlank(status)) {
                    if ("H".equals(status)) {
                        top++;
                    } else if ("L".equals(status)) {
                        low++;
                    } else if ("HH".equals(status)) {
                        HH++;
                    } else if ("LL".equals(status)) {
                        LL++;
                    }
                }
            }
            //异常的检验项数量
            result.put("high", top);
            result.put("low", low);
            result.put("HH", HH);
            result.put("LL", LL);
//        } catch (Exception e) {
//            result.put("high", 0);
//            result.put("low", 0);
//            result.put("HH", 0);
//            result.put("LL", 0);
//            e.printStackTrace();
//            return result;
//        }
        return result;
    }


    public void getAllInspectReportYC(Page<Map<String, String>> page, String oid, String patientId,String visitId,  String reportNo) {
        //封装数据
        Map<String, Object> result = new HashMap<String, Object>();
        try {
            //查询当前检验报告下的  检验细项列表
            List<PropertyFilter> filter2 = new ArrayList<PropertyFilter>();
            filter2.add(new PropertyFilter("REPORT_NO", MatchType.IN.getOperation(), reportNo));
            List<Map<String, String>> subLabs = new ArrayList<>();
            ResultVo<PageResultVo<Map<String, String>>> resultVo = hbaseQueryClient.getPageByCondition(
                    PageRequestBuilder.init()
                            .tableName("HDR_LAB_REPORT_DETAIL")
                            .patientId(patientId)
                            .oid(oid)
                            .visitId(visitId)
                            .visitTypeCode("")
                            .filters(filter2)
                            .pageNo(0)
                            .pageSize(0)
                            .orderBy("")
                            .desc()
                            .column("REPORT_NO",
                                    "RESULT_STATUS_CODE")
                            .build());
            if(resultVo.isSuccess()){
                subLabs = resultVo.getContent().getResult();
            }
//            List<Map<String, String>> subLabs = hbaseDao.findConditionByPatient(
//                    HdrTableEnum.HDR_LAB_REPORT_DETAIL.getCode(), oid, patientId, filter2, new String[]{"REPORT_NO",
//                            "RESULT_STATUS_CODE"});
            for (Map<String, String> lab : page) {
                if (StringUtils.isBlank(lab.get("HIGH"))) {
                    lab.put("HIGH", "0");
                }
                if (StringUtils.isBlank(lab.get("LOW"))) {
                    lab.put("LOW", "0");
                }
                if (StringUtils.isBlank(lab.get("HH"))) {
                    lab.put("HH", "0");
                }
                if (StringUtils.isBlank(lab.get("LL"))) {
                    lab.put("LL", "0");
                }
                //处理检验细项
                for (Map<String, String> map : subLabs) {
                    if (lab.get("REPORT_NO").equals(map.get("REPORT_NO"))) {
                        //异常项判断
                        String status = map.get("RESULT_STATUS_CODE");
                        if (StringUtils.isNotBlank(status)) {
                            if ("H".equals(status)) {
                                int high = Integer.parseInt(lab.get("HIGH")) + 1;
                                lab.put("HIGH", String.valueOf(high));
                            } else if ("L".equals(status)) {
                                int high = Integer.parseInt(lab.get("LOW")) + 1;
                                lab.put("LOW", String.valueOf(high));
                            } else if ("HH".equals(status)) {
                                int high = Integer.parseInt(lab.get("HH")) + 1;
                                lab.put("HH", String.valueOf(high));
                            } else if ("LL".equals(status)) {
                                int high = Integer.parseInt(lab.get("LL")) + 1;
                                lab.put("LL", String.valueOf(high));
                            }
                        }
                    }
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    public Map<String, Object> getInspectReportDetailsLine(String oid, String patientId, String visitType, String outpatientId, String dateType, String startDate,
                                                           String endDate, String subItemCode,String specimanTypeCode) {
        //封装数据
        Map<String, Object> result = new HashMap<String, Object>();
        List<Map<String, String>> list = new ArrayList<Map<String, String>>();
//        if (StringUtils.isNotBlank(patientId)) {
//            getReportDetailsLine(oid, patientId, visitType, dateType, startDate, endDate, subItemCode, list);
//        }
        if (StringUtils.isNotBlank(outpatientId)) {
            String[] pats = outpatientId.split(",");
            for (int i = 0; i < pats.length; i++) {
                if (StringUtils.isNotBlank(pats[i])) {
                    String[] pat = pats[i].split("\\|");
                    getReportDetailsLine(pat[2], pat[1], pat[0], pat[3], dateType, startDate, endDate, subItemCode, list, specimanTypeCode);
                }
            }
        }
        //时间 降序
        Utils.sortListByDate(list, "REPORT_TIME", "desc");
        //近3次，近5次，需要分页
        int splitNum = 0;
        if (list.size() == 0) {
            splitNum = 1;
        } else {
            splitNum = list.size();
        }
        ListPage<Map<String, String>> listPage = new ListPage<Map<String, String>>(list, 1, splitNum);
        if ("3".equals(dateType)) {
            listPage = new ListPage<Map<String, String>>(list, 1, 3);
        } else if ("5".equals(dateType)) {
            listPage = new ListPage<Map<String, String>>(list, 1, 5);
        } else if ("10".equals(dateType)) {
            listPage = new ListPage<Map<String, String>>(list, 1, 10);
        } else if ("50".equals(dateType)) {
            listPage = new ListPage<Map<String, String>>(list, 1, 50);
        }
        List<Map<String, String>> pagedList = listPage.getPagedList();
        //当前页记录再排序，为统计时间坐标准备
        Utils.sortListByDate(pagedList, "REPORT_TIME", "asc");
        //名称
        String subItemName = null;
        //单位
        String subItemUnit = null;
        //参考值
        String subItemRange = null;
        //x坐标
        List<String> categories = new ArrayList<String>();
        //y坐标  上限值+下限值+结果值
        List<Map<String, Object>> series = new ArrayList<Map<String, Object>>();
        //y轴  检验结果值
        List<Double> valueList = new ArrayList<Double>();
        //遍历检验结果
        for (Map<String, String> map : pagedList) {
            if (StringUtils.isBlank(subItemName)) {
                subItemName = map.get("LAB_SUB_ITEM_NAME");
            }
            if (StringUtils.isBlank(subItemUnit)) {
                subItemUnit = map.get("LAB_RESULT_UNIT");
            }
            if (StringUtils.isBlank(subItemRange)) {
                subItemRange = map.get("RANGE");
            }
            //y轴  先取定量结果  若没有值 再取定性结果
            String resultValue = map.get("LAB_RESULT_VALUE");
            if (StringUtils.isNotBlank(resultValue)) {
                if (Utils.isNumber(resultValue)) {
                    valueList.add(Double.parseDouble(resultValue));
                } else {
                    resultValue = map.get("LAB_QUAL_RESULT");
                    if (Utils.isNumber(resultValue)) {
                        valueList.add(Double.parseDouble(resultValue));
                    }
                }
            } else {
                //取定性结果
                resultValue = map.get("LAB_QUAL_RESULT");
                if (Utils.isNumber(resultValue)) {
                    valueList.add(Double.parseDouble(resultValue));
                }
            }
            //x轴
            String dateValue = map.get("REPORT_TIME");
            categories.add(Utils.getDate("yyyy/MM/dd", dateValue));
        }
        //趋势图特殊标识
        Map<String, Object> mk = new HashMap<String, Object>();
        mk.put("enabled", false);
        //上限值 和 下限值
        Map<String, Object> range = CivUtils.parseRange(subItemRange);
        String max = (String) range.get("max");
        String min = (String) range.get("min");
        //上限值
        Map<String, Object> topMap = new HashMap<String, Object>();
        List<Double> topList = new ArrayList<Double>();
        if (StringUtils.isNotBlank(max)) {
            topList.add(Double.parseDouble(max));
        }
        topMap.put("name", "上限");
        topMap.put("data", topList);
        topMap.put("dashStyle", "ShortDot");
        topMap.put("marker", mk);
        series.add(topMap);
        //结果值
        Map<String, Object> valueMap = new HashMap<String, Object>();
        valueMap.put("name", "数值");
        valueMap.put("data", valueList);
        series.add(valueMap);
        //下限值
        Map<String, Object> downMap = new HashMap<String, Object>();
        List<Double> downList = new ArrayList<Double>();
        if (StringUtils.isNotBlank(min)) {
            downList.add(Double.parseDouble(min));
        }
        downMap.put("name", "下限");
        downMap.put("data", downList);
        downMap.put("dashStyle", "ShortDot");
        downMap.put("marker", mk);
        series.add(downMap);
        //检验细项名称
        if (StringUtils.isNotBlank(subItemName)) {
            result.put("name", subItemName);
        } else {
            result.put("name", subItemCode);
        }
        //检验细项单位
        result.put("unit", subItemUnit);
        result.put("total", list.size());
        //y坐标
        result.put("series", series);
        //x坐标
        result.put("categories", categories);
        return result;
    }

    public void getReportDetailsLine(String oid, String patientId, String visitType, String visitId, String dateType, String startDate, String endDate,
                                     String subItemCode, List<Map<String, String>> list,String specimanTypeCode) {
        List<Map<String, String>> rs = new ArrayList<Map<String, String>>();
        //过滤条件
        List<PropertyFilter> filters = new ArrayList<PropertyFilter>();
        //细项编码
        filters.add(new PropertyFilter("LAB_SUB_ITEM_CODE", MatchType.EQ.getOperation(), subItemCode));
        filters.add(new PropertyFilter("VISIT_TYPE_CODE",  MatchType.EQ.getOperation(), visitType));
        //filters.add(new PropertyFilter("VISIT_ID", "STRING", MatchType.EQ.getOperation(), visitId));
        //时间条件
        if ("0".equals(dateType)) {
            if (StringUtils.isNotBlank(startDate) && StringUtils.isNotBlank(endDate)) {
                filters.add(new PropertyFilter("REPORT_TIME", MatchType.GE.getOperation(), startDate));
                filters.add(new PropertyFilter("REPORT_TIME", MatchType.LE.getOperation(), endDate));
            }
        }

        //是否按样本类型查询趋势图数据 1是 0否
        isBySpecimantype(oid, patientId, visitId, visitType, specimanTypeCode, filters);

        ResultVo<PageResultVo<Map<String, String>>> resultVo = hbaseQueryClient.getPageByCondition(
                PageRequestBuilder.init()
                        .tableName(HdrTableEnum.HDR_LAB_REPORT_DETAIL.getCode())
                        .patientId(patientId)
                        .oid(oid)
                        .visitId(visitId)
                        .visitTypeCode(visitType)
                        .filters(filters)
                        .pageNo(0)
                        .pageSize(0)
                        .orderBy("")
                        .desc()
                        .column("REPORT_TIME", "LAB_QUAL_RESULT", "LAB_RESULT_UNIT", "LAB_RESULT_VALUE",
                                "LAB_SUB_ITEM_NAME", "LAB_SUB_ITEM_CODE", "RANGE")
                        .build());
        if(resultVo.isSuccess()){
            rs = resultVo.getContent().getResult();
        }
        if (rs.size() > 0) {
            list.addAll(rs);
        }
    }

    /**
     * 20250703
     * 是否按样本类型查询趋势图数据 1是 0否
     * @param oid
     * @param patientId
     * @param visitId
     * @param visitType
     * @param specimanTypeCode
     * @param filters
     */
    public void isBySpecimantype(String oid, String patientId, String visitId, String visitType, String specimanTypeCode, List<PropertyFilter> filters) {
        String labLineFiler = ConfigCache.getCache(oid, "LAB_LINE_BY_SPECIMANTYPE");
        logger.info("...................labLineFiler===" + labLineFiler);
        if ("1".equals(labLineFiler)) {
            List<PropertyFilter> filters1 = new ArrayList<PropertyFilter>();
            filters1.add(new PropertyFilter("SPECIMAN_TYPE_CODE", MatchType.EQ.getOperation(), specimanTypeCode));
            List<Map<String, String>> reportList = new ArrayList<>();
            ResultVo<PageResultVo<Map<String, String>>> resultVo = hbaseQueryClient.getPageByCondition(
                    PageRequestBuilder.init()
                            .tableName(HdrTableEnum.HDR_LAB_REPORT.getCode())
                            .patientId(patientId)
                            .oid(oid)
                            .visitId(visitId)
                            .visitTypeCode(visitType)
                            .filters(filters1)
                            .pageNo(0)
                            .pageSize(0)
                            .orderBy("")
                            .desc()
                            .column("REPORT_NO")
                            .build());
            if (resultVo.isSuccess()) {
                reportList = resultVo.getContent().getResult();
                String reportNos = reportList.stream().filter(map -> map.containsKey("REPORT_NO")).map(map -> map.get("REPORT_NO")).collect(Collectors.joining(","));
                filters.add(new PropertyFilter("REPORT_NO", MatchType.IN.getOperation(), reportNos));
            }
        }
    }

    @Override
    public long getInspectCount(String oid, String patientId, String visitId, String visitType,String tabCode) {
        //查询检验报告
        Page<Map<String, String>> page = getInspectReportList(oid, patientId, visitId, visitType, "", "", 0, 0,tabCode);
        long inspectCount = page.getTotalCount();
        if (inspectCount < 0) {
            inspectCount = 0;
        }
        return inspectCount;
    }

    @Override
    public List<Map<String, Object>> getInspectReports(String this_oid, String oid, String patientId, String visitType, int pageNo, int pageSize, String orderby,
                                                       String orderdir, String outpatientId, String year,String typeCode) {
        List<Map<String, String>> reports = new ArrayList<Map<String, String>>();
        //获得所有类型
        List<Map<String, String>> types = getReportsTypes(this_oid, oid, patientId, outpatientId, visitType);

        //获取默认当前年
        if (StringUtils.isBlank(year)) {
            Calendar date = Calendar.getInstance();
            year = String.valueOf((date.get(Calendar.YEAR) + 1));
        }
        int num = 0;
        while (reports.size() == 0) {
            year = (Integer.valueOf(year) - 1) + "";
//            if (StringUtils.isNotBlank(patientId) && StringUtils.isNotBlank(this_oid) && this_oid.equals(oid) || this_oid.equals("ALL")) {
//                getInspectReportsByPat(oid, patientId, visitType, types, reports, year);
//            }
            if (StringUtils.isNotBlank(outpatientId)) {
                List<InspectionReportTabVo> list = getReportType(oid);
                String labCode = null;
                for (InspectionReportTabVo i : list) {
                    if (typeCode.equals("ALL") || StringUtils.isBlank(typeCode)) {
                        labCode = "ALL";
                        break;
                    }
                    if (typeCode.equals(i.getTabCode())) {
                        labCode =i.getLabCode();
                        break;
                    }
                }

                String[] pats = outpatientId.split(",");
                for (int i = 0; i < pats.length; i++) {
                    if (StringUtils.isNotBlank(pats[i])) {
                        String[] pat = pats[i].split("\\|");
                        getInspectReportsByPat(pat[2], pat[1], pat[0], pat[3], types, reports, year, labCode);
                    }
                }
            }
            num++;
            if (num == 20)
                break;
        }

        //报告信息处理
        Map<String, Object> resultList = new HashMap<String, Object>();
        CivUtils.groupByDate(resultList, reports, "reportTime");
        List<Map<String, Object>> result = new ArrayList<Map<String, Object>>();
        for (String time : resultList.keySet()) {
            Map<String, Object> rs = new HashMap<String, Object>();
            rs.put("time", time);
            rs.put("order", Integer.valueOf(CivUtils.changeFormatDate(time)));
            rs.put("data", resultList.get(time));
            result.add(rs);
        }
        Utils.sortListByDate(result, "order", Page.Sort.DESC);
        return result;
    }

    public void getInspectReportsByPat(String oid, String patientId, String visitType, String visitId, List<Map<String, String>> types,
                                       List<Map<String, String>> reports, String year, String labCode) {
        //分页
        Page<Map<String, String>> page = new Page<Map<String, String>>();
        page.setPageNo(1);
        page.setPageSize(10000);

        //条件过滤  按照检验项查询
        List<PropertyFilter> filters = new ArrayList<PropertyFilter>();
        if(visitType.equals("00")){
            visitType="01";
        }
        filters.add(new PropertyFilter("VISIT_TYPE_CODE", MatchType.EQ.getOperation(), visitType));
       // filters.add(new PropertyFilter("VISIT_ID", "STRING", MatchType.EQ.getOperation(), visitId));
        if (StringUtils.isNotBlank(year)) {
            PropertyFilter filter1 = new PropertyFilter("REPORT_TIME", MatchType.GE.getOperation(), year
                    + "-01-01");
            filters.add(filter1);
            PropertyFilter filter2 = new PropertyFilter("REPORT_TIME", MatchType.LE.getOperation(), year
                    + "-12-31 23:59:59");
            filters.add(filter2);
        }

        if (StringUtils.isNotBlank(labCode) && !labCode.equals("ALL")) {
            filters.add(new PropertyFilter("LAB_TYPE_CODE", MatchType.IN.getOperation(), labCode));
        }
        ResultVo<PageResultVo<Map<String, String>>> resultVo = hbaseQueryClient.getPageByCondition(
                PageRequestBuilder.init()
                        .tableName(HdrTableEnum.HDR_LAB_REPORT.getCode())
                        .patientId(patientId)
                        .oid(oid)
                        .visitId(visitId)
                        .visitTypeCode(visitType)
                        .filters(filters)
                        .pageNo(0)
                        .pageSize(0)
                        .orderBy("")
                        .desc()
                        .column()
                        .build());
        if(resultVo.isSuccess()){
            page.setResult(resultVo.getContent().getResult());
        }
        //page = hbaseDao.findPageConditionByPatient(HdrTableEnum.HDR_LAB_REPORT.getCode(), oid, patientId, page, filters);

        //统计每份检验报告下的异常检验细项
        String reportNo = "";
        for (Map<String, String> lab : page) {
            if (lab.get("REPORT_NO") == null) {
                continue;
            }
            if (StringUtils.isBlank(reportNo)) {
                reportNo += lab.get("REPORT_NO");
            } else {
                reportNo += "," + lab.get("REPORT_NO");
            }
//			Map<String, Object> m = getInspectReportYC(patientId, lab.get("REPORT_NO"));
//			lab.put("HIGH", m.get("high").toString());
//			lab.put("LOW", m.get("low").toString());
        }
        //新增统计明细定性结果优化，通过in查询一次统计所有数据再处理，去掉循环查询
        if(StringUtils.isNotBlank(reportNo)) {
            getAllInspectReportYC(page, oid, patientId, visitId, reportNo);
        }

        for (Map<String, String> map : page) {
            if (map.get("REPORT_NO") == null) {
                continue;
            }
            Map<String, String> report = new HashMap<String, String>();

            ColumnUtil.convertMapping(report, map, "LAB_PERFORMED_TIME", "LAB_ITEM_NAME","LAB_TYPE_NAME",
                    "REPORT_TIME", "REPORT_NO", "HIGH", "LOW", "HH", "LL", "P", "OID", "ORG_NO", "ORG_NAME","LAB_ITEM_CODE","VISIT_ID","WYHR_REPORT_NO");
            for (Map<String, String> type : types) {
                if (map.get("LAB_TYPE_NAME") == null || "".equals(map.get("LAB_TYPE_NAME"))) {
                    if ("其他".equals(type.get("name"))) {
                        report.put("reportTypeCode", type.get("id"));
                    }
                } else {
                    if (map.get("LAB_TYPE_NAME").equals(type.get("name"))) {
                        report.put("reportTypeCode", type.get("id"));
                    }
                }
            }
            report.put("patient_Id", patientId);
            reports.add(report);
        }
    }

    @Override
    public void getAllReportsCount(Map<String, Object> resultMap, String outpatientId) {

        int num = 0;
//        if (StringUtils.isNotBlank(patientId) && StringUtils.isNotBlank(this_oid) && this_oid.equals(oid) || this_oid.equals("ALL")) {
//            num = num + getReportsTypesByPat(oid, patientId, visitType);
//        }

        if (StringUtils.isNotBlank(outpatientId)) {
            String[] pats = outpatientId.split(",");
            for (int i = 0; i < pats.length; i++) {
                if (StringUtils.isNotBlank(pats[i])) {
                    String[] pat = pats[i].split("\\|");
                    num = num + getReportsTypesByPat(pat[2], pat[1], pat[0], pat[3],"");
                }
            }
        }
        resultMap.put("num", num);
    }

    /**
     * @param items     报告类型集合
     * @param patientId 患者编号
     * @param is        标记其他类型
     * @Description 方法描述: 根据患者ID查询报告类型
     */
    public void getReportsTypesByPat(String oid, String patientId, String visitType, String visitId, Map<String, String> items, StringBuffer is) {
        List<PropertyFilter> filters = new ArrayList<PropertyFilter>();
        List<Map<String, String>> outpage = new ArrayList<>();
        if (visitType.equals("00")){
            visitType="01";
        }
        filters.add(new PropertyFilter("VISIT_TYPE_CODE",  MatchType.EQ.getOperation(), visitType));
        //filters.add(new PropertyFilter("VISIT_ID", "STRING", MatchType.EQ.getOperation(), visitId));
        ResultVo<PageResultVo<Map<String, String>>> resultVo = hbaseQueryClient.getPageByCondition(
                PageRequestBuilder.init()
                        .tableName("HDR_LAB_REPORT")
                        .patientId(patientId)
                        .oid(oid)
                        .visitId(visitId)
                        .visitTypeCode(visitType)
                        .filters(filters)
                        .pageNo(0)
                        .pageSize(0)
                        .orderBy("")
                        .desc()
                        .column( "LAB_TYPE_NAME", "REPORT_NO")
                        .build());
        if(resultVo.isSuccess()){
            outpage = resultVo.getContent().getResult();
        }
//        List<Map<String, String>> outpage = hbaseDao.findConditionByPatient(HdrTableEnum.HDR_LAB_REPORT.getCode(), oid,
//                patientId, filters, "LAB_TYPE_NAME", "REPORT_NO");
        for (Map<String, String> map : outpage) {
            if (map.get("LAB_TYPE_NAME") == null || "".equals(map.get("LAB_TYPE_NAME"))) {
                is.append("true");
            } else {
                if (null == items.get(map.get("LAB_TYPE_NAME"))) {
                    items.put(map.get("LAB_TYPE_NAME"), map.get("LAB_TYPE_NAME"));
                }
            }
        }
    }

    /**
     * @param patientId 患者编号
     * @return 返回类型：int
     * @Description 方法描述: 根据患者ID查询报告总数
     */
    public int getReportsTypesByPat(String oid, String patientId, String visitType, String visitId, String labCode) {
        List<Map<String, String>> outpage = new ArrayList<>();
        List<PropertyFilter> filters = new ArrayList<PropertyFilter>();
        filters.add(new PropertyFilter("VISIT_TYPE_CODE", MatchType.EQ.getOperation(), visitType));
        //filters.add(new PropertyFilter("VISIT_ID", "STRING", MatchType.EQ.getOperation(), visitId));
        if (StringUtils.isNotBlank(labCode) && !labCode.contains("ALL")) {
            filters.add(new PropertyFilter("LAB_TYPE_CODE", MatchType.IN.getOperation(), labCode));
        }
        ResultVo<PageResultVo<Map<String, String>>> resultVo = hbaseQueryClient.getPageByCondition(
                PageRequestBuilder.init()
                        .tableName(HdrTableEnum.HDR_LAB_REPORT.getCode())
                        .patientId(patientId)
                        .oid(oid)
                        .visitId(visitId)
                        .visitTypeCode(visitType)
                        .filters(filters)
                        .pageNo(0)
                        .pageSize(0)
                        .orderBy("")
                        .desc()
                        .column( "LAB_ITEM_NAME", "REPORT_NO")
                        .build());
        if(resultVo.isSuccess()){
            outpage = resultVo.getContent().getResult();
        }
//        List<Map<String, String>> outpage = hbaseDao.findConditionByPatient(HdrTableEnum.HDR_LAB_REPORT.getCode(), oid,
//                patientId, filters, "LAB_ITEM_NAME", "REPORT_NO");
        return outpage.size();
    }

    @Override
    public List<Map<String, String>> getReportsTypes(String this_oid, String oid, String patientId, String outpatientId, String visitType) {
        List<Map<String, String>> list = new ArrayList<Map<String, String>>();
        Map<String, String> items = new HashMap<String, String>();

        StringBuffer is = new StringBuffer("false");
//        if (StringUtils.isNotBlank(patientId) && StringUtils.isNotBlank(this_oid) && this_oid.equals(oid) || this_oid.equals("ALL")) {
//            getReportsTypesByPat(oid, patientId, visitType, items, is);
//        }

        if (StringUtils.isNotBlank(outpatientId)) {
            String[] pats = outpatientId.split(",");
            for (int i = 0; i < pats.length; i++) {
                if (StringUtils.isNotBlank(pats[i])) {
                    String[] pat = pats[i].split("\\|");
                    getReportsTypesByPat(pat[2], pat[1], pat[0], pat[3], items, is);
                }
            }
        }

        int count = 1;
        for (String item : items.keySet()) {
            Map<String, String> map = new HashMap<String, String>();
            map.put("id", count + "");
            map.put("name", item);
            count++;
            list.add(map);
        }
        if (!"false".equals(is.toString())) {
            Map<String, String> map = new HashMap<String, String>();
            map.put("id", count + "");
            map.put("name", "其他");
            list.add(map);
        }
        return list;
    }

    @Override
    public List<Map<String, String>> getExLabResult(String oid, String patientId, String visitId, String visitType, String mainDiag, String deptCode) {
        //异常结果
        List<Map<String, String>> result = new ArrayList<Map<String, String>>();
        List<PropertyFilter> filters1 = new ArrayList<PropertyFilter>();
        filters1.add(new PropertyFilter("TRANS_NO", MatchType.EQ.getOperation(), "0")); //仅查询入出院记录
        List<Map<String, String>> adts = new ArrayList<>();
        if (StringUtils.isNotBlank(oid) ) {
            ResultVo<PageResultVo<Map<String, String>>> resultVo = hbaseQueryClient.getPageByCondition(
                    PageRequestBuilder.init()
                            .tableName(HdrTableEnum.HDR_PAT_ADT.getCode())
                            .patientId(patientId)
                            .oid(oid)
                            .visitId(visitId)
                            .visitTypeCode("")
                            .filters(filters1)
                            .pageNo(0)
                            .pageSize(0)
                            .orderBy("")
                            .desc()
                            .column("VISIT_ID", "ADMISSION_TIME", "DISCHARGE_TIME", "TRANS_NO")
                            .build());
            if(resultVo.isSuccess()){
                adts = resultVo.getContent().getResult();
            }
//            adts = hbaseDao.findConditionByPatientVisitId(HdrTableEnum.HDR_PAT_ADT.getCode(), oid,
//                    patientId, visitId, filters1, "VISIT_ID", "ADMISSION_TIME", "DISCHARGE_TIME", "TRANS_NO");
        }
        //未找到住院记录  则认为未查到检验明细
        if (adts.size() == 0) {
            return result;
        }
        Map<String, String> adt = adts.get(0);
        String startTime = adt.get("ADMISSION_TIME");
        if (StringUtils.isBlank(startTime)) {
            return result;
        }
        String endTime = adt.get("DISCHARGE_TIME");
        DateTimeFormatter df2 = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        if (StringUtils.isBlank(endTime)) {
            endTime = LocalDate.now().format(df2);
        }
        List<PropertyFilter> filters2 = new ArrayList<PropertyFilter>();
        filters2.add(new PropertyFilter("VISIT_TYPE_CODE", MatchType.EQ.getOperation(), "02"));
        //filters2.add(new PropertyFilter("VISIT_ID", "STRING", MatchType.EQ.getOperation(), visitId));

        filters2.add(new PropertyFilter("REPORT_TIME", MatchType.GE.getOperation(), startTime));
        filters2.add(new PropertyFilter("REPORT_TIME", MatchType.LT.getOperation(), endTime));
        List<Map<String, String>> labs = new ArrayList<>();
        if (StringUtils.isNotBlank(oid)) {
            ResultVo<PageResultVo<Map<String, String>>> resultVo = hbaseQueryClient.getPageByCondition(
                    PageRequestBuilder.init()
                            .tableName(HdrTableEnum.HDR_LAB_REPORT_DETAIL.getCode())
                            .patientId(patientId)
                            .oid(oid)
                            .visitId(visitId)
                            .visitTypeCode("02")
                            .filters(filters2)
                            .pageNo(0)
                            .pageSize(0)
                            .orderBy("")
                            .desc()
                            .column("LAB_SUB_ITEM_CODE", "LAB_SUB_ITEM_NAME", "RANGE", "LAB_RESULT_UNIT",
                                    "LAB_RESULT_VALUE", "LAB_QUAL_RESULT", "RESULT_STATUS_CODE")
                            .build());
            if(resultVo.isSuccess()){
                labs = resultVo.getContent().getResult();
            }
//            labs = hbaseDao.findConditionByPatient(HdrTableEnum.HDR_LAB_REPORT_DETAIL.getCode(), oid,
//                    patientId, filters2, "LAB_SUB_ITEM_CODE", "LAB_SUB_ITEM_NAME", "RANGE", "LAB_RESULT_UNIT",
//                    "LAB_RESULT_VALUE", "LAB_QUAL_RESULT", "RESULT_STATUS_CODE");
        }
        //未找到细项，终止执行
        if (labs.size() == 0) {
            return result;
        }
        //处理检验细项
        for (Map<String, String> map : labs) {
            Map<String, String> item = new HashMap<String, String>();
            //字段映射
            ColumnUtil.convertMapping(item, map, new String[]{"LAB_SUB_ITEM_CODE", "LAB_SUB_ITEM_NAME", "RANGE",
                    "LAB_RESULT_UNIT"});
            //检验结果
            if (StringUtils.isNotBlank(map.get("LAB_RESULT_VALUE"))) {
                String labResult = map.get("LAB_RESULT_VALUE"); //定量
                item.put("labResultValue", labResult);
                if (Utils.isNumber(labResult)) {
                    item.put("showLine", "true"); //定量结果存在且为数字，显示趋势图
                }
            } else {
                Utils.checkAndPutToMap(item, "labResultValue", map.get("LAB_QUAL_RESULT"), "结果未知", false); //定性
            }
            //异常项判断
            boolean ex = false;
            String status = map.get("RESULT_STATUS_CODE");
            if (StringUtils.isNotBlank(status)) {
                if ("H".equals(status)) {
                    item.put("status", "high");
                    ex = true;
                } else if ("L".equals(status)) {
                    item.put("status", "low");
                    ex = true;
                }
            }
            //仅保存异常项
            if (ex) {
                result.add(item);
            }
        }
        //查询专科设置并处理结果数据
        List<Map<String, String>> resultData = new ArrayList<Map<String, String>>();
        List<Map<String, String>> resultTemp = new ArrayList<Map<String, String>>();

        List<Map<String, String>> list = new ArrayList<Map<String, String>>();
        if (StringUtils.isNotBlank(mainDiag) || StringUtils.isNotBlank(deptCode)) {
            list = specialtyViewPowerService.getSpecialtyConfig(oid, mainDiag, "LabDetail", deptCode);
        }
        //将重点指标筛选出来
        for (Map<String, String> deptMap : list) {
            String sub_item_code_dept = deptMap.get("subItemCode");
            for (Map<String, String> map : result) {
                String sub_item_code = map.get("labSubItemCode");
                if (StringUtils.isNotBlank(sub_item_code) && sub_item_code.equals(sub_item_code_dept)) {
                    resultData.add(map);
                    break;
                }
            }
        }
        //统计剩下的非重点值标
        for (Map<String, String> map : result) {
            String sub_item_code = map.get("labSubItemCode");
            boolean isContinue = false;
            for (Map<String, String> deptMap : resultData) {
                String sub_item_code_dept = deptMap.get("labSubItemCode");
                if (StringUtils.isNotBlank(sub_item_code) && sub_item_code.equals(sub_item_code_dept)) {
                    isContinue = true;
                    break;
                }
            }
            if (isContinue) {
                continue;
            }
            resultTemp.add(map);
        }

        //合并
        for (Map<String, String> map : resultTemp) {
            resultData.add(map);
        }
        return resultData;
    }

    @Override
    public List<Map<String, String>> getLabReportDetail(String oid, String patientId, String visitId, String orderNo, String orderItemCode) {
        List<PropertyFilter> filters = new ArrayList<PropertyFilter>();
        List<Map<String, String>> outpage = new ArrayList<>();
                //filters.add(new PropertyFilter("VISIT_ID", "STRING", MatchType.EQ.getOperation(), visitId));
        filters.add(new PropertyFilter("ORDER_NO", MatchType.EQ.getOperation(), orderNo));
        filters.add(new PropertyFilter("ORDER_ITEM_CODE", MatchType.EQ.getOperation(), orderItemCode));
        ResultVo<PageResultVo<Map<String, String>>> resultVo = hbaseQueryClient.getPageByCondition(
                PageRequestBuilder.init()
                        .tableName(HdrTableEnum.HDR_LAB_REPORT_DETAIL.getCode())
                        .patientId(patientId)
                        .oid(oid)
                        .visitId(visitId)
                        .visitTypeCode("")
                        .filters(filters)
                        .pageNo(0)
                        .pageSize(0)
                        .orderBy("")
                        .desc()
                        .column("ROWKEY", "REPORT_TIME")
                        .build());
        if(resultVo.isSuccess()){
            outpage = resultVo.getContent().getResult();
        }
//        List<Map<String, String>> outpage = hbaseDao.findConditionByPatient(HdrTableEnum.HDR_LAB_REPORT_DETAIL.getCode(), oid,
//                patientId, filters, "ROWKEY", "REPORT_TIME");
        return outpage;
    }

    @Override
    public List<Map<String, String>> getExamReportDetail(String oid, String patientId, String visitId, String orderNo, String orderItemCode) {
        List<PropertyFilter> filters = new ArrayList<PropertyFilter>();
        List<Map<String, String>> outpage = new ArrayList<>();
                //filters.add(new PropertyFilter("VISIT_ID", "STRING", MatchType.EQ.getOperation(), visitId));
        filters.add(new PropertyFilter("ORDER_NO", MatchType.EQ.getOperation(), orderNo));
        ResultVo<PageResultVo<Map<String, String>>> resultVo = hbaseQueryClient.getPageByCondition(
                PageRequestBuilder.init()
                        .tableName(HdrTableEnum.HDR_EXAM_REPORT.getCode())
                        .patientId(patientId)
                        .oid(oid)
                        .visitId(visitId)
                        .visitTypeCode("")
                        .filters(filters)
                        .pageNo(0)
                        .pageSize(0)
                        .orderBy("")
                        .desc()
                        .column("ROWKEY", "REPORT_TIME")
                        .build());
        if(resultVo.isSuccess()){
            outpage = resultVo.getContent().getResult();
        }
//        List<Map<String, String>> outpage = hbaseDao.findConditionByPatient(HdrTableEnum.HDR_EXAM_REPORT.getCode(), oid,
//                patientId, filters, "ROWKEY", "REPORT_TIME");
        return outpage;
    }

    @Override
    public ResultVO<Map<String, InspectVoForMedicalTechologies>> getInspect(String oid, String oidLast, String pid, String visitId, String visitTypeCode, String outPatientId, String date) {
        String dateString = getDateString(date);
        String[] pats = outPatientId.split(",");
        Map<String, InspectVoForMedicalTechologies> inspectVoMap = new LinkedHashMap<>();
        List<Map<String, String>> list = new ArrayList<>();
        for (int i = 0; i < pats.length; i++) {
            if (StringUtils.isNotBlank(pats[i])) {
                String[] pat = pats[i].split("\\|");
                list.addAll(getInspectVo(pat[2], pat[1], pat[0],pat[3], dateString, oidLast, pid, visitId, visitTypeCode));
            }
        }
        Map<String, InspectVoForMedicalTechologies> map = getInspectVoForMedicalTechnologiesMap(oidLast, pid, visitId, visitTypeCode, list);
        ResultVO<Map<String, InspectVoForMedicalTechologies>> resultVo = new ResultVO<>();
        resultVo.success("查询成功", map);
        return resultVo;
    }

    private static String getDateString(String date) {
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime startDate = null;
        if (date.equals("near_half_year")) {
            startDate = now.minusMonths(6);
        } else if (date.equals("near_1_year")) {
            startDate = now.minusYears(1);
        }
        String dateString = "";
        if (startDate != null) {
            dateString = startDate.toString();
        }
        return dateString;
    }

    private List<Map<String, String>> getInspectVo(String oid, String pid, String visitType ,String visitId,String date, String oidLast, String lastPatientId, String lastVisitId, String lastVisitTypeCode) {
        List<PropertyFilter> filters = new ArrayList<>();
        List<Map<String, String>> list = new ArrayList<>();
        if (StringUtils.isNotBlank(date)) {
            filters.add(new PropertyFilter("REPORT_TIME", MatchType.GE.getOperation(), date));
        }
        filters.add(new PropertyFilter("VISIT_TYPE_CODE",  MatchType.EQ.getOperation(), visitType));
        ResultVo<PageResultVo<Map<String, String>>> resultVo = hbaseQueryClient.getPageByCondition(
                PageRequestBuilder.init()
                        .tableName(HdrTableEnum.HDR_LAB_REPORT.getCode())
                        .patientId(pid)
                        .oid(oid)
                        .visitId(visitId)
                        .visitTypeCode(visitType)
                        .filters(filters)
                        .pageNo(0)
                        .pageSize(0)
                        .orderBy("")
                        .desc()
                        .column("LAB_ITEM_NAME", "REPORT_TIME", "REPORT_NO", "VISIT_ID", "OID", "IN_PATIENT_ID", "OUT_PATIENT_ID", "VISIT_TYPE_CODE")
                        .build());
        if(resultVo.isSuccess()){
            list = resultVo.getContent().getResult();
        }
//        List<Map<String, String>> list = hbaseDao.findConditionByPatient(HdrTableEnum.HDR_LAB_REPORT.getCode(), oid,
//                pid, filters, "LAB_ITEM_NAME", "REPORT_TIME", "REPORT_NO", "VISIT_ID", "OID", "IN_PATIENT_ID", "OUT_PATIENT_ID", "VISIT_TYPE_CODE");
        list.sort((t1, t2) -> t2.get("REPORT_TIME").compareTo(t1.get("REPORT_TIME")));
//        Map<String, InspectVoForMedicalTechologies> subMap = new LinkedHashMap<>();
//        for (Map<String, String> map : list) {
//            String itemName = map.get("LAB_ITEM_NAME");
//            if (subMap.get(itemName) != null) {
//                continue;
//            }
//            InspectVoForMedicalTechologies vo = new InspectVoForMedicalTechologies();
//            Map<String, Object> m = getInspectReportYC(oid, pid, map.get("REPORT_NO"));
//            vo.setHigh(m.get("high").toString());
//            vo.setLow(m.get("low").toString());
//            vo.setHh(m.get("HH").toString());
//            vo.setLl(m.get("LL").toString());
//            vo.setPatientId(pid);
//            vo.setVisitId(map.get("VISIT_ID"));
//            vo.setTime(map.get("REPORT_TIME"));
//            vo.setVisitType(visitType);
//            vo.setOid(oid);
//            vo.setCurrentVisit(map.get("IN_PATIENT_ID").equals(lastPatientId) && map.get("VISIT_ID").equals(lastVisitId)
//                    && map.get("VISIT_TYPE_CODE").equals(lastVisitTypeCode) && map.get("OID").equals(oidLast));
//            subMap.put(itemName, vo);
//        }
//        return subMap;
        return list;
    }

    private Map<String, InspectVoForMedicalTechologies> getInspectVoForMedicalTechnologiesMap(String oidLast, String lastPatientId, String lastVisitId, String lastVisitTypeCode, List<Map<String, String>> list) {
        Map<String, InspectVoForMedicalTechologies> subMap = new LinkedHashMap<>();
        for (Map<String, String> map : list) {
            String itemName = map.get("LAB_ITEM_NAME");
            if (subMap.get(itemName) != null) {
                continue;
            }
            InspectVoForMedicalTechologies vo = new InspectVoForMedicalTechologies();
            Map<String, Object> m = getInspectReportYC(map.get("OID"), StringUtils.isNotBlank(map.get("IN_PATIENT_ID")) ? map.get("IN_PATIENT_ID") : map.get("OUT_PATIENT_ID"),map.get("VISIT_ID"), map.get("REPORT_NO"));
            vo.setHigh(m.get("high").toString());
            vo.setLow(m.get("low").toString());
            vo.setHh(m.get("HH").toString());
            vo.setLl(m.get("LL").toString());
            vo.setPatientId(StringUtils.isNotBlank(map.get("IN_PATIENT_ID")) ? map.get("IN_PATIENT_ID") : map.get("OUT_PATIENT_ID"));
            vo.setVisitId(map.get("VISIT_ID"));
            vo.setTime(map.get("REPORT_TIME"));
            vo.setVisitType(map.get("VISIT_TYPE_CODE"));
            vo.setOid(map.get("OID"));
            if (lastPatientId.equals(map.get("IN_PATIENT_ID")) || lastPatientId.equals(map.get("OUT_PATIENT_ID"))) {
                vo.setCurrentVisit(lastVisitId.equals(map.get("VISIT_ID"))
                        && lastVisitTypeCode.equals(map.get("VISIT_TYPE_CODE")) && oidLast.equals(map.get("OID")));
            } else {
                vo.setCurrentVisit(false);
            }
            subMap.put(itemName, vo);
        }
        return subMap;
    }


    @Override
    public ResultVO<List<Map<String, String>>> getInspectList(String outPatientId, String itemName, String date, boolean currentVist) {
        String dateString = getDateString(date);
        String[] pats = outPatientId.split(",");
        List<Map<String, String>> list = new ArrayList<>();
        for (int i = 0; i < pats.length; i++) {
            if (StringUtils.isNotBlank(pats[i])) {
                String[] pat = pats[i].split("\\|");
                list.addAll(queryListByItemName(pat[3],pat[0], pat[1], pat[2], itemName, dateString));
            }
        }
        if (currentVist && !list.isEmpty()) {
            list.get(0).put("currentVisit", "true");
        }
        ResultVO<List<Map<String, String>>> resultVo = new ResultVO<>();
        resultVo.success("查询成功", list);
        return resultVo;
    }

    private List<Map<String, String>> queryListByItemName(String visitId,String visitTypeCode, String patientId, String oid, String itemName, String dateString) {
        List<PropertyFilter> filters = new ArrayList<>();
        List<Map<String, String>> list = new ArrayList<>();
        if (StringUtils.isNotBlank(dateString)) {
            filters.add(new PropertyFilter("REPORT_TIME", MatchType.GE.getOperation(), dateString));
        }
        filters.add(new PropertyFilter("VISIT_TYPE_CODE",  MatchType.EQ.getOperation(), visitTypeCode));
        filters.add(new PropertyFilter("LAB_ITEM_NAME", MatchType.EQ.getOperation(), itemName));
        ResultVo<PageResultVo<Map<String, String>>> resultVo = hbaseQueryClient.getPageByCondition(
                PageRequestBuilder.init()
                        .tableName(HdrTableEnum.HDR_LAB_REPORT.getCode())
                        .patientId(patientId)
                        .oid(oid)
                        .visitId(visitId)
                        .visitTypeCode(visitTypeCode)
                        .filters(filters)
                        .pageNo(0)
                        .pageSize(0)
                        .orderBy("REPORT_TIME")
                        .desc()
                        .column("OID", "LAB_ITEM_NAME", "REPORT_TIME", "SAMPLE_TIME", "APPLY_DEPT_NAME", "APPLY_DOCTOR_NAME", "REPORT_NO", "VISIT_ID", "IN_PATIENT_ID", "OUT_PATIENT_ID", "VISIT_TYPE_NAME")
                        .build());



        if(resultVo.isSuccess()){
            list = resultVo.getContent().getResult();
        }
//        List<Map<String, String>> list = hbaseDao.findConditionByPatient(HdrTableEnum.HDR_LAB_REPORT.getCode(), oid,
//                patientId, filters, "OID", "LAB_ITEM_NAME", "REPORT_TIME", "SAMPLE_TIME", "APPLY_DEPT_NAME", "APPLY_DOCTOR_NAME", "REPORT_NO", "VISIT_ID", "IN_PATIENT_ID", "OUT_PATIENT_ID", "VISIT_TYPE_NAME");
//        Utils.sortListByDate(list, "REPORT_TIME", "desc");
        list.sort((t1, t2) -> t2.get("REPORT_TIME").compareTo(t1.get("REPORT_TIME")));
        for (Map<String, String> map : list) {
            Map<String, Object> m = getInspectReportYC(oid, patientId, visitId,map.get("REPORT_NO"));
            map.put("HIGH", m.get("high").toString());
            map.put("LOW", m.get("low").toString());
            map.put("HH", m.get("HH").toString());
            map.put("LL", m.get("LL").toString());
        }
        return list;

    }
    @Override
    public List<Map<String, String>> getInspectionReportTab(String oid, String patientId, String visitId, String visitType, String outPatientId,List<InspectionReportTabVo> list) {
        //将该代码块移到for外 提高查询效率
        /*String inspection_report_tab = ConfigCache.getCache(oid,"INSPECTION_REPORT_TAB");
        List<InspectionReportTabVo> list = null;
        try {
            list = objectMapper.readValue(inspection_report_tab, new TypeReference<List<InspectionReportTabVo>>() {
            });
        } catch (IOException e) {
            e.printStackTrace();
        }
        if (list == null || list.isEmpty()) {
            throw new ApplicationException("配置INSPECTION_REPORT_TAB");
        }*/
        //========================
        List<Map<String, String>> result = new ArrayList<Map<String, String>>();
        if(visitType.equals("OUTPV")){
            visitType = "01";
        }else {
            visitType = "02";
        }
        for(InspectionReportTabVo inspectionReportTabVo : list){
            Map<String, String> num = new HashMap<String, String>();
            String tabCode = inspectionReportTabVo.getTabCode();
            String labCode = inspectionReportTabVo.getLabCode();
            num.put("labCode",labCode);
            num.put("tabName",inspectionReportTabVo.getTabName());
            num.put("tabCode",tabCode);
            int tabNum = 0;
            Page<Map<String, String>> page = new Page<Map<String, String>>();
            page.setPageNo(1);
            page.setPageSize(100000);
            //优先 根据vid查询
            if(StringUtils.isNotBlank(outPatientId)){
                String[] pats = outPatientId.split(",");
                for (int i = 0; i < pats.length; i++) {
                    if (StringUtils.isNotBlank(pats[i])) {
                        String[] pat = pats[i].split("\\|");
                        tabNum = tabNum + getReportsTypesByPat(pat[2], pat[1], pat[0], pat[3], labCode);
                    }
                }
            }else {
                /*if(visitType.equals("OUTPV")){
                    visitType = "01";
                }else {
                    visitType = "02";
                }*/
                tabNum = tabNum + getReportsTypesByPat(oid,patientId,visitType,visitId, labCode);
            }
            num.put("tabCount",String.valueOf(tabNum));
            result.add(num);
        }
        //=========================
        return result;
    }

    /**
     * 获取外院检验结果明细
     * @param result
     * @param tabCode
     *
     * @param patientId
     * @param wyhrReportNo
     */
    public void addLabReportDeatilsForWY(Map<String, Object> result, String tabCode, String oid, String patientId, String wyhrReportNo, int pageNo, int pageSize, String show) {
        //获取外院检验结果表头
        String labReportDetailHeadByWY = Config.getCIV_LAB_REPORT_DETAIL_HEAD_WY_TAB(oid, tabCode);
        if (StringUtils.isBlank(labReportDetailHeadByWY)) {
            result.put("headWY", "");
            result.put("itemsWY", "");
            return;
        }

        result.put("headWY", labReportDetailHeadByWY);
        //获取外院检验结果细项
        Page<Map<String, String>> page = getInspectSubItemsForWY(oid, patientId, wyhrReportNo, pageNo, pageSize, show);
        result.put("itemsWY", page.getResult());
    }


    public Page<Map<String, String>> getInspectSubItemsForWY(String oid, String patientId, String wyhrReportNo, int pageNo, int pageSize, String show) {
        //分页
        Page<Map<String, String>> page = new Page<Map<String, String>>();
        boolean pageable = true;
        if (pageNo == 0 || pageSize == 0) {
            pageable = false;
        } else {
            //HBASE基础查询服务无法先排序再分页，故先查所有的数据，再排序分页。
            page.setPageNo(1);
            page.setPageSize(100000);
        }

        //当前患者 外院检验报告下的 检验细项
        List<PropertyFilter> filters = new ArrayList<PropertyFilter>();
        filters.add(new PropertyFilter("WYHR_REPORT_NO", MatchType.IN.getOperation(), wyhrReportNo));
        String[] columns = Config.getCIV_LAB_REPORT_DETAIL_FIELD_WY(oid);

        ResultVo<PageResultVo<Map<String, String>>> resultVo = hbaseQueryClient.getPageByCondition(
                PageRequestBuilder.init()
                        .tableName("HDR_LAB_REPORT_DETAIL")
                        .patientId(patientId)
                        .oid(oid)
                        .visitId("")
                        .visitTypeCode("")
                        .filters(filters)
                        .pageNo(0)
                        .pageSize(0)
                        .orderBy("")
                        .desc()
                        .column(columns)
                        .build());
        if (resultVo.isSuccess()) {
            page.setTotalCount(resultVo.getContent().getTotal());
            page.setResult(resultVo.getContent().getResult());
        }
        //未找到细项，终止执行
        if (page.getTotalCount() <= 0) {
            return page;
        }

        //排序
        List<Map<String, String>> list = page.getResult();
        list = Utils.sortNumStringList(list, Config.getHDR_LAB_REPORT_DETAIL_SORT(oid), "asc");
        page.setResult(list);

        //处理检验细项
        List<Map<String, String>> items = new ArrayList<Map<String, String>>();
        for (Map<String, String> map : page) {
            Map<String, String> item = new HashMap<String, String>();
            //字段映射
            ColumnUtil.convertMapping(item, map, columns);
            //检验结果
            if (StringUtils.isNotBlank(map.get("LAB_RESULT_VALUE")) && !"-".equals(map.get("LAB_RESULT_VALUE"))) {
                String labResult = map.get("LAB_RESULT_VALUE"); //定量
                item.put("labResultValue", labResult);
                /*if (Utils.isNumber(labResult)) {
                    item.put("showLine", "true"); //定量结果存在且为数字，显示趋势图
                }*/
            } else {
                Utils.checkAndPutToMap(item, "labResultValue", map.get("LAB_QUAL_RESULT"), "结果未知", true); //定性
            }
            //异常项判断
            boolean ex = false;
            String status = map.get("RESULT_STATUS_CODE");
            if (StringUtils.isNotBlank(status)) {
                if ("H".equals(status)) {
                    item.put("status", "high");
                    ex = true;
                } else if ("L".equals(status)) {
                    item.put("status", "low");
                    ex = true;
                } else if ("HH".equals(status)) {
                    item.put("status", "HH");
                    ex = true;
                } else if ("LL".equals(status)) {
                    item.put("status", "LL");
                    ex = true;
                }
                //仅保存异常项
                if ("1".equals(show) && ex) {
                    items.add(item);
                }
            }
            //保存所有检验项
            if (!"1".equals(show)) {
                items.add(item);
            }
        }
        //重置分页
        page.setResult(items);
        return page;
    }

    /**
     * 获取检验报告
     *
     * @param oid
     * @param lab
     * @return
     */
    public String getLabReportUrl(String oid, Map<String, String> lab,String pid,String vid) {
        //新增影像浏览url和pacsurl
        List<Map<String, String>> urlList = new ArrayList<Map<String, String>>();
        List<Map<String, String>> fieldList = Config.getCIV_LAB_REPORT_URL_CONFIG(oid);
        for (Map<String, String> fieldInfo : fieldList) {
            Map<String, String> pacsUrl = new HashMap<String, String>();

            String filed=fieldInfo.get("field");
            if ("PDF".equalsIgnoreCase(lab.get("URL_CLASS"))) {
                String pdfUrl=CivUtils.getPdfData(lab.get(filed), oid, pid, vid, lab.get("REPORT_NO"));
                logger.info("pdfUrl.................."+pdfUrl);
                pacsUrl.put("url", pdfUrl);
            } else {
                String url = lab.get(filed);
                String param = Config.getCIV_LAB_REPORT_URL_PARAM_CONFIG(oid);
                if ("true".equals(param) && StringUtils.isNotBlank(url)) {
                    String username = SecurityCommonUtil.getLoginUserCode();
                    if (url.contains("?")) {
                        url += "&loginName=" + username;
                    }
                }
                logger.info("url.............................."+url);
                pacsUrl.put("url", StringUtils.isNotBlank(url) ? url.trim() : url);
            }

            pacsUrl.put("name", fieldInfo.get("name"));
            //pacsUrl.put("url", url);
            pacsUrl.put("linkType", fieldInfo.get("linkType"));
            urlList.add(pacsUrl);
        }
        String json = "";
        try {
            json = objectMapper.writeValueAsString(urlList);
        } catch (JsonProcessingException e) {
            logger.error("urlList转json异常");
        }
        return json;
    }

    /**
     * 20250224 添加微生物培养信息
     */
    public void addWSWMessage(Map<String, Object> result, String oid, String tabCode, String patientId, String visitId, String reportNo,
                              Map<String, String> labMap, String head) {
        if (tabCode.equals("WSW") && "true".equals(ConfigCache.getCache(oid, "INSPECT_TP_CONFIG"))) {
            List<KeyValueDto> WSWTP = new ArrayList<KeyValueDto>();
            List<KeyValueDto> WSWPY = new ArrayList<KeyValueDto>();
            List<Map<String, String>> items = new ArrayList<Map<String, String>>();

            String inpNo = labMap.get("INP_NO");
            String applyNo = labMap.get("APPLY_NO");
            String sampleNo = labMap.get("SAMPLE_NO");

            //当前检验报告
            List<Map<String, String>> wswLabs = new ArrayList<>();
            List<PropertyFilter> filters = new ArrayList<PropertyFilter>();
            filters.add(new PropertyFilter("INP_NO", MatchType.EQ.getOperation(), inpNo));
            filters.add(new PropertyFilter("APPLY_NO", MatchType.EQ.getOperation(), applyNo));
            filters.add(new PropertyFilter("SAMPLE_NO", MatchType.EQ.getOperation(), sampleNo));
            filters.add(new PropertyFilter("REPORT_NO", MatchType.EQ.getOperation(), reportNo));

            ResultVo<PageResultVo<Map<String, String>>> resultVo = hbaseQueryClient.getPageByCondition(
                    PageRequestBuilder.init()
                            .tableName("HDR_LAB_REPORT_DETAIL_WSWXJBG")
                            .patientId(patientId)
                            .oid(oid)
                            .visitId(visitId)
                            .visitTypeCode("")
                            .filters(filters)
                            .pageNo(0)
                            .pageSize(0)
                            .orderBy("")
                            .desc()
                            .column("LAB_SUB_ITEM_NAME", "LAB_RESULT_VALUE", "LAB_ITEM_ENAME", "LAB_SUB_ITEM_CODE")
                            .build());
            if (resultVo.isSuccess()) {
                wswLabs = resultVo.getContent().getResult();
            }

            for (Map<String, String> wswxjbg : wswLabs) {
                if (wswxjbg.get("LAB_SUB_ITEM_NAME").contains("涂片")) {
                    KeyValueDto keyValueDto = new KeyValueDto();
                    keyValueDto.setKey(wswxjbg.get("LAB_SUB_ITEM_NAME"));
                    keyValueDto.setValue(wswxjbg.get("LAB_RESULT_VALUE"));
                    keyValueDto.setValue(wswxjbg.get("EXTENDED FIELD1"));
                    keyValueDto.setValue(wswxjbg.get("EXTENDED FIELD2"));
                    WSWTP.add(keyValueDto);
                } else {
                    KeyValueDto keyValueDto = new KeyValueDto();
                    keyValueDto.setKey(wswxjbg.get("LAB_SUB_ITEM_NAME"));
                    keyValueDto.setValue(wswxjbg.get("LAB_RESULT_VALUE"));
                    keyValueDto.setValue(wswxjbg.get("EXTENDED FIELD1"));
                    keyValueDto.setValue(wswxjbg.get("EXTENDED FIELD2"));
                    WSWPY.add(keyValueDto);
                    String labSubItemCode = wswxjbg.get("LAB_SUB_ITEM_CODE");
                    if (StringUtils.isNotBlank(labSubItemCode)) {
                        List<Map<String, String>> headMap = new ArrayList<>();
                        try {
                            headMap = objectMapper.readValue(head, new TypeReference<List<Map<String, String>>>() {
                            });
                        } catch (IOException e) {
                            throw new ApplicationException("读取" + tabCode + "表头配置时异常，检查json格式,正确格式：\n" +
                                    "[{\"name\":\"labSubItemCode\",\"display\":\"代码\",\"field\":\"LAB_SUB_ITEM_CODE\"}...]");
                        }
                        getInspectWswSubItems(oid, patientId, inpNo, applyNo, sampleNo, labSubItemCode, items, headMap);
                    }
                }
            }
            result.put("items", items);
            result.put("WSWTP", WSWTP);
            result.put("WSWPY", WSWPY);
        }
    }

    public void getInspectWswSubItems(String oid, String patientId, String inpNo, String applyNo, String sampleNo, String microItemCode,
                                      List<Map<String, String>> list, List<Map<String, String>> head) {
        //分页
        List<String> fieldList = new ArrayList<>();
        Map<String, String> convertRule = new HashMap<>();

        for (Map<String, String> map : head) {
            String name = map.get("name");
            String field = map.get("field");
            if (StringUtils.isNotBlank(field)) {
                convertRule.put(field, name);
                fieldList.add(field);
            }
        }
        String[] fieldArray = new String[fieldList.size()];
        fieldList.toArray(fieldArray);

        //当前检验报告下的  检验细项
        List<Map<String, String>> wsWsubLabs = new ArrayList<>();

        List<PropertyFilter> filters = new ArrayList<PropertyFilter>();
        filters.add(new PropertyFilter("MICRO_ITEM_CODE", MatchType.EQ.getOperation(), microItemCode));
        filters.add(new PropertyFilter("INP_NO", MatchType.EQ.getOperation(), inpNo));
        filters.add(new PropertyFilter("APPLY_NO", MatchType.EQ.getOperation(), applyNo));
        filters.add(new PropertyFilter("SAMPLE_NO", MatchType.EQ.getOperation(), sampleNo));

        ResultVo<PageResultVo<Map<String, String>>> resultVo = hbaseQueryClient.getPageByCondition(
                PageRequestBuilder.init()
                        .tableName("HDR_LAB_REPORT_DETAIL")
                        .patientId(patientId)
                        .oid(oid)
                        .visitId("")
                        .visitTypeCode("")
                        .filters(filters)
                        .pageNo(0)
                        .pageSize(0)
                        .orderBy("")
                        .desc()
                        .column(fieldArray)
                        .build());
        if (resultVo.isSuccess()) {
            wsWsubLabs = resultVo.getContent().getResult();
        }

        wsWsubLabs = Utils.sortList(wsWsubLabs, Config.getHDR_LAB_REPORT_DETAIL_SORT(oid), "asc");
        //处理检验细项
        for (Map<String, String> map : wsWsubLabs) {
            Map<String, String> item = new HashMap<String, String>();

            ColumnUtil.convertMappingByRule(map, item, convertRule);
            if (StringUtils.isNotBlank(map.get("LAB_RESULT_VALUE")) /*&& !"-".equals(map.get("LAB_RESULT_VALUE"))*/) {
                String labResult = map.get("LAB_RESULT_VALUE"); //定量
                item.put("labResultValue", labResult);
                if (Utils.isNumber(labResult)) {
                    item.put("showLine", "true"); //定量结果存在且为数字，显示趋势图
                }
            } else {
                Utils.checkAndPutToMap(item, "labResultValue", map.get("LAB_QUAL_RESULT"), "结果未知", true); //定性
            }

            String status = map.get("RESULT_STATUS_CODE");
            if (StringUtils.isNotBlank(status)) {
                if (status.equals("H")) {
                    item.put("status", "high");
                }
                if (status.equals("L")) {
                    item.put("status", "low");
                }
                if (status.equals("HH")) {
                    item.put("status", "HH");
                }
                if (status.equals("LL")) {
                    item.put("status", "LL");
                }
                if (status.equals("P")) {
                    item.put("status", "P");
                }
            }
            list.add(item);
        }
    }

    /**
     * 获取检验报告类型
     */
    @Override
    public List<InspectionReportTabVo> getReportType(String oid) {
        String inspection_report_tab = ConfigCache.getCache(oid, "INSPECTION_REPORT_TAB");
        List<InspectionReportTabVo> list = null;
        try {
            list = objectMapper.readValue(inspection_report_tab, new TypeReference<List<InspectionReportTabVo>>() {
            });
        } catch (IOException e) {
            e.printStackTrace();
        }
        if (list == null || list.isEmpty()) {
            throw new ApplicationException("配置INSPECTION_REPORT_TAB");
        }
        return list;
    }

}
