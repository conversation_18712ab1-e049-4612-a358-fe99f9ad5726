package com.goodwill.hdr.civ.service;



import com.goodwill.hdr.core.orm.Page;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Description 类描述：当前视图服务接口
 * @Date 2018年8月29日
 * @modify 修改记录：
 */
public interface CurrentViewService {

    /**
     * @param patientId 患者编号
     * @param visitId   就诊次数
     * @return
     * @Description 方法描述: 获取病历主诉内容
     */
    public Map<String, String> getCVMain( String oid, String patientId, String visitId);

    /**
     * @param patientId 患者编号
     * @param visitId   就诊次数
     * @return
     * @Description 方法描述: 获取病历现病史
     */
    public Map<String, String> getCVNowHis( String oid, String patientId, String visitId);

    /**
     * @param patientId
     * @param visitId
     * @return
     * @Description 方法描述: 获取诊断信息
     */
    public Map<String, Object> getCVDiag(String oid, String patientId, String visitId);

    /**
     * @param patientId 患者编号
     * @param visitId   就诊次数
     * @return
     * @Description 方法描述: 获取患者最新体征测量数据
     */
    public List<Map<String, String>> getPatVitalSign( String oid, String patientId, String visitId, String mainDiag, String deptCode);

    /**
     * 获取患者护理任务数据
     *
     * @param patientId
     * @param visitId
     * @return
     */
    public List<Map<String, String>> getPatNurseTask(String oid, String patientId, String visitId);

    /**
     * 获取患者风险评估数据
     *
     * @param patientId
     * @param visitId
     * @return
     */
    public Page<Map<String, String>> getPatRiskAssess(String oid, String patientId, String visitId, int pageNo, int pageSize);

    /**
     * 获取患者诊疗计划
     *
     * @param patientId
     * @param visitId
     * @return
     */
    public String getPatAssessPlan(String oid, String patientId, String visitId);

    /**
     * 获取检验危急值
     *
     * @param patientId
     * @param visitId
     * @return
     */

    public Page<Map<String, String>> getPatCriticalValues(String oid, String patientId, String visitId, int pageNo, int pageSize);

    /**
     * 获取院感
     *
     * @param patientId
     * @param visitId   HDR_INFECTION_WARN
     * @return
     */
    public Page<Map<String, String>> getPatInfectionWarn(String oid, String patientId, String visitId, int pageNo, int pageSize);

    Map<String, String> getEMRContent( String oid, String patientId, String visitId, String visitType, String type);
}
