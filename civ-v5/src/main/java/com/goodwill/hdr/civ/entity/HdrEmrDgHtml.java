package com.goodwill.hdr.civ.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.io.Serializable;

/**
 * @TableName hdr_emr_dg_html
 */
@TableName(value = "hdr_emr_dg_html")
public class HdrEmrDgHtml implements Serializable {
    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
    /**
     *
     */
    @TableId(value = "id")
    private Integer id;
    /**
     *
     */
    @TableField(value = "emr_class_code")
    private String emrClassCode;
    /**
     *
     */
    @TableField(value = "emr_class_name")
    private String emrClassName;
    /**
     *
     */
    @TableField(value = "html_text")
    private String htmlText;
    /**
     *
     */
    @TableField(value = "is_enabled")
    private String isEnabled;

    /**
     *
     */
    public Integer getId() {
        return id;
    }

    /**
     *
     */
    public void setId(Integer id) {
        this.id = id;
    }

    /**
     *
     */
    public String getEmrClassCode() {
        return emrClassCode;
    }

    /**
     *
     */
    public void setEmrClassCode(String emrClassCode) {
        this.emrClassCode = emrClassCode;
    }

    /**
     *
     */
    public String getEmrClassName() {
        return emrClassName;
    }

    /**
     *
     */
    public void setEmrClassName(String emrClassName) {
        this.emrClassName = emrClassName;
    }

    /**
     *
     */
    public String getHtmlText() {
        return htmlText;
    }

    /**
     *
     */
    public void setHtmlText(String htmlText) {
        this.htmlText = htmlText;
    }

    /**
     *
     */
    public String getIsEnabled() {
        return isEnabled;
    }

    /**
     *
     */
    public void setIsEnabled(String isEnabled) {
        this.isEnabled = isEnabled;
    }

    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        HdrEmrDgHtml other = (HdrEmrDgHtml) that;
        return (this.getId() == null ? other.getId() == null : this.getId().equals(other.getId()))
                && (this.getEmrClassCode() == null ? other.getEmrClassCode() == null : this.getEmrClassCode().equals(other.getEmrClassCode()))
                && (this.getEmrClassName() == null ? other.getEmrClassName() == null : this.getEmrClassName().equals(other.getEmrClassName()))
                && (this.getHtmlText() == null ? other.getHtmlText() == null : this.getHtmlText().equals(other.getHtmlText()))
                && (this.getIsEnabled() == null ? other.getIsEnabled() == null : this.getIsEnabled().equals(other.getIsEnabled()));
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getId() == null) ? 0 : getId().hashCode());
        result = prime * result + ((getEmrClassCode() == null) ? 0 : getEmrClassCode().hashCode());
        result = prime * result + ((getEmrClassName() == null) ? 0 : getEmrClassName().hashCode());
        result = prime * result + ((getHtmlText() == null) ? 0 : getHtmlText().hashCode());
        result = prime * result + ((getIsEnabled() == null) ? 0 : getIsEnabled().hashCode());
        return result;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", emrClassCode=").append(emrClassCode);
        sb.append(", emrClassName=").append(emrClassName);
        sb.append(", htmlText=").append(htmlText);
        sb.append(", isEnabled=").append(isEnabled);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}