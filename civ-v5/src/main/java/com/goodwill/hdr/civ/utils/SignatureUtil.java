package com.goodwill.hdr.civ.utils;


import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONException;
import cn.hutool.json.JSONObject;
import java.io.UnsupportedEncodingException;
import java.net.URI;
import java.net.URISyntaxException;
import java.net.URLDecoder;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.*;

/**
 * 生成数字签章
 * 2024-11-28
 */
public class SignatureUtil {
    /**
     * 百分比编码
     */
    public static String percentEncode(String s) {
        String encoded = "";
        try {
            boolean flag = isValidJson(s);
            if (flag) {
                JSONObject jsonObject = new JSONObject(s);
                for (Map.Entry<String, Object> map : jsonObject.entrySet()) {
                    String key = map.getKey();
                    String value = String.valueOf(map.getValue());
                    encoded = URLEncoder.encode(s, StandardCharsets.UTF_8.toString()).toLowerCase();
                    encoded = encoded.replace(key.toLowerCase(), key).replace(value.toLowerCase(), value);
                }
            } else {
                encoded = URLEncoder.encode(s, StandardCharsets.UTF_8.toString()).toLowerCase();
            }
        } catch (UnsupportedEncodingException e) {
            e.printStackTrace();
        }
        return encoded;
    }

    public static boolean isValidJson(String jsonString) {
        try {
            new JSONObject(jsonString);
        } catch (JSONException ex) {
            try {
                new JSONArray(jsonString);
            } catch (JSONException ex1) {
                return false;
            }
        }
        return true;
    }

    /**
     * 创建数字签名
     */
    public static String createDigitalSignature(String url, String method, String requestBody, String secret, String timestamp) {
        System.out.println("url: " + url);
        System.out.println("method: " + method);
        System.out.println("requestBody: " + requestBody);
        System.out.println("secret: " + secret);
        System.out.println("timestamp: " + timestamp);

        try {
            StringBuffer sb = new StringBuffer();
            sb.append(method.toUpperCase()).append("|");

            URI uri = new URI(url);
            String path = uri.getPath().toLowerCase();
            if (path.endsWith("/")) {
                path = path.substring(0, path.length() - 1);
            }
            sb.append(percentEncode(path)).append("|");

            // 查询字符串
            String query = uri.getQuery();
            if (query != null && !query.isEmpty()) {
                String[] params = query.split("&");
                List<Map.Entry<String, String>> sortedParams = new ArrayList<>();
                for (String param : params) {
                    String[] parts = param.split("=", 2);
                    if (parts.length == 2) {
                        String key = URLDecoder.decode(parts[0], StandardCharsets.UTF_8.toString()).trim().toLowerCase();
                        String value = URLDecoder.decode(parts[1], StandardCharsets.UTF_8.toString()).trim();
                        sortedParams.add(new AbstractMap.SimpleEntry<>(key, value));
                    }
                }
                // 按键名排序并重新组合查询字符串
                Collections.sort(sortedParams, Map.Entry.comparingByKey());
                String encodedQuery = String.join("&",
                        sortedParams.stream()
                                .map(entry -> percentEncode(entry.getKey()) + "=" + percentEncode(entry.getValue()))
                                .toArray(String[]::new));
                sb.append(encodedQuery).append("|");
            } else {
                sb.append("").append("|");
            }
            String body = percentEncode(requestBody == null ? "" : requestBody);
            sb.append(body).append("|");
            sb.append(secret).append("|");
            sb.append(timestamp);

            // 使用SHA-256算法进行哈希处理
            MessageDigest digest = MessageDigest.getInstance("SHA-256");
            byte[] hash = digest.digest(sb.toString().getBytes(StandardCharsets.UTF_8));
            String signNature = Base64.getEncoder().encodeToString(hash);

            System.out.println("Path: " + percentEncode(path));
            System.out.println("Body: " + body);
            System.out.println("Signature Raw: " + sb);
            System.out.println("signNature: " + signNature);

            return signNature;
        } catch (URISyntaxException e) {
            e.printStackTrace();
        } catch (UnsupportedEncodingException e) {
            e.printStackTrace();
        } catch (NoSuchAlgorithmException e) {
            e.printStackTrace();
        }
        return "";
    }

    public static void main(String[] args) {
        try {
            String url = "http://10.1.9.100:8000/api/studytoken";
            String method = "post";
            String requestBody = "{\"AccessionNumber\":\"ES2462500029\"}";
            String secret = "62vq6G2OiBB4X5WnWYZ4Y0VvbGj";
            String timestamp = "1733293577725"; // 转换为秒

            String signature = createDigitalSignature(url, method, requestBody, secret, timestamp);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

}
