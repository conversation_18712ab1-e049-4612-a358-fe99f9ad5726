package com.goodwill.hdr.civ.enums.commonModule;

/**
 * 通用模块调用第三方ulr的配置
 *
 * <AUTHOR>
 * @since 2021/9/6
 */

public enum CommonUrlEnum {

    /**
     * 访问第三方的地址
     */
    URL {
        @Override
        public String getCode(String id) {
            return "SD_" + id + "_URL";
        }

        @Deprecated
        @Override
        public String getCode(String id, Integer i) {
            return null;
        }

    },
    /**
     * 链接类型
     */
    LINKTYPE {
        @Override
        public String getCode(String id) {
            return "SD_" + id + "_LINKTYPE";
        }

        @Deprecated
        @Override
        public String getCode(String id, Integer i) {
            return null;
        }

    },
    /**
     * 参数个数
     */
    PARAM_NUM {
        @Override
        public String getCode(String id) {
            return "SD_" + id + "_PARAM_NUM";
        }

        @Deprecated
        @Override
        public String getCode(String id, Integer i) {
            return null;
        }
    },
    /**
     * url 上的占位形参
     */
    PARAMETER_FIELD {
        @Deprecated
        @Override
        public String getCode(String id) {
            return null;
        }

        @Override
        public String getCode(String id, Integer i) {
            return "SD_" + id + "_PARAMETER_FIELD" + i;
        }
    },
    /**
     * url 上的占位形参的秘钥
     */
    SM4_KEY {
        @Override
        public String getCode(String id) {
            return "SD_" + id + "_SM4_KEY";
        }

        @Deprecated
        @Override
        public String getCode(String id, Integer i) {
            return null;
        }
    },
    /**
     * url 上的占位形参的秘钥长度
     */
    SM4_KEY_LEN {
        @Override
        public String getCode(String id) {
            return "SD_" + id + "_SM4_KEY_LEN";
        }

        @Deprecated
        @Override
        public String getCode(String id, Integer i) {
            return null;
        }
    },
    /**
     * url 上的占位形参的SM4加密后大小写
     */
    SM4_ISUPPER {
        @Override
        public String getCode(String id) {
            return "SD_" + id + "_SM4_ISUPPER";
        }

        @Deprecated
        @Override
        public String getCode(String id, Integer i) {
            return null;
        }
    },
    /**
     * url 上的占位形参 需要加密的组合值
     */
    SM4_FIELD {
        @Override
        public String getCode(String id) {
            return "SD_" + id + "_SM4_FIELD";
        }

        @Deprecated
        @Override
        public String getCode(String id, Integer i) {
            return null;
        }
    },
    /**
     * 形参对应的数据库字段
     */
    PARAMETER_FIELD_CONFIG {
        @Deprecated
        @Override
        public String getCode(String id) {
            return null;
        }

        @Override
        public String getCode(String id, Integer i) {
            return "SD_" + id + "_PARAMETER_FIELD_CONFIG" + i;
        }
    },
    TABLE_TEMPLATE_CONFIG {
        @Override
        public String getCode(String id) {
            return "SD_" + id + "_TABLE_TEMPLATE_CONFIG";
        }

        @Deprecated
        @Override
        public String getCode(String id, Integer i) {
            return null;
        }

    },
    URL_TABLE_TEMPLATE_CONFIG {
        @Override
        public String getCode(String id) {
            return "SD_" + id + "_URL_TABLE_TEMPLATE_CONFIG";
        }

        @Deprecated
        @Override
        public String getCode(String id, Integer i) {
            return null;
        }

    },
    PUBLIC_KEY {
        @Override
        public String getCode(String id) {
            return "SD_" + id + "_PUBLIC_KEY";
        }

        @Deprecated
        @Override
        public String getCode(String id, Integer i) {
            return null;
        }

    },
    UNENCRYPT_FIELD {
        @Override
        public String getCode(String id) { return "SD_" + id + "_UNENCRYPT_FIELD"; }

        @Deprecated
        @Override
        public String getCode(String id, Integer i) {  return null; }
    },
    URL_TOKEN_URL{
        @Override
        public String getCode(String id) {
            return "SD_" + id + "_TOKEN_URL";
        }

        @Deprecated
        @Override
        public String getCode(String id, Integer i) {
            return null;
        }
    },
    //第三方链接请求结果格式
    REQUEST_RESULT_FORMAT{
        @Override
        public String getCode(String id) { return "SD_" + id + "_REQUEST_RESULT_FORMAT"; }

        @Deprecated
        @Override
        public String getCode(String id, Integer i) {  return null; }
    },
    //第三方链接请求结果节点配置
    REQUEST_RESULT_NODE{
        @Override
        public String getCode(String id) { return "SD_" + id + "_REQUEST_RESULT_NODE"; }

        @Deprecated
        @Override
        public String getCode(String id, Integer i) {  return null; }
    },
    //第三方webservice链接请求入参value
    REQUEST_PARAM_VALUE{
        @Override
        public String getCode(String id) { return "SD_" + id + "_REQUEST_PARAM_VALUE"; }

        @Deprecated
        @Override
        public String getCode(String id, Integer i) {  return null; }
    },
    //第三方链接请求结果前缀
    REQUEST_RESULT_START{
        @Override
        public String getCode(String id) { return "SD_" + id + "_REQUEST_RESULT_START"; }

        @Deprecated
        @Override
        public String getCode(String id, Integer i) {  return null; }
    },
    //第三方链接请求结果格结尾
    REQUEST_PARAM_END{
        @Override
        public String getCode(String id) { return "SD_" + id + "_REQUEST_PARAM_END"; }

        @Deprecated
        @Override
        public String getCode(String id, Integer i) {  return null; }
    };

    private String code;

    public abstract String getCode(String id);


    public abstract String getCode(String id, Integer i);


}
