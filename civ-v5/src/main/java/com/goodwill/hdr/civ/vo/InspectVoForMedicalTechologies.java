package com.goodwill.hdr.civ.vo;

import java.sql.Time;

/**
 * 医技视图实验室报告对象
 */
public class InspectVoForMedicalTechologies {

    private Boolean currentVisit;

    private String hh;

    private String ll;

    private String high;

    private String low;

    private String time;

    private String patientId;

    private String visitId;

    private String oid;

    private String visitType;

    public Boolean getCurrentVisit() {
        return currentVisit;
    }

    public void setCurrentVisit(Boolean currentVisit) {
        this.currentVisit = currentVisit;
    }

    public String getHh() {
        return hh;
    }

    public void setHh(String hh) {
        this.hh = hh;
    }

    public String getLl() {
        return ll;
    }

    public void setLl(String ll) {
        this.ll = ll;
    }

    public String getHigh() {
        return high;
    }

    public void setHigh(String hight) {
        this.high = high;
    }

    public String getLow() {
        return low;
    }

    public void setLow(String low) {
        this.low = low;
    }

    public String getTime() {
        return time;
    }

    public void setTime(String time) {
        this.time = time;
    }

    public String getPatientId() {
        return patientId;
    }

    public void setPatientId(String patientId) {
        this.patientId = patientId;
    }

    public String getVisitId() {
        return visitId;
    }

    public void setVisitId(String visitId) {
        this.visitId = visitId;
    }

    public String getOid() {
        return oid;
    }

    public void setOid(String oid) {
        this.oid = oid;
    }

    public String getVisitType() {
        return visitType;
    }

    public void setVisitType(String visitType) {
        this.visitType = visitType;
    }
}
