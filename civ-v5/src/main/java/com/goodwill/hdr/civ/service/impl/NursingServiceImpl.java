package com.goodwill.hdr.civ.service.impl;


import com.goodwill.hdr.civ.config.Config;
import com.goodwill.hdr.civ.enums.HdrConstantEnum;
import com.goodwill.hdr.civ.enums.HdrTableEnum;
import com.goodwill.hdr.civ.service.NursingService;
import com.goodwill.hdr.civ.utils.*;
import com.goodwill.hdr.core.orm.MatchType;
import com.goodwill.hdr.core.orm.Page;
import com.goodwill.hdr.hbase.bo.PropertyFilter;
import com.goodwill.hdr.hbase.dto.responseVo.PageResultVo;
import com.goodwill.hdr.hbase.dto.responseVo.ResultVo;
import com.goodwill.hdr.hbaseQueryClient.builder.PageRequestBuilder;
import com.goodwill.hdr.hbaseQueryClient.service.HbaseQueryClient;
import com.goodwill.hdr.rest.client.transmission.JhdcpHttpSender;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

@Service
public class NursingServiceImpl implements NursingService {

    private static Logger logger = LoggerFactory.getLogger(NursingServiceImpl.class);

    @Autowired
    JhdcpHttpSender jhdcpHttpSender;
    private final HbaseQueryClient hbaseQueryClient;

    public NursingServiceImpl(HbaseQueryClient hbaseQueryClient) {
        this.hbaseQueryClient = hbaseQueryClient;
    }

    private Map<String, String> getParamValue(String oid, String patientId, String visitId, Map<String, String> configs) {
        // TODO Auto-generated method stub
        Map<String, String> rs = new HashMap<String, String>();
        for (String key : configs.keySet()) {
            String[] config = configs.get(key).split(",");
            if ("IN_PATIENT_ID".equals(config[0])) {
                rs.put(key, patientId);
            }
            if ("VISIT_ID".equals(config[0])) {
                rs.put(key, visitId);
            }
            List<Map<String, String>> list = new ArrayList<>();
            ResultVo<PageResultVo<Map<String, String>>> resultVo2 = hbaseQueryClient.getPageByCondition(
                    PageRequestBuilder.init()
                            .tableName(config[1])
                            .patientId(patientId)
                            .oid(oid)
                            .visitId(visitId)
                            .visitTypeCode("")
                            .filters(new ArrayList<PropertyFilter>())
                            .pageNo(0)
                            .pageSize(0)
                            .orderBy("")
                            .desc()
                            .column(config[0])
                            .build());
            if(resultVo2.isSuccess()){
                list = resultVo2.getContent().getResult();
            }
            //List<Map<String, String>> list = hbaseDao.findConditionByPatientVisitId(config[1], oid, patientId, visitId, new ArrayList<PropertyFilter>(), config[0]);
            if (list.size() > 0) {
                rs.put(key, list.get(0).get(config[0]));
            }
        }
        return rs;
    }




    @Override
    public List<Map<String, String>> getAllINVisits(String outPatientId) {

        //就诊列表
        List<Map<String, String>> visits = new ArrayList<Map<String, String>>();

            //查询中台solr
        String[] split = outPatientId.split(",");


            //根据上述筛选后的患者编号和就诊类型   获取所有就诊列表
            for (String s : split) {
                String[] vtpid = s.split("\\|");

                getVisitsAndAddToVisits(visits, vtpid[3],vtpid[2], vtpid[1], vtpid[0]);
            }
        Utils.sortListByDate(visits, "START_TIME", "desc");
        return visits;
    }

    /**
     * @param visits    存储就诊列表的集合
     * @param patientId 患者编号
     * @param visitType 就诊类型
     * @return 返回类型： void
     * @Description 方法描述: 根据患者编号和就诊类型查询该类型下的就诊列表，并将数据填充到visits
     */
    private void getVisitsAndAddToVisits(List<Map<String, String>> visits,String visitId, String oid, String patientId, String visitType) {
        List<PropertyFilter> filters = new ArrayList<PropertyFilter>();
        if (visitType.equals("02")) {

            //住院
            filters.add(new PropertyFilter("TRANS_NO", MatchType.EQ.getOperation(), "0")); //仅取入出院，不取转科
            List<Map<String, String>> patAdts = new ArrayList<>();
            ResultVo<PageResultVo<Map<String, String>>> resultVo2 = hbaseQueryClient.getPageByCondition(
                    PageRequestBuilder.init()
                            .tableName(   HdrTableEnum.HDR_PAT_ADT.getCode())
                            .patientId(patientId)
                            .oid(oid)
                            .visitId(visitId)
                            .visitTypeCode("02")
                            .filters(filters)
                            .pageNo(0)
                            .pageSize(0)
                            .orderBy("")
                            .desc()
                            .column("DEPT_DISCHARGE_FROM_CODE", "DEPT_DISCHARGE_FROM_NAME",
                                    "ADMISSION_TIME", "DISCHARGE_TIME", "VISIT_ID", "DEPT_ADMISSION_TO_NAME",
                                    "DEPT_ADMISSION_TO_CODE", "INP_NO", "IN_PATIENT_ID", "OID", "ORG_NO", "ORG_NAME")
                            .build());
            if(resultVo2.isSuccess()){
                patAdts = resultVo2.getContent().getResult();
            }
//            List<Map<String, String>> patAdts = hbaseDao.findConditionByPatient(HdrTableEnum.HDR_PAT_ADT.getCode(), oid,
//                    patientId, filters, new String[]{"DEPT_DISCHARGE_FROM_CODE", "DEPT_DISCHARGE_FROM_NAME",
//                            "ADMISSION_TIME", "DISCHARGE_TIME", "VISIT_ID", "DEPT_ADMISSION_TO_NAME",
//                            "DEPT_ADMISSION_TO_CODE", "INP_NO", "IN_PATIENT_ID", "OID", "ORG_NO", "ORG_NAME"});
            for (Map<String, String> adt : patAdts) {
                Map<String, String> covert = new HashMap<String, String>();
                covert.put("END_DEPT_CODE", adt.get("DEPT_DISCHARGE_FROM_CODE")); //出院科室
                covert.put("END_DEPT_NAME", adt.get("DEPT_DISCHARGE_FROM_NAME"));
                covert.put("START_DEPT_NAME", adt.get("DEPT_ADMISSION_TO_NAME")); //入院科室
                covert.put("START_DEPT_CODE", adt.get("DEPT_ADMISSION_TO_CODE"));
                covert.put("START_TIME", adt.get("ADMISSION_TIME")); //入院时间
                covert.put("END_TIME", adt.get("DISCHARGE_TIME")); //出院时间
                covert.put("VISIT_ID", adt.get("VISIT_ID")); //就诊次数
                covert.put("OID", adt.get("OID"));
                covert.put("orgNo", adt.get("ORG_NO"));
                covert.put("orgName", adt.get("ORG_NAME"));
                //查询本次住院诊断  主诊断
                //String visitId = adt.get("VISIT_ID");
                if (!covert.isEmpty()) {
                    covert.put("INP_NO", adt.get("INP_NO"));
                    covert.put("VISIT_TYPE", "INPV");
                    covert.put("NOW_PATIENT", adt.get("IN_PATIENT_ID"));//区分传入的患者编号和通过EID关联出来的患者编号
                    visits.add(covert);
                }
            }
        } else {
//            if (VisitTypeEnum.Emergency.getCode().equals(visitType)) {
//                filters.add(new PropertyFilter("EMERGENCY_VISIT_IND", "STRING", MatchType.EQ.getOperation(), "true"));
//            }

            List<Map<String, String>> outVisits = new ArrayList<>();
            ResultVo<PageResultVo<Map<String, String>>> resultVo2 = hbaseQueryClient.getPageByCondition(
                    PageRequestBuilder.init()
                            .tableName(   HdrTableEnum.HDR_OUT_VISIT.getCode())
                            .patientId(patientId)
                            .oid(oid)
                            .visitId(visitId)
                            .visitTypeCode("")
                            .filters(filters)
                            .pageNo(0)
                            .pageSize(0)
                            .orderBy("")
                            .desc()
                            .column("REGISTING_TIME", "VISIT_TIME", "VISIT_ID", "VISIT_DEPT_NAME",
                                    "VISIT_DEPT_CODE", "VISIT_FLAG", "OUT_PATIENT_ID", "EMERGENCY_VISIT_IND"
                                    , "OUTP_NO", "VISIT_STATUS_CODE", "VISIT_STATUS_NAME", "OID", "ORG_NO", "ORG_NAME")
                            .build());
            if(resultVo2.isSuccess()){
                outVisits = resultVo2.getContent().getResult();
            }
//            List<Map<String, String>> outVisits = hbaseDao
//                    .findConditionByPatient(HdrTableEnum.HDR_OUT_VISIT.getCode(), oid, patientId, filters,
//                            "REGISTING_TIME", "VISIT_TIME", "VISIT_ID", "VISIT_DEPT_NAME",
//                            "VISIT_DEPT_CODE", "VISIT_FLAG", "OUT_PATIENT_ID", "EMERGENCY_VISIT_IND"
//                            , "OUTP_NO", "VISIT_STATUS_CODE", "VISIT_STATUS_NAME", "OID", "ORG_NO", "ORG_NAME");
            for (Map<String, String> outVisit : outVisits) {
                //北医三院特殊处理：1-挂号未就诊 9-退号，忽略这两类门诊信息
                if (HdrConstantEnum.HOSPITAL_BYSY.getCode().equals(oid)) {
                    String visitFlag = outVisit.get("VISIT_FLAG");
                    if (visitFlag != null && "1,9,".contains(visitFlag + ",")) {
                        continue;
                    }
                }
                if (StringUtils.isBlank(outVisit.get("VISIT_DEPT_NAME")) || StringUtils.isBlank(outVisit.get("VISIT_TIME"))) {
                    continue;
                }
                Map<String, String> covert = new HashMap<String, String>();
                covert.put("START_DEPT_NAME", outVisit.get("VISIT_DEPT_NAME")); //就诊科室
                covert.put("START_DEPT_CODE", outVisit.get("VISIT_DEPT_CODE"));
                covert.put("OID", outVisit.get("OID"));
                covert.put("orgNo", outVisit.get("ORG_NO"));
                covert.put("orgName", outVisit.get("ORG_NAME"));
                //若就诊时间没有，用挂号时间代替
                if (StringUtils.isBlank(outVisit.get("START_TIME"))) {
                    covert.put("START_TIME", outVisit.get("REGISTING_TIME"));
                } else {
                    covert.put("START_TIME", outVisit.get("VISIT_TIME")); //就诊时间
                }
                covert.put("VISIT_ID", outVisit.get("VISIT_ID").trim()); //就诊次数
                covert.put("VISIT_NO", outVisit.get("OUTP_NO")); //就诊号
                covert.put("VISIT_STATUS_NAME", outVisit.get("VISIT_STATUS_NAME")); //就诊状态
                covert.put("VISIT_STATUS_CODE", outVisit.get("VISIT_STATUS_CODE")); //就诊状态code
                //区分门诊和急诊
                if ("true".equals(outVisit.get("EMERGENCY_VISIT_IND"))) {
                    covert.put("VISIT_TYPE", "EMPV");
                } else {
                    covert.put("VISIT_TYPE", "OUTPV");
                }
                covert.put("NOW_PATIENT", outVisit.get("OUT_PATIENT_ID"));
                covert.put("VISIT_STATUS_NAME", outVisit.get("VISIT_STATUS_NAME"));
                visits.add(covert);

            }

        }
    }


    public Map<String, String> getInfoFromPatient(String oid, String patientId, String visitType,String visitId) {
        Map<String, String> infos = new HashMap<String, String>();
        infos.put("PATIENT_ID", patientId);
        List<PropertyFilter> filters = new ArrayList<PropertyFilter>();
        if (StringUtils.isNotBlank(visitType)) {
            filters.add(new PropertyFilter("VISIT_TYPE_CODE",  MatchType.EQ.getOperation(), visitType));
        }
        //可能得到两条记录  门诊患者信息 和 住院患者信息
        List<Map<String, String>> indexs = new ArrayList<>();
        ResultVo<PageResultVo<Map<String, String>>> resultVo2 = hbaseQueryClient.getPageByCondition(
                PageRequestBuilder.init()
                        .tableName(   HdrTableEnum.HDR_PATIENT.getCode())
                        .patientId(patientId)
                        .oid(oid)
                        .visitId(visitId)
                        .visitTypeCode(visitType)
                        .filters(filters)
                        .pageNo(0)
                        .pageSize(0)
                        .orderBy("")
                        .desc()
                        .column("EID", "PERSON_NAME", "SEX_NAME", "DATE_OF_BIRTH", "INP_NO",
                                "OUTP_NO", "IN_PATIENT_ID", "OUT_PATIENT_ID", "OID", "GLOBLE_ID", "ID_CARD_NO")
                        .build());
        if(resultVo2.isSuccess()){
            indexs = resultVo2.getContent().getResult();
        }
//        List<Map<String, String>> indexs = hbaseDao.findConditionByPatient(HdrTableEnum.HDR_PATIENT.getCode(), oid,
//                patientId, filters, new String[]{"EID", "PERSON_NAME", "SEX_NAME", "DATE_OF_BIRTH", "INP_NO",
//                        "OUTP_NO", "IN_PATIENT_ID", "OUT_PATIENT_ID", "OID", "GLOBLE_ID", "ID_CARD_NO"});
        if (indexs.size() == 0) { //未找到患者
            infos.put("STATUS", "0");
            return infos;
        }
        //循环遍历患者信息  记录需要的字段
        for (Map<String, String> one : indexs) {
            Utils.checkAndPutToMap(infos, "PERSON_NAME", one.get("PERSON_NAME"), "", false);
            Utils.checkAndPutToMap(infos, "SEX_NAME", one.get("SEX_NAME"), "", false);
            Utils.checkAndPutToMap(infos, "EID", one.get("EID"), "", false);
            Utils.checkAndPutToMap(infos, "OID", one.get("OID"), "", false);
            Utils.checkAndPutToMap(infos, "GLOBLE_ID", one.get("GLOBLE_ID"), "", false);
            Utils.checkAndPutToMap(infos, "INP_NO", one.get("INP_NO"), "住院号未知", false);
            Utils.checkAndPutToMap(infos, "OUTP_NO", one.get("OUT_NO"), "门诊号未知", false);
            Utils.checkAndPutToMap(infos, "IN_PATIENT_ID", one.get("IN_PATIENT_ID"), "", false);
            Utils.checkAndPutToMap(infos, "OUT_PATIENT_ID", one.get("OUT_PATIENT_ID"), "", false);
            Utils.checkAndPutToMap(infos, "DATE_OF_BIRTH", one.get("DATE_OF_BIRTH"), "", false);
            Utils.checkAndPutToMap(infos, "ID_CARD_NO", one.get("ID_CARD_NO"), "", false);
        }
        //处理出生日期
        String birthday = infos.get("DATE_OF_BIRTH");
        if (StringUtils.isNotBlank(birthday)) {
            infos.put("DATE_OF_BIRTH",  birthday);
        }
        return infos;
    }


    /**
     * 获取护理单表头
     *
     * @return
     */
    @Override
    public List<Map<String, String>> getNurseTableHead(String oid) {
        List<Map<String, String>> head = Config.getCIV_NURSE_TABLE_HEAD(oid);
        return head;
    }

    /**
     * 获取护理单详情
     *
     * @param
     * @param date
     */
    @Override
    public Page<Map<String, String>> getNurseDetail(String oid, String patientId, String visitId, String visitType, String topic, String date, int pageNo, int pageSize) {
        Page<Map<String, String>> page = new Page<Map<String, String>>();
        if (0 == pageNo) {
            pageNo = 1;
        }
        if (0 == pageSize) {
            pageSize = 10;
        }
        page.setOrderBy("LAST_MODIFY_DATE_TIME");
        page.setOrderDir("desc");
        List<PropertyFilter> filters = new ArrayList<PropertyFilter>();

//        if (StringUtils.isNotBlank(visitId)) {
//            filters.add(new PropertyFilter("VISIT_ID", "STRING", MatchType.EQ.getOperation(), visitId));
//        }
        if (StringUtils.isNotBlank(visitType)) {
            filters.add(new PropertyFilter("VISIT_TYPE_CODE", MatchType.EQ.getOperation(), visitType));
        }
        if (StringUtils.isNotBlank(topic)) {
            filters.add(new PropertyFilter("NR_NAME", MatchType.EQ.getOperation(), topic));
        }
        if(StringUtils.isNotBlank(date)){
            filters.add(new PropertyFilter("LAST_MODIFY_DATE_TIME", MatchType.GE.getOperation(), date + " 06:00:00"));

            LocalDate localDate = LocalDate.parse(date, DateTimeFormatter.ofPattern("yyyy-MM-dd"));
            LocalDate plusDay = localDate.plusDays(1);

            filters.add(new PropertyFilter("LAST_MODIFY_DATE_TIME", MatchType.LE.getOperation(), plusDay + " 06:00:00"));
        }
        List<Map<String, String>> list = new ArrayList<>();
        ResultVo<PageResultVo<Map<String, String>>> resultVo2 = hbaseQueryClient.getPageByCondition(
                PageRequestBuilder.init()
                        .tableName(   HdrTableEnum.HDR_NURSE_CONTENT.getCode())
                        .patientId(patientId)
                        .oid(oid)
                        .visitId(visitId)
                        .visitTypeCode(visitType)
                        .filters(filters)
                        .pageNo(0)
                        .pageSize(0)
                        .orderBy("")
                        .desc()
                        .column(Config.getCIV_NURSE_TABLE_FIELD(oid))
                        .build());
        if(resultVo2.isSuccess()){
            list = resultVo2.getContent().getResult();
        }
//        List<Map<String, String>> list = hbaseDao.findConditionByPatient(HdrTableEnum.HDR_NURSE_CONTENT.getCode(), oid, patientId,
//                filters, Config.getCIV_NURSE_TABLE_FIELD(oid));

        page.setTotalCount(list.size());
        //首先通过日期排序
        Utils.sortListByDate(list, "LAST_MODIFY_DATE_TIME", "desc");
        ListPage<Map<String, String>> listPage = new ListPage<Map<String, String>>(list, pageNo, pageSize);
        List<Map<String, String>> item = new ArrayList<Map<String, String>>();
        for (Map<String, String> map : listPage.getPagedList()) {
            Map<String, String> tempMap = new HashMap<String, String>();
            //处理html,PDF和url
            if ("HTML".equals(map.get("FILE_CLASS"))) {
                tempMap.put("linkType", "HTML");
            } else if ("PDF".equals(map.get("FILE_CLASS"))) {
                tempMap.put("linkType", "PDF");
                //处理PDF数据为文件
                map.put("URL", CivUtils.getPdfData(map.get("NR_CONTENT_HTML"), oid, patientId, "0", map.get("DOCUMENT_ID")));
            } else if ("URL".equals(map.get("FILE_CLASS"))) {
                tempMap.put("linkType", "URL");
                //处理url中的参数
                map.put("URL", dealURL(map.get("URL"), oid, patientId, visitId));
            } else {
                tempMap.put("linkType", "HTML");
            }
            //字段映射
            ColumnUtil.convertMapping(tempMap, map, Config.getCIV_NURSE_TABLE_FIELD(oid));
            item.add(tempMap);
        }
        page.setResult(item);
        return page;
    }

    /**
     * 2024-08-02
     * 目前只有 世纪坛医院-护理记录-危重症记录单 使用，日后依情况模板化开发
     * @param url
     * @param oid
     * @param patientId
     * @param visitId
     * @return
     */
    public String dealURL(String url, String oid, String patientId, String visitId) {
        String regex = "\\$\\{(.*?)\\}";
        Pattern pattern = Pattern.compile(regex);
        Matcher matcher = pattern.matcher(url);
        List<String> matches = new ArrayList<>();
        while (matcher.find()) {
            matches.add(matcher.group(1));
        }

        for (String s : matches) {
            if (s.equals("NOWDATE")) {
                url = url.replace("${" + s + "}", Utils.dateToStr(new Date(), "yyyy-MM-dd"));
            } else {
                String[] arr = s.split("\\|");
                String tableName = arr[0];
                String column = arr[1];
                logger.info("tableName:" + tableName + ";column:" + column);
                List<Map<String, String>> list = new ArrayList<>();
                ResultVo<PageResultVo<Map<String, String>>> resultVo2 = hbaseQueryClient.getPageByCondition(
                        PageRequestBuilder.init()
                                .tableName(tableName)
                                .patientId(patientId)
                                .oid(oid)
                                .visitId(visitId)
                                .visitTypeCode("")
                                .filters(new ArrayList<PropertyFilter>())
                                .pageNo(0)
                                .pageSize(0)
                                .orderBy("")
                                .desc()
                                .column(column)
                                .build());
                if (resultVo2.isSuccess()) {
                    list = resultVo2.getContent().getResult();
                    String value = list.get(0).get(column);
                    value = DateUtil.getDateStringByTime(value);
                    url = url.replace("${" + s + "}", value);
                }
            }
        }
        return url;
    }

    /**
     * 护理数量
     *
     * @return
     */
    @Override
    public Integer getCategoryNurseNum(String outPatientId) {
        int count = 0;

        //outPatientId
        if (StringUtils.isNotBlank(outPatientId)) {
            String[] outPids = outPatientId.split(",");
            for (String outPid : outPids) {
                String[] pid_vid = outPid.split("\\|");

                List<PropertyFilter> filters = new ArrayList<PropertyFilter>();

               filters.add(new PropertyFilter("VISIT_TYPE_CODE",MatchType.EQ.getOperation(), pid_vid[0]));
//
//                filters.add(new PropertyFilter("VISIT_ID", "STRING", MatchType.EQ.getOperation(), pid_vid[3]));
                List<Map<String, String>> list = new ArrayList<>();
                ResultVo<PageResultVo<Map<String, String>>> resultVo2 = hbaseQueryClient.getPageByCondition(
                        PageRequestBuilder.init()
                                .tableName(   HdrTableEnum.HDR_NURSE_CONTENT.getCode())
                                .patientId(pid_vid[1])
                                .oid(pid_vid[2])
                                .visitId(pid_vid[3])
                                .visitTypeCode(pid_vid[0])
                                .filters(filters)
                                .pageNo(0)
                                .pageSize(0)
                                .orderBy("")
                                .desc()
                                .column("LAST_MODIFY_DATE_TIME", "NR_NAME")
                                .build());
                if(resultVo2.isSuccess()){
                    list = resultVo2.getContent().getResult();
                }
//                List<Map<String, String>> list = hbaseDao.findConditionByPatient(HdrTableEnum.HDR_NURSE_CONTENT.getCode(), pid_vid[2],
//                        pid_vid[1], filters, new String[]{"LAST_MODIFY_DATE_TIME", "NR_NAME"});
                count += list.size();
            }
        }

        return count;
    }

    /**
     * 护理数量
     *
     * @param patientId
     * @param visitId
     * @return
     */
    @Override
    public Integer getVisitNurseNum(String oid, String patientId, String visitId, String visitType) {

        List<PropertyFilter> filters = new ArrayList<PropertyFilter>();
//
//        if (StringUtils.isNotBlank(visitId)) {
//            filters.add(new PropertyFilter("VISIT_ID", "STRING", MatchType.EQ.getOperation(), visitId));
//        }
        if (StringUtils.isNotBlank(visitType)) {
            if (visitType.equals("INPV")) {
                visitType = "02";
                filters.add(new PropertyFilter("VISIT_TYPE_CODE",  MatchType.EQ.getOperation(), "02"));
            } else {
                visitType = "01";
                filters.add(new PropertyFilter("VISIT_TYPE_CODE",  MatchType.EQ.getOperation(), "01"));
            }
        }
        List<Map<String, String>> list = new ArrayList<>();
        ResultVo<PageResultVo<Map<String, String>>> resultVo2 = hbaseQueryClient.getPageByCondition(
                PageRequestBuilder.init()
                        .tableName(   HdrTableEnum.HDR_NURSE_CONTENT.getCode())
                        .patientId(patientId)
                        .oid(oid)
                        .visitId(visitId)
                        .visitTypeCode(visitType)
                        .filters(filters)
                        .pageNo(0)
                        .pageSize(0)
                        .orderBy("")
                        .desc()
                        .column("LAST_MODIFY_DATE_TIME", "NR_NAME")
                        .build());
        if(resultVo2.isSuccess()){
            list = resultVo2.getContent().getResult();
        }
//        List<Map<String, String>> list = hbaseDao.findConditionByPatient(HdrTableEnum.HDR_NURSE_CONTENT.getCode(), oid,
//                patientId, filters, "LAST_MODIFY_DATE_TIME", "NR_NAME");


        return list.size();
    }

    @Override
    public List<Map<String, String>> getNurseTypesByVisit(String oid, String patientId, String visitId, String visitType) {


        List<PropertyFilter> filters = new ArrayList<PropertyFilter>();
//        if (StringUtils.isNotBlank(visitId)) {
//            filters.add(new PropertyFilter("VISIT_ID", "STRING", MatchType.EQ.getOperation(), visitId));
//        }
        if (StringUtils.isNotBlank(visitType)) {
            filters.add(new PropertyFilter("VISIT_TYPE_CODE",  MatchType.EQ.getOperation(), visitType));
        }
        List<Map<String, String>> list = new ArrayList<>();
        ResultVo<PageResultVo<Map<String, String>>> resultVo2 = hbaseQueryClient.getPageByCondition(
                PageRequestBuilder.init()
                        .tableName(   HdrTableEnum.HDR_NURSE_CONTENT.getCode())
                        .patientId(patientId)
                        .oid(oid)
                        .visitId(visitId)
                        .visitTypeCode(visitType)
                        .filters(filters)
                        .pageNo(0)
                        .pageSize(0)
                        .orderBy("")
                        .desc()
                        .column("NR_CODE", "NR_NAME")
                        .build());
        if(resultVo2.isSuccess()){
            list = resultVo2.getContent().getResult();
        }
//        List<Map<String, String>> list = hbaseDao.findConditionByPatient(HdrTableEnum.HDR_NURSE_CONTENT.getCode(), oid,
//                patientId, filters, "NR_CODE", "NR_NAME");

        List<Map<String, String>> res = new ArrayList<Map<String, String>>();
        Map<String, String> all = new HashMap<String, String>();
        all.put("code", "all");
        all.put("name", "全部");
        res.add(all);
        Set<String> set = new HashSet<String>();
        for (Map<String, String> map : list) {
            Map<String, String> temp = new HashMap<String, String>();
            if (StringUtils.isBlank(map.get("NR_NAME")) || StringUtils.isBlank(map.get("NR_CODE"))) {
                continue;
            }
            if (set.contains(map.get("NR_CODE"))) {
                continue;
            }
            set.add(map.get("NR_CODE"));
            temp.put("code", map.get("NR_CODE"));
            temp.put("name", map.get("NR_NAME"));
            res.add(temp);
        }
        return res;
    }

    @Override
    public List<Map<String, String>> getNurseTypesByCaregory(String outPatientId) {
        List<Map<String, String>> list = new ArrayList<>();


        //outPatientId
        if (StringUtils.isNotBlank(outPatientId)) {
            String[] outPids = outPatientId.split(",");
            for (String outPid : outPids) {
                String[] pid_vid = outPid.split("\\|");

                List<PropertyFilter> filters = new ArrayList<PropertyFilter>();

                filters.add(new PropertyFilter("VISIT_TYPE_CODE", MatchType.EQ.getOperation(), pid_vid[0]));

                //filters.add(new PropertyFilter("VISIT_ID", "STRING", MatchType.EQ.getOperation(), pid_vid[3]));
                List<Map<String, String>> tmpList = new ArrayList<>();
                ResultVo<PageResultVo<Map<String, String>>> resultVo2 = hbaseQueryClient.getPageByCondition(
                        PageRequestBuilder.init()
                                .tableName(   HdrTableEnum.HDR_NURSE_CONTENT.getCode())
                                .patientId( pid_vid[1])
                                .oid(pid_vid[2])
                                .visitId(pid_vid[3])
                                .visitTypeCode(pid_vid[0])
                                .filters(filters)
                                .pageNo(0)
                                .pageSize(0)
                                .orderBy("")
                                .desc()
                                .column("NR_CODE", "NR_NAME")
                                .build());
                if(resultVo2.isSuccess()){
                    tmpList = resultVo2.getContent().getResult();
                }
//                List<Map<String, String>> tmpList = hbaseDao.findConditionByPatient(HdrTableEnum.HDR_NURSE_CONTENT.getCode(), pid_vid[2],
//                        pid_vid[1], filters, "NR_CODE", "NR_NAME");
                list.addAll(tmpList);

            }
        }
        List<Map<String, String>> res = new ArrayList<Map<String, String>>();
        Map<String, String> all = new HashMap<String, String>();
        all.put("code", "all");
        all.put("name", "全部");
        res.add(all);
        Set<String> set = new HashSet<String>();
        for (Map<String, String> map : list) {
            Map<String, String> temp = new HashMap<String, String>();
            if (StringUtils.isBlank(map.get("NR_NAME")) || StringUtils.isBlank(map.get("NR_CODE"))) {
                continue;
            }
            if (set.contains(map.get("NR_CODE"))) {
                continue;
            }
            set.add(map.get("NR_CODE"));
            temp.put("code", map.get("NR_CODE"));
            temp.put("name", map.get("NR_NAME"));
            res.add(temp);
        }
        return res;

    }

    @Override
    public Page<Map<String, String>> getNurseListByVisit(String oid, String patientId, String visitId, String visitType, String nurseType, int pageNo, int pageSize) {
        Page<Map<String, String>> page = new Page<Map<String, String>>();

        page.setPageNo(pageNo);
        page.setPageSize(pageSize);
        List<PropertyFilter> filters = new ArrayList<PropertyFilter>();
        if (StringUtils.isNotBlank(nurseType) && !"all".equals(nurseType)) {
            filters.add(new PropertyFilter("NR_CODE",  MatchType.EQ.getOperation(), nurseType));
        }
//        if (StringUtils.isNotBlank(visitId)) {
//            filters.add(new PropertyFilter("VISIT_ID", "STRING", MatchType.EQ.getOperation(), visitId));
//        }
        if (StringUtils.isNotBlank(visitType)) {
            filters.add(new PropertyFilter("VISIT_TYPE_CODE",  MatchType.EQ.getOperation(), visitType));
        }
        List<Map<String, String>> list = new ArrayList<>();
        ResultVo<PageResultVo<Map<String, String>>> resultVo2 = hbaseQueryClient.getPageByCondition(
                PageRequestBuilder.init()
                        .tableName(   HdrTableEnum.HDR_NURSE_CONTENT.getCode())
                        .patientId(patientId)
                        .oid(oid)
                        .visitId(visitId)
                        .visitTypeCode(visitType)
                        .filters(filters)
                        .pageNo(0)
                        .pageSize(0)
                        .orderBy("")
                        .desc()
                        .column("LAST_MODIFY_DATE_TIME", "NR_NAME", "IN_PATIENT_ID", "OUT_PATIENT_ID", "VISIT_ID", "VISIT_TYPE_CODE","FILE_CLASS")
                        .build());
        if(resultVo2.isSuccess()){
            list = resultVo2.getContent().getResult();
        }
//        List<Map<String, String>> list = hbaseDao.findConditionByPatient(HdrTableEnum.HDR_NURSE_CONTENT.getCode(), oid,
//                patientId, filters, "LAST_MODIFY_DATE_TIME", "NR_NAME", "IN_PATIENT_ID", "OUT_PATIENT_ID", "VISIT_ID", "VISIT_TYPE_CODE");
        //通过日期统计数据列表
        Map<String, String> data = new HashMap<String, String>();
        for (Map<String, String> map : list) {
            if (StringUtils.isBlank(map.get("LAST_MODIFY_DATE_TIME"))) {
                continue;
            }
            String time = DateUtil.getDateStringByTime(map.get("LAST_MODIFY_DATE_TIME"));
            String name = StringUtils.isBlank(map.get("NR_NAME")) ? "-" : map.get("NR_NAME");
            String pid = StringUtils.isNotBlank(map.get("IN_PATIENT_ID")) ? map.get("IN_PATIENT_ID") : map.get("OUT_PATIENT_ID");
            String vid = StringUtils.isNotBlank(map.get("VISIT_ID")) ? map.get("VISIT_ID") : "";
            String visitTypeCode = StringUtils.isNotBlank(map.get("VISIT_TYPE_CODE")) ? map.get("VISIT_TYPE_CODE") : "";
            String fileClass = StringUtils.isNotBlank(map.get("FILE_CLASS")) ? map.get("FILE_CLASS") : "";

            String s=time + "|" + name + "|" + pid + "|" + vid + "|" + visitTypeCode + "|" + fileClass;
            if (null != data.get(s)) {
                continue;
            }
            data.put(s, name);
        }
        List<Map<String, String>> res = new ArrayList<Map<String, String>>();
        for (String key : data.keySet()) {
            Map<String, String> temp = new HashMap<String, String>();
            temp.put("topic", data.get(key));
            temp.put("time", key.split("\\|")[0]);
            temp.put("patientId", key.split("\\|")[2]);
            temp.put("visitId", key.split("\\|")[3]);
            temp.put("visitType", key.split("\\|")[4]);
            temp.put("linkType", key.split("\\|")[5]);
            res.add(temp);
        }
        //首先通过日期排序
        Utils.sortListByDate(res, "time", "desc");
        page.setTotalCount(res.size());
        page.setResult(new ListPage<Map<String, String>>(res, pageNo, pageSize).getPagedList());
        return page;
    }

    @Override
    public Page<Map<String, String>> getNurseListByCategory(String outPatientId, String nurseType, int pageNo, int pageSize) {
        Page<Map<String, String>> page = new Page<Map<String, String>>();

        page.setPageNo(pageNo);
        page.setPageSize(pageSize);
        List<Map<String, String>> list = new ArrayList<>();
        //outPatientId
        if (StringUtils.isNotBlank(outPatientId)) {
            String[] outPids = outPatientId.split(",");
            for (String outPid : outPids) {
                String[] pid_vid = outPid.split("\\|");

                List<PropertyFilter> filters2 = new ArrayList<PropertyFilter>();
                if (StringUtils.isNotBlank(nurseType) && !"all".equals(nurseType)) {
                    filters2.add(new PropertyFilter("NR_CODE",  MatchType.EQ.getOperation(), nurseType));
                }


                filters2.add(new PropertyFilter("VISIT_TYPE_CODE",  MatchType.EQ.getOperation(), pid_vid[0]));
//
//                filters2.add(new PropertyFilter("VISIT_ID", "STRING", MatchType.EQ.getOperation(), pid_vid[3]));
                List<Map<String, String>> tmplist = new ArrayList<>();
                ResultVo<PageResultVo<Map<String, String>>> resultVo2 = hbaseQueryClient.getPageByCondition(
                        PageRequestBuilder.init()
                                .tableName(   HdrTableEnum.HDR_NURSE_CONTENT.getCode())
                                .patientId( pid_vid[1])
                                .oid(pid_vid[2])
                                .visitId( pid_vid[3])
                                .visitTypeCode( pid_vid[0])
                                .filters(filters2)
                                .pageNo(0)
                                .pageSize(0)
                                .orderBy("")
                                .desc()
                                .column("LAST_MODIFY_DATE_TIME", "NR_NAME", "IN_PATIENT_ID", "OUT_PATIENT_ID", "VISIT_ID", "VISIT_TYPE_CODE", "OID")
                                .build());
                if(resultVo2.isSuccess()){
                    tmplist = resultVo2.getContent().getResult();
                }
//                List<Map<String, String>> tmplist = hbaseDao.findConditionByPatient(HdrTableEnum.HDR_NURSE_CONTENT.getCode(), pid_vid[2],
//                        pid_vid[1], filters2, "LAST_MODIFY_DATE_TIME", "NR_NAME", "IN_PATIENT_ID", "OUT_PATIENT_ID", "VISIT_ID", "VISIT_TYPE_CODE", "OID");
                list.addAll(tmplist);
            }

        }
        List<Map<String, String>> sortList = list.stream().
                filter(m -> StringUtils.isNotBlank(m.get("LAST_MODIFY_DATE_TIME"))).
                sorted((m1, m2) -> m2.get("LAST_MODIFY_DATE_TIME").compareTo(m1.get("LAST_MODIFY_DATE_TIME"))).
                collect(Collectors.toList());
        int fromIndex = (pageNo - 1) * pageSize;
        int toIndex = Math.min(pageNo * pageSize, sortList.size());
        List<Map<String, String>> subList = sortList.subList(fromIndex, toIndex);
        List<Map<String, String>> res = new ArrayList<Map<String, String>>();



        Map<String, String> data = new HashMap<String, String>();
        for (Map<String, String> map : subList) {
            if (StringUtils.isBlank(map.get("LAST_MODIFY_DATE_TIME"))) {
                continue;
            }
            String time = DateUtil.getDateStringByTime(map.get("LAST_MODIFY_DATE_TIME"));
            String name = StringUtils.isBlank(map.get("NR_NAME")) ? "-" : map.get("NR_NAME");
            String pid = StringUtils.isNotBlank(map.get("IN_PATIENT_ID")) ? map.get("IN_PATIENT_ID") : map.get("OUT_PATIENT_ID");
            String vid = StringUtils.isNotBlank(map.get("VISIT_ID")) ? map.get("VISIT_ID") : "";
            String oid = StringUtils.isNotBlank(map.get("OID")) ? map.get("OID") : "";
            String visitTypeCode = StringUtils.isNotBlank(map.get("VISIT_TYPE_CODE")) ? map.get("VISIT_TYPE_CODE") : "";
            if (null != data.get(time + "|" + name + "|" + pid + "|" + vid + "|" + visitTypeCode+"|"+oid)) {
                continue;
            }
            data.put(time + "|" + name + "|" + pid + "|" + vid + "|" + visitTypeCode+"|"+oid, name);
        }

        for (String key : data.keySet()) {
            Map<String, String> temp = new HashMap<String, String>();
            temp.put("topic", data.get(key));
            temp.put("time", key.split("\\|")[0]);
            temp.put("patientId", key.split("\\|")[2]);
            temp.put("visitId", key.split("\\|")[3]);
            temp.put("visitType", key.split("\\|")[4]);
            temp.put("oid",key.split("\\|")[5]);
            res.add(temp);
        }

        page.setTotalCount(sortList.size());
        page.setResult(res);
        return page;
    }
}
