package com.goodwill.hdr.civ.service;

import com.goodwill.hdr.web.common.vo.ResultVO;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Description 类描述：诊断服务接口
 * @Date 2018年5月11日
 * @modify 修改记录：
 */
public interface DiagnoseService {





    /**
     * @return 返回类型： List<Map<String, Object>>
     * @Description 方法描述: 根据患者获得所有诊断记录
     */
    public List<Map<String, Object>> getDiagsList(String outPatientId, String startDate, String endDate);

    /**
     * @return 返回类型：int
     * @Description 方法描述: 根据患者获得所有诊断记录数量
     */
    public void getDiagsListNum(String outPatientId, Map<String, Object> resultMap);

    /**
     * 返回医技视图诊断信息
     *
     * @param this_oid
     * @param oid
     * @param patientId
     * @param visitId
     * @param visitType
     * @return
     */
    public ResultVO<Map<String, Object>> getDiags(String this_oid, String oid, String patientId, String visitId, String visitType);

    /**
     * 返回医技视图诊断信息详情列表
     * @param this_oid
     * @param oid
     * @param outPatientId
     * @return
     */
    public ResultVO<List<Map<String, Object>>> getDiagLists(String this_oid, String oid, String outPatientId);
}
