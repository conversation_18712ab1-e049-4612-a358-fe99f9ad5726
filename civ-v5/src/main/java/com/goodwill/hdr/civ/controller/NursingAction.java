package com.goodwill.hdr.civ.controller;


import com.goodwill.hdr.civ.config.Config;
import com.goodwill.hdr.civ.service.NursingService;
import com.goodwill.hdr.core.orm.Page;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Description 类描述：护理记录Action
 * @Date 2018年6月19日
 * @modify 修改记录：
 */
@RequestMapping("/nursing")
@RestController
@Api(tags = "护理记录查询")
public class NursingAction {



    @Autowired
    private NursingService nursingService;


    /**
     * @Description 住院的就诊列表
     */
    @ApiOperation(value = "获取住院的就诊列表", notes = "获取住院的就诊列表", httpMethod = "POST")
    @RequestMapping(value = "/getAllINVisits", method = RequestMethod.POST)
    public List<Map<String, String>> getAllINVisits(String outPatientId) {
//        String year = getParameter("year");
        List<Map<String, String>> rs = new ArrayList<Map<String, String>>();
        rs = nursingService.getAllINVisits(outPatientId);
        return rs;
    }

    /**
     * 护理记录调用第三方的url 护理类型配置
     */
    @ApiOperation(value = "护理记录调用第三方的url 护理类型配置", notes = "护理记录调用第三方的url 护理类型配置", httpMethod = "POST")
    @RequestMapping(value = "/getNuringUrlTypes", method = RequestMethod.POST)
    public Map<String, Object> getNuringUrlTypes(String oid) {
        Map<String, Object> rs = Config.getCIV_NURSE_URL_TYPES(oid);
        return rs;
    }

    /**
     * 新增护理单-查看护理单类型下拉框
     */
    @ApiOperation(value = "查看护理单类型下拉框", notes = "查看护理单类型下拉框", httpMethod = "POST")
    @RequestMapping(value = "/getNurseTypeByVisit", method = RequestMethod.POST)
    public List<Map<String, String>> getNurseTypeByVisit(String oid,String patientId, String visitId, String visitType) {
        List<Map<String, String>> rs = nursingService.getNurseTypesByVisit(oid, patientId, visitId, visitType);
        return rs;
    }
    /**
     * 新增护理单-查看护理单类型下拉框
     */
    @ApiOperation(value = "查看护理单类型下拉框", notes = "查看护理单类型下拉框", httpMethod = "POST")
    @RequestMapping(value = "/getNurseTypeByCaregory", method = RequestMethod.POST)
    public List<Map<String, String>> getNurseTypeByCaregory( String outPatientId) {
        List<Map<String, String>> rs = nursingService.getNurseTypesByCaregory(outPatientId);
        return rs;
    }

    /**
     * 新增护理单-护理单列表
     */

    @RequestMapping(value = "/getNurseListByVisit", method = RequestMethod.POST)
    public Page<Map<String, String>> getNurseListByVisit(String oid, String patientId, String visitId, String visitType, String nurseType, int pageSize, int pageNo) {
        if (pageNo == 0) {
            pageNo = 1;
        }
        if (pageSize == 0) {
            pageSize = 10;
        }
        Page<Map<String, String>> rs = nursingService.getNurseListByVisit(oid, patientId, visitId, visitType, nurseType, pageNo, pageSize);
        return rs;
    }
    /**
     * 新增护理单-护理单列表
     */

    @RequestMapping(value = "/getNurseListByCategory", method = RequestMethod.POST)
    public Page<Map<String, String>> getNurseListByCategory( String outPatientId, String nurseType, int pageSize, int pageNo) {
        if (pageNo == 0) {
            pageNo = 1;
        }
        if (pageSize == 0) {
            pageSize = 10;
        }
        Page<Map<String, String>> rs = nursingService.getNurseListByCategory( outPatientId, nurseType, pageNo, pageSize);
        return rs;
    }

    /**
     * 获取护理单表头
     */
    @ApiOperation(value = "获取护理单表头", notes = "获取护理单表头", httpMethod = "POST")
    @RequestMapping(value = "/getNurseTableHead", method = RequestMethod.POST)
    public List<Map<String, String>> getNurseTableHead(String oid) {
        List<Map<String, String>> rs = nursingService.getNurseTableHead(oid);
        return rs;
    }

    /**
     * 查询护理单详情
     */
    @ApiOperation(value = "查询护理单详情", notes = "查询护理单详情", httpMethod = "POST")
    @RequestMapping(value = "/getNurseDetail", method = RequestMethod.POST)
    public Page<Map<String, String>> getNurseDetail(String oid, String patientId, String visitId, String visitType, String topic, String date,int pageNo, int pageSize) {
//        String date = getParameter("date");
//        String outPatientId = getParameter("outPatientId");
        Page<Map<String, String>> rs = nursingService.getNurseDetail(oid, patientId, visitId, visitType, topic, date, pageNo, pageSize);
        return rs;
    }
}
