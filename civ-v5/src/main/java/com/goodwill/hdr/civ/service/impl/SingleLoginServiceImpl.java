package com.goodwill.hdr.civ.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.goodwill.hdr.civ.config.ConfigCache;
import com.goodwill.hdr.civ.service.SingleLoginService;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.Response;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.io.OutputStream;
import java.net.HttpURLConnection;
import java.net.URL;
import java.util.HashMap;
import java.util.Map;

@Service
public class SingleLoginServiceImpl implements SingleLoginService {
    private static Logger logger = LoggerFactory.getLogger(SingleLoginServiceImpl.class);

    @Override
    public String getSingleloginUserByOther(String oid, String param) {
        String result = "";

        String url = ConfigCache.getCache(oid, "SSO_CONFIG");
        String ssoRequestMetnod = ConfigCache.getCache(oid, "SSO_REQUEST_METNOD_CONFIG");
        ssoRequestMetnod = StringUtils.isNotBlank(ssoRequestMetnod) ? ssoRequestMetnod.trim() : ssoRequestMetnod;
        String returnConfig = ConfigCache.getCache(oid, "SSO_RETURN_FORMAT_CONFIG");
        if (StringUtils.isBlank(returnConfig)) {
            System.out.printf("请配置单点登录用户名获取方式【SSO_RETURN_FORMAT_CONFIG】，值示例：如果返回值格式为{\"result\":\"{\"userCode\":\"admin\"}\"},则配置result:userCode;如果返回值格式为{\"userCode\":\"admin\"}\"},则配置userCode。");
            return result;
        }

        String s = "";
        JSONObject jsonObject = new JSONObject();
        if ("GET".equalsIgnoreCase(ssoRequestMetnod)) {
            logger.info("进入get请求");
            url = url + "?" + param;
            logger.info("url====：" + url);
            s = requestByGet(url);

            jsonObject = JSONObject.parseObject(s);
            if (jsonObject.getIntValue("code") != 200) {
                logger.info("请求失败：" + jsonObject.getIntValue("code") + "：" + s);
                return result;
            }
        } else {
            logger.info("进入post请求");
            String ssoParamFormat = ConfigCache.getCache(oid, "SSO_PARAM_FORMAT_CONFIG");
            String ssoParam = ConfigCache.getCache(oid, "SSO_PARAM_CONFIG");
            String ssoParamPosition = ConfigCache.getCache(oid, "SSO_PARAM_POSITION_CONFIG");

            String contentType = "";
            if ("json".equalsIgnoreCase(ssoParamFormat)) {
                contentType = "application/json";
                ssoParam = ssoParam.replace("#{param}", param);
            } else {
                ssoParam = param;
            }

            if("header".equalsIgnoreCase(ssoParamPosition)){
                Map<String, String> headersMap = new HashMap<>();
                JSONObject json = JSONObject.parseObject(ssoParam);
                for (Map.Entry<String, Object> entry : json.entrySet()) {
                    headersMap.put(entry.getKey(), entry.getValue().toString());
                }
                s = getPost(url, headersMap, contentType);
            }else{
                s = getPost(url, ssoParam, contentType);
            }

            if (StringUtils.isBlank(s)) {
                return result;
            }
            jsonObject = JSONObject.parseObject(s);
        }
        result = getUserCode(returnConfig, jsonObject);

        return result;
    }


    /**
     * get方式调用接口
     */
    public String requestByGet(String url) {
        String result = "";
        OkHttpClient client = new OkHttpClient();
        Request request = new Request.Builder().url(url).build();
        try (Response response = client.newCall(request).execute()) {
            if (!response.isSuccessful())
                throw new IOException("Unexpected code " + response);
            result = response.body().string();
        } catch (Exception e) {
            e.printStackTrace();
        }
        logger.info("get接口结果值：" + result);
        return result;
    }

    /**
     * post方式调用接口
     * 参数在请求体中
     */
    public String getPost(String url, String param, String contentType) {
        logger.info("url：" + url + "，param：" + param + "，contentType=" + contentType);
        String result = "";
        try {
            URL apiUrl = new URL(url);
            HttpURLConnection con = (HttpURLConnection) apiUrl.openConnection();
            con.setRequestMethod("POST");
            con.setRequestProperty("Content-Type", StringUtils.isBlank(contentType) ? "text/xml;charset=utf-8" : contentType);
            con.setDoOutput(true);
            OutputStream os = con.getOutputStream();
            os.write(param.getBytes());
            os.flush();
            os.close();

            int responseCode = con.getResponseCode();
            logger.info("responseCode=======" + responseCode);
            if (responseCode == HttpURLConnection.HTTP_OK) {
                BufferedReader ins = new BufferedReader(new InputStreamReader(con.getInputStream()));
                String inputLine;
                StringBuffer response = new StringBuffer();

                while ((inputLine = ins.readLine()) != null) {
                    response.append(inputLine);
                }
                ins.close();
                result = response.toString();
                logger.info("result=======" + result);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return result;
    }


    /**
     * post方式调用接口
     * 参数在HTTP Header中
     */
    public String getPost(String url, Map<String, String> headers, String contentType) {
        logger.info("url：" + url + "，headers：" + headers + "，contentType=" + contentType);

        String result = "";
        try {
            URL apiUrl = new URL(url);
            HttpURLConnection con = (HttpURLConnection) apiUrl.openConnection();
            con.setRequestMethod("POST");
            con.setRequestProperty("Content-Type", StringUtils.isBlank(contentType) ? "text/xml;charset=utf-8" : contentType);

            if (headers != null) {
                for (Map.Entry<String, String> entry : headers.entrySet()) {
                    con.setRequestProperty(entry.getKey(), entry.getValue());
                }
            }

            int responseCode = con.getResponseCode();
            logger.info("responseCode=======" + responseCode);
            if (responseCode == HttpURLConnection.HTTP_OK) {
                BufferedReader ins = new BufferedReader(new InputStreamReader(con.getInputStream()));
                String inputLine;
                StringBuffer response = new StringBuffer();

                while ((inputLine = ins.readLine()) != null) {
                    response.append(inputLine);
                }
                ins.close();
                result = response.toString();
                logger.info("result=======" + result);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return result;
    }

    public static String getUserCode(String returnConfig, JSONObject jsonObject) {
        String userCode = "";
        String[] arr = returnConfig.split(":");
        int len = arr.length;
        if (len == 1) {
            userCode = jsonObject.get(arr[0]).toString();
        } else {
            for (int i = 0; i < len; i++) {
                if (i < len - 1) {
                    jsonObject = jsonObject.getJSONObject(arr[0]);
                } else {
                    userCode = jsonObject.get(arr[len - 1]).toString();
                }
            }
        }
        logger.info("请求成功：userCode=" + userCode);
        return userCode;
    }

    public static void main(String[] args) {
        String s="{\"Success\":\"true\",\"Msg\":\"验证成功\",\"Data\":{\"UserId\":\"15728\",\"UserCode\":\"890055\",\"UserAccount\":\"CAOJW\"}}";
        getUserCode("Data:UserCode",JSONObject.parseObject(s));
    }

}
