package com.goodwill.hdr.civ.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.goodwill.hdr.civ.entity.SpecialtyConfig;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * <p>
 * Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-03-03
 */
@Mapper
public interface SpecialtyConfigMapper extends BaseMapper<SpecialtyConfig> {

    @Select("select *  from  civ_specialty_config where  is_inuse = 'Y' and doctor_code =#{doctorCode} and  sickness_code =#{maindaig} group by sickness_code,sickness_name,item_code,item_name order by  array_index asc")
    List<SpecialtyConfig> getSpecityConfig(String doctorCode, String maindaig);

}
