package com.goodwill.hdr.civ.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.io.Serializable;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2022-03-03
 */
@TableName("civ_sickness")
public class Sickness implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    private String sicknessCode;

    private String sicknessName;

    private String isInuse;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getSicknessCode() {
        return sicknessCode;
    }

    public void setSicknessCode(String sicknessCode) {
        this.sicknessCode = sicknessCode;
    }

    public String getSicknessName() {
        return sicknessName;
    }

    public void setSicknessName(String sicknessName) {
        this.sicknessName = sicknessName;
    }

    public String getIsInuse() {
        return isInuse;
    }

    public void setIsInuse(String isInuse) {
        this.isInuse = isInuse;
    }

    @Override
    public String toString() {
        return "Sickness{" +
                "id=" + id +
                ", sicknessCode=" + sicknessCode +
                ", sicknessName=" + sicknessName +
                ", isInuse=" + isInuse +
                "}";
    }
}
