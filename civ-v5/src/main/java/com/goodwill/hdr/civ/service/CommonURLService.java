package com.goodwill.hdr.civ.service;


import com.goodwill.hdr.civ.vo.*;
import com.goodwill.hdr.core.orm.Page;

import java.util.List;
import java.util.Map;


public interface CommonURLService {
    /**
     * @return
     * @Description 方法描述: 返回调用地址
     */
    Map<String, String> getCommonUrl(String oid, String patientId, String visitId, String visitType, String id, String sysCode);


    Page<Map<String, String>> getUrlListFromHbaseForVisit(String oid, String patientId, String visitId, String visitType, String syscode, int pageNo, int pageSize);

    Map<String, String> getEhrUrl(String oid, String idCardNo, String id, String sysCode);

    Map<String, String> getSysencUrl(String oid, String patientId, String visitId, String visitType, String id, String sysCode);

    Page<Map<String, String>> getTableCommonModule(String oid, String patientId, String visitId, String visitType, String id, List<KeyValueDto> paramList, int pageNo, int pageSize);

    Page<Map<String, String>> getCategoryTableTemplate( String outPid, String id, List<KeyValueDto> paramList, int pageNo, int pageSize);


    List<Map<String,String>> getCommonListForVisit(String oid, String patientId, String visitId, String visitType, String id);

    List<Map<String,String>> getCommonListForCategory(String outPatientId, String id);

    List<NameAndCodeVo> getCommonTab(String oid, String id);


    TableTemplateConfig getTableTemplateConfig(String oid, String id);

    List<ColumnConfig> getCommonTableConfig(String oid, String id);

    CommonListConfigResponse getCommonListConfig(String oid, String id);

    Map<String, String> getCommonWebUrl(String oid, String patientId, String visitId, String visit_type_code, String id, String sysCode, Map<String, String> map);
}
