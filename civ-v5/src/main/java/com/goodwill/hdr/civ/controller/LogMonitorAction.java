package com.goodwill.hdr.civ.controller;


import com.goodwill.hdr.civ.entity.LogRecord;
import com.goodwill.hdr.civ.service.LogMonitorService;
import com.goodwill.hdr.civ.service.PowerService;
import com.goodwill.hdr.civ.utils.CusInfoUtil;
import com.goodwill.hdr.core.orm.Page;
import com.goodwill.hdr.security.utils.SecurityCommonUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Description 类描述：日志监控action
 * @Date 2019-06-12 11:37
 * @modify 修改记录：
 */
@RestController
@RequestMapping("/log")
public class LogMonitorAction {

    private static Logger logger = LoggerFactory.getLogger(LogMonitorAction.class);
    @Autowired
    private PowerService powerService;
    @Autowired
    private LogMonitorService logMonitorService;


    /**
     * @Description 各页面点击触发监控日志保存
     */
    @PostMapping("/insetMonitorLog")
    public Integer insetMonitorLog(String oid, String dept_code, String dept_name, String code, String name, HttpServletRequest httpServletRequest) {
        logger.info("准备插入日志");
        LogRecord logRecord = new LogRecord();
        logRecord.setDeptcode(dept_code);
        logRecord.setDeptname(dept_name);
        logRecord.setPagecode(code);
        logRecord.setPagename(name);
        logRecord.setOid(oid);
        //获取客户端ip
        logRecord.setIp(CusInfoUtil.getRealIP(httpServletRequest));
        return logMonitorService.insertMonitorLog(logRecord);

    }

    /**
     * 获得监控日志查询条件：医生列表
     */
    @PostMapping("/getDoctorList")
    public Page<Map<String, String>> getDoctorList(String oid, HttpServletRequest httpServletRequest) {
        String userName = httpServletRequest.getParameter("keyWord");
        String deptCode = httpServletRequest.getParameter("dept");
        String pageNo = httpServletRequest.getParameter("pageNo");
        String pageSize = httpServletRequest.getParameter("pageSize");
        Page<Map<String, String>> map = logMonitorService.getDoctorPage(oid, userName, deptCode, Integer.valueOf(pageNo), Integer.valueOf(pageSize));
        return map;
    }

    /**
     * 获得当前权限视图
     */
    @PostMapping("/getPowerConfigByPage")
    public Map<String, String> getPowerConfigByPage(String oid, String userCode) {

        if (null == userCode) {
            userCode = SecurityCommonUtil.getCurrentLoginUser().getUsercode();
            ;
        }
        Map<String, String> map = powerService.getPowerConfigByPage(oid, userCode);
        map = logMonitorService.handleView(oid, map);
        return map;
    }

    /**
     * 获取监控日志数据列表
     */
    @PostMapping("/getMonitorLog")
    public Page<Map<String, String>> getMonitorLog(HttpServletRequest request) {
        String oid = request.getParameter("oid");
        String deptCode = request.getParameter("dept");
        String userCode = request.getParameter("doctor");
        String visitPageCode = request.getParameter("page");
        String beginDate = request.getParameter("start_time");
        String endDate = request.getParameter("end_time");
        String pageNoStr = request.getParameter("pageNo");
        int pageNo = pageNoStr.equals("0") ? 10 : Integer.valueOf(pageNoStr);
        String pageSizeStr = request.getParameter("pageSize");
        int pageSize = pageSizeStr.equals("0") ? 10 : Integer.valueOf(pageSizeStr);
        Page<Map<String, String>> map = logMonitorService.getMonitorLogPage(oid, deptCode, userCode, visitPageCode,
                beginDate, endDate, pageNo, pageSize);
        return map;
    }

    /**
     * 获取所有的监控日志数据
     */
    @RequestMapping("/getAllMonitorLog")
    public Page<Map<String, Object>> getAllMonitorLog() {
        Page<Map<String, Object>> map = logMonitorService.getAllMonitorLogPage();
        return map;
    }

    /**
     * 根据不同条件进行分类获取监控日志数据
     * 条形图：按科室，按医生
     */
    @PostMapping("/getClassifyMonitorLog")
    public Map<String, Object> getClassifyMonitorLog(String oid, HttpServletRequest request) {
        //分类 according_doctor  according_dept
        String flag = request.getParameter("id");
        String beginDate = request.getParameter("start_time");
        String endDate = request.getParameter("end_time");
        String value = request.getParameter("detp");
        Map<String, Object> map = logMonitorService.getClassifyMonitorLog(oid, value, beginDate, endDate, flag);
        return map;
    }

    /**
     * 条形图：科室按时间统计，医生按时间统计
     */
    @PostMapping("/getClassifyByTimeMonitorLog")
    public Map<String, Object> getClassifyByTimeMonitorLog(String oid, HttpServletRequest request) {
        // according_depttime  according_doctortime
        String flag = request.getParameter("id");
        String dept = request.getParameter("detp");
        String doctor = request.getParameter("doctor");
        String days = request.getParameter("time");
        if (null == dept || dept.equals("")) {
            dept = "01";
        }
        //默认最近一个月
        if (null == days || days.equals("")) {
            days = "1";
        }
        if ("according_doctortime".equals(flag)) {
            if (null == doctor || doctor.equals("")) {
                Map<String, Object> mapEmpty = logMonitorService.getEmptyData(oid, days);

                return mapEmpty;
            }
        }
        Map<String, Object> map = logMonitorService.getSortedByTimeMonitorLog(oid, dept, doctor, days, flag);
        return map;
    }


    /**
     * top 5 监控日志查询
     */
    @PostMapping("/getTopFiveMonitorLogData")
    public List<Map<String, Object>> getTopFiveMonitorLogData(String id, String code, String userCode) {
        // id = top5_dept  科室top 5
        // id = top5_doctor   医生top5
        String value = (code == null ? "" : code);
        List<Map<String, Object>> map = logMonitorService.getTopFiveLogData(id, value, userCode);
        return map;
    }


    /**
     * 扇形图
     */
    @RequestMapping("/getSectorData")
    public List<Map<String, Object>> getSectorData() {
        List<Map<String, Object>> map = logMonitorService.getSectorData();
        return map;
    }


}
