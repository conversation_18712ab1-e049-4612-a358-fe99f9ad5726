package com.goodwill.hdr.civ.utils;

import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.jsoup.nodes.Element;
import org.jsoup.select.Elements;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

public class DataMaskUtil {

    public static String toBeMasked(String html, Map<String, String> replaceRule) {
        for (Map.Entry<String, String> entry : replaceRule.entrySet()) {
            String beMasked = toBeMasked(entry.getKey(), entry.getValue());
            replaceRule.put(entry.getKey(), beMasked);
        }

        String record = getMaskedMedicalRecord(html, replaceRule);
        return record;
    }


    public static String toBeMasked(String target, String pattern) {
//        获取*号的索引
        List<Integer> index = new ArrayList<>();
        for (int i = 0; i < pattern.length(); i++) {
            if (pattern.charAt(i) == '*') {
                index.add(i);
            }
        }

        StringBuilder s = new StringBuilder(target);
        for (Integer i : index) {
            if (i < s.length()) {
                s.setCharAt(i, '*');
            }
        }
        return s.toString();

    }

    private static String getMaskedMedicalRecord(String html, Map<String, String> replacementMap) {
        for (Map.Entry<String, String> entry : replacementMap.entrySet()) {
            html = html.replaceAll(entry.getKey(), entry.getValue());

        }
        return html;
    }

    /**
     * html指定字符串替换
     * @param html 原字符串
     * @param replaceRule key为原值 vale为要替换的结果值
     * @return
     */
    public static String maskHtml(String html, Map<String, String> replaceRule) {
        Document doc = Jsoup.parse(html);
        Elements spans = doc.select("span");
        for (Element span : spans) {
            String text = span.text();
            for (Map.Entry<String, String> entry : replaceRule.entrySet()) {
                if (text.contains(entry.getKey())) {
                    text = text.replace(entry.getKey(), entry.getValue());
                    span.text(text);
                }
            }
        }
        return doc.html();
    }

}
