package com.goodwill.hdr.civ.controller;


import com.goodwill.hdr.civ.service.CheckReportService;
import com.goodwill.hdr.core.orm.Page;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Description 类描述：检查Action
 * @Date 2018年6月17日
 * @modify 修改记录：
 */
@RequestMapping("/check")
@RestController
@Api(tags = "检查报告查询")
public class CheckAction {

    @Autowired
    private CheckReportService checkReportService;

    /**
     * @Description 某次就诊的检查报告
     */
    @ApiOperation(value = "获取某次就诊的检查报告", notes = "获取某次就诊的检查报告", httpMethod = "POST")
    @RequestMapping(value = "/getPatientVisitChecks", method = RequestMethod.POST)
    public Page<Map<String, String>> getPatientVisitChecks(String pno, String oid, String patientId, String visitId, String visitType, int pageSize) {
        int pno1 = StringUtils.isBlank(pno) ? 0 : Integer.parseInt(pno);
        //参数 患者编号  就诊次  就诊类型
        Page<Map<String, String>> result = checkReportService.getCheckReportList(oid, patientId, visitId, visitType,
                "REPORT_TIME", "desc", "", "", pno1, pageSize);
        return result;
    }

    /**
     * @Description 当前视图 - 检查报告
     */
    @ApiOperation(value = "当前视图检查报告", notes = "当前视图检查报告", httpMethod = "POST")
    @RequestMapping(value = "/getCVChecks", method = RequestMethod.POST)
    public Page<Map<String, String>> getCVChecks(String mainDiag, String dept_code, String oid, String patientId, String visitId) {
        Page<Map<String, String>> result = new Page<Map<String, String>>();
//		String mainDiag = getParameter("mainDiag");
//        String deptCode =  getParameter("dept_code");
        if (StringUtils.isNotBlank(patientId)) {
            result = checkReportService.getCheckReportList(oid, patientId, visitId, "INPV",
                    "REPORT_TIME", "desc", mainDiag, dept_code, 0, 0);
        }
        return result;
    }

    /**
     * @Description 某份检查报告详情
     */
    @ApiOperation(value = "某份检查报告详情", notes = "某份检查报告详情", httpMethod = "POST")
    @RequestMapping(value = "/getCheckDetails", method = RequestMethod.POST)
    public Map<String, String> getCheckDetails(String oid, String patientId, String visitId, String reportNo) {
        //检查报告主键
//		String reportNo = getParameter("reportNo");
        Map<String, String> result = checkReportService.getCheckReportDetails(oid, patientId, visitId, reportNo);
        //响应
        return result;
    }

    /**
     * @Description 获得患者所有的检查报告
     */
    @ApiOperation(value = "获取患者所有的检查报告", notes = "获取患者所有的检查报告", httpMethod = "POST")
    @RequestMapping(value = "/getCheckReports", method = RequestMethod.POST)
    public List<Map<String, Object>> getCheckReports( String oid, String patientId, String outPatientId, String visitType, String year, String key, String type, String click_type) {
//        String outPatientId = getParameter("outPatientId");
//        String visitType = getParameter("visitType");
//        String year = getParameter("year");
//        String key = getParameter("key");
//        String type = getParameter("type");
//        String click_Type = getParameter("click_type");
        List<Map<String, Object>> result = new ArrayList<Map<String, Object>>();
        result = checkReportService.getCheckReports(oid, patientId, 1, 2000, "REPORT_TIME", "desc", outPatientId, visitType, year, key, type, click_type);
        //响应
        return result;
    }

    /**
     * @Description 获得患者所有的检查报告类型
     */
    @ApiOperation(value = "获取患者所有的检查报告类型", notes = "获取患者所有的检查报告类型", httpMethod = "POST")
    @RequestMapping(value = "/getAllReportTypes", method = RequestMethod.POST)
    public List<Map<String, String>> getAllReportTypes(String this_oid, String oid, String patientId, String outPatientId, String visitType, String key) {
//        String outPatientId = getParameter("outPatientId");
//        String visitType = getParameter("visitType");
//        String key = getParameter("key");
        List<Map<String, String>> result = checkReportService.getAllReportTypes(outPatientId);
        //响应
        return result;
    }

    /**
     * @Description 获得患者超声测量值
     */
    @ApiOperation(value = "获得患者超声测量值", notes = "获得患者超声测量值", httpMethod = "POST")
    @RequestMapping(value = "/getUlMeasure", method = RequestMethod.POST)
    public List<Map<String, String>> getUlMeasure(String oid, String patientId, String visitType, String reportNo) {
        List<Map<String, String>> resultList = this.checkReportService.getUlMeasure(oid, patientId, visitType, reportNo);
        return resultList;
    }

}
