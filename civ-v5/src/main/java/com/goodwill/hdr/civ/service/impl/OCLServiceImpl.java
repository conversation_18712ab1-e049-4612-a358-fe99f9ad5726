package com.goodwill.hdr.civ.service.impl;


import com.goodwill.hdr.civ.config.Config;
import com.goodwill.hdr.civ.enums.HdrTableEnum;
import com.goodwill.hdr.civ.service.OCLService;
import com.goodwill.hdr.civ.utils.Utils;
import com.goodwill.hdr.civ.utils.WSUtils;
import com.goodwill.hdr.core.orm.MatchType;
import com.goodwill.hdr.hbase.bo.PropertyFilter;
import com.goodwill.hdr.hbase.dto.responseVo.PageResultVo;
import com.goodwill.hdr.hbase.dto.responseVo.ResultVo;
import com.goodwill.hdr.hbaseQueryClient.builder.PageRequestBuilder;
import com.goodwill.hdr.hbaseQueryClient.service.HbaseQueryClient;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 *
 * @Description
 * 类描述：医嘱闭环，远程webservice接口调用
 * <AUTHOR>
 * @Date 2018年10月22日
 * @modify
 * 修改记录：
 *
 */
@Service
public class OCLServiceImpl implements OCLService {

	private final HbaseQueryClient hbaseQueryClient;

	public OCLServiceImpl(HbaseQueryClient hbaseQueryClient) {
		this.hbaseQueryClient = hbaseQueryClient;
	}

	@Override
	public String getDrugOCL(String oid, String pid, String vid, String orderNo) {
		return WSUtils.wsDrugOCL(oid,pid, vid, orderNo);
	}

	@Override
	public String getLabOCL(String oid, String pid, String vid, String orderNo) {
		return WSUtils.wsLabOCL(oid,pid, vid, orderNo);
	}

	@Override
	public String getExamOCL(String oid, String pid, String vid, String orderNo) {
		return WSUtils.wsExamOCL(oid,pid, vid, orderNo);
	}

	@Override
	public String getOperOCL(String oid, String pid, String vid, String orderNo) {
		return WSUtils.wsOperOCL(oid,pid, vid, orderNo);
	}

	@Override
	public String getBloodOCL(String oid, String pid, String vid, String timesNo) {
		return WSUtils.wsBloodOCL(oid,pid, vid, timesNo);
	}

	@Override
	public String getCVOperOCL(String oid, String patientId, String visitId) {
		String result = "";
		//获取手术医嘱
		List<Map<String, String>> orderList = getOperList(oid,patientId, visitId, 0);
		if (orderList.size() == 0) {
			return result;
		}
		//获取本次最新的手术医嘱
		Map<String, String> order = orderList.get(0);
		return getOperOCL(oid,patientId, visitId, order.get("ORDER_NO"));
	}

	/**
	 * @Description
	 * 方法描述: 住院手术医嘱列表
	 * @return 返回类型： List<Map<String,String>>
	 * @param pid 患者编号
	 * @param vid 就诊次数
	 * @param returnNum
	 * @return
	 */
	public List<Map<String, String>> getOperList(String oid, String pid, String vid, int returnNum) {
		//读取患者手术医嘱
		List<String> orderClass = Config.getOrderClass(oid,"ORDERCLOSE_OPER");
		//将患者的全部医嘱取出，医嘱性质包含长期和临时，医嘱状态包含下达/审核/开始/停止，将状态为撤销的医嘱排除
		String tableName = "HDR_IN_ORDER";
		//拼接rowkey前缀
//		String preRowkey = HbaseCURDUtils.getRowkeyPrefix(patId) + "|" + visitId + "|";
		//页面查询条件
		List<PropertyFilter> filters = new ArrayList<PropertyFilter>();
		filters.add(new PropertyFilter("ORDER_STATUS_NAME", MatchType.NOTIN.getOperation(), "撤销,废弃"));
		//createPropertyFilter("ORDER_STATUS_NAME", "撤销,废弃", MatchType.NOTIN.getOperation(), filters);

		if (orderClass != null && orderClass.size() > 0) {
			String orderClassString = "";
			for (String orderString : orderClass) {
				orderClassString += orderString + ",";
			}
			orderClassString = orderClassString.substring(0, orderClassString.length() - 1);
			filters.add(new PropertyFilter("ORDER_CLASS_CODE", MatchType.IN.getOperation(), orderClassString));
			//createPropertyFilter("ORDER_CLASS_CODE", orderClassString, MatchType.IN.getOperation(), filters);
		}

		//获取列包括 医嘱号、父医嘱号、医嘱类别、医嘱性质、医嘱项、开始时间、结束时间、医嘱状态、开据医生
		//读取出医嘱列表，并按医嘱时间倒序
		//filters.add(new PropertyFilter("VISIT_ID", "STRING", MatchType.EQ.getOperation(), vid));
		List<Map<String, String>> OrderList = new ArrayList<>();
		ResultVo<PageResultVo<Map<String, String>>> resultVo = hbaseQueryClient.getPageByCondition(PageRequestBuilder
				.init()
				.tableName(tableName)
				.patientId(pid)
				.oid(oid)
				.visitId(vid)
				.visitTypeCode("")
				.filters(filters)
				.pageNo(0)
				.pageSize(0)
				.orderBy("")
				.asc()
				.column("ORDER_NO", "PARENT_ORDER_NO", "ORDER_CLASS_CODE", "ORDER_CLASS_NAME", "ORDER_PROPERTIES_NAME",
						"ORDER_ITEM_CODE", "ORDER_ITEM_NAME", "ORDER_BEGIN_TIME", "ORDER_END_TIME", "ORDER_STATUS_NAME",
						"ORDER_DOCTOR_CODE", "ORDER_DOCTOR_NAME", "ORDER_TIME", "FREQUENCY_NAME", "DOSAGE_VALUE",
						"DOSAGE_UNIT", "PHARMACY_WAY_NAME", "ORDER_TEXT")
				.build()
		);
		if (resultVo.isSuccess()) {
			OrderList = resultVo.getContent().getResult();
		}
//		List<Map<String, String>> OrderList = hbaseDao.findConditionByPatient(tableName, oid,
//				pid, filters, "ORDER_NO", "PARENT_ORDER_NO", "ORDER_CLASS_CODE", "ORDER_CLASS_NAME", "ORDER_PROPERTIES_NAME",
//				"ORDER_ITEM_CODE", "ORDER_ITEM_NAME", "ORDER_BEGIN_TIME", "ORDER_END_TIME", "ORDER_STATUS_NAME",
//				"ORDER_DOCTOR_CODE", "ORDER_DOCTOR_NAME", "ORDER_TIME", "FREQUENCY_NAME", "DOSAGE_VALUE",
//				"DOSAGE_UNIT", "PHARMACY_WAY_NAME", "ORDER_TEXT");
		for (Map<String, String> map : OrderList) {
			String orderNO = map.get("ORDER_NO");
			//拼接手术申请数据
			filters = new ArrayList<PropertyFilter>();
			filters.add(new PropertyFilter("OPER_APPLY_NO", MatchType.EQ.getOperation(), orderNO));
			List<Map<String, String>> operApplyList = new ArrayList<>();
			ResultVo<PageResultVo<Map<String, String>>> resultVo1 = hbaseQueryClient.getPageByCondition(PageRequestBuilder
					.init()
					.tableName("HDR_OPER_APPLY")
					.patientId(pid)
					.oid(oid)
					.visitId(vid)
					.visitTypeCode("")
					.filters(filters)
					.pageNo(0)
					.pageSize(0)
					.orderBy("")
					.asc()
					.column("OPERATION_NAME",
							"DIAG_BEFORE_OPERATION_NAME", "PLAN_OPER_DOCTOR_NAME")
					.build()
			);
			if (resultVo1.isSuccess()) {
				operApplyList = resultVo1.getContent().getResult();
			}
//			List<Map<String, String>> operApplyList = hbaseDao.findConditionByPatient("HDR_OPER_APPLY", oid,
//					pid, filters, new String[]{"OPERATION_NAME",
//							"DIAG_BEFORE_OPERATION_NAME", "PLAN_OPER_DOCTOR_NAME"});
			if (operApplyList.size() > 0) {
				map.put("ORDER_ITEM_NAME",
						Utils.objToStr(operApplyList.get(0).get("OPERATION_NAME"), map.get("ORDER_ITEM_NAME")));
				map.put("DIAG_BEFORE_OPERATION_NAME",
						Utils.objToStr(operApplyList.get(0).get("DIAG_BEFORE_OPERATION_NAME")));
				map.put("PLAN_OPER_DOCTOR_NAME", Utils.objToStr(operApplyList.get(0).get("PLAN_OPER_DOCTOR_NAME")));
			}

			//拼接手术过程数据
			filters = new ArrayList<PropertyFilter>();
			filters.add(new PropertyFilter("ORDER_NO", MatchType.EQ.getOperation(), orderNO));
			List<Map<String, String>> operAnaesList = new ArrayList<>();
			ResultVo<PageResultVo<Map<String, String>>> resultVo2 = hbaseQueryClient.getPageByCondition(PageRequestBuilder
					.init()
					.tableName("HDR_OPER_ANAES")
					.patientId(pid)
					.oid(oid)
					.visitId(vid)
					.visitTypeCode("")
					.filters(filters)
					.pageNo(0)
					.pageSize(0)
					.orderBy("")
					.asc()
					.column("ANESTHESIA_END_TIME",
							"ANESTHESIA_DOCTOR_NAME", "OPER_NO")
					.build()
			);
			if (resultVo2.isSuccess()) {
				operAnaesList = resultVo2.getContent().getResult();
			}
//			List<Map<String, String>> operAnaesList = hbaseDao.findConditionByPatient("HDR_OPER_ANAES", oid,
//					pid, filters, new String[]{"ANESTHESIA_END_TIME",
//							"ANESTHESIA_DOCTOR_NAME", "OPER_NO"});
			if (operAnaesList.size() > 0) {
				map.put("ANESTHESIA_END_TIME", Utils.objToStr(operAnaesList.get(0).get("ANESTHESIA_END_TIME")));
				map.put("ANESTHESIA_DOCTOR_NAME", Utils.objToStr(operAnaesList.get(0).get("ANESTHESIA_DOCTOR_NAME")));
				map.put("OPER_NO", Utils.objToStr(operAnaesList.get(0).get("OPER_NO")));
			}

		}

		//按医嘱时间倒序
		List<Map<String, String>> OrderSortList = Utils.sortListDescByDate(OrderList, "ORDER_TIME");

		return OrderSortList;
	}

	public Map<String, String> getOclUrl(String oid, String patientId, String visitId, String id,
											String token) {
		Map<String, String> rs = new HashMap<String, String>();

		//参数加密
		String patientId_base64 = Utils.UrlEncodeBase64(patientId);
		String visitId_base64 = Utils.UrlEncodeBase64(visitId);
		String oId_base64 = Utils.UrlEncodeBase64(oid);

		//解析token
		token = token.replace("Bearer ", "");

		//获取调用url 例如：http://192.168.7.116:2076/hdrocl/pages/orderTrack/orderTrack.jsp
		String url = null;
		if(id.equals("inpvOCL_module")){
			url =Config.getConfigValue(oid,"OCL_INPV_URL");
		}
		if(id.equals("outpvOCL_module")){
			url =Config.getConfigValue(oid,"OCL_OUTPV_URL");
		}
		if(id.equals("businessOCL_module")){
			url =Config.getConfigValue(oid,"OCL_BUSINESS_URL");
		}

		//拼接url
		if(StringUtils.isNotBlank(url)){
			String rs_url = url+"?lat=hdrtk&token="+token+"&patientid="+patientId_base64+"&visitid="+visitId_base64+"&oid="+oId_base64+"&way=em";
			rs.put("status", "1");
			rs.put("linkType", "Iframe");
			rs.put("url", rs_url);
		}else {
			rs.put("status", "0");
			rs.put("msg", "请配置"+id+"的调用地址");
		}

		return rs;
	}

}
