package com.goodwill.hdr.civ.service.impl;


import com.goodwill.hdr.civ.config.Config;
import com.goodwill.hdr.civ.service.AllergyService;
import com.goodwill.hdr.civ.utils.CivUtils;
import com.goodwill.hdr.civ.utils.ColumnUtil;
import com.goodwill.hdr.civ.utils.ListPage;
import com.goodwill.hdr.civ.utils.Utils;
import com.goodwill.hdr.core.orm.MatchType;
import com.goodwill.hdr.core.orm.Page;
import com.goodwill.hdr.hbase.bo.PropertyFilter;
import com.goodwill.hdr.hbase.dto.responseVo.PageResultVo;
import com.goodwill.hdr.hbase.dto.responseVo.ResultVo;
import com.goodwill.hdr.hbaseQueryClient.builder.PageRequestBuilder;
import com.goodwill.hdr.hbaseQueryClient.service.HbaseQueryClient;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.goodwill.hdr.civ.utils.Utils.getEndPageNum;
import static com.goodwill.hdr.civ.utils.Utils.getStartPageNum;

@Service
public class AllergyServiceImpl implements AllergyService {
    private static Logger log = LoggerFactory.getLogger(AllergyServiceImpl.class);

    private final HbaseQueryClient hbaseQueryClient;

	public AllergyServiceImpl(HbaseQueryClient hbaseQueryClient) {
		this.hbaseQueryClient = hbaseQueryClient;
	}

	@Override
	public Page<Map<String, String>> getAllergyList(String oid, String patientId, String visitId, String visitType, String orderBy,
													String orderDir, int pageNo, int pageSize, String filterString) {
		Page<Map<String, String>> page = new Page<Map<String, String>>();
		//分页
		boolean pageable = true;
		if (pageNo == 0 || pageSize == 0) {
			pageable = false;
		} else {
			page.setPageNo(1);
			page.setPageSize(100000);
		}
		//排序
		if (StringUtils.isBlank(orderBy) || StringUtils.isBlank(orderDir)) {
			page.setOrderBy("RECORDTIME");
			page.setOrderDir("desc");
		} else {
			page.setOrderBy(orderBy);
			page.setOrderDir(orderDir);
		}
		//过滤条件
		String vtype = "02";
		if ("OUTPV".equals(visitType)) {
			vtype = "01";
		}
		List<PropertyFilter> filters = new ArrayList<PropertyFilter>();
		//filters.add(new PropertyFilter("VISIT_ID", "STRING", MatchType.EQ.getOperation(), visitId));
		filters.add(new PropertyFilter("VISIT_TYPE_CODE", MatchType.EQ.getOperation(), vtype));
		//附加条件
		if (StringUtils.isNotBlank(filterString)) {
			//设置条件 支持多条件
			CivUtils.strToFilter(filterString, filters, ";");
		}
		//分页判断
		if (pageable) {
//			page = hbaseDao.findPageConditionByPatientVisitId("HDR_ALLERGY",oid, patientId, visitId,page, filters,
//					"ALLERGY_CATEGORY_NAME", "ALLERGEN", "ALLERGEN_NAME", "ALLERGY_REASON", "ALLERGY_REASON_NAME",
//					"ALLERGY_REACTION", "ALLERGY_SEVERITY", "ALLERGY_SEVERITY_NAME", "OPERATOR_NURSE",
//					"OPERATOR_NURSE_NAME", "OPERATE_NURSE_TIME", "RECORD_PERSON", "RECORD_PERSON_NAME", "RECORDTIME",
//					"ALLERGY_NAME");
			ResultVo<PageResultVo<Map<String, String>>> resultVo = hbaseQueryClient.getPageByCondition(
					PageRequestBuilder.init()
							.tableName("HDR_ALLERGY")
							.patientId(patientId)
							.oid(oid)
							.visitId(visitId)
							.visitTypeCode(vtype)
							.filters(filters)
							.pageNo(0)
							.pageSize(0)
							.orderBy(orderBy)
							.desc()
							.column("ALLERGY_CATEGORY_NAME", "ALLERGEN", "ALLERGEN_NAME", "ALLERGY_REASON", "ALLERGY_REASON_NAME",
									"ALLERGY_REACTION", "ALLERGY_SEVERITY", "ALLERGY_SEVERITY_NAME", "OPERATOR_NURSE",
									"OPERATOR_NURSE_NAME", "OPERATE_NURSE_TIME", "RECORD_PERSON", "RECORD_PERSON_NAME", "RECORDTIME",
									"ALLERGY_NAME")
							.build());
			if(resultVo.isSuccess()){
				page.setResult(resultVo.getContent().getResult());
				page.setTotalCount(resultVo.getContent().getTotal());
			}
			Page<Map<String, String>> page2 = new Page<Map<String, String>>();
			page2.setPageNo(pageNo);
			page2.setPageSize(pageSize);
			page2.setTotalCount(page.getTotalCount());
			page2.setTotalPages(page.getTotalPages());
			page2.setResult(page.getResult().subList(getStartPageNum(pageNo,pageSize),getEndPageNum(pageNo,pageSize,page.getTotalCount())));
			page= page2;
		} else {
//			List<Map<String, String>> list = hbaseDao.findConditionByPatient("HDR_ALLERGY", oid,patientId, filters,
//					"ALLERGY_CATEGORY_NAME", "ALLERGEN", "ALLERGEN_NAME", "ALLERGY_REASON", "ALLERGY_REASON_NAME",
//					"ALLERGY_REACTION", "ALLERGY_SEVERITY", "ALLERGY_SEVERITY_NAME", "OPERATOR_NURSE",
//					"OPERATOR_NURSE_NAME", "OPERATE_NURSE_TIME", "RECORD_PERSON", "RECORD_PERSON_NAME", "RECORDTIME",
//					"ALLERGY_NAME");
			List<Map<String, String>> list = new ArrayList<>();
			ResultVo<PageResultVo<Map<String, String>>> resultVo = hbaseQueryClient.getPageByCondition(
					PageRequestBuilder.init()
							.tableName("HDR_ALLERGY")
							.patientId(patientId)
							.oid(oid)
							.visitId(visitId)
							.visitTypeCode(vtype)
							.filters(filters)
							.pageNo(0)
							.pageSize(0)
							.orderBy("RECORDTIME")
							.desc()
							.column("ALLERGY_CATEGORY_NAME", "ALLERGEN", "ALLERGEN_NAME", "ALLERGY_REASON", "ALLERGY_REASON_NAME",
									"ALLERGY_REACTION", "ALLERGY_SEVERITY", "ALLERGY_SEVERITY_NAME", "OPERATOR_NURSE",
									"OPERATOR_NURSE_NAME", "OPERATE_NURSE_TIME", "RECORD_PERSON", "RECORD_PERSON_NAME", "RECORDTIME",
									"ALLERGY_NAME")
							.build());
			if(resultVo.isSuccess()){
				list = resultVo.getContent().getResult();
			}
			if (list.size() > 0) {
				Utils.sortListByDate(list, page.getOrderBy(), page.getOrderDir());
				page.setTotalCount(list.size());
				page.setResult(list);
			}
		}
		//字段映射
		List<Map<String, String>> allergys = new ArrayList<Map<String, String>>();
		for (Map<String, String> map : page) {
			Map<String, String> allergy = new HashMap<String, String>();
			//过敏原因字段处理
			if (StringUtils.isBlank(map.get("ALLERGY_REASON"))) {
				map.put("ALLERGY_REASON", map.get("ALLERGY_REASON_NAME"));
			}
			//过敏程度字段处理
			if (StringUtils.isBlank(map.get("ALLERGY_SEVERITY"))) {
				map.put("ALLERGY_SEVERITY", map.get("ALLERGY_SEVERITY_NAME"));
			}
			//过敏源字段处理
			if (StringUtils.isBlank(map.get("ALLERGEN"))) {
				if (StringUtils.isNotBlank(map.get("ALLERGEN_NAME"))) {
					map.put("ALLERGEN", map.get("ALLERGEN_NAME"));
				} else {
					map.put("ALLERGEN", map.get("ALLERGY_NAME"));
				}
			}
			//记录人字段处理
			if (StringUtils.isBlank(map.get("RECORD_PERSON"))) {
				map.put("RECORD_PERSON", map.get("RECORD_PERSON_NAME"));
			}
			//处理护士字段处理
			if (StringUtils.isBlank(map.get("OPERATOR_NURSE"))) {
				map.put("OPERATOR_NURSE", map.get("OPERATOR_NURSE_NAME"));
			}
			ColumnUtil.convertMapping(allergy, map, "ALLERGY_CATEGORY_NAME", "ALLERGEN", "ALLERGY_REASON",
					"ALLERGY_REACTION", "ALLERGY_SEVERITY", "OPERATOR_NURSE", "OPERATE_NURSE_TIME", "RECORD_PERSON",
					"RECORDTIME");
			allergys.add(allergy);
		}
		page.setResult(allergys);
		return page;
	}

	@Override
	public Page<Map<String, String>> getAllergys(String oid, String patientId, String outPatientId, String orderBy,
                                                 String orderDir, int pageNo, int pageSize, String filterString) {
		Page<Map<String, String>> page = new Page<Map<String, String>>();
		//分页
		boolean pageable = true;
		if (pageNo == 0 || pageSize == 0) {
			pageable = false;
		} else {
			page.setPageNo(pageNo);
			page.setPageSize(pageSize);
		}
		//排序
		if (StringUtils.isBlank(orderBy) || StringUtils.isBlank(orderDir)) {
			page.setOrderBy("RECORDTIME");
			page.setOrderDir("desc");
		} else {
			page.setOrderBy(orderBy);
			page.setOrderDir(orderDir);
		}
		//过滤条件
		List<PropertyFilter> filters = new ArrayList<PropertyFilter>();
		//附加条件
		if (StringUtils.isNotBlank(filterString)) {
			//设置条件 支持多条件
			CivUtils.strToFilter(filterString, filters, ";");
		}
		List<Map<String, String>> list = new ArrayList<Map<String, String>>();
//		if (StringUtils.isNotBlank(patientId)) {
//			List<Map<String, String>> list1 = new ArrayList<>();
//			ResultVo<PageResultVo<Map<String, String>>> resultVo = hbaseQueryClient.getPageByCondition(
//					PageRequestBuilder.init()
//							.tableName("HDR_ALLERGY")
//							.patientId(patientId)
//							.oid(oid)
//							.visitId("")
//							.visitTypeCode("")
//							.filters(filters)
//							.pageNo(0)
//							.pageSize(0)
//							.orderBy("RECORDTIME")
//							.desc()
//							.column("ALLERGY_CATEGORY_NAME", "ALLERGEN", "ALLERGEN_NAME", "ALLERGY_REASON", "ALLERGY_REASON_NAME",
//									"ALLERGY_REACTION", "ALLERGY_SEVERITY", "ALLERGY_SEVERITY_NAME", "OPERATOR_NURSE",
//									"OPERATOR_NURSE_NAME", "OPERATE_NURSE_TIME", "RECORD_PERSON", "RECORD_PERSON_NAME", "RECORDTIME",
//									"ALLERGY_NAME")
//							.build());
//			if(resultVo.isSuccess()){
//				list1 = resultVo.getContent().getResult();
//			}
////			List<Map<String, String>> list1 = hbaseDao.findConditionByPatient("HDR_ALLERGY", oid, patientId, filters,
////					"ALLERGY_CATEGORY_NAME", "ALLERGEN", "ALLERGEN_NAME", "ALLERGY_REASON", "ALLERGY_REASON_NAME",
////					"ALLERGY_REACTION", "ALLERGY_SEVERITY", "ALLERGY_SEVERITY_NAME", "OPERATOR_NURSE",
////					"OPERATOR_NURSE_NAME", "OPERATE_NURSE_TIME", "RECORD_PERSON", "RECORD_PERSON_NAME", "RECORDTIME",
////					"ALLERGY_NAME");
//			list.addAll(list1);
//		}
		//关联患者号

            String[] ids = outPatientId.split(",");
            for (String pid : ids) {
                String[] pid_vid = pid.split("\\|");
                filters.removeIf(filter -> "VISIT_TYPE_CODE".equals(filter.getPropertyName()));
                filters.add(new PropertyFilter("VISIT_TYPE_CODE", MatchType.EQ.getOperation(), pid_vid[0]));

                List<Map<String, String>> list2 = new ArrayList<>();
                ResultVo<PageResultVo<Map<String, String>>> resultVo = hbaseQueryClient.getPageByCondition(
                        PageRequestBuilder.init()
                                .tableName("HDR_ALLERGY")
                                .patientId(pid_vid[1])
                                .oid(pid_vid[2])
                                .visitId(pid_vid[3])
                                .visitTypeCode("")
                                .filters(filters)
                                .pageNo(0)
                                .pageSize(0)
                                .orderBy("RECORDTIME")
                                .desc()
                                .column("ALLERGY_CATEGORY_NAME", "ALLERGEN", "ALLERGEN_NAME", "ALLERGY_REASON", "ALLERGY_REASON_NAME",
                                        "ALLERGY_REACTION", "ALLERGY_SEVERITY", "ALLERGY_SEVERITY_NAME", "OPERATOR_NURSE",
                                        "OPERATOR_NURSE_NAME", "OPERATE_NURSE_TIME", "RECORD_PERSON", "RECORD_PERSON_NAME",
                                        "RECORDTIME", "ALLERGY_NAME")
                                .build());
                if(resultVo.isSuccess()){
                    list2 = resultVo.getContent().getResult();
                }
                list.addAll(list2);
            }

		//排序
		Utils.sortListByDate(list, page.getOrderBy(), page.getOrderDir());
		if (pageable) {
			//分页
			ListPage<Map<String, String>> listPage = new ListPage<Map<String, String>>(list, page.getPageNo(),
					page.getPageSize());
			page.setTotalCount(listPage.getTotalCount());
			page.setResult(listPage.getPagedList());
		} else {
			page.setTotalCount(list.size());
			page.setResult(list);
		}
		//字段映射
		List<Map<String, String>> allergys = new ArrayList<Map<String, String>>();
		for (Map<String, String> map : page) {
			Map<String, String> allergy = new HashMap<String, String>();
			//过敏原因字段处理
			if (StringUtils.isBlank(map.get("ALLERGY_REASON"))) {
				map.put("ALLERGY_REASON", map.get("ALLERGY_REASON_NAME"));
			}
			//过敏程度字段处理
			if (StringUtils.isBlank(map.get("ALLERGY_SEVERITY"))) {
				map.put("ALLERGY_SEVERITY", map.get("ALLERGY_SEVERITY_NAME"));
			}
			//过敏源字段处理
			if (StringUtils.isBlank(map.get("ALLERGEN"))) {
				if (StringUtils.isNotBlank(map.get("ALLERGEN_NAME"))) {
					map.put("ALLERGEN", map.get("ALLERGEN_NAME"));
				} else {
					map.put("ALLERGEN", map.get("ALLERGY_NAME"));
				}
			}
			//记录人字段处理
			if (StringUtils.isBlank(map.get("RECORD_PERSON"))) {
				map.put("RECORD_PERSON", map.get("RECORD_PERSON_NAME"));
			}
			//处理护士字段处理
			if (StringUtils.isBlank(map.get("OPERATOR_NURSE"))) {
				map.put("OPERATOR_NURSE", map.get("OPERATOR_NURSE_NAME"));
			}
			ColumnUtil.convertMapping(allergy, map, "ALLERGY_CATEGORY_NAME", "ALLERGEN", "ALLERGY_REASON",
					"ALLERGY_REACTION", "ALLERGY_SEVERITY", "OPERATOR_NURSE", "OPERATE_NURSE_TIME", "RECORD_PERSON",
					"RECORDTIME");
			allergys.add(allergy);
		}
		page.setResult(allergys);
		return page;
	}

	@Override
	public long getAllergyCount(String oid, String patientId, String visitId, String visitType) {
		Page<Map<String, String>> page = getAllergyList(oid,patientId, visitId, visitType, "", "", 0, 0, "");
		long allergyCount = page.getTotalCount();
		if (allergyCount < 0) {
			allergyCount = 0;
		}
		return allergyCount;
	}

	@Override
	public List<Map<String, String>> getAllergyTypes(String oid) {
		List<Map<String, String>> list = new ArrayList<Map<String, String>>();
		String allergyTypeString = Config.getAllergyTypes(oid);
		String[] types = allergyTypeString.split(";");
		for (String type : types) {
			Map<String, String> map = new HashMap<String, String>();
			String[] array = type.split("\\|");
			map.put("code", array[0]);
			map.put("name", array[1]);
			list.add(map);
		}
		return list;
	}

	@Override
	public List<Map<String, String>> getAllergySeverity(String oid) {
		List<Map<String, String>> list = new ArrayList<Map<String, String>>();
		String allergyTypeString = Config.getAllergySeverity(oid);
		String[] types = allergyTypeString.split(";");
		for (String type : types) {
			Map<String, String> map = new HashMap<String, String>();
			String[] array = type.split("\\|");
			map.put("code", array[1]);
			map.put("name", array[1]);
			list.add(map);
		}
		return list;
	}
}
