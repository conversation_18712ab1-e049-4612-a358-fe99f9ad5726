package com.goodwill.hdr.civ.controller;


import com.goodwill.hdr.civ.enums.VisitTypeEnum;
import com.goodwill.hdr.civ.service.MedicalRecordService;
import com.goodwill.hdr.core.orm.Page;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.ServletContext;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Description 类描述：病历文书Action
 * @Date 2018年6月19日
 * @modify 修改记录：
 */
@RestController
@RequestMapping("/mr")
public class MedicalRecordAction {


    @Autowired
    private MedicalRecordService medicalRecordService;
    @Autowired
    private ServletContext servletContext;


    /**
     * 某次就诊的病历文书
     */
    @PostMapping("/getPatientVisitMrs")
    public Page<Map<String, String>> getPatientVisitMrs(String recordType, String oid, String patientId,
                                                        String visitId, String visitType, int pageSize,
                                                        String orderBy, int pageNo) {
        //参数  患者编号 就诊次 就诊类型
        Page<Map<String, String>> result = medicalRecordService.getTechnologiesMedicalRecordList(oid, patientId, visitId, visitType,
                "CREATE_DATE_TIME", orderBy, pageNo, pageSize, recordType);
        //响应
        return result;
    }

    /**
     * @Description 统计某次就诊的病历类型
     */
    @PostMapping("/getCVMrTypes")
    public List<Map<String, String>> getCVMrTypes(String this_oid, String oid, String patientId, String visitId) {
        List<Map<String, String>> result = new ArrayList<Map<String, String>>();
        if (StringUtils.isNotBlank(patientId)) {
            result = medicalRecordService.getCVMrTypes(oid, patientId, visitId);
        }
        return result;
    }

    /**
     * @Description 根据条件筛选某次就诊病历文书
     */
    @PostMapping("/getCVMrList")
    public Page<Map<String, String>> getCVMrList(String this_oid, String oid, String patientId, String visitId, String type,
                                                 int pageNo, int pageSize) {
        Page<Map<String, String>> result = new Page<Map<String, String>>();
        if (StringUtils.isNotBlank(patientId)) {
            result = medicalRecordService.getCVMrList(oid, patientId, visitId, type, pageNo,
                    pageSize);
        }

        return result;
    }

    /**
     * @Description 某份病历文书详情
     */
    @PostMapping("/getMrDetails")
    public Map<String, String> getMrDetails(String fileNo, String mrClassCode, String oid,
                                            String patientId, String visitId, String visitType) {
        String visitTypeCode = "";
        if (visitType.equals(VisitTypeEnum.IN_VISIT.getLabel())) {
            visitTypeCode = VisitTypeEnum.IN_VISIT.getCode();
        } else if (visitType.equals(VisitTypeEnum.OUT_VISIT.getLabel())) {
            visitTypeCode = VisitTypeEnum.OUT_VISIT.getCode();
        }
        Map<String, String> result = medicalRecordService.getMedicalRecordDetails(fileNo, oid, patientId, visitId, visitTypeCode, mrClassCode, "RM");
        return result;
    }

    /**
     * @Description 获得所有的病例文书
     */
    @PostMapping("/getMedicalRecords")
    public List<Map<String, Object>> getMedicalRecords(String oid, String this_oid, String patientId,
                                                       String visitType, String outPatientId, String year, String key,
                                                       String type, String click_Type) {
        List<Map<String, Object>> result = new ArrayList<Map<String, Object>>();
        result = medicalRecordService.getMedicalRecords(oid, patientId, visitType, outPatientId, year, key, type, click_Type);
        return result;

    }

    /**
     * @Description 获得所有的病例文书类型
     */
    @PostMapping("/getMedicalTypes")
    public List<Map<String, String>> getMedicalTypes(String this_oid, String oid, String patientId, String outPatientId, String visitType) {

        List<Map<String, String>> result = medicalRecordService.getMedicalTypes(this_oid, oid, patientId, outPatientId, visitType);
        return result;

    }


    /**
     * @Description 根据病例类型筛选某次就诊的病历文书
     */
    @PostMapping("/getPatientVisitMrsByMrClass")
    public List<Map<String, Object>> getPatientVisitMrsByMrClass(String oid, String patientId, String visitId, String visitType, String orderBy) {

        //默认查询住院的病历文书
        String vtype = "";
        if ("OUTPV".equals(visitType)) {
            vtype = "01";
        } else if ("INPV".equals(visitType)) {
            vtype = "02";
        }
        //参数  患者编号 就诊次 就诊类型
        List<Map<String, Object>> result = medicalRecordService.getMedicalRecordListByMrClassCode(oid, patientId, visitId, vtype,
                "CREATE_DATE_TIME", orderBy);
        return result;
    }

}
