package com.goodwill.hdr.civ.service.impl;


import com.goodwill.hdr.civ.config.DictConfigs;
import com.goodwill.hdr.civ.enums.DictType;
import com.goodwill.hdr.civ.service.DictHbaseService;
import com.goodwill.hdr.civ.service.TimeAxisViewService;
import com.goodwill.hdr.civ.utils.ListPage;
import com.goodwill.hdr.civ.utils.Utils;
import com.goodwill.hdr.core.orm.MatchType;
import com.goodwill.hdr.core.orm.Page;
import com.goodwill.hdr.hbase.bo.PropertyFilter;
import com.goodwill.hdr.hbase.dto.responseVo.PageResultVo;
import com.goodwill.hdr.hbase.dto.responseVo.ResultVo;
import com.goodwill.hdr.hbaseQueryClient.builder.PageRequestBuilder;
import com.goodwill.hdr.hbaseQueryClient.service.HbaseQueryClient;
import com.goodwill.hdr.security.utils.SecurityCommonUtil;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@Service
public class DictHbaseServiceImpl implements DictHbaseService {

    protected Logger logger = LoggerFactory.getLogger(getClass());


    private final TimeAxisViewService axisViewService;
    private final HbaseQueryClient hbaseQueryClient;

    public DictHbaseServiceImpl(TimeAxisViewService axisViewService, HbaseQueryClient hbaseQueryClient) {
        this.axisViewService = axisViewService;
        this.hbaseQueryClient = hbaseQueryClient;
    }

    /**
     * @param filterPage
     * @return
     * @Description 获取字典
     */
    public Page<Map<String, String>> getDict(String oid, String visitId, DictType type, String columnName, String keyword,
                                             Page<Map<String, String>> filterPage) {
        List<PropertyFilter> filters = new ArrayList<PropertyFilter>();
        if (StringUtils.isBlank(columnName)) {
            columnName = "DICT_ITEM_VALUE";
        }
        //字典基本查询方法
        String mtString = columnName.equals("DICT_ITEM_VALUE") ? MatchType.INLIKE.getOperation() : MatchType.STARTWITH
                .getOperation();
        createPropertyFilter(columnName, keyword, mtString, filters);
        //根据配置来查询字典中的数据
        Map<String, String> queryTypeMap = DictConfigs.getQueryType(oid, type);
        ConfigToFilter(filters, queryTypeMap);

        return getDictBase(oid, DictConfigs.getDictCode(oid, type), visitId, filters, filterPage);
    }

    /**
     * @param
     * @param
     * @param filterPage
     * @return
     * @Description 获取门诊检查字典
     */
    public Page<Map<String, String>> getOutExamDict(String oid, String visitId, DictType type, String columnName, String keyword,
                                                    Page<Map<String, String>> filterPage) {
        List<PropertyFilter> filters = new ArrayList<PropertyFilter>();
        if (StringUtils.isBlank(columnName)) {
            columnName = "DICT_ITEM_VALUE";
        }
        //字典基本查询方法
        String mtString = columnName.equals("DICT_ITEM_VALUE") ? MatchType.INLIKE.getOperation() : MatchType.STARTWITH
                .getOperation();
        //字典
        String rowkey = DictConfigs.getDictCode(oid, type);
        //		String searchKeyWord = "";
        //		if (columnName.equals("DICT_ITEM_CODE")) {
        //			if (keyword.length() > 2) {
        //				rowkey = rowkey + "|" + keyword.substring(0, 2) + "|" + keyword.substring(2, keyword.length());
        //			} else {
        //				rowkey = rowkey + "|" + keyword;
        //			}
        //		} else {
        //			createPropertyFilter(columnName, keyword, mtString, filters);
        //		}
        createPropertyFilter(columnName, keyword, mtString, filters);
        //根据配置来查询字典中的数据
        Map<String, String> queryTypeMap = DictConfigs.getQueryType(oid, type);
        ConfigToFilter(filters, queryTypeMap);
        Page<Map<String, String>> pageResult = getDictBase(oid, rowkey, visitId, filters, filterPage);
        //个性化处理北医三院的大类小类拼接门诊检查号问题
        List<Map<String, String>> list = pageResult.getResult();
        for (Map<String, String> map : list) {
            map.put("DICT_ITEM_CODE", map.get("typeCode") + map.get("subCode"));
        }
        pageResult.setResult(list);
        return pageResult;
    }

    /**
     * 获取字典数据基础方法
     *
     * @param rowKey
     * @param filters
     * @param filterPage
     * @return
     */
    public Page<Map<String, String>> getDictBase(String oid, String rowKey, String visitId, List<PropertyFilter> filters,
                                                 Page<Map<String, String>> filterPage) {
        Page<Map<String, String>> initPage = new Page<Map<String, String>>();
//		initPage = hbaseDao.findByConditionPage(DictConfigs.getDictTableNameString(), rowKey + "|", filters, filterPage);
        ResultVo<PageResultVo<Map<String, String>>> resultVo = hbaseQueryClient.getPageByCondition(
                PageRequestBuilder.init()
                        .tableName(DictConfigs.getDictTableNameString(oid))
                        .patientId(rowKey)
                        .oid(oid)
                        .visitId(visitId)
                        .visitTypeCode("")
                        .filters(filters)
                        .pageNo(0)
                        .pageSize(0)
                        .orderBy("")
                        .desc()
                        .column()
                        .build());
        //initPage = hbaseDao.findByKeyConditionPage(DictConfigs.getDictTableNameString(oid), rowKey, filters, filterPage);
        if (resultVo.isSuccess()) {
            initPage.setResult(resultVo.getContent().getResult());
            initPage.setTotalCount(resultVo.getContent().getTotal());
        }
        return initPage;
    }

    /**
     * 根据字典code查询字典名称基础方法
     *
     * @param rowKey
     * @param filters
     * @param filterPage
     * @return
     */
    public Page<Map<String, String>> getDictByRowkeyList(String oid, String rowKey, String visitId, List<PropertyFilter> filters,
                                                         Page<Map<String, String>> filterPage) {
        String tableName = DictConfigs.getDictTableNameString(oid);
        Page<Map<String, String>> initPage = new Page<Map<String, String>>();
        ResultVo<PageResultVo<Map<String, String>>> resultVo = hbaseQueryClient.getPageByCondition(
                PageRequestBuilder.init()
                        .tableName(tableName)
                        .patientId(rowKey)
                        .oid(oid)
                        .visitId(visitId)
                        .visitTypeCode("")
                        .filters(filters)
                        .pageNo(0)
                        .pageSize(0)
                        .orderBy("")
                        .desc()
                        .column()
                        .build());
        //initPage = hbaseDao.findByKeyConditionPage(DictConfigs.getDictTableNameString(oid), rowKey, filters, filterPage);
        if (resultVo.isSuccess()) {
            initPage.setResult(resultVo.getContent().getResult());
            initPage.setTotalCount(resultVo.getContent().getTotal());
        }
        //initPage = hbaseDao.findByConditionPage(tableName, rowKey, "", filters, filterPage);
        return initPage;
    }

    /**
     * @param
     * @param filterPage
     * @param configFilter 是否需要从配置中读取查询条件来查询
     * @return
     * @Description 根据code查询名称
     */
    public Page<Map<String, String>> getNamebyCode(String oid, String visitId, DictType type, String keywords,
                                                   Page<Map<String, String>> filterPage, boolean configFilter) {
        List<PropertyFilter> filters = new ArrayList<PropertyFilter>();
        if (keywords.contains("|")) {
            keywords = keywords.replace("|", "\\|");
        }
        if (keywords.contains("+")) {
            keywords = keywords.replace("+", "\\+");
        }
        if (keywords.contains("*")) {
            keywords = keywords.replace("*", "\\*");
        }
        if (StringUtils.isNotBlank(keywords)) {
            createPropertyFilter("DICT_ITEM_CODE", keywords, MatchType.IN.getOperation(), filters);
        }
        if (configFilter) {
            //根据配置来查询字典中的数据
            Map<String, String> queryTypeMap = DictConfigs.getQueryType(oid, type);
            ConfigToFilter(filters, queryTypeMap);
        }
        return getDictByRowkeyList(oid, DictConfigs.getDictCode(oid, type), visitId, filters, filterPage);
    }


    /**
     * @param
     * @param filterPage
     * @param configFilter 是否需要从配置中读取查询条件来查询
     * @return
     * @Description 根据code查询名称
     */
    public Page<Map<String, String>> getNamebyDetailName(String oid, String visitId, DictType type, String column, String keywords,
                                                         Page<Map<String, String>> filterPage, boolean configFilter) {
        List<PropertyFilter> filters = new ArrayList<PropertyFilter>();
        if (keywords.contains("|")) {
            keywords = keywords.replace("|", "\\|");
        }
        if (keywords.contains("+")) {
            keywords = keywords.replace("+", "\\+");
        }
        if (keywords.contains("*")) {
            keywords = keywords.replace("*", "\\*");
        }
        if (StringUtils.isNotBlank(keywords)) {
            createPropertyFilter(column, keywords, MatchType.LIKE.getOperation(), filters);
        }
        if (configFilter) {
            //根据配置来查询字典中的数据
            Map<String, String> queryTypeMap = DictConfigs.getQueryType(oid, type);
            ConfigToFilter(filters, queryTypeMap);
        }
        return getDictByRowkeyList(oid, DictConfigs.getDictCode(oid, type), visitId, filters, filterPage);
    }

    /**
     * 创建Filter
     *
     * @param columnName
     * @param keyword
     * @param filters
     */
    private void createPropertyFilter(String columnName, String keyword, String MatchType,
                                      List<PropertyFilter> filters) {
        if (StringUtils.isNotBlank(keyword)) {
            PropertyFilter filter1 = new PropertyFilter();
            filter1.setMatchType(MatchType);
            filter1.setPropertyName(columnName);
            filter1.setPropertyValue(keyword);
            //filter1.setPropertyType("STRING");
            filters.add(filter1);
        }
    }

    /**
     * 将读取的配置转化为filter
     *
     * @param filters
     * @param queryTypeMap
     */
    private void ConfigToFilter(List<PropertyFilter> filters, Map<String, String> queryTypeMap) {
        String mtString;
        if (queryTypeMap.size() > 0) {
            for (Map.Entry<String, String> entry : queryTypeMap.entrySet()) {
                //当配置中多个项时用in查询，当为单个项时用eq查询
                mtString = entry.getValue().contains(",") ? MatchType.IN.getOperation() : MatchType.EQ.getOperation();
                createPropertyFilter(entry.getKey(), entry.getValue(), mtString, filters);
            }
        }
    }

    /**
     * @return
     * @Description 获取检验明细字典，由于三院的检验明细数据字典在单独的一张表中，所以该字典的方法单独使用
     */
    public Page<Map<String, String>> getDictSubLab(String oid, String visitId, String columnName, String keyword,
                                                   Page<Map<String, String>> filterPage) {//getDICTLabNew
        if (StringUtils.isBlank(columnName)) {
            columnName = "CName";
        }
        String tableName = DictConfigs.getDTLABSUB_TEMP(oid);
        Page<Map<String, String>> initPage = new Page<Map<String, String>>();
        //页面查询条件
        List<PropertyFilter> filters = new ArrayList<PropertyFilter>();
        PropertyFilter filter1 = new PropertyFilter();
        filter1.setMatchType(MatchType.EQ.getOperation());//模糊匹配的
        filter1.setPropertyName("IsProfile");
        filter1.setPropertyValue("0");
        filters.add(filter1);
        if (StringUtils.isNotBlank(keyword)) {

            if (Utils.isEnglishChar(keyword)) {
                filter1.setPropertyName("pyCode");
                filter1.setMatchType(MatchType.STARTWITH.getOperation());//模糊匹配的
            } else {
                filter1.setPropertyName(columnName);

                filter1.setMatchType(MatchType.LIKE.getOperation());//模糊匹配的
            }
            filter1.setPropertyValue(Utils.isEnglishChar(keyword) ? keyword.toUpperCase() : keyword);

            filters.add(filter1);
        }
        String[] dict = {"CName"};

        ResultVo<PageResultVo<Map<String, String>>> resultVo = hbaseQueryClient.getPageByCondition(
                PageRequestBuilder.init()
                        .tableName(tableName)
                        .patientId(DictConfigs.getDictCode(oid, DictType.LABSUB))
                        .oid("")
                        .visitId(visitId)
                        .visitTypeCode("02")
                        .filters(filters)
                        .pageNo(0)
                        .pageSize(0)
                        .orderBy("")
                        .desc()
                        .column(dict)
                        .build());
        if (resultVo.isSuccess()) {
            initPage.setResult(resultVo.getContent().getResult());
            initPage.setTotalCount(resultVo.getContent().getTotal());
        }
//        initPage = hbaseDao.findByConditionPage(tableName, "", DictConfigs.getDictCode(oid, DictType.LABSUB), filters,
//                filterPage, dict);
        return initPage;
    }

    public Page<Map<String, String>> getLabSubTypeList(String oid, String search, int pageNo, int pageSize, String patientId, String visitId, String visitType) {
        // TODO Auto-generated method stub
        Page<Map<String, String>> page = new Page<Map<String, String>>();
//		page.setPageNo(pageNo);
//		page.setPageSize(pageSize);
//		//		String rowKeyPrefix="1.2.156.112636.1.2.12.1|";
//		String rowKeyPrefix = DictConfigs.getDictCode(oid, DictType.LABSUB);
//		List<PropertyFilter> filters = new ArrayList<PropertyFilter>();
//		if (StringUtils.isNotBlank(search)) {
//			PropertyFilter filter = new PropertyFilter();
//			filter.setMatchType(MatchType.LIKE.getOperation());
//			filter.setPropertyName("DICT_ITEM_VALUE");
//			filter.setPropertyValue(search);
//			filters.add(filter);
//		}
//		hbaseDao.findByConditionPage("HDR_DICT_ITEM","", rowKeyPrefix, filters,
//				page, new String[] { "LAB_RESULT_UNIT", "LAB_ITEM_CODE", "DICT_ITEM_VALUE", "LAB_ITEM_NAME", "RANGE",
//						"DICT_NAME", "DICT_CODE", "DICT_ITEM_CODE" });

        //检验指标查询检验结果表
        List<PropertyFilter> filters = new ArrayList<PropertyFilter>();
        if (StringUtils.isNotBlank(search)) {
            PropertyFilter filter = new PropertyFilter();
            filter.setMatchType(MatchType.LIKE.getOperation());
            filter.setPropertyName("LAB_SUB_ITEM_NAME");
            filter.setPropertyValue(search);
            filters.add(filter);
        }
        //filters.add(new PropertyFilter("VISIT_ID", "STRING", MatchType.EQ.getOperation(), visitId));
        List<Map<String, String>> subLabs = new ArrayList<>();
//        List<Map<String, String>> subLabs = hbaseDao.findConditionByPatient("HDR_LAB_REPORT_DETAIL", oid, patientId, filters,
//                new String[]{"LAB_RESULT_UNIT", "LAB_ITEM_CODE", "LAB_SUB_ITEM_CODE", "LAB_SUB_ITEM_NAME", "LAB_ITEM_NAME", "RANGE", "REPORT_TIME"});
        ResultVo<PageResultVo<Map<String, String>>> resultVo = hbaseQueryClient.getPageByCondition(
                PageRequestBuilder.init()
                        .tableName("HDR_LAB_REPORT_DETAIL")
                        .patientId(patientId)
                        .oid(oid)
                        .visitId(visitId)
                        .visitTypeCode("")
                        .filters(filters)
                        .pageNo(0)
                        .pageSize(0)
                        .orderBy("REPORT_TIME")
                        .desc()
                        .column("LAB_RESULT_UNIT", "LAB_ITEM_CODE", "LAB_SUB_ITEM_CODE", "LAB_SUB_ITEM_NAME", "LAB_ITEM_NAME", "RANGE", "REPORT_TIME")
                        .build());
        if (resultVo.isSuccess()) {
            subLabs = resultVo.getContent().getResult();
        }
        //获取已添加指标
        List<Map<String, String>> addeds = new ArrayList<Map<String, String>>();
        String usercode = SecurityCommonUtil.getLoginUserCode();
        addeds = axisViewService.getConfigList(oid, usercode, patientId, visitType, visitId);

        //去重
        subLabs = executeDuplicates(subLabs, addeds);

        subLabs.sort((t1, t2) -> t2.get("REPORT_TIME").compareTo(t1.get("REPORT_TIME")));
        ListPage<Map<String, String>> listPage = new ListPage<Map<String, String>>(subLabs, pageNo, pageSize);
        List<Map<String, String>> labs = listPage.getPagedList();
        page.setTotalCount(listPage.getTotalCount());
        page.setResult(labs);
        page.setPageNo(pageNo);
        page.setPageSize(pageSize);

        return page;
    }

    public List<Map<String, String>> executeDuplicates(List<Map<String, String>> items, List<Map<String, String>> addeds) {
        for (Map<String, String> map : items) {
            map.put("status", "0");
            //map.put("has_add", "未添加");
            for (Map<String, String> temp : addeds) {
                if (temp.get("subItemCode").equals(map.get("LAB_SUB_ITEM_CODE"))) {
                    map.put("status", "1");
                    //map.put("has_add", "已添加");
                    break;
                }
            }
        }
        return items;
    }

}
