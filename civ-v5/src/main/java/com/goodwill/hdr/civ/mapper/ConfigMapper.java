package com.goodwill.hdr.civ.mapper;

import com.goodwill.hdr.civ.entity.Config;
import com.goodwill.hdr.civ.provider.CivConfigProvider;
import com.goodwill.hdr.web.core.mapper.HdrBaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.SelectProvider;

import java.util.List;

/**
 * <p>
 * Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-03-03
 */
@Mapper
public interface ConfigMapper extends HdrBaseMapper<Config> {

    /**
     * 查询配置
     *
     * @param keyWord
     * @param configType
     * @return
     */
//    @SelectProvider(type= CivConfigProvider.class,method="queryConfigList")
    List<Config> queryConfigList(@Param("oid") String oid,
                                 @Param("keyWord") String keyWord,
                                 @Param("configScopePage") String configType);

    /**
     * 获取所有配置
     *
     * @return
     */
    @Select(" select id,config_code,ifnull(config_name,\"\") as config_name,ifnull(config_value,\"\") as config_value,ifnull(config_desc,\"\") as config_desc,config_type,config_level,config_index,config_scope_page,group_id,group_name,group_index,create_time,last_update_time,is_inuse from  civ_config where is_inuse = 'Y' and oid=#{oid} order by id,group_index ")
    List<Config> queryAllConfig(String oid);


    /**
     * 获取所有院区oid name
     *
     * @param oid
     * @return
     */
    @Select("select config_value from civ_config where config_code = 'OID' and oid = #{oid} and is_inuse = 'Y'")
    List<Config> getHospitalOid(String oid);

    /**
     * 根据配置code获取code
     */
    @SelectProvider(type = CivConfigProvider.class, method = "getConfigBycode")
    List<Config> getConfigBycode(String oid, String configCode);


}
