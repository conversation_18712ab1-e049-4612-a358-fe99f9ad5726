package com.goodwill.hdr.civ.controller;


import com.goodwill.hdr.civ.service.InspectReportService;
import com.goodwill.hdr.civ.vo.InspectionReportTabVo;
import com.goodwill.hdr.core.orm.Page;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Description 类描述：检验Action
 * @Date 2018年6月14日
 * @modify 修改记录：
 */
@RequestMapping("/inspect")
@RestController
@Api(tags = "检验报告查询")
public class InspectAction {


    @Autowired
    private InspectReportService inspectReportService;

    /**
     * @Description 某次就诊的检验报告
     */
    @ApiOperation(value = "获取某次就诊的检验报告", notes = "获取某次就诊的检验报告", httpMethod = "POST")
    @RequestMapping(value = "/getPatientVisitInspects", method = RequestMethod.POST)
    public Page<Map<String, String>> getPatientVisitInspects(String oid, String patientId, String visitId, String visitType, int pageNo, int pageSize,String tabCode) {
        //患者编号  就诊次  就诊类型
        Page<Map<String, String>> result = inspectReportService.getInspectReportList(oid, patientId, visitId, visitType,
                "REPORT_TIME", "desc", pageNo, pageSize,tabCode);
        //响应
        return result;
    }

    /**
     * @Description 某份检验报告的详情  基本信息+检验细项列表
     */
    @ApiOperation(value = "获取检验报告详情", notes = "获取检验报告详情", httpMethod = "POST")
    @RequestMapping(value = "/getInspectDetails", method = RequestMethod.POST)
    public Map<String, Object> getInspectDetails(String reportNo, String show, String oid, String patientId, String visitId,String tabCode,String wyhrReportNo) {
        //报告号 显示标识 1:显示异常结果
        int pageNo = 1;
        int pageSize = 10;
        Map<String, Object> result = inspectReportService.getInspectReportDetails(oid, patientId, visitId,reportNo, pageNo,
                pageSize, show, tabCode, wyhrReportNo);
        //响应
        return result;
    }
    @ApiOperation(value = "获取检验报告详情(历次报告对比)", notes = "获取检验报告详情(历次报告对比)", httpMethod = "POST")
    @RequestMapping(value = "/getInspectDetailSubItems", method = RequestMethod.POST)
    public List<Map<String, Object>> getInspectDetailSubItems(String reportNo, String oid,String visitId, String patientId, @RequestParam("reportTime")List<String> reportTime, String labItemCode) {
        //报告号 显示标识 1:显示异常结果
        int pageNo = 1;
        int pageSize = 10;
        List<Map<String, Object>> result = inspectReportService.getInspectReportDetailSubItems(oid, patientId,visitId, reportNo,pageNo,
                pageSize, reportTime,labItemCode);
        //响应
        return result;
    }
    /**
     * @Description 某个检验细项的趋势图  如：白细胞变化趋势等
     */
    @ApiOperation(value = "获取检验细项的趋势图", notes = "获取检验细项的趋势图", httpMethod = "POST")
    @RequestMapping(value = "/getInspectItemLine", method = RequestMethod.POST)
    public Map<String, Object> getInspectItemLine(String dateType, String startDate, String endDate, String code, String outPatientId, String visitType,
                                                  String oid, String patientId,String specimanTypeCode) {

        Map<String, Object> result = inspectReportService.getInspectReportDetailsLine(oid, patientId, visitType, outPatientId, dateType, startDate,
                endDate, code, specimanTypeCode);
        //响应
        return result;
    }

    /**
     * @Description 当前视图 - 获取末次住院的异常检验结果
     */
    @ApiOperation(value = "获取末次住院的异常检验结果", notes = "获取末次住院的异常检验结果", httpMethod = "POST")
    @RequestMapping(value = "/getCVExLabResult", method = RequestMethod.POST)
    public List<Map<String, String>> getCVExLabResult( String oid, String patientId, String visitId, String mainDiag, String dept_code) {
        List<Map<String, String>> result = new ArrayList<Map<String, String>>();
        if (StringUtils.isNotBlank(patientId)) {
            result = inspectReportService.getExLabResult(oid, patientId, visitId, "INPV", mainDiag, dept_code);
        }
        return result;
    }

    /**
     * @Description 获得所有检验报告
     */
    @ApiOperation(value = "获得所有检验报告", notes = "获得所有检验报告", httpMethod = "POST")
    @RequestMapping(value = "/getInspectReports", method = RequestMethod.POST)
    public List<Map<String, Object>> getInspectReports(String this_oid, String oid, String patientId, String outPatientId, String year, String visitType,String typeCode) {
        List<Map<String, Object>> result = inspectReportService.getInspectReports(this_oid, oid, patientId, visitType, 1, 10000, "REPORT_TIME",
                "desc", outPatientId, year,typeCode);
        //响应
        return result;
    }

    /**
     * @Description 获得所有检验报告类型
     */
    @ApiOperation(value = "获得所有检验报告类型", notes = "获得所有检验报告类型", httpMethod = "POST")
    @RequestMapping(value = "/getReportsTypes", method = RequestMethod.POST)
    public List<Map<String, String>> getReportsTypes(String this_oid, String oid, String patientId, String outPatientId, String visitType) {
        List<Map<String, String>> result = inspectReportService.getReportsTypes(this_oid, oid, patientId, outPatientId, visitType);
        //响应
        return result;
    }

    /**
     * @Description 获得检验报告tab页签
     */
    @ApiOperation(value = "获得检验报告tab页签", notes = "获得检验报告tab页签", httpMethod = "POST")
    @RequestMapping(value = "/getInspectionReportTab", method = RequestMethod.POST)
    public List<Map<String, String>>  getInspectionReportTab(String oid, String patientId, String visitId, String visitType, String outPatientId){
        List<InspectionReportTabVo> list = inspectReportService.getReportType(oid);
        List<Map<String, String>>  result = inspectReportService.getInspectionReportTab(oid,patientId, visitId, visitType,outPatientId,list);
        return result;
    }
}
