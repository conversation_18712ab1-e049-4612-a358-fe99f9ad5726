package com.goodwill.hdr.civ.controller;

import com.goodwill.hdr.civ.config.CommonConfig;
import com.goodwill.hdr.civ.config.Config;
import com.goodwill.hdr.civ.enums.VisitTypeEnum;
import com.goodwill.hdr.civ.service.CommonURLService;
import com.goodwill.hdr.civ.service.NursingService;
import com.goodwill.hdr.civ.service.OperService;
import com.goodwill.hdr.core.orm.Page;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Description 类描述：手术Action
 * @Date 2018年6月19日
 * @modify 修改记录：
 */
@RequestMapping("/oper")
@RestController
@Api(tags = "手术记录查询")
public class OperationAction {

    @Autowired
    private OperService operService;
    @Autowired
    private CommonURLService commonURLService;
    @Autowired
    private NursingService nursingService;

    /**
     * @Description 某次就诊的手术记录
     */
    @ApiOperation(value = "获取某次就诊的手术记录", notes = "获取某次就诊的手术记录", httpMethod = "POST")
    @RequestMapping(value = "/getPatientVisitOperas", method = RequestMethod.POST)
    public Page<Map<String, String>> getPatientVisitOperas(String pno, String oid, String patientId, String visitType, String visitId, int pageSize) {
        int pno1 = StringUtils.isBlank(pno) ? 0 : Integer.parseInt(pno);
        //参数 患者编号 就诊次 就诊类型
        Page<Map<String, String>> result = operService.getOperList(oid, patientId, visitType, visitId, "OPER_START_TIME",
                "desc", pno1, pageSize);
        //响应
        return result;
    }

    /**
     * @Description 某次手术的术前访视信息
     */
    @ApiOperation(value = "获取某次手术的术前访视信息", notes = "获取某次手术的术前访视信息", httpMethod = "POST")
    @RequestMapping(value = "/getOperVisit", method = RequestMethod.POST)
    public Map<String, Object> getOperVisit(String oid, String patientId,  String visitId,String orderNo, String operNo) {
        //参数  医嘱号 手术序号
//        String orderNo = getParameter("orderNo");
//        String operNo = getParameter("operNo");
        Map<String, Object> result = operService.getOperVisit(oid, patientId,visitId, orderNo, operNo);
        //响应
        return result;
    }

    /**
     * @Description 某次手术的麻醉信息
     */
    @ApiOperation(value = "获取某次手术的麻醉信息", notes = "获取某次手术的麻醉信息", httpMethod = "POST")
    @RequestMapping(value = "/getOperAnaes", method = RequestMethod.POST)
    public Map<String, Object> getOperAnaes(String oid, String patientId,  String visitId,String orderNo, String operNo) {
        //参数  医嘱号 手术序号
//        String orderNo = getParameter("orderNo");
//        String operNo = getParameter("operNo");
        Map<String, Object> result = operService.getOperAnaes(oid, patientId, visitId,orderNo, operNo);
        //响应
        return result;
    }

    /**
     * @Description 某次手术的术中信息
     */
    @ApiOperation(value = "获取某次手术的术中信息", notes = "获取某次手术的术中信息", httpMethod = "POST")
    @RequestMapping(value = "/getOperProcess", method = RequestMethod.POST)
    public Map<String, Object> getOperProcess(String oid, String patientId,  String visitId,String orderNo, String operNo) {
        //参数  医嘱号 手术序号
//        String orderNo = getParameter("orderNo");
//        String operNo = getParameter("operNo");
        Map<String, Object> result = operService.getOperProcess(oid, patientId,visitId, orderNo, operNo);
        //响应
        return result;
    }

    /**
     * @Description 某次手术的手术用药
     */
    @ApiOperation(value = "获取某次手术的手术用药", notes = "获取某次手术的手术用药", httpMethod = "POST")
    @RequestMapping(value = "/getOperDrug", method = RequestMethod.POST)
    public Page<Map<String, String>> getOperDrug(String oid, String patientId, String visitId,String orderNo, String operNo, int pageNo, int pageSize) {
        //参数  医嘱号 手术序号
//        String orderNo = getParameter("orderNo");
//        String operNo = getParameter("operNo");
        Page<Map<String, String>> result = operService.getOperDrug(oid, patientId, visitId,orderNo, operNo, pageNo, pageSize);
        //响应
        return result;
    }

    /**
     * @Description 某次手术的恢复室信息
     */
    @ApiOperation(value = "获取某次手术的恢复室信息", notes = "获取某次手术的恢复室信息", httpMethod = "POST")
    @RequestMapping(value = "/getOperRecovery", method = RequestMethod.POST)
    public Map<String, Object> getOperRecovery(String oid, String patientId,String visitId, String orderNo, String operNo) {
        //参数  医嘱号 手术序号
//        String orderNo = getParameter("orderNo");
//        String operNo = getParameter("operNo");
        Map<String, Object> result = operService.getOperRecovery(oid, patientId, visitId,orderNo, operNo);
        //响应
        return result;
    }

    /**
     * @Description 某次手术的术后访视信息
     */
    @ApiOperation(value = "获取某次手术的术后访视信息", notes = "获取某次手术的术后访视信息", httpMethod = "POST")
    @RequestMapping(value = "/getOperAfter", method = RequestMethod.POST)
    public Map<String, Object> getOperAfter(String oid, String patientId, String visitId,String orderNo, String operNo) {
        //参数  医嘱号 手术序号
//        String orderNo = getParameter("orderNo");
//        String operNo = getParameter("operNo");
        Map<String, Object> result = operService.getOperAfter(oid, patientId, visitId,orderNo, operNo);
        //响应
        return result;
    }

    /**
     * @Description 获得患者所有手术记录
     */
    @ApiOperation(value = "获取患者所有手术记录", notes = "获取患者所有手术记录", httpMethod = "POST")
    @RequestMapping(value = "/getOpers", method = RequestMethod.POST)
    public Page<Map<String, String>> getOpers( String oid, String patientId, String outPatientId, String visitType, String orderBy, String orderDir) {

        int pageNo = 1;
        int pageSize = 10;
        Page<Map<String, String>> page = operService.getOpers(oid, patientId, visitType, pageNo, pageSize, orderBy, orderDir,
                outPatientId);
        //响应
        return page;
    }

    /**
     * 是否配置外部手术url
     */
    @ApiOperation(value = "是否配置外部手术url", notes = "是否配置外部手术url", httpMethod = "POST")
    @RequestMapping(value = "/getOperConfig", method = RequestMethod.POST)
    public Map<String, String> getOperConfig(String oid) {
        Map<String, String> list = operService.getOperConfig(oid);
        //响应
        return list;
    }


    /**
     * 若未配置外部url,获取手术麻醉单配置
     */
    @ApiOperation(value = "获取手术麻醉单配置", notes = "获取手术麻醉单配置", httpMethod = "POST")
    @RequestMapping(value = "/getOperMenuConfig", method = RequestMethod.POST)
    public List<Map<String, String>> getOperMenuConfig(String oid) {
        List<Map<String, String>> map = operService.getAnesthesiaConfig(oid);
        //响应
        return map;
    }

    /**
     * 获取手术麻醉单数据
     */
    @ApiOperation(value = "获取手术麻醉单数据", notes = "获取手术麻醉单数据", httpMethod = "POST")
    @RequestMapping(value = "/getAnesthesiaData", method = RequestMethod.POST)
    public Map<String, Object> getAnesthesiaData(String oid, String patientId, String visitId, String visitType, String operNo, String inpNo) {

        String visitTypeCode = visitType.equals(VisitTypeEnum.IN_VISIT.getLabel()) ? VisitTypeEnum.IN_VISIT.getCode() : VisitTypeEnum.OUT_VISIT.getCode();
        Map<String, String> url = commonURLService.getCommonUrl(oid, patientId, visitId, visitTypeCode, "ANES", "url");
        if (StringUtils.isNotBlank(url.get("url"))) {
            //前端需要type去判断手麻的显示类型，这里暂时这样处理一下
            url.put("type", CommonConfig.getLinkType(oid, "ANES"));
            Map<String, Object> map = new HashMap<>(url);
            return map;
        } else {
            Map<String, Object> urlRes = new HashMap<>();
            List<Map<String, Object>> listMap = operService.getAnesthesiaData(oid, patientId, visitId,operNo);
            urlRes.put("data", listMap);
            urlRes.put("linkType", "image");
            return urlRes;
        }
    }

    /**
     * 是否显示手术列表
     */
    @ApiOperation(value = "是否显示手术列表", notes = "是否显示手术列表", httpMethod = "POST")
    @RequestMapping(value = "/getOperListConfig", method = RequestMethod.POST)
    public Map<String, String> getOperListConfig(String oid) {
        String config = Config.getSHOW_OPER_LIST(oid);
        Map<String, String> configMap = new HashMap<String, String>();
        configMap.put("showList", config);
        return configMap;
    }

    /**
     * @Description 住院的就诊列表
     */
    @ApiOperation(value = "获取住院的就诊列表", notes = "获取住院的就诊列表", httpMethod = "POST")
    @RequestMapping(value = "/getAllINVisits", method = RequestMethod.POST)
    public List<Map<String, String>> getAllINVisits(String outPatientId) {
//        String year = getParameter("year");
        List<Map<String, String>> rs = new ArrayList<Map<String, String>>();
        rs = nursingService.getAllINVisits(outPatientId);
        return rs;
    }

}
