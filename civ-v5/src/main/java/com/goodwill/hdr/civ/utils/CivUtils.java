package com.goodwill.hdr.civ.utils;


import com.goodwill.hdr.civ.config.ConfigCache;
import com.goodwill.hdr.core.orm.Page;
import com.goodwill.hdr.hbase.bo.PropertyFilter;
import com.goodwill.hdr.hbaseQueryClient.service.HbaseQueryClient;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import sun.misc.BASE64Decoder;

import javax.crypto.Cipher;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import java.io.File;
import java.io.FileOutputStream;
import java.io.OutputStream;
import java.text.SimpleDateFormat;
import java.time.Duration;
import java.time.LocalDateTime;
import java.time.Month;
import java.time.format.DateTimeFormatter;
import java.util.*;

/**
 * <AUTHOR>
 * @Description 类描述： 业务相关的工具类
 * @Date 2018年6月14日
 * @modify 修改记录：
 */
@Service
public class CivUtils {

    private static Logger logger = LoggerFactory.getLogger(CivUtils.class);

    //    @Autowired
//    private static HbaseDao hbaseDao;
//    private static HbaseDao hbaseDao = (HbaseDao) SpringContextHolder.getBean("hbaseDao");
    private final HbaseQueryClient hbaseQueryClient;

    public CivUtils(HbaseQueryClient hbaseQueryClient) {
        this.hbaseQueryClient = hbaseQueryClient;
    }


    public static void main1(String[] args) {
        String range = "0.86-2.9 ND-3.6 1.5 - 5.5 3.0-68 ND-3.18";
        Map<String, Object> map = parseRange(range);
        System.out.println(map);
    }

    /**
     * @param page      分页
     * @param patientId 患者编号
     * @param visitId   就诊次数
     * @param pageable  是否分页
     * @return
     * @Description 方法描述: 查询某次住院的手术记录
     */
//    public  Page<Map<String, String>> getInpOperas(Page<Map<String, String>> page, String oid, String patientId,
//                                                         String visitId, boolean pageable) {
//        List<Map<String, String>> opeas = new ArrayList<Map<String, String>>();
////		String rowkeyPrefix = HbaseCURDUtils.getRowkeyPrefix(patientId) + "|" + visitId + "|";
//        //查询住院记录
//        List<PropertyFilter> filters1 = new ArrayList<PropertyFilter>();
//        filters1.add(new PropertyFilter("TRANS_NO", MatchType.EQ.getOperation(), "0")); //仅查询入出院记录
//        List<Map<String, String>> adts = new ArrayList<>();
//        ResultVo<PageResultVo<Map<String, String>>> resultVo = hbaseQueryClient.getPageByCondition(
//                PageRequestBuilder.init()
//                        .tableName(HdrTableEnum.HDR_PAT_ADT.getCode())
//                        .patientId(patientId)
//                        .oid(oid)
//                        .visitId(visitId)
//                        .visitTypeCode("02")
//                        .filters(filters1)
//                        .pageNo(0)
//                        .pageSize(0)
//                        .orderBy("")
//                        .desc()
//                        .column("ADMISSION_TIME", "DISCHARGE_TIME", "TRANS_NO")
//                        .build());
//        if(resultVo.isSuccess()){
//            adts =  resultVo.getContent().getResult();
//        }
////        List<Map<String, String>> adts = hbaseDao.findConditionByPatientVisitId(HdrTableEnum.HDR_PAT_ADT.getCode(), oid,
////                patientId, visitId, filters1, "ADMISSION_TIME", "DISCHARGE_TIME", "TRANS_NO");
//        if (adts.size() == 0) {
//            return page;
//        }
//        //获取入出院记录
//        Map<String, String> adt = adts.get(0);
//        String admTime = adt.get("ADMISSION_TIME");
//        String disTime = adt.get("DISCHARGE_TIME");
//        //入院时间为空  中断执行
//        if (StringUtils.isBlank(admTime)) {
//            return page;
//        }
//        if (StringUtils.isBlank(disTime)) {
//            //取当前时间
//            disTime = DateUtils.getNowDateTime();
//        }
//        List<PropertyFilter> filters2 = new ArrayList<PropertyFilter>();
//        filters2.add(new PropertyFilter("OPER_START_TIME", MatchType.GE.getOperation(), admTime));
//        filters2.add(new PropertyFilter("OPER_END_TIME", MatchType.LE.getOperation(), disTime));
////		rowkeyPrefix = HbaseCURDUtils.getRowkeyPrefix(patientId) + "|";
//        //分页判断
//        if (pageable) {
//            page = hbaseDao.findPageConditionByPatient(HdrTableEnum.HDR_OPER_ANAES.getCode(), oid, patientId,
//                    page, filters2, "OPER_START_TIME", "OPERATION_CODE", "OPERATION_NAME", "OPER_NO", "ORDER_NO");
//        } else {
//            ResultVo<PageResultVo<Map<String, String>>> resultVo1 = hbaseQueryClient.getPageByCondition(
//                    PageRequestBuilder.init()
//                            .tableName(HdrTableEnum.HDR_OPER_ANAES.getCode())
//                            .patientId(patientId)
//                            .oid(oid)
//                            .visitId("")
//                            .visitTypeCode("")
//                            .filters(filters2)
//                            .pageNo(0)
//                            .pageSize(0)
//                            .orderBy("")
//                            .desc()
//                            .column( "OPER_START_TIME", "OPERATION_CODE", "OPERATION_NAME", "OPER_NO", "ORDER_NO")
//                            .build());
//            if(resultVo1.isSuccess()){
//                opeas =  resultVo1.getContent().getResult();
//            }
////            opeas = hbaseDao.findConditionByPatient(HdrTableEnum.HDR_OPER_ANAES.getCode(), oid, patientId, filters2,
////                    "OPER_START_TIME", "OPERATION_CODE", "OPERATION_NAME", "OPER_NO", "ORDER_NO");
//            Utils.sortListByDate(opeas, page.getOrderBy(), page.getOrderDir()); //排序
//            page.setResult(opeas);
//            page.setTotalCount(opeas.size());
//        }
//        return page;
//    }
//
//    /**
//     * @param page     分页
//     * @param patId    患者编号
//     * @param visitId  就诊次数
//     * @param pageable 是否分页
//     * @return 返回类型： void
//     * @Description 方法描述: 查询某次门诊的检验报告
//     * 根据门诊医技申请，就诊时间，申请时间，采样时间等条件筛选
//     */
//    public static Page<Map<String, String>> getOutpLabReports(Page<Map<String, String>> page, String oid, String patId,
//                                                              String visitId, boolean pageable) {
//        List<Map<String, String>> labs = new ArrayList<Map<String, String>>();
//        //根据门诊医技申请查询
////		String rowkeyPrefix = HbaseCURDUtils.getRowkeyPrefix(patId) + "|" + visitId + "|";
//        //本次门诊的检验申请
//        List<PropertyFilter> filters1 = new ArrayList<PropertyFilter>();
//        filters1.add(new PropertyFilter("APPLY_TYPE_CODE", "STRING", MatchType.IN.getOperation(), "02")); //检验申请
//        List<Map<String, String>> applys = hbaseDao.findConditionByPatientVisitId(HdrTableEnum.HDR_OUT_APPLY.getCode(), oid,
//                patId, visitId, filters1, "APPLY_NO");
//        //拼接检验申请号
//        if (applys.size() > 0) {
//            StringBuffer sb = new StringBuffer();
//            for (Map<String, String> apply : applys) {
//                sb.append(apply.get("APPLY_NO") + ",");
//            }
//            String appNo = sb.substring(0, sb.lastIndexOf(",")).toString();
//            //根据检验申请号 查询检验报告
////			rowkeyPrefix = HbaseCURDUtils.getRowkeyPrefix(patId) + "|";
//            List<PropertyFilter> filters2 = new ArrayList<PropertyFilter>();
//            filters2.add(new PropertyFilter("VISIT_TYPE_CODE", "STRING", MatchType.EQ.getOperation(), "01"));
//            filters2.add(new PropertyFilter("APPLY_NO", "STRING", MatchType.IN.getOperation(), appNo));
//            labs = hbaseDao.findConditionByPatient(HdrTableEnum.HDR_LAB_REPORT.getCode(), oid, patId, filters2,
//                    "LAB_ITEM_NAME", "REPORT_TIME", "REPORT_NO", "VISIT_ID");
//        }
//        //根据门诊医技申请未查到结果，再根据就诊时间、检验申请时间、采样时间查询
//        if (labs.size() == 0) {
//            labs = labList(oid, patId, visitId);
//        }
//        //上述均未查到，终止执行
//        if (labs.size() == 0) {
//            return page;
//        }
//        //排序
//        Utils.sortListByDate(labs, page.getOrderBy(), page.getOrderDir());
//        //分页判断
//        if (pageable) {
//            ListPage<Map<String, String>> listPage = new ListPage<Map<String, String>>(labs, page.getPageNo(),
//                    page.getPageSize());
//            page.setResult(listPage.getPagedList());
//            page.setTotalCount(listPage.getTotalCount());
//        } else {
//            page.setResult(labs);
//            page.setTotalCount(labs.size());
//        }
//        //只取缺失VISIT_ID的检验报告
//        for (Map<String, String> lab : page) {
//            if (StringUtils.isNotBlank(lab.get("VISIT_ID"))) {
//                page.getResult().remove(lab);
//            }
//        }
//        return page;
//    }
//
//    /**
//     * @param page     分页
//     * @param patId    患者编号
//     * @param visitId  就诊次数
//     * @param pageable 是否分页
//     * @return
//     * @Description 方法描述: 查询某次住院的检验报告
//     * 根据入院，出院时间筛选
//     */
//    public static Page<Map<String, String>> getInpLabReports(Page<Map<String, String>> page, String oid, String patId,
//                                                             String visitId, boolean pageable) {
//        List<Map<String, String>> labs = new ArrayList<Map<String, String>>();
//        //根据入院  出院时间查询
////		String rowkeyPrefix = HbaseCURDUtils.getRowkeyPrefix(patId) + "|" + visitId + "|";
//        List<PropertyFilter> filters1 = new ArrayList<PropertyFilter>();
//        filters1.add(new PropertyFilter("TRANS_NO", "STRING", MatchType.EQ.getOperation(), "0")); //仅查询入出院记录
//        List<Map<String, String>> adts = hbaseDao.findConditionByPatientVisitId(HdrTableEnum.HDR_PAT_ADT.getCode(), oid,
//                patId, visitId, filters1, "VISIT_ID", "ADMISSION_TIME", "DISCHARGE_TIME", "TRANS_NO");
//        //未找到住院记录  则认为未查到住院检验报告
//        if (adts.size() == 0) {
//            return page;
//        }
//        Map<String, String> adt = adts.get(0);
//        String startTime = adt.get("ADMISSION_TIME");
//        if (StringUtils.isBlank(startTime)) {
//            return page;
//        }
//        String endTime = adt.get("DISCHARGE_TIME");
//        if (StringUtils.isBlank(endTime)) {
//            endTime = DateUtils.getNowDateTime();
//        }
//        //查询入院到出院 或 入院到至今的检验报告
//        List<PropertyFilter> filters2 = new ArrayList<PropertyFilter>();
//        filters2.add(new PropertyFilter("VISIT_TYPE_CODE", "STRING", MatchType.EQ.getOperation(), "02"));
//        filters2.add(new PropertyFilter("REPORT_TIME", "STRING", MatchType.GE.getOperation(), startTime));
//        filters2.add(new PropertyFilter("REPORT_TIME", "STRING", MatchType.LT.getOperation(), endTime));
////		rowkeyPrefix = HbaseCURDUtils.getRowkeyPrefix(patId) + "|";
//        //分页判断
//        if (pageable) {
//            page = hbaseDao.findByConditionPage(HdrTableEnum.HDR_LAB_REPORT.getCode(), oid, patId, filters2,
//                    page, "LAB_ITEM_NAME", "REPORT_TIME", "REPORT_NO");
//        } else {
//            labs = hbaseDao.findConditionByPatient(HdrTableEnum.HDR_LAB_REPORT.getCode(), oid, patId, filters2,
//                    "LAB_ITEM_NAME", "REPORT_TIME", "REPORT_NO");
//            Utils.sortListByDate(labs, page.getOrderBy(), page.getOrderDir()); //排序
//            page.setResult(labs);
//            page.setTotalCount(labs.size());
//        }
//        return page;
//    }
//
//    /**
//     * @param patientId 患者编号
//     * @param visitId   就诊次数
//     * @return
//     * @Description 方法描述: 根据就诊时间，申请时间，采样时间筛选本次门诊的检验报告
//     */
//    public static List<Map<String, String>> labList(String oid, String patientId, String visitId) {
//        //存储检验报告
//        List<Map<String, String>> labs = new ArrayList<Map<String, String>>();
//        //门诊列表
////		String rowkeyPrefix = HbaseCURDUtils.getRowkeyPrefix(patientId) + "|";
//        List<Map<String, String>> outVisits = hbaseDao.findAllByPatient(HdrTableEnum.HDR_OUT_VISIT.getCode(), oid,
//                patientId, "REGISTING_TIME", "VISIT_ID", "VISIT_TIME", "VISIT_DEPT_CODE");
//        if (outVisits.size() == 0) {
//            return labs;
//        }
//        //遍历门诊列表  若就诊时间为空 用挂号时间替换
//        for (Map<String, String> out : outVisits) {
//            if (StringUtils.isBlank(out.get("VISIT_TIME"))) {
//                String registingTime = out.get("REGISTING_TIME"); //挂号时间
//                out.put("VISIT_TIME", registingTime);
//            }
//        }
//        //门诊列表  按照就诊时间 降序排列
//        Utils.sortListByDate(outVisits, "VISIT_TIME", "desc");
//        String visitTime = null; //本次门诊时间
//        String nextVisitTime = null; //下次门诊时间
//        String visitDept = null; //本次门诊科室
//        int index = 0; //门诊在列表中的索引
//        for (Map<String, String> visit : outVisits) {
//            if (visitId.equals(visit.get("VISIT_ID"))) {
//                visitTime = visit.get("VISIT_TIME");
//                visitDept = visit.get("VISIT_DEPT_CODE");
//                if (index != 0) {
//                    nextVisitTime = outVisits.get(index - 1).get("VISIT_TIME");
//                }
//                break;
//            }
//            index++;
//        }
//        //查询所有的门诊检验报告
//        List<PropertyFilter> filter1 = new ArrayList<PropertyFilter>();
//        filter1.add(new PropertyFilter("VISIT_TYPE_CODE", "STRING", MatchType.EQ.getOperation(), "01"));
//        List<Map<String, String>> allLabs = hbaseDao.findConditionByPatient(HdrTableEnum.HDR_LAB_REPORT.getCode(), oid,
//                patientId, filter1, "LAB_ITEM_NAME", "REPORT_TIME", "REPORT_NO", "VISIT_ID",
//                "APPLY_DEPT_CODE", "APPLY_TIME", "SAMPLE_TIME");
//        for (Map<String, String> lab : allLabs) {
//            //根据检验申请时间和本次就诊时间判断
//            String applyDept = lab.get("APPLY_DEPT_CODE");
//            String applyTime = lab.get("APPLY_TIME");
//            if (StringUtils.isNotBlank(applyTime)) {
//                if (applyTime.length() > 19) { //限制申请时间格式  yyyy-MM-dd HH:mm:ss
//                    applyTime = applyTime.substring(0, 19);
//                } else if (applyTime.length() == 10 && visitTime.length() != 10) {
//                    applyTime = applyTime + " 00:00:00";
//                }
//                //申请时间=本次就诊时间  且 申请科室=本次就诊科室  报告属于本次就诊
//                if (Utils.compareStrDateTime(applyTime, visitTime) == 0) { //申请时间等于本次就诊时间
//                    if (CivUtils.checkApplyDept(visitDept, applyDept)) {
//                        labs.add(lab);
//                        continue;
//                    }
//                }
//                //下次就诊时间为空，本次为最后一次，则本次之后且科室相同的检验报告都认为是本次的
//                if (StringUtils.isBlank(nextVisitTime)) {
//                    if (Utils.compareStrDateTime(applyTime, visitTime) > 0) {
//                        if (CivUtils.checkApplyDept(visitDept, applyDept)) {
//                            labs.add(lab);
//                            continue;
//                        }
//                    } else { //如果发生在本次之前，跳过
//                        continue;
//                    }
//                    //下次就诊时间不为空，本次和下次之间且科室相同的检验报告均归纳为本次
//                } else {
//                    if (Utils.compareStrDateTime(applyTime, visitTime) > 0
//                            && Utils.compareStrDateTime(applyTime, nextVisitTime) < 0) {
//                        if (CivUtils.checkApplyDept(visitDept, applyDept)) {
//                            labs.add(lab);
//                            continue;
//                        }
//                    }
//                }
//            }
//            //根据检验采样时间和就诊时间判断
//            String sampleTime = lab.get("SAMPLE_TIME");
//            if (StringUtils.isNotBlank(sampleTime)) {
//                if (sampleTime.length() > 19) {
//                    sampleTime = sampleTime.substring(0, 19);
//                }
//                //采样时间=就诊时间 且 申请科室=本次就诊科室  报告属于本次就诊
//                if (Utils.compareStrDateTime(sampleTime, visitTime) == 0) {
//                    if (CivUtils.checkApplyDept(visitDept, applyDept)) {
//                        labs.add(lab);
//                        continue;
//                    }
//                }
//                //下次就诊时间为空，本次为最后一次，则本次之后且科室相同的检验报告都认为是本次的
//                if (StringUtils.isBlank(nextVisitTime)) {
//                    if (Utils.compareStrDateTime(sampleTime, visitTime) > 0) {
//                        if (CivUtils.checkApplyDept(visitDept, applyDept)) {
//                            labs.add(lab);
//                            continue;
//                        }
//                    } else { //如果发生在本次之前，跳过
//                        continue;
//                    }
//                    //下次就诊时间不为空，本次和下次之间且科室相同的检验报告均归纳为本次
//                } else {
//                    if (Utils.compareStrDateTime(sampleTime, visitTime) > 0
//                            && Utils.compareStrDateTime(sampleTime, nextVisitTime) < 0) {
//                        if (CivUtils.checkApplyDept(visitDept, applyDept)) {
//                            labs.add(lab);
//                            continue;
//                        }
//                    }
//                }
//            }
//        }
//        return labs;
//    }
//
//    /**
//     * @param page     分页
//     * @param patId    患者编号
//     * @param visitId  就诊次数
//     * @param pageable 是否分页
//     * @return
//     * @Description 方法描述: 查询某次门诊的检查报告
//     * 根据门诊医技申请，就诊时间，申请时间等条件筛选
//     */
//    public static Page<Map<String, String>> getOutpExamReports(Page<Map<String, String>> page, String oid, String patId,
//                                                               String visitId, boolean pageable) {
//        List<Map<String, String>> exams = new ArrayList<Map<String, String>>();
//        //根据按门诊医技申请查询
////		String rowkeyPrefix = HbaseCURDUtils.getRowkeyPrefix(patId) + "|" + visitId + "|";
//        List<PropertyFilter> filters1 = new ArrayList<PropertyFilter>();
//        filters1.add(new PropertyFilter("APPLY_TYPE_CODE", "STRING", MatchType.IN.getOperation(), "01")); //检查申请
//        List<Map<String, String>> outVisits = hbaseDao.findConditionByPatientVisitId(HdrTableEnum.HDR_OUT_APPLY.getCode(), oid,
//                patId, visitId, filters1, "APPLY_NO");
//        if (outVisits.size() > 0 || !outVisits.isEmpty()) {
//            //拼接 检查申请号
//            StringBuffer sb = new StringBuffer();
//            for (Map<String, String> appNo : outVisits) {
//                sb.append(appNo.get("APPLY_NO") + ",");
//            }
//            String appNo = sb.toString().substring(0, sb.toString().lastIndexOf(","));
//            //根据检查申请的申请号  查询检查报告
//            List<PropertyFilter> filters2 = new ArrayList<PropertyFilter>();
//            filters2.add(new PropertyFilter("VISIT_TYPE_CODE", "STRING", MatchType.EQ.getOperation(), "01"));
//            filters2.add(new PropertyFilter("APPLY_NO", "STRING", MatchType.IN.getOperation(), appNo));
////			rowkeyPrefix = HbaseCURDUtils.getRowkeyPrefix(patId) + "|";
//            exams = hbaseDao.findConditionByPatient(HdrTableEnum.HDR_EXAM_REPORT.getCode(), oid, patId, filters2,
//                    "EXAM_ITEM_NAME", "REPORT_TIME", "VISIT_ID");
//        }
//        //根据门诊医技申请未查到，再根据就诊时间和检查申请时间查询
//        if (exams.size() == 0) {
//            exams = examList(oid, patId, visitId);
//        }
//        //上述未查到，终止执行
//        if (exams.size() == 0) {
//            return page;
//        }
//        //排序
//        Utils.sortListByDate(exams, page.getOrderBy(), page.getOrderDir());
//        //分页判断
//        if (pageable) {
//            ListPage<Map<String, String>> listPage = new ListPage<Map<String, String>>(exams, page.getPageNo(),
//                    page.getPageSize());
//            page.setResult(listPage.getPagedList());
//            page.setTotalCount(listPage.getTotalCount());
//        } else {
//            page.setResult(exams);
//            page.setTotalCount(exams.size());
//        }
//        //只取缺失VISIT_ID的检查报告
//        for (Map<String, String> exam : page) {
//            if (StringUtils.isNotBlank(exam.get("VISIT_ID"))) {
//                page.getResult().remove(exam);
//            }
//        }
//        return page;
//    }
//
//    /**
//     * @param page     分页
//     * @param patId    患者编号
//     * @param visitId  就诊次数
//     * @param pageable 是否分页
//     * @param power    检查报告类型权限
//     * @return
//     * @Description 方法描述: 查询某次住院的检查报告
//     * 根据入院，出院时间查询
//     */
//    public static Page<Map<String, String>> getInpExamReports(Page<Map<String, String>> page, String oid, String patId,
//                                                              String visitId, boolean pageable, Map<String, Object> power) {
//        //检查报告
//        List<Map<String, String>> exams = new ArrayList<Map<String, String>>();
//        //根据 入院 出院时间查询
////		String rowkeyPrefix = HbaseCURDUtils.getRowkeyPrefix(patId) + "|" + visitId + "|";
//        //获取当次住院的 入出院记录
//        List<PropertyFilter> filters1 = new ArrayList<PropertyFilter>();
//        filters1.add(new PropertyFilter("TRANS_NO", "STRING", MatchType.EQ.getOperation(), "0")); //仅获取入出院记录
//        List<Map<String, String>> adts = hbaseDao.findConditionByPatientVisitId(HdrTableEnum.HDR_PAT_ADT.getCode(), oid,
//                patId, visitId, filters1, "VISIT_ID", "ADMISSION_TIME", "DISCHARGE_TIME", "TRANS_NO");
//        if (adts == null || adts.size() == 0) {
//            return page;
//        }
//        Map<String, String> adt = adts.get(0);
//        String startTime = adt.get("ADMISSION_TIME");
//        if (StringUtils.isBlank(startTime)) {
//            return page;
//        }
//        String endTime = adt.get("DISCHARGE_TIME");
//        if (StringUtils.isBlank(endTime)) {
//            endTime = DateUtils.getNowDateTime();
//        }
//        //查询本次 入院和出院时间范围内的检查报告
//        List<PropertyFilter> filters2 = new ArrayList<PropertyFilter>();
//        //检查报告权限筛选
//        if ("false".equals(power.get("isAll"))) {
//            List<String> typeList = (List<String>) power.get("power");
//            String typeStr = StringUtils.join(typeList.toArray(), ",");
//            filters2.add(new PropertyFilter("EXAM_CLASS_CODE", "STRING", MatchType.IN.getOperation(), typeStr));
//        }
//        filters2.add(new PropertyFilter("EXAM_CLASS_CODE", "STRING", MatchType.NOTIN.getOperation(),
//                Config.getCIV_EXAM_VALUE(oid).replace("/", ",")));
//        filters2.add(new PropertyFilter("VISIT_TYPE_CODE", "STRING", MatchType.EQ.getOperation(), "02"));
//        filters2.add(new PropertyFilter("REPORT_TIME", "STRING", MatchType.GE.getOperation(), startTime));
//        filters2.add(new PropertyFilter("REPORT_TIME", "STRING", MatchType.LE.getOperation(), endTime));
//        Config.setExamStatusFilter(oid, filters1);
//        //分页判断
//        if (pageable) {
//            page = hbaseDao.findByConditionPage(HdrTableEnum.HDR_EXAM_REPORT.getCode(), oid, patId, filters2,
//                    page, "EXAM_ITEM_CODE", "EXAM_ITEM_NAME", "REPORT_TIME", "EXAM_DIAG", "REPORT_NO");
//            Utils.sortListByDate(exams, page.getOrderBy(), page.getOrderDir());
//        } else {
//            exams = hbaseDao.findConditionByPatient(HdrTableEnum.HDR_EXAM_REPORT.getCode(), oid, patId, filters2,
//                    "EXAM_ITEM_CODE", "EXAM_ITEM_NAME", "REPORT_TIME", "EXAM_DIAG", "REPORT_NO");
//            Utils.sortListByDate(exams, page.getOrderBy(), page.getOrderDir());
//            page.setResult(exams);
//            page.setTotalCount(exams.size());
//        }
//        return page;
//    }

    /**
     * @param page     分页
     * @param patId    患者编号
     * @param visitId  就诊次数
     * @param pageable 是否分页
     * @param power    检查报告类型权限
     * @return
     * @Description 方法描述: 查询某次住院的检查报告
     * 根据入院，出院时间查询
     */
//    public static Page<Map<String, String>> getInpExamReportsForCheck(Page<Map<String, String>> page, String oid, String patId,
//                                                                      String visitId, boolean pageable, Map<String, Object> power) {
//        //检查报告
//        List<Map<String, String>> exams = new ArrayList<Map<String, String>>();
//        //根据 入院 出院时间查询
////		String rowkeyPrefix = HbaseCURDUtils.getRowkeyPrefix(patId) + "|" + visitId + "|";
//        //获取当次住院的 入出院记录
//        List<PropertyFilter> filters1 = new ArrayList<PropertyFilter>();
//        filters1.add(new PropertyFilter("TRANS_NO", "STRING", MatchType.EQ.getOperation(), "0")); //仅获取入出院记录
//        List<Map<String, String>> adts = hbaseDao.findConditionByPatientVisitId(HdrTableEnum.HDR_PAT_ADT.getCode(), oid,
//                patId, visitId, filters1, "VISIT_ID", "ADMISSION_TIME", "DISCHARGE_TIME", "TRANS_NO");
//        if (adts == null || adts.size() == 0) {
//            return page;
//        }
//        Map<String, String> adt = adts.get(0);
//        String startTime = adt.get("ADMISSION_TIME");
//        if (StringUtils.isBlank(startTime)) {
//            return page;
//        }
//        String endTime = adt.get("DISCHARGE_TIME");
//        if (StringUtils.isBlank(endTime)) {
//            endTime = DateUtils.getNowDateTime();
//        }
//        //查询本次 入院和出院时间范围内的检查报告
//        List<PropertyFilter> filters2 = new ArrayList<PropertyFilter>();
//        //检查报告权限筛选
//        if ("false".equals(power.get("isAll"))) {
//            List<String> typeList = (List<String>) power.get("power");
//            String typeStr = StringUtils.join(typeList.toArray(), ",");
//            filters2.add(new PropertyFilter("EXAM_CLASS_CODE", "STRING", MatchType.IN.getOperation(), typeStr));
//        }
//        filters2.add(new PropertyFilter("EXAM_CLASS_CODE", "STRING", MatchType.NOTIN.getOperation(),
//                Config.getCIV_PATHOLOGY_VALUE(oid).replace("/", ",")));
//        filters2.add(new PropertyFilter("VISIT_TYPE_CODE", "STRING", MatchType.EQ.getOperation(), "02"));
//        filters2.add(new PropertyFilter("REPORT_TIME", "STRING", MatchType.GE.getOperation(), startTime));
//        filters2.add(new PropertyFilter("REPORT_TIME", "STRING", MatchType.LE.getOperation(), endTime));
//        Config.setExamStatusFilter(oid, filters1);
//        //分页判断
//        if (pageable) {
//            page = hbaseDao.findByConditionPage(HdrTableEnum.HDR_EXAM_REPORT.getCode(), oid, patId, filters2,
//                    page, "EXAM_ITEM_CODE", "EXAM_ITEM_NAME", "REPORT_TIME", "EXAM_DIAG", "REPORT_NO");
//            Utils.sortListByDate(exams, page.getOrderBy(), page.getOrderDir());
//        } else {
//            exams = hbaseDao.findConditionByPatient(HdrTableEnum.HDR_EXAM_REPORT.getCode(), oid, patId, filters2,
//                    "EXAM_ITEM_CODE", "EXAM_ITEM_NAME", "REPORT_TIME", "EXAM_DIAG", "REPORT_NO");
//            Utils.sortListByDate(exams, page.getOrderBy(), page.getOrderDir());
//            page.setResult(exams);
//            page.setTotalCount(exams.size());
//        }
//        return page;
//    }

    /**
     * @param patientId 患者编号
     * @param visitId   就诊次数
     * @return
     * @Description 方法描述: 根据就诊时间，申请时间筛选本次门诊的检查报告
     */
//    public static List<Map<String, String>> examList(String oid, String patientId, String visitId) {
//        //获取患者门诊列表
////		String rowkeyPrefix = HbaseCURDUtils.getRowkeyPrefix(patientId) + "|";
//        List<Map<String, String>> outVisits = hbaseDao.findAllByPatient(HdrTableEnum.HDR_OUT_VISIT.getCode(), oid,
//                patientId, "VISIT_ID", "VISIT_TIME", "REGISTING_TIME", "VISIT_DEPT_CODE");
//        String visitTime = null; //本次就诊时间
//        String nextVisitTime = null; //下次就诊时间
//        String visitDept = null; //本次就诊科室
//        if (outVisits.size() != 0) {
//            //没有就诊时间的，用挂号时间代替
//            for (Map<String, String> out : outVisits) {
//                if (StringUtils.isBlank(out.get("VISIT_TIME"))) {
//                    String regTime = out.get("REGISTING_TIME");
//                    out.put("VISIT_TIME", regTime);
//                }
//            }
//            //门诊列表 时间降序
//            Utils.sortListByDate(outVisits, "VISIT_TIME", "desc");
//            //门诊在列表中的索引
//            int index = 0;
//            for (Map<String, String> visit : outVisits) {
//                if (visitId.equals(visit.get("VISIT_ID"))) {
//                    //获取当次要查询的门诊  就诊时间  就诊科室
//                    visitTime = visit.get("VISIT_TIME");
//                    visitDept = visit.get("VISIT_DEPT_CODE");
//                    //获取下次门诊就诊时间
//                    if (index != 0) {
//                        nextVisitTime = outVisits.get(index - 1).get("VISIT_TIME");
//                    }
//                    break;
//                }
//                index++;
//            }
//        }
//        //获取患者所有的门诊检查报告
//        List<PropertyFilter> filters = new ArrayList<PropertyFilter>();
//        filters.add(new PropertyFilter("VISIT_TYPE_CODE", "STRING", MatchType.EQ.getOperation(), "01"));
//        List<Map<String, String>> allExams = hbaseDao.findConditionByPatient(HdrTableEnum.HDR_EXAM_REPORT.getCode(), oid,
//                patientId, filters, "EXAM_ITEM_NAME", "REPORT_TIME", "VISIT_ID", "APPLY_TIME",
//                "APPLY_DEPT_CODE");
//        //根据就诊时间 和 申请时间筛选本次就诊的检查报告
//        List<Map<String, String>> exams = new ArrayList<Map<String, String>>();
//        for (Map<String, String> exam : allExams) {
//            //用申请时间判断，申请时间符合的，再检查一下申请科室
//            String applyTime = exam.get("APPLY_TIME"); //申请时间
//            String applyDept = exam.get("APPLY_DEPT_CODE"); //申请科室
//            if (StringUtils.isNotBlank(applyTime)) {
//                if (Utils.compareStrDateTime(applyTime, visitTime) == 0) { //申请时间=本次就诊时间
//                    //申请科室=本次就诊科室
//                    if (CivUtils.checkApplyDept(visitDept, applyDept)) {
//                        exams.add(exam);
//                        continue;
//                    }
//                }
//                if (StringUtils.isBlank(nextVisitTime)) { //如果下一次就诊时间为空，本次是最后一次就诊，发生在本次时间之后的检查都认为是本次的
//                    if (Utils.compareStrDateTime(applyTime, visitTime) > 0) {
//                        //如果申请科室不为空，申请科室必须一致
//                        if (CivUtils.checkApplyDept(visitDept, applyDept)) {
//                            exams.add(exam);
//                            continue;
//                        }
//                    } else { //如果发生在本次之前，不符合条件，跳过
//                        continue;
//                    }
//                } else {
//                    if (Utils.compareStrDateTime(applyTime, visitTime) > 0
//                            && Utils.compareStrDateTime(applyTime, nextVisitTime) < 0) {//如果发生时间在本次就诊和下次就诊中间，则按本次挂载
//                        //如果申请科室不为空，申请科室必须一致
//                        if (CivUtils.checkApplyDept(visitDept, applyDept)) {
//                            exams.add(exam);
//                            continue;
//                        }
//                    }
//                }
//            }
//        }
//        return exams;
//    }

    /**
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return 间隔毫秒数
     * @Description 方法描述: 计算两个时间之间的间隔时间，取毫秒
     */
    public static long calIntervalTime(String startTime, String endTime) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

        LocalDateTime startDate = LocalDateTime.parse(startTime, formatter);

        LocalDateTime endDate = LocalDateTime.parse(endTime, formatter);
        Duration between = Duration.between(startDate, endDate);


        return between.toMillis();
    }

    /**
     * @param range 参考值
     * @return
     * @Description 方法描述: 解析检验细项参考值，获取最大值和最小值
     */
    public static Map<String, Object> parseRange(String range) {
        Map<String, Object> result = new HashMap<String, Object>();
        //参考值为空，中断执行
        if (StringUtils.isBlank(range)) {
            return result;
        }
        String[] ranges = null;
        if (range.contains("-") && !range.contains("--")) {
            ranges = range.split("-");
            if (ranges.length == 2) {
                if (Utils.isNumber(ranges[0])) {
                    result.put("min", ranges[0]);
                }
                if (Utils.isNumber(ranges[1])) {
                    result.put("max", ranges[1]);
                }
                result.put("link", "-");
            }
        } else if (range.contains("--")) {
            ranges = range.split("--");
            if (ranges.length == 2) {
                if (Utils.isNumber(ranges[0])) {
                    result.put("min", ranges[0]);
                }
                if (Utils.isNumber(ranges[1])) {
                    result.put("max", ranges[1]);
                }
                result.put("link", "--");
            }
        } else if (range.contains("<")) {
            ranges = range.split("<");
            if (ranges.length == 2) {
                if (Utils.isNumber(ranges[1])) {
                    result.put("max", ranges[1]);
                }
                result.put("link", "<");
            }
        } else if (range.contains("≤")) {
            ranges = range.split("≤");
            if (ranges.length == 2) {
                if (Utils.isNumber(ranges[1])) {
                    result.put("max", ranges[1]);
                }
                result.put("link", "≤");
            }
        } else if (range.contains(">")) {
            ranges = range.split(">");
            if (ranges.length == 2) {
                if (Utils.isNumber(ranges[1])) {
                    result.put("min", ranges[1]);
                }
                result.put("link", ">");
            }
        } else if (range.contains("≥")) {
            ranges = range.split("≥");
            if (ranges.length == 2) {
                if (Utils.isNumber(ranges[1])) {
                    result.put("min", ranges[1]);
                }
                result.put("link", "≥");
            }
        } else if (range.contains("～")) {
            ranges = range.split("～");
            if (ranges.length == 2) {
                if (Utils.isNumber(ranges[0])) {
                    result.put("min", ranges[0]);
                }
                if (Utils.isNumber(ranges[1])) {
                    result.put("max", ranges[1]);
                }
                result.put("link", "～");
            }
        }
        return result;
    }

    /**
     * @param key 类别key
     * @return
     * @Description 方法描述: 获取配置文件中的医嘱类别
     */
    public static List<String> getOrderClass(String oid, String key) {
        List<String> orderClass = new ArrayList<String>();
        if (StringUtils.isNotBlank(key)) {
            String value = ConfigCache.getCache(oid, key);//PropertiesUtils.getPropertyValue(CDSS_CONFIG_FILE, key);
            if (StringUtils.isNotBlank(value)) {
                String[] items = value.split(",");
                for (String item : items) {
                    orderClass.add(item);
                }
            }
        }
        return orderClass;
    }



    /**
     * @param result 存储分组结果  key:xxxx年x月  value:记录列表
     * @param list   要分组的记录列表
     * @param column 时间字段
     * @return 返回类型： void
     * @Description 方法描述: 查询到的记录列表按某个时间字段进行月份分组，同月下的归为一组，最终降序排列
     */
    public static void groupByDate(Map<String, Object> result, List<Map<String, String>> list, String column) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        for (Map<String, String> map : list) {
            //获取时间字段值

            String time = map.get(column);
            LocalDateTime date = LocalDateTime.parse(time, formatter);

            //将时间拼接为 x年x月
            int year = date.getYear();
            Month month = date.getMonth();
            String ny = year + "年" + month.getValue() + "月";
            if (!result.containsKey(ny)) {
                //获取当前月份下的记录
                List<Map<String, String>> nowList = new ArrayList<Map<String, String>>();
                for (Map<String, String> nowMap : list) {
                    String nowTime = nowMap.get(column);
                    LocalDateTime nowDate = LocalDateTime.parse(nowTime, formatter);

                    //转换格式 2017-02
                    int nowY = nowDate.getYear();
                    Month nowM = nowDate.getMonth();
                    time = year + "-" + month.getValue();
                    nowTime = nowY + "-" + nowM.getValue();
                    //比较是否属于同一月份
                    if (StringUtils.equals(nowTime, time)) {
                        nowList.add(nowMap);
                    }
                }
                //时间字段  降序排列
                Utils.sortListByDate(nowList, column, Page.Sort.DESC);
                result.put(ny, nowList);
            }
        }
    }

    /**
     * @param result 存储分组结果  key:xxxx年x月  value:记录列表
     * @param list   要分组的记录列表
     * @param column 时间字段
     * @return 返回类型： void
     * @Description 方法描述: 查询到的记录列表按某个时间字段进行月份分组，同年下的归为一组，最终降序排列
     */
    public static void groupByYearDate(Map<String, Object> result, List<Map<String, Object>> list, String column) {

        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

        for (Map<String, Object> map : list) {
            //获取时间字段值
            String time = map.get(column).toString();

            LocalDateTime date = LocalDateTime.parse(time, formatter);

            //将时间拼接为 x年
            int year = date.getYear();

            if (!result.containsKey(year)) {
                //获取当前月份下的记录
                List<Map<String, Object>> nowList = new ArrayList<Map<String, Object>>();
                for (Map<String, Object> nowMap : list) {
                    String nowTime = nowMap.get(column).toString();

                    LocalDateTime nowDate = LocalDateTime.parse(nowTime, formatter);

                    //转换格式 2017-02
                    int nowY = nowDate.getYear();

                    //比较是否属于同一年
                    if (year==nowY) {
                        nowList.add(nowMap);
                    }
                }
                //时间字段  降序排列
                Utils.sortListByDate(nowList, column, Page.Sort.DESC);
                result.put(Integer.toString(year), nowList);
            }
        }
    }

    /**
     * @param result 存储分组结果  key:yyyy-mm-yy  value:记录列表
     * @param list   要分组的记录列表
     * @param column 时间字段
     * @return 返回类型： void
     * @Description 方法描述: 查询到的记录列表按某个时间字段进行日期分组，同日期一组，最终降序排列
     */
    public static void groupByDayDate(Map<String, List<Map<String, String>>> result, List<Map<String, String>> list, String column) {

        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        for (Map<String, String> map : list) {
            //获取时间字段值
            String time = map.get(column);
            LocalDateTime date = LocalDateTime.parse(time, formatter);

            //将时间拼接为 x年

            String dayString = date.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
            if (result.containsKey(dayString)) {
                result.get(dayString).add(map);
            } else {
                List<Map<String, String>> maps = new ArrayList<Map<String, String>>();
                maps.add(map);
                result.put(dayString, maps);
            }
        }
    }

    /**
     * @return 201701
     * @Description 方法描述: 将日期转换用于排序
     */
    public static String changeFormatDate(String targetDate) {
        String date = targetDate.replace("年", "").replace("月", "");
        if (date.length() == 5) {
            StringBuffer time = new StringBuffer(date);
            time.insert(4, "0");
            date = time.toString();
        }
        return date;
    }


    /**
     * @param filterString 条件字符串 column|in|080,079,004,035,088,134
     * @param filters      条件集合
     * @param strSplit     分隔每个查询条件的分隔符
     * @return 返回类型： void
     * @Description 方法描述: 条件字符串转化为过滤对象，并添加到条件集合
     */
    public static void strToFilter(String filterString, List<PropertyFilter> filters, String strSplit) {
        //将配置字符串转换为查询
        if (StringUtils.isNotBlank(filterString)) {
            String[] filterStrings = filterString.split(strSplit);
            for (String filterItemString : filterStrings) {
                String[] tempString = filterItemString.split("\\|");
                createPropertyFilter(tempString[0], tempString[2], tempString[1], filters);
            }
        }
    }

    /**
     * @param columnName 字段
     * @param keyword    值
     * @param MatchType  条件类型
     * @param filters    条件集合
     * @return 返回类型： void
     * @Description 方法描述: 创建过滤对象，并添加到条件集合
     */
    public static void createPropertyFilter(String columnName, String keyword, String MatchType,
                                            List<PropertyFilter> filters) {
        if (StringUtils.isNotBlank(keyword)) {
            PropertyFilter filter1 = new PropertyFilter();
            filter1.setMatchType(MatchType);
            filter1.setPropertyName(columnName);
            filter1.setPropertyValue(keyword);
            //filter1.setPropertyType("STRING");
            filters.add(filter1);
        }
    }

    /**
     * 统一视图兴化市人民医院解密代码
     *
     * @param sSrc
     * @param iv
     * @return
     * @throws Exception
     */
    public static String decrypt(String sSrc, String iv, String sKey) {
        try {
            //解密密钥，16 位
//			String sKey = "GOODWILLCIS-JHIP";
            byte[] raw = sKey.getBytes("ASCII");
            SecretKeySpec skeySpec = new SecretKeySpec(raw, "AES");
            Cipher cipher = Cipher.getInstance("AES/CBC/PKCS5Padding");
            IvParameterSpec i = new IvParameterSpec(iv.getBytes());
            cipher.init(Cipher.DECRYPT_MODE, skeySpec, i);
            byte[] encrypted1 = new BASE64Decoder().decodeBuffer(sSrc);//先用 base64 解密
            try {
                byte[] original = cipher.doFinal(encrypted1);
                String originalString = new String(original);
                return originalString;
            } catch (Exception e) {
                System.out.println(e.toString());
                return null;
            }
        } catch (Exception ex) {
            System.out.println(ex.toString());
            return null;
        }
    }

    /**
     * 解析PDF数据为PDF路径
     *
     * @param imgStr
     * @param patienId
     * @param visitId
     * @param fileNo
     * @param imgFilePath
     * @return
     */
    public static String Base64ToImage(String imgStr, String oid, String patienId, String visitId, String fileNo, String imgFilePath) {
        // 对字节数组字符串进行Base64解码并生成图片
        if (StringUtils.isBlank(imgStr)) // 图像数据为空
            return imgFilePath;
        BASE64Decoder decoder = new BASE64Decoder();
        try {
            // Base64解码
            byte[] b = decoder.decodeBuffer(imgStr);
            for (int i = 0; i < b.length; ++i) {
                if (b[i] < 0) {// 调整异常数据
                    b[i] += 256;
                }
            }
            String fileName = imgFilePath + oid + "_" + patienId + "_" + visitId + "_" + fileNo + ".pdf";
            String returnName = oid + "_" + patienId + "_" + visitId + "_" + fileNo + ".pdf";
            OutputStream out = new FileOutputStream(fileName);
            out.write(b);
            out.flush();
            out.close();
            return returnName;
        } catch (Exception e) {
            e.printStackTrace();
            return "";
        }

    }

    /**
     * 将base64数据解析成PDF数据，并返回路径
     *
     * @return
     */
    public static String getPdfData(String imgStr, String oid, String patienId, String visitId, String fileNo) {
        //这里配置的路径 须为前端tomcat下的路径
        String path = ConfigCache.getCache(oid, "NURSE_PDF_PATH");
        File file = new File(path + File.separator + "pdf_datas");
        logger.info("文件路径："+file.getPath());
        file.mkdirs();
        path = file.getPath() + File.separator;
        String relativePath = File.separator + "pdf_datas";
        return relativePath + File.separator + Base64ToImage(imgStr, oid, patienId, visitId, fileNo, path);
    }

    public static String Encrypt(String sSrc, String sKey) throws Exception {
        if (sKey == null) {
            System.out.print("Key为空null");
            return null;
        }
        // 判断Key是否为16位
        if (sKey.length() != 16) {
            System.out.print("Key长度不是16位");
            return null;
        }
        byte[] raw = sKey.getBytes("utf-8");
        SecretKeySpec skeySpec = new SecretKeySpec(raw, "AES");
        Cipher cipher = Cipher.getInstance("AES/ECB/PKCS5Padding");//"算法/模式/补码方式"
        cipher.init(Cipher.ENCRYPT_MODE, skeySpec);
        byte[] encrypted = cipher.doFinal(sSrc.getBytes("utf-8"));
        return encrypted.toString();
    }


}
