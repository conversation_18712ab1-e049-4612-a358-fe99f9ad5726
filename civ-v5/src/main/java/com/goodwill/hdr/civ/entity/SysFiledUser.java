package com.goodwill.hdr.civ.entity;

import com.baomidou.mybatisplus.annotation.TableName;

import java.io.Serializable;

/**
 * (SysFiledUser)实体类
 *
 * <AUTHOR>
 * @since 2024-09-27 11:18:15
 */
@TableName("civ_sys_filed_user")
public class SysFiledUser implements Serializable {
    private static final long serialVersionUID = 692592612790597669L;
    /**
     * 主键
     */
    private String id;
    /**
     * 脱敏字段编码
     */
    private String filedCode;
    /**
     * 用户编码
     */
    private String userCode;
    private String oid;


    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getFiledCode() {
        return filedCode;
    }

    public void setFiledCode(String filedCode) {
        this.filedCode = filedCode;
    }

    public String getUserCode() {
        return userCode;
    }

    public void setUserCode(String userCode) {
        this.userCode = userCode;
    }

    public String getOid() {
        return oid;
    }

    public void setOid(String oid) {
        this.oid = oid;
    }
}

