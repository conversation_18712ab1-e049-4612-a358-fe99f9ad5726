package com.goodwill.hdr.civ.config;

import com.goodwill.hdr.civ.utils.Utils;
import com.goodwill.hdr.hbase.bo.PropertyFilter;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 配置读取类
 *
 * <AUTHOR>
 */
public class Config {
    //全局配置


    //    //时间轴视图转换周类型
    private static final List<String> WEEK_TYPE = new ArrayList<String>();

    public static String getORDER_LONG_PROPERTY_CONFIG(String oid) {
        return StringUtils.isBlank(ConfigCache.getCache(oid, "ORDER_LONG_PROPERTY_CONFIG")) ? "长期"
                : ConfigCache.getCache(oid, "ORDER_LONG_PROPERTY_CONFIG");
    }

    public static String getORDER_SHORT_PROPERTY_CONFIG(String oid) {
        return StringUtils.isBlank(ConfigCache.getCache(oid, "ORDER_SHORT_PROPERTY_CONFIG")) ? "临时"
                : ConfigCache.getCache(oid, "ORDER_SHORT_PROPERTY_CONFIG");
    }

    public static String getSHOW_OPER_LIST(String oid) {
        String SHOW_OPER_LIST = ConfigCache.getCache(oid, "SHOW_OPER_LIST");
        return StringUtils.isBlank(SHOW_OPER_LIST) ? "1" : SHOW_OPER_LIST;
    }

    public static String getVisitDeptSelectConfig(String oid) {
        String VISIT_DEPT_SELECT_CONFIG = ConfigCache.getCache(oid, "VISIT_DEPT_SELECT_CONFIG");
        return StringUtils.isBlank(VISIT_DEPT_SELECT_CONFIG) ? "on" : VISIT_DEPT_SELECT_CONFIG;
    }

    /**
     * 检查关联闭环字段配置
     * @param oid
     * @return
     */
    public static String getOrderCloseJoinField(String oid) {
        String ORDER_CLOSE_JOIN_FIELD = ConfigCache.getCache(oid, "ORDER_CLOSE_JOIN_FIELD");
        return StringUtils.isBlank(ORDER_CLOSE_JOIN_FIELD) ? "ORDER_NO|orderNo" : ORDER_CLOSE_JOIN_FIELD;
    }

    /**
     * 检验关联门诊闭环字段配置
     * @param oid
     * @return
     */
    public static String getOrderCloseLabJoinField(String oid) {
        String ORDER_CLOSE_LAB_JOIN_FIELD = ConfigCache.getCache(oid, "ORDER_CLOSE_LAB_JOIN_FIELD");
        return StringUtils.isBlank(ORDER_CLOSE_LAB_JOIN_FIELD) ? "ORDER_NO|orderNo" : ORDER_CLOSE_LAB_JOIN_FIELD;
    }

    /**
     * 检验关联住院闭环字段配置
     * @param oid
     * @return
     */
    public static String getOrderCloseZYLabJoinField(String oid) {
        String ORDER_CLOSE_ZY_LAB_JOIN_FIELD = ConfigCache.getCache(oid, "ORDER_CLOSE_ZY_LAB_JOIN_FIELD");
        return StringUtils.isBlank(ORDER_CLOSE_ZY_LAB_JOIN_FIELD) ? "ORDER_NO|orderNo" : ORDER_CLOSE_ZY_LAB_JOIN_FIELD;
    }

    /**
     * 通过配置字段名称添加过滤条件 配置案例：  pay_code|in|1,2,3;c_code|in|23
     *
     * @param
     * @param filters
     */
    public static void setEmrViewVonfig(String oid, List<PropertyFilter> filters) {
        String EMR_VIEW_CONFIG = ConfigCache.getCache(oid, "EMR_VIEW_CONFIG");
        if (StringUtils.isNotBlank(EMR_VIEW_CONFIG)) {
            //拆分多个字段
            String[] configes = EMR_VIEW_CONFIG.split(";");
            //遍历每一个字段的 信息， 字段值，查询关系， 值
            for (String config : configes) {
                if (StringUtils.isNotBlank(config)) {
                    String[] confes = config.split("\\|");
                    if (null != confes && confes.length == 3) {
                        filters.add(new PropertyFilter(confes[0], confes[1], confes[2]));
                    }
                }
            }
        }
    }

    public static String getEmrTypeFieldName(String oid) {
        String emrTypeFieldName = ConfigCache.getCache(oid, "EMR_TYPE_FIELD_NAME");
        return Utils.objToStr(emrTypeFieldName, "MR_CLASS_CODE");
    }

    public static String getHTMLCommonModel(String oid) {
        String emrTypeFieldName = ConfigCache.getCache(oid, "MR_HTML_COMMON_MODEL");
        return emrTypeFieldName;
    }

    /**
     * 患者列表页提示
     *
     * @return
     */
    public static String getCIV_USER_REMIND(String oid) {
        String value = ConfigCache.getCache(oid,"CIV_USER_REMIND");
        return StringUtils.isNotBlank(value) ? value : "您无权查看此病历！";
    }

    /**
     * 获取费用字段配置
     */
    //    public static final String VISIT_SUMMARY_FEE_COLUMN = PropertiesUtils.getPropertyValue(CIV_FILE_NAME,"VISIT_SUMMARY_FEE_COLUMN");

    /**
     * solr 时间差配置
     */
    //    public static String SOLR_DATE_OPER = PropertiesUtils.getPropertyValue(CIV_FILE_NAME,
    //            "SOLR_DATE_OPER");

    //    public static String SOLR_DATE_OPER_VALUE = PropertiesUtils.getPropertyValue(CIV_FILE_NAME,
    //            "SOLR_DATE_OPER_VALUE");

    /**
     * 获取就诊费用显示字段配置
     *
     * @return
     */
    public static String getVISIT_SUMMARY_FEE_COLUMN(String oid) {
        String VISIT_SUMMARY_FEE_COLUMN = ConfigCache.getCache(oid, "VISIT_SUMMARY_FEE_COLUMN");
        return StringUtils.isBlank(VISIT_SUMMARY_FEE_COLUMN) ? "" : VISIT_SUMMARY_FEE_COLUMN;
    }

    public static String getSOLR_DATE_OPER(String oid) {
        String SOLR_DATE_OPER = ConfigCache.getCache(oid, "SOLR_DATE_OPER");
        return StringUtils.isBlank(SOLR_DATE_OPER) ? "add" : SOLR_DATE_OPER;

    }

    /**
     * 是否展示水印 1展示 0不展示
     * @return
     */
    public static String getIS_SHOW_WATER_MARK(String oid) {
        String isShowWaterMark = ConfigCache.getCache(oid,"IS_SHOW_WATER_MARK");
        return StringUtils.isBlank(isShowWaterMark) ? "0" : isShowWaterMark;
    }

    public static String getSOLR_DATE_OPER_VALUE(String oid) {
        String SOLR_DATE_OPER_VALUE = ConfigCache.getCache(oid, "SOLR_DATE_OPER_VALUE");
        return StringUtils.isBlank(SOLR_DATE_OPER_VALUE) ? "0H" : SOLR_DATE_OPER_VALUE;

    }

    public static String getBirthday_switchConfig(String oid) {
        String flag = ConfigCache.getCache(oid, "BIRTHDAY_DATE_SWITCH");
        return StringUtils.isBlank(flag) ? "false" : flag;

    }

    /**
     * 患者列表查询条件显示配置
     *
     * @return
     */
    public static String getPATIENT_LIST_QUERY_VIEW(String oid) {
        String PATIENT_LIST_QUERY_VIEW = ConfigCache.getCache(oid, "PATIENT_LIST_QUERY_VIEW");
        return StringUtils.isBlank(PATIENT_LIST_QUERY_VIEW) ? "" : PATIENT_LIST_QUERY_VIEW;
    }

    /**
     * 查看病历权限提醒是否开启：true开启 false不开启
     *
     * @return
     */
    public static String getAuthorityReminder(String oid) {
        String authorityReminder = ConfigCache.getCache(oid, "AUTHORITY_REMINDER");
        return StringUtils.isBlank(authorityReminder) ? "false" : authorityReminder;
    }

    /**
     * 患者末次就诊信息显示
     *
     * @return
     */
    public static String getPATIENT_LAST_VISIT_INFO_VIEW(String oid) {
        String PATIENT_LAST_VISIT_INFO_VIEW = ConfigCache.getCache(oid, "PATIENT_LAST_VISIT_INFO_VIEW");
        return StringUtils.isBlank(PATIENT_LAST_VISIT_INFO_VIEW) ? "" : PATIENT_LAST_VISIT_INFO_VIEW;
    }

    /**
     * 就诊试图就诊次显示配置
     *
     * @return
     */
    public static String getVISIT_SHOW_VID_CONFIG(String oid) {
        String VISIT_SHOW_VID_CONFIG = ConfigCache.getCache(oid, "VISIT_SHOW_VID_CONFIG");
        return StringUtils.isBlank(VISIT_SHOW_VID_CONFIG) ? "all" : VISIT_SHOW_VID_CONFIG;
    }

    /**
     * 当前视图模块配置
     *
     * @return
     */
    public static String getCIV_CURRENT_CONFIG(String oid) {
        String CIV_CURRENT_CONFIG = ConfigCache.getCache(oid, "CIV_CURRENT_CONFIG");
        return StringUtils.isBlank(CIV_CURRENT_CONFIG) ? "{\"configure_index\": [{\"module_18\": {\"id\": \"module_18\",\"enable\": false,\"name\": \"危急值\",\"code\": \"crisisValue\",\"height\": \"300px\",\"width\": \"21\",\"float\": \"fl\",\"box\": \"box_style_160\"},\"module_19\": {\"id\": \"module_19\",\"enable\": false,\"name\": \"院感\",\"code\": \"hospitalInfection\",\"height\": \"300px\",\"width\": \"21\",\"float\": \"fl\",\"box\": \"box_style_160\"}},{\"module_3\": {\"id\": \"module_3\",\"enable\": true,\"name\": \"诊断信息\",\"code\": \"diagnosticInformation\",\"height\": \"170px\",\"width\": \"41\",\"float\": \"fl mb-10\",\"box\": \"box_style_three\"},\"module_4\": {\"id\": \"module_4\",\"enable\": false,\"name\": \"相似病历推荐\",\"code\": \"similarMedicalRecords\",\"height\": \"143px\",\"width\": \"41\",\"float\": \"fl\",\"box\": \"box_style_two\"},\"module_5\": {\"id\": \"module_5\",\"enable\": true,\"name\": \"主诉\",\"code\": \"chiefCcomplainte\",\"height\": \"170px\",\"width\": \"41\",\"float\": \"fl\",\"box\": \"box_style_three\"},\"module_8\": {\"id\": \"module_8\",\"enable\": true,\"name\": \"生命体征\",\"code\": \"vitalSigns\",\"height\": \"343px\",\"width\": \"41\",\"float\": \"fr\",\"box\": \"box_style_seven\"},\"module_7\": {\"id\": \"module_7\",\"enable\": true,\"name\": \"检验结果\",\"code\": \"abnormalInspectionResults\", \"height\": \"343px\", \"width\": \"41\", \"float\": \"fr\",\t \"box\": \"box_style_four\" }, \"module_6\": { \"id\": \"module_6\", \"enable\": true, \"name\": \"现病史\", \"code\": \"presentHistory\", \"height\": \"160px\", \"width\": \"21\", \"float\": \"fl\", \"box\": \"box_style_three\" }\t }, {\"module_12\": {\t\"id\": \"module_12\",\t\"enable\": true,\t\"name\": \"病历文书\",\t\"code\": \"medicalRecord\",\t\"height\": \"350px\",\t\"width\": \"21\",\t\"float\": \"fl\",\t\"box\": \"box_style_250\"},\"module_13\": {\t\"id\": \"module_13\",\t\"enable\": true,\t\"name\": \"用药医嘱\",\t\"code\": \"medication\",\t\"height\": \"350px\",\t\"width\": \"21\",\t\"float\": \"fl\",\t\"box\": \"box_style_250\"}}, {\"module_11\": {\t\"id\": \"module_11\",\t\"enable\": true,\t\"name\": \"手术进度\",\t\"code\": \"operationSchedule\",\t\"height\": \"240px\",\t\"width\": \"21\",\t\"float\": \"fl\"},\"module_9\": {\t\"id\": \"module_9\",\t\"enable\": true,\t\"name\": \"检查报告\",\t\"code\": \"checkReport\",\t\"height\": \"240px\",\t\"width\": \"21\",\t\"float\": \"fl\"},\"module_10\": {\t\"id\": \"module_10\",\t\"enable\": false,\t\"name\": \"过敏信息\",\t\"code\": \"allergyInformation\",\t\"height\": \"303px\",\t\"width\": \"21\",\t\"box\": \"box_style_six\"}}, {\"module_14\": {\t\"id\": \"module_14\",\t\"enable\": false,\t\"name\": \"风险评估\",\t\"code\": \"riskAssessment\",\t\"height\": \"300px\",\t\"width\": \"21\",\t\"float\": \"fl\",\t\"box\": \"box_style_160\"},\"module_17\": {\t\"id\": \"module_17\",\t\"enable\": false,\t\"name\": \"过敏记录\",\t\"code\": \"allergy\",\t\"height\": \"300px\",\t\"width\": \"21\",\t\"float\": \"fl\",\t\"box\": \"box_style_160\"}\t\t\t}, {\"module_15\": {\t\"id\": \"module_15\",\t\"enable\": false,\t\"name\": \"诊疗计划\",\t\"code\": \"assessmentPlan\",\t\"height\": \"160px\",\t\"width\": \"21\",\t\"float\": \"fl\",\t\"box\": \"box_style_three\"},\"module_16\": {\t\"id\": \"module_16\",\t\"enable\": false,\t\"name\": \"护理任务\",\t\"code\": \"nursingTask\",\t\"height\": \"160px\",\t\"width\": \"21\",\t\"float\": \"fl\"}}]}"
                : CIV_CURRENT_CONFIG;
    }

    public static String getHealthConfigs(String oid) {
        String HEALTH_CONFIGS = ConfigCache.getCache(oid, "HEALTH_CONFIGS");
        return StringUtils.isNotBlank(HEALTH_CONFIGS) ? HEALTH_CONFIGS : "呼吸|呼吸;脉搏|脉搏;体温|体温;血压|血压";
    }

    public static String getCIV_EMR_SPLIT_FIELD(String oid) {
        String CIV_EMR_SPLIT_FIELD = ConfigCache.getCache(oid, "CIV_EMR_SPLIT_FIELD");
        return StringUtils.isBlank(CIV_EMR_SPLIT_FIELD)
                ? "主诉,现病史,创伤因素,创伤史,个人史,既往史,家族史,婚育史,月经婚育史,预防接种史,体格检查,专科查体,专科检查,辅助检查,初步诊断,基本信息" : CIV_EMR_SPLIT_FIELD;
    }

    public static String getCIV_EMR_REPLACE_FIELD(String oid) {
        String CIV_EMR_REPLACE_FIELD = ConfigCache.getCache(oid, "CIV_EMR_REPLACE_FIELD");
        return StringUtils.isBlank(CIV_EMR_REPLACE_FIELD) ? "体 格 检 查,体格检查;专 科 查 体,专科查体;专  科  检  查,专科检查;辅  助  检  查,辅助检查"
                : CIV_EMR_REPLACE_FIELD;
    }

    public static String getCIV_CV_MAIN(String oid) {
        String CIV_CV_MAIN = ConfigCache.getCache(oid, "CIV_CV_MAIN");
        return StringUtils.isBlank(CIV_CV_MAIN) ? "主诉" : CIV_CV_MAIN;
    }

    public static String getCIV_CV_NOW_HIS(String oid) {
        String CIV_CV_NOW_HIS = ConfigCache.getCache(oid, "CIV_CV_NOW_HIS");
        return StringUtils.isBlank(CIV_CV_NOW_HIS) ? "现病史" : CIV_CV_NOW_HIS;
    }

    /**
     * 获取科室列表字典前缀，默认返回北医三院科室字典前缀
     *
     * @return
     */
    public static String getCIV_DICT_DEPT_ROWKEY(String oid) {
        String CIV_DICT_DEPT_ROWKEY = ConfigCache.getCache(oid, "CIV_DICT_DEPT_ROWKEY");
        return StringUtils.isBlank(CIV_DICT_DEPT_ROWKEY) ? "1.2.156.112636.1.1.2.1.4.1|" : CIV_DICT_DEPT_ROWKEY;
    }

    /**
     * 患者列表列明
     *
     * @return
     */
    public static String getCIV_PATIENT_COLUMN(String oid) {
        String CIV_PATIENT_COLUMN = ConfigCache.getCache(oid, "CIV_PATIENT_COLUMN");
        return StringUtils.isBlank(CIV_PATIENT_COLUMN) ? "[{\"display\" : \"编码\",\"key\" : true,\"name\" : \"code\",\"hidden\":true},{\"display\":\"医院院区\",\"name\":\"ORG_NAME\",\"width\":\"100\",\"align\":\"center\"},{\"display\" : \"患者ID\",\"name\" : \"PATIENT_ID\",\"parseData\":\"jParseData.parsePatientIDCard\",\"width\":\"100\"},{\"display\" : \"姓名\",\"name\" : \"PERSON_NAME\",\"parseData\":\"jParseData.personName\",\"width\":\"80\",\"center\":true},{\"display\" : \"性别\",\"name\" : \"SEX_NAME\",\"width\":\"40\",\"center\":true},{\"display\" : \"年龄（岁）\",\"name\" : \"AGE_VALUE\",\"width\":\"50\",\"center\":true},{\"display\" : \"身份证号\",\"name\" : \"ID_CARD_NO\",\"align\":\"center\",\"width\":\"120\",\"parseData\":\"jParseData.parsePatientIDCard\"},{\"display\" : \"就诊类型\",\"name\" : \"VISIT_TYPE_NAME\",\"width\":\"60\",\"center\":true},{\"display\" : \"就诊号\",\"name\" : \"VISIT_NO\",\"width\":\"80\",\"center\":true},{\"display\" : \"就诊科室\",\"name\" : \"DEPT_ADMISSION_TO_NAME\"},{\"display\" : \"疾病诊断\",\"name\" : \"DIAGNOSIS_NAME\"},{\"display\" : \"入院日期\",\"name\" : \"ADMISSION_TIME\"},{\"display\" : \"出院日期\",\"name\" : \"DISCHARGE_TIME\"},{\"display\" : \"住院天数\",\"name\" : \"IN_HOSPITAL_DAYS\",\"width\":\"80\",\"center\":true},{\"display\" : \"是否有体检数据\",\"name\" : \"MEDICAL_VIEW\",\"width\":\"80\",\"center\":true},{\"display\" : \"当前状态\",\"name\" : \"HOSPITAL_STATE_NAME\",\"width\":\"80\",\"center\":true}]" : CIV_PATIENT_COLUMN;
    }

    /**
     * 患者列表就诊类型
     *
     * @return
     */
    public static String getCIV_PATIENT_LIST_VISIT_TYPE(String oid) {
        String CIV_PATIENT_LIST_VISIT_TYPE = ConfigCache.getCache(oid, "CIV_PATIENT_LIST_VISIT_TYPE");
        return StringUtils.isBlank(CIV_PATIENT_LIST_VISIT_TYPE) ? "[{\"typeCode\":\"01\",\"typeName\":\"门诊\"},{\"typeCode\":\"02\",\"typeName\":\"住院\"},{\"typeCode\":\"00\",\"typeName\":\"急诊\"},{\"typeCode\":\"03\",\"typeName\":\"体检\"}]" : CIV_PATIENT_LIST_VISIT_TYPE;
    }

    /**
     * 就诊视图检查检验和医嘱关联字段配置
     *
     * @return
     */
    public static String getCIV_ORDER_LABOREXAM(String oid) {
        String CIV_ORDER_LABOREXAM = ConfigCache.getCache(oid, "CIV_ORDER_LABOREXAM");
        return StringUtils.isBlank(CIV_ORDER_LABOREXAM) ? "ORDER_NO" : CIV_ORDER_LABOREXAM;
    }

    /**
     * 就诊视图 门诊医嘱和检验关联字段配置
     *
     * @return
     */
    public static String getCIV_ORDER_LABOREXAM_MZ(String oid) {
        String CIV_ORDER_LABOREXAM_MZ = ConfigCache.getCache(oid, "CIV_ORDER_LABOREXAM_MZ");
        return StringUtils.isBlank(CIV_ORDER_LABOREXAM_MZ) ? "ORDER_NO" : CIV_ORDER_LABOREXAM_MZ;
    }

    public static String getOCL_INPV_NurseFilter(String oid) {
        String OCL_INPV_NurseFilter = ConfigCache.getCache(oid, "OCL_INPV_NurseFilter");
        return StringUtils.isBlank(OCL_INPV_NurseFilter) ? "" : OCL_INPV_NurseFilter;
    }

    public static String getOCL_INPV_OthersFilter(String oid) {
        String OCL_INPV_OthersFilter = ConfigCache.getCache(oid, "OCL_INPV_OthersFilter");
        return StringUtils.isBlank(OCL_INPV_OthersFilter) ? "" : OCL_INPV_OthersFilter;
    }

    public static String getOCL_MZ_NurseFilter(String oid) {
        String OCL_MZ_NurseFilter = ConfigCache.getCache(oid, "OCL_MZ_NurseFilter");
        return StringUtils.isBlank(OCL_MZ_NurseFilter) ? "" : OCL_MZ_NurseFilter;
    }

    public static String getOCL_MZ_OthersFilter(String oid) {
        String OCL_MZ_OthersFilter = ConfigCache.getCache(oid, "OCL_MZ_OthersFilter");
        return StringUtils.isBlank(OCL_MZ_OthersFilter) ? "" : OCL_MZ_OthersFilter;
    }

    /**
     * 手术显示配置
     *
     * @return
     */
    public static String getCIV_OPER_CONFIG(String oid) {
        String CIV_OPER_CONFIG = ConfigCache.getCache(oid, "CIV_OPER_CONFIG");
        return StringUtils.isBlank(CIV_OPER_CONFIG) ? "OperVisit,1;OperProcess,1;OperAfter,1" : CIV_OPER_CONFIG;
    }

    /**
     * 手术麻醉单配置
     *
     * @return
     */
    public static String getCIV_ANESTHESIA_CONFIG(String oid) {
        String CIV_ANESTHESIA_CONFIG = ConfigCache.getCache(oid, "CIV_ANESTHESIA_CONFIG");
        return StringUtils.isBlank(CIV_ANESTHESIA_CONFIG) ? "" : CIV_ANESTHESIA_CONFIG;
    }

    /**
     * 手术麻醉单下拉框配置
     *
     * @return
     */
    public static String getCIV_ANESTHESIA_SELECT_CONFIG(String oid) {
        String CIV_ANESTHESIA_SELECT_CONFIG = ConfigCache.getCache(oid, "CIV_ANESTHESIA_SELECT_CONFIG");
        return StringUtils.isBlank(CIV_ANESTHESIA_SELECT_CONFIG) ? "" : CIV_ANESTHESIA_SELECT_CONFIG;
    }

    /**
     * 获取 医嘱状态名称列表
     *
     * @return
     */
    public static List<String> getCIV_ORDERSTATUS(String oid) {
        Map<String, String> statusMap = new HashMap<String, String>();
        List<String> result = new ArrayList<String>();
        String CIV_ORDERSTATUS = ConfigCache.getCache(oid, "CIV_ORDERSTATUS");
        //默认返回北医三院数据
        if (StringUtils.isBlank(CIV_ORDERSTATUS)) {
            statusMap.put("0", "录入");
            statusMap.put("1", "提交");
            statusMap.put("2", "开立");
            statusMap.put("3", "审核");
            statusMap.put("4", "执行");
            statusMap.put("5", "停止");
            statusMap.put("6", "撤销");
            statusMap.put("7", "废弃");
        } else {
            statusMap.putAll(Utils.StrSplitToMap(CIV_ORDERSTATUS, ";", ","));
        }
        for (Map.Entry<String, String> entry : statusMap.entrySet()) {
            result.add(entry.getValue());
        }
        return result;
    }
    public static List<String> getCIV_ORDERDRUGTYPE(String oid) {
        Map<String, String> statusMap = new HashMap<String, String>();
        List<String> result = new ArrayList<String>();
        String CIV_ORDERDRUGTYPE = ConfigCache.getCache(oid, "CIV_ORDERDRUGTYPE");
        //默认返回北医三院数据
        if (StringUtils.isBlank(CIV_ORDERDRUGTYPE)) {
            statusMap.put("0", "抗生素");
            statusMap.put("1", "抗菌药");
            statusMap.put("2", "毒麻素");
        } else {
            statusMap.putAll(Utils.StrSplitToMap(CIV_ORDERDRUGTYPE, ";", ","));
        }
        for (Map.Entry<String, String> entry : statusMap.entrySet()) {
            result.add(entry.getValue());
        }
        return result;
    }
    public static List<String> getCIV_ORDERDRUGPROPERTY(String oid) {
        Map<String, String> statusMap = new HashMap<String, String>();
        List<String> result = new ArrayList<String>();
        String CIV_ORDERDRUGPROPERTY = ConfigCache.getCache(oid, "CIV_ORDERDRUGPROPERTY");
        //默认返回北医三院数据
        if (StringUtils.isBlank(CIV_ORDERDRUGPROPERTY)) {
            statusMap.put("0", "抗生素");
            statusMap.put("1", "抗菌药");
            statusMap.put("2", "毒麻素");
        } else {
            statusMap.putAll(Utils.StrSplitToMap(CIV_ORDERDRUGPROPERTY, ";", ","));
        }
        for (Map.Entry<String, String> entry : statusMap.entrySet()) {
            result.add(entry.getValue());
        }
        return result;
    }

    /**
     * 获取当前视图诊断信息查询条件
     *
     * @return
     */
    public static String getCIV_CVDIAGFILTER(String oid) {
        String CIV_CVDIAGFILTER = ConfigCache.getCache(oid, "CIV_CVDIAGFILTER");
        return StringUtils.isBlank(CIV_CVDIAGFILTER) ? "DIAGNOSIS_PROPERTY_CODE|in|2,3" : CIV_CVDIAGFILTER;
    }

    /**
     * 获取生命体征类型名称
     *
     * @param key 体温,脉搏,心率,呼吸,血压,血氧
     * @return
     */
    public static String getCIV_VITAL_TYPE_NAME(String oid, String key) {
        Map<String, String> result = new HashMap<String, String>();
        String resultStr = "";
        String CIV_VITAL_TYPE_NAME = ConfigCache.getCache(oid, "CIV_VITAL_TYPE_NAME");
        //默认返回北医三院类型数据
        if (StringUtils.isBlank(CIV_VITAL_TYPE_NAME)) {
            result.put("体温", "体温");
            result.put("脉搏", "脉搏");
            result.put("心率", "心率");
            result.put("呼吸", "呼吸");
            result.put("血压", "血压");
            result.put("血氧", "血氧");
        } else {
            result.putAll(Utils.StrSplitToMap(CIV_VITAL_TYPE_NAME, ";", ","));
        }

        return result.get(key);
    }

    /**
     * 患者主诊断查询配置,默认返回为北医三院查询方式
     *
     * @return
     */
    public static String getCIV_PAT_MAINDIAG_INP_FILTER(String oid) {
        String CIV_PAT_MAINDIAG_INP_FILTER = ConfigCache.getCache(oid, "CIV_PAT_MAINDIAG_INP_FILTER");
        return StringUtils.isBlank(CIV_PAT_MAINDIAG_INP_FILTER)
                ? "DIAGNOSIS_PROPERTY_CODE|in|2,3;DIAGNOSIS_NUM|=|1;DIAGNOSIS_SUB_NUM|=|0"
                : CIV_PAT_MAINDIAG_INP_FILTER;
    }


    /**
     * 就診試圖医嘱 显示列配置
     */
//    public static String getVISIT_ORDER_ALL_SHOW_CONFIG(String oid) {
//        String VISIT_ORDER_ALL_SHOW_CONFIG = ConfigCache.getCache(oid, "VISIT_ORDER_ALL_SHOW_CONFIG");
//        String config = "{\"0\": [{\"display\":\"医嘱类别\",\"name\":\"orderClassName\", \"width\": 80},{\"display\":\"医嘱性质\",\"name\":\"orderPropertiesName\"},{\"display\":\"医嘱项\",\"name\":\"orderItemName\"},{\"display\":\"医嘱开始时间\",\"name\":\"orderBeginTime\"},{\"display\":\"医嘱结束时间\",\"name\":\"orderEndTime\"},{\"display\":\"执行频次\",\"name\":\"frequencyName\",\"width\":60},{\"display\":\"持续时间\",\"name\":\"durationValue\",\"width\":50},{\"display\":\"医嘱状态\",\"name\": \"orderStatusName\",\"width\":60},{\"display\":\"医嘱类别\",\"name\": \"orderType\",\"hidden\":\"true\"}],\"1\": [{\"display\":\"医嘱名称\",\"name\":\"orderItemName\", \"width\": 80},{\"display\":\"剂量\",\"name\":\"dosageValue\"},{\"display\":\"单位\",\"name\":\"dosageUnit\"},{\"display\":\"用法\",\"name\":\"pharmacyWayName\"},{\"display\":\"频率\",\"name\":\"frequencyName\"},{\"display\":\"医嘱类型\",\"name\":\"orderClassName\",\"width\":60},{\"display\":\"开立人\",\"name\":\"orderDoctorName\",\"width\":50},{\"display\":\"开立时间\",\"name\":\"orderTime\", \"width\": 75},{\"display\":\"开始时间\",\"name\": \"orderBeginTime\", \"width\": 75},{\"display\":\"结束时间\",\"name\": \"orderEndTime\", \"width\": 75},{\"display\":\"当前状态\",\"name\": \"orderStatusName\",\"width\":60}],\"2\": [{\"display\":\"组标志\",\"name\":\"parentOrderNo\"},{\"display\":\"医嘱名称\",\"name\":\"orderItemName\", \"width\": 80},{\"display\":\"剂量\",\"name\":\"dosageValue\"},{\"display\":\"单位\",\"name\":\"dosageUnit\"},{\"display\":\"用法\",\"name\":\"pharmacyWayName\"},{\"display\":\"频率\",\"name\":\"frequencyName\"},{\"display\":\"医嘱类型\",\"name\":\"orderClassName\",\"width\":60},{\"display\":\"开立人\",\"name\":\"orderDoctorName\",\"width\":50},{\"display\":\"开立时间\",\"name\":\"orderDoctorName\", \"width\": 75},{\"display\":\"开始时间\",\"name\": \"orderBeginTime\", \"width\": 75},{\"display\":\"结束时间\",\"name\": \"orderEndTime\", \"width\": 75},{\"display\":\"当前状态\",\"name\": \"orderStatusName\",\"width\":60}],\"3\": [{\"display\":\"申请单号\",\"name\":\"orderNo\"},{\"display\":\"申请时间\",\"name\":\"applyDate\"},{\"display\":\"申请人\",\"name\":\"applyPersonName\"},{\"display\":\"申请科室\",\"name\":\"applyDeptName\"},{\"display\":\"申请单状态\",\"name\":\"sqdStatusName\"}],\"4\": [{\"display\":\"医嘱名称\",\"name\":\"orderItemName\", \"width\": 80},{\"display\":\"剂量\",\"name\":\"dosageValue\"},{\"display\":\"单位\",\"name\":\"dosageUnit\"},{\"display\":\"用法\",\"name\":\"pharmacyWayName\"},{\"display\":\"频率\",\"name\":\"frequencyName\"},{\"display\":\"医嘱类型\",\"name\":\"orderClassName\",\"width\":60},{\"display\":\"开立人\",\"name\":\"orderDoctorName\",\"width\":50},{\"display\":\"开立时间\",\"name\":\"orderTime\", \"width\": 75},{\"display\":\"开始时间\",\"name\": \"orderBeginTime\", \"width\": 75},{\"display\":\"结束时间\",\"name\": \"orderEndTime\", \"width\": 75},{\"display\":\"当前状态\",\"name\": \"orderStatusName\",\"width\":60}],\"5\": [{\"display\":\"拟手术名称\",\"name\":\"operationName\"},{\"display\":\"开立人\",\"name\":\"orderDoctorName\"},{\"display\":\"开立时间\",\"name\":\"orderTime\"},{\"display\":\"施术者\",\"name\": \"planOperDoctorName\"},{\"display\":\"术前诊断\",\"name\": \"diagBeforeOperationName\"},{\"display\":\"手术日期\",\"name\": \"operTime\"}],\"6\": [{\"display\":\"医嘱名称\",\"name\":\"orderItemName\"},{\"display\":\"医嘱类型\",\"name\":\"orderClassName\"},{\"display\":\"开立人\",\"name\":\"orderDoctorName\"},{\"display\":\"开立时间\",\"name\":\"orderTime\"},{\"display\":\"当前状态\",\"name\": \"reportStatus\"}],\"6.1\": [{\"display\":\"医嘱名称\",\"name\":\"orderItemName\"},{\"display\":\"医嘱类型\",\"name\":\"orderClassName\"},{\"display\":\"开立人\",\"name\":\"orderDoctorName\"},{\"display\":\"开立时间\",\"name\":\"orderTime\"},{\"display\":\"是否超时\",\"name\":\"overTime\"},{\"display\":\"当前状态\",\"name\": \"reportStatus\"}],\"7\": [{\"display\":\"医嘱名称\",\"name\":\"ORDER_ITEM_NAME\"},{\"display\":\"危急值项目\",\"name\":\"LAB_SUB_ITEM_NAME\"},{\"display\":\"危急值\",\"name\":\"CRISIS_VALUE\"},{\"display\":\"参考范围\",\"name\":\"RANGE\"},{\"display\":\"报告时间\",\"name\":\"REPORT_TIME\"},{\"display\":\"处理人\",\"name\":\"OPERATOR_NAME\"},{\"display\":\"处理时间\",\"name\":\"OPERATE_TIME\"},{\"display\":\"处理状态\",\"name\": \"OPERATE_STATUS\"},{\"display\":\"危急值标志\",\"name\": \"CRISIS_FLAG_NAME\"}],\"8\": [{\"display\":\"医嘱名称\",\"name\":\"orderItemName\"},{\"display\":\"医嘱类型\",\"name\":\"orderClassName\"},{\"display\":\"开立人\",\"name\":\"orderDoctorName\"},{\"display\":\"开立时间\",\"name\":\"orderTime\"},{\"display\":\"当前状态\",\"name\": \"reportStatus\"}],\"8.1\": [{\"display\":\"医嘱名称\",\"name\":\"orderItemName\"},{\"display\":\"医嘱类型\",\"name\":\"orderClassName\"},{\"display\":\"开立人\",\"name\":\"orderDoctorName\"},{\"display\":\"开立时间\",\"name\":\"orderTime\"},{\"display\":\"是否超时\",\"name\":\"overTime\"},{\"display\":\"当前状态\",\"name\": \"reportStatus\"}],\"11\":[{\"display\":\"医嘱名称\",\"name\":\"orderItemName\"},{\"display\":\"医嘱类\",\"name\":\"orderClassName\"},{\"display\":\"开立人\",\"name\":\"orderDoctorName\"},{\"display\":\"开立时间\",\"name\":\"orderTime\"},{\"display\":\"开始时间\",\"name\":\"orderBeginTime\"},{\"display\":\"结束时间\",\"name\":\"orderEndTime\"},{\"display\":\"护理等级\",\"name\":\"orderItemName\"},{\"display\":\"医嘱序号\",\"name\":\"orderNo\"},{\"display\":\"医嘱性质\",\"name\":\"orderPropertiesName\"},{\"display\":\"医嘱状态\",\"name\":\"orderStatusName\"}],\"12\":[{\"display\":\"医嘱名称\",\"name\":\"orderItemName\"},{\"display\":\"剂量\",\"name\":\"dosageValue\"},{\"display\":\"频率\",\"name\":\"frequencyName\"},{\"display\":\"医嘱类\",\"name\":\"orderClassName\"},{\"display\":\"护理等级\",\"name\":\"orderItemName\"},{\"display\":\"开立人\",\"name\":\"orderDoctorName\"},{\"display\":\"开立时间\",\"name\":\"orderTime\"},{\"display\":\"开始时间\",\"name\":\"orderBeginTime\"},{\"display\":\"结束时间\",\"name\":\"orderEndTime\"},{\"display\":\"医嘱性质\",\"name\":\"orderPropertiesName\"},{\"display\":\"医嘱状态\",\"name\":\"orderStatusName\"}]}";
//        return StringUtils.isBlank(VISIT_ORDER_ALL_SHOW_CONFIG)
//                ? config
//                : VISIT_ORDER_ALL_SHOW_CONFIG;
//    }
    public static String getVISIT_TIME_CHOICE_CONFIG(String oid) {
        String timeChoice = ConfigCache.getCache(oid, "VISIT_TIME_CHOICE_CONFIG");
        if ("1".equals(timeChoice)) {
            return "allTime";
        } else if ("2".equals(timeChoice)) {
            return "near_30_day";
        } else if ("3".equals(timeChoice)) {
            return "near_3_month";
        } else if ("4".equals(timeChoice)) {
            return "near_3_year";
        }
        return "near_1_year";
    }
    public static String getCATEGORY_TIME_CHOICE_CONFIG(String oid) {
        String timeChoice = ConfigCache.getCache(oid, "CATEGORY_TIME_CHOICE_CONFIG");
        if ("1".equals(timeChoice)) {
            return "allTime";
        } else if ("2".equals(timeChoice)) {
            return "near_30_day";
        } else if ("3".equals(timeChoice)) {
            return "near_3_month";
        } else if ("4".equals(timeChoice)) {
            return "near_3_year";
        }
        return "near_1_year";
    }

    /**
     * 配置格式 id,name,sysCode,order;id2,name2,sysCode2,order2
     *
     * @return
     */
    public static Map<String, Object> getMODULES_CONFIG(String oid, String moduleId) {
        String modules = ConfigCache.getCache(oid, "CIV_MODULES");
        if (modules != null && modules.length() > 0) {
            boolean flag = modules.contains(";");
            if (flag) {
                String[] modulesInfo = modules.split(";");
                if (modulesInfo.length > 0) {
                    for (String info : modulesInfo) {
                        Map<String, Object> moduleMap = getModuleMap(oid,info, moduleId);
                        if (null != moduleMap) {
                            return moduleMap;
                        }
                    }
                }
            } else {
                return getModuleMap(oid,modules, moduleId);
            }

        }
        return null;
    }

    private static Map<String, Object> getModuleMap(String oid, String strModule, String moduleId) {
        String[] data = strModule.split(",");
        if (moduleId.equalsIgnoreCase(data[0])) {
            Map<String, Object> map = new HashMap<String, Object>();
            map.put("id", data[0]);
            map.put("name", data[1]);
            map.put("sysCode", data[2]);
            map.put("order", data[3]);
            map.put("linkType", data[4]);
            return map;
        }
        return null;
    }

    /**
     * 就诊视图中隐藏就诊类表上的就诊次数配置
     * true 隐藏，false 不隐藏，默认为不隐藏
     *
     * @return
     */
    public static String getCIV_HIDDEN_VISITlIST_VISITID(String oid) {
        String CIV_HIDDEN_VISITlIST_VISITID = ConfigCache.getCache(oid, "CIV_HIDDEN_VISITlIST_VISITID");
        return StringUtils.isBlank(CIV_HIDDEN_VISITlIST_VISITID) ? "false" : CIV_HIDDEN_VISITlIST_VISITID;
    }

    /**
     * 患者列表查询是否启用模糊查询
     * true 启用  false 弃用
     *
     * @return
     */
    public static String getPAT_LIST_QUERY_LIKE_CONFIG(String oid) {
        String value = ConfigCache.getCache(oid, "PAT_LIST_QUERY_LIKE_CONFIG");
        return StringUtils.isBlank(value) ? "true" : value;
    }

    /**
     * 入院记录的类型编码，默认返回EMR09.00.01
     *
     * @return
     */
    public static String getEMR_RAN_MRCLASSCODE(String oid) {
        String EMR_RAN_MRCLASSCODE = ConfigCache.getCache(oid, "EMR_RAN_MRCLASSCODE");
        return StringUtils.isBlank(EMR_RAN_MRCLASSCODE) ? "EMR09.00.01" : EMR_RAN_MRCLASSCODE;
    }

    /**
     * 主诉，现病史查询表，默认返回1   1：病历文书表  2：病历章节表
     *
     * @return
     */
    public static String getHDR_EMR_CONTENT_DG_OR_NOT(String oid) {
        String HDR_EMR_CONTENT_DG_OR_NOT = ConfigCache.getCache(oid, "HDR_EMR_CONTENT_DG_OR_NOT");
        return StringUtils.isBlank(HDR_EMR_CONTENT_DG_OR_NOT) ? "1" : HDR_EMR_CONTENT_DG_OR_NOT;
    }

    /**
     * 主诉DG_CODE配置
     *
     * @return
     */
    public static String getMAIN_DG_CODE(String oid) {
        String MAIN_DG_CODE = ConfigCache.getCache(oid, "MAIN_DG_CODE");
        return StringUtils.isBlank(MAIN_DG_CODE) ? "1" : MAIN_DG_CODE;
    }

    /**
     * 现病史DG_CODE配置
     *
     * @return
     */
    public static String getNOW_HIS_DG_CODE(String oid) {
        String NOW_HIS_DG_CODE = ConfigCache.getCache(oid, "NOW_HIS_DG_CODE");
        return StringUtils.isBlank(NOW_HIS_DG_CODE) ? "1" : NOW_HIS_DG_CODE;
    }

    /**
     * 获取就诊视图默认值
     *
     * @return
     */
    public static String getCIV_VISIT_VALUE(String oid) {
        String CIV_VISIT_VALUE = ConfigCache.getCache(oid, "CIV_VISIT_VALUE");
        return StringUtils.isNotBlank(CIV_VISIT_VALUE) ? CIV_VISIT_VALUE : "index_module/order_module/exam_module/check_module/record_module/oper_module/nurse_module/allergy_module/others_module/ocl_module/blood_module";
    }


    /**
     * 新增检验显示超时信息
     *
     * @return
     */
    public static boolean getLAB_OVER_TIME(String oid) {
        String LAB_OVER_TIME = ConfigCache.getCache(oid, "LAB_OVER_TIME");
        boolean result = false;
        if (StringUtils.isNotBlank(LAB_OVER_TIME) && LAB_OVER_TIME.equalsIgnoreCase("true")) {
            result = true;
        }
        return result;
    }

    /**
     * 新增检查显示超时信息
     *
     * @return
     */
    public static boolean getEXAM_OVER_TIME(String oid) {
        String EXAM_OVER_TIME = ConfigCache.getCache(oid, "EXAM_OVER_TIME");
        boolean result = false;
        if (StringUtils.isNotBlank(EXAM_OVER_TIME) && EXAM_OVER_TIME.equalsIgnoreCase("true")) {
            result = true;
        }
        return result;
    }

    /**
     * 获取分类视图默认值
     *
     * @return
     */
    public static String getCIV_CATEGORY_VALUE(String oid) {
        String CIV_CATEGORY_VALUE = ConfigCache.getCache(oid, "CIV_CATEGORY_VALUE");
        return StringUtils.isNotBlank(CIV_CATEGORY_VALUE) ? CIV_CATEGORY_VALUE : "check_module/pathology_module/exam_module/oper_module/main_diag_module/record_module/nurse_module/durg_orally_module/durg_vein_module/durg_qt_module/history_module/dialysis_module";
    }

    /**
     * 是否使用病例章节模板
     *
     * @return
     */
    public static String getIS_EMR_DG_HTML(String oid) {
        String IS_EMR_DG_HTML = ConfigCache.getCache(oid, "IS_EMR_DG_HTML");
        return StringUtils.isBlank(IS_EMR_DG_HTML) ? "false" : IS_EMR_DG_HTML;
    }

    /**
     * 末次就诊栏是否跟随就诊试图的就诊次而变化
     *
     * @return
     */
    public static String getSCNNAR_SHOW_WITH_VISIT(String oid) {
        String SCNNAR_SHOW_WITH_VISIT = ConfigCache.getCache(oid, "SCNNAR_SHOW_WITH_VISIT");
        return StringUtils.isBlank(SCNNAR_SHOW_WITH_VISIT) ? "false" : SCNNAR_SHOW_WITH_VISIT;
    }

    /**
     * 就诊试图是否显示病区
     *
     * @return
     */
    public static String getIS_SHOW_VISIT_CARD_BQ(String oid) {
        String IS_SHOW_VISIT_CARD_BQ = ConfigCache.getCache(oid, "IS_SHOW_VISIT_CARD_BQ");
        return StringUtils.isBlank(IS_SHOW_VISIT_CARD_BQ) ? "false" : IS_SHOW_VISIT_CARD_BQ;
    }

    /**
     * 获取专科视图时间轴默认值
     *
     * @return
     */
    public static String getCIV_SPECIALTYTIMEAXIS_VALUE(String oid) {
        String CIV_SPECIALTYTIMEAXIS_VALUE = ConfigCache.getCache(oid, "CIV_SPECIALTYTIMEAXIS_VALUE");
        return StringUtils.isNotBlank(CIV_SPECIALTYTIMEAXIS_VALUE) ? CIV_SPECIALTYTIMEAXIS_VALUE : "";
    }

    /**
     * 获取体检视图默认值
     *
     * @return
     */
    public static String getCIV_MEDICAL_VALUE(String oid) {
        String civ_medical_value = ConfigCache.getCache(oid, "CIV_MEDICAL_VALUE");
        return StringUtils.isNotBlank(civ_medical_value) ? civ_medical_value : "";
    }

    /**
     * 获取检查报告默认值
     *
     * @return
     */
    public static String getCIV_EXAM_VALUE(String oid) {
        String CIV_EXAM_VALUE = ConfigCache.getCache(oid, "CIV_EXAM_VALUE");
        return StringUtils.isNotBlank(CIV_EXAM_VALUE) ? CIV_EXAM_VALUE : "MNK/143/EIS/187/189/ZQG/191/190/RF/ENT/PIS_NEW/183/184/PIS/PFU/185/WCDL/NM/ECG/UIS/EYE/142";
    }

    /**
     * 获取病理报告默认值
     *
     * @return
     */
    public static String getCIV_PATHOLOGY_VALUE(String oid) {
        String CIV_PATHOLOGY_VALUE = ConfigCache.getCache(oid, "CIV_PATHOLOGY_VALUE");
        return StringUtils.isNotBlank(CIV_PATHOLOGY_VALUE) ? CIV_PATHOLOGY_VALUE : "PIS_NEW/184";
    }

    /**
     * 获取检验报告过滤数据配置项
     *
     * @return
     */
    public static String getINSPECT_REPORT_CONFIG_VALUE(String oid) {
        String inspectReportConfigValue = ConfigCache.getCache(oid, "INSPECT_REPORT_CONFIG_VALUE");
        return StringUtils.isNotBlank(inspectReportConfigValue) ? inspectReportConfigValue : "null";
    }

    /**
     * 获取病历文书默认值
     *
     * @return
     */
    public static String getCIV_EMR_VALUE(String oid) {
        String CIV_EMR_VALUE = ConfigCache.getCache(oid, "CIV_EMR_VALUE");
        return StringUtils.isNotBlank(CIV_EMR_VALUE) ? CIV_EMR_VALUE : "EMR02.00.01/EMR02.00.02/EMR09.00.01/EMR09.00.02/EMR09.00.03/EMR10.00.01/EMR10.00.02/EMR10.00.03/EMR10.00.04/EMR10.00.05/EMR10.00.06/EMR10.00.07/EMR10.00.08/EMR10.00.12/EMR10.00.14/EMR10.00.15";
    }

    /**
     * 获取当前视图默认值
     *
     * @return
     */
    public static String getCIV_CURRENT_VALUE(String oid) {
        String CIV_CURRENT_VALUE = ConfigCache.getCache(oid, "CIV_CURRENT_VALUE");
        return StringUtils.isNotBlank(CIV_CURRENT_VALUE) ? CIV_CURRENT_VALUE : "";
    }

    /**
     * 获取专科视图默认值
     *
     * @return
     */
    public static String getCIV_SPECIALTY_VALUE(String oid) {
        String CIV_SPECIALTY_VALUE = ConfigCache.getCache(oid, "CIV_SPECIALTY_VALUE");
        return StringUtils.isNotBlank(CIV_SPECIALTY_VALUE) ? CIV_SPECIALTY_VALUE : "";
    }

    /**
     * 获取时间轴默认值
     *
     * @return
     */
    public static String getCIV_TIMEAXIS_VALUE(String oid) {
        String CIV_TIMEAXIS_VALUE = ConfigCache.getCache(oid, "CIV_TIMEAXIS_VALUE");
        return StringUtils.isNotBlank(CIV_TIMEAXIS_VALUE) ? CIV_TIMEAXIS_VALUE : "";
    }

    /**
     * 专科视图检查 用药检验Table配置
     *
     * @return
     */
    public static String getSPECIAL_CUSTOM_TABLE(String oid) {
        String SPECIAL_CUSTOM_TABLE = ConfigCache.getCache(oid, "SPECIAL_CUSTOM_TABLE");
        return StringUtils.isNotBlank(SPECIAL_CUSTOM_TABLE) ? SPECIAL_CUSTOM_TABLE : "[{\"type\":\"Lab\",\"name\":\"核心检验指标\",\"url\":\"sv_getLabSubList\",\"details_url\":\"sv_getLabSubDetails\"},{\"type\":\"Drug\",\"name\":\"重点关注药品\",\"url\":\"sv_getDrugList\",\"details_url\":\"sv_getDrugDetails\"},{\"type\":\"Exam\",\"name\":\"重点关注检查\",\"url\":\"sv_getExamList\",\"details_url\":\"sv_getExamDetails\"}]";
    }

    /**
     * 获取admin用户
     *
     * @return
     */
    public static String getCiv_Admin(String oid) {
        String CIV_ADMIN = ConfigCache.getCache(oid, "CIV_ADMIN");
        return StringUtils.isNotBlank(CIV_ADMIN) ? CIV_ADMIN : "admin";
    }

    /**
     * 所有用户能否查看所有科室 默认可以
     *
     * @return
     */
    public static boolean isAllDept(String oid) {
        String isAllDept = ConfigCache.getCache(oid, "IS_ALL_DEPT");
        return isAllDept.equals("false") ? true : false;
    }

    public static String getNoticeSwitch(String oid) {
        String noticeSwitch = ConfigCache.getCache(oid, "NOTICE_SWITCH");
        return StringUtils.isNotBlank(noticeSwitch) ? noticeSwitch : "0";
    }

    public static List<String> getNoticeContent(String oid) {
        String noticeContent = ConfigCache.getCache(oid, "NOTICE_CONTENT");
        List<String> list = new ArrayList<>();
        if (StringUtils.isNotBlank(noticeContent)) {
            String[] split = noticeContent.split("|");
            for (String str : split) {
                list.add(str);
            }
        }
        if (list.size() == 0) {
            list.add("暂无通知！");
        }
        return list;
    }


    /**
     * 是否查询所有科室
     *
     * @return
     */
    public static boolean getAllDeptdata(String oid) {
        String flag = ConfigCache.getCache(oid, "IS_QUERY_ALL_DEPT");
        return flag.equals("1") ? true : false;
    }

    /**
     * 查询中台solr还是集群solr   false中台solr  true集群solr  默认集群solr
     *
     * @return
     */
    public static boolean isQuerySolr() {
        String type = ConfigCache.getCache("ALL", "IS_QUERY_SOLR");
        return !type.equals("false");
    }

    /**
     * 获取就诊视图
     *
     * @return
     */
    public static List<Map<String, String>> getCiv_Visit(String oid) {
        List<Map<String, String>> list = new ArrayList<Map<String, String>>();
        String CIV_VISIT = ConfigCache.getCache(oid, "CIV_VISIT");
        String[] strs = CIV_VISIT.split(";");
        for (int i = 0; i < strs.length; i++) {
            Map<String, String> type = new HashMap<String, String>();
            String[] config = strs[i].split(",");
            type.put("code", config[0]);
            type.put("name", config[1]);
            list.add(type);
        }
        return list;
    }

    /**
     * 获取就诊视图
     *
     * @return
     */
    public static List<Map<String, String>> getSummaryFields(String oid) {
        List<Map<String, String>> list = new ArrayList<Map<String, String>>();
        String CIV_VISIT_SUMMARY = ConfigCache.getCache(oid, "CIV_VISIT_SUNNARY");
        if (StringUtils.isNotBlank(CIV_VISIT_SUMMARY)) {
            String[] strs = CIV_VISIT_SUMMARY.split(";");
            for (int i = 0; i < strs.length; i++) {
                Map<String, String> type = new HashMap<String, String>();
                String[] config = strs[i].split(",");
                type.put("name", config[0]);
                type.put("code", config[1]);
                list.add(type);
            }
        }
        return list;
    }

    /**
     * 获取就诊视图
     *
     * @return
     */
    public static List<Map<String, String>> getOutpSummaryFields(String oid) {
        List<Map<String, String>> list = new ArrayList<Map<String, String>>();
        String CIV_VISIT_OUTP_SUMMARY = ConfigCache.getCache(oid, "CIV_VISIT_OUTP_SUNNARY");
        if (StringUtils.isNotBlank(CIV_VISIT_OUTP_SUMMARY)) {
            String[] strs = CIV_VISIT_OUTP_SUMMARY.split(";");
            for (int i = 0; i < strs.length; i++) {
                Map<String, String> type = new HashMap<String, String>();
                String[] config = strs[i].split(",");
                type.put("name", config[0]);
                type.put("code", config[1]);
                list.add(type);
            }
        }
        return list;
    }

    /**
     * 获取就诊视图
     *
     * @return
     */
    public static List<Map<String, String>> getSummaryInfoFields(String oid) {
        List<Map<String, String>> list = new ArrayList<Map<String, String>>();
        String CIV_VISIT_SUNNARY_INFO = ConfigCache.getCache(oid, "CIV_VISIT_SUNNARY_INFO");
        if(StringUtils.isNotBlank(CIV_VISIT_SUNNARY_INFO)){
            String[] strs = CIV_VISIT_SUNNARY_INFO.split(";");
            for (int i = 0; i < strs.length; i++) {
                Map<String, String> type = new HashMap<String, String>();
                String[] config = strs[i].split(",");
                type.put("name", config[0]);
                type.put("code", config[1]);
                list.add(type);
            }
        }
        return list;
    }

    /**
     * 获取分类视图
     *
     * @return
     */
    public static List<Map<String, String>> getCiv_Category(String oid) {
        List<Map<String, String>> list = new ArrayList<Map<String, String>>();
        String CIV_CATEGORY = ConfigCache.getCache(oid, "CIV_CATEGORY");
        String[] strs = CIV_CATEGORY.split(";");
        for (int i = 0; i < strs.length; i++) {
            Map<String, String> type = new HashMap<String, String>();
            String[] config = strs[i].split(",");
            type.put("code", config[0]);
            type.put("name", config[1]);
            list.add(type);
        }
        return list;
    }

    /**
     * 获取分类视图
     *
     * @return
     */
    public static boolean getCiv_Cv_Drugadr(String oid) {
        String CIV_CV_DRUGADR = ConfigCache.getCache(oid, "CIV_CV_DRUGADR");
        if (StringUtils.isNotBlank(CIV_CV_DRUGADR)) {
            return "true".equals(CIV_CV_DRUGADR);
        }
        return false;
    }

    /**
     * 获取检查报告类型
     *
     * @return
     */
    public static List<Map<String, String>> getCiv_Exam_Types(String oid) {
        List<Map<String, String>> list = new ArrayList<Map<String, String>>();
        String CIV_EXAM_TYPE = ConfigCache.getCache(oid, "CIV_EXAM_TYPE");
        String[] strs = CIV_EXAM_TYPE.split(";");
        for (int i = 0; i < strs.length; i++) {
            Map<String, String> type = new HashMap<String, String>();
            String[] config = strs[i].split(",");
            type.put("code", config[0]);
            type.put("name", config[1]);
            list.add(type);
        }
        return list;
    }

    /**
     * 获取病理报告类型
     *
     * @return
     */
    public static List<Map<String, String>> getCIV_PATHOLOGY_TYPE(String oid) {
        List<Map<String, String>> list = new ArrayList<Map<String, String>>();
        String CIV_EXAM_TYPE = ConfigCache.getCache(oid, "CIV_PATHOLOGY_TYPE");
        String[] strs = CIV_EXAM_TYPE.split(";");
        for (int i = 0; i < strs.length; i++) {
            Map<String, String> type = new HashMap<String, String>();
            String[] config = strs[i].split(",");
            type.put("code", config[0]);
            type.put("name", config[1]);
            list.add(type);
        }
        return list;
    }

    /**
     * 获取病例类型
     *
     * @return
     */
    public static List<Map<String, String>> getCiv_Emr_Types(String oid) {
        List<Map<String, String>> list = new ArrayList<Map<String, String>>();
        String CIV_EMR_TYPE = ConfigCache.getCache(oid, "CIV_EMR_TYPE");
        String[] strs = CIV_EMR_TYPE.split(";");
        for (int i = 0; i < strs.length; i++) {
            Map<String, String> type = new HashMap<String, String>();
            String[] config = strs[i].split(",");
            type.put("code", config[0]);
            type.put("name", config[1]);
            list.add(type);
        }
        return list;
    }

    /**
     * @param type 模块名称
     * @return 启用状态 true|false
     * @Description 方法描述: 获取全局配置中各个模块的启用状态
     */
    public static String getGlobalUseStatus(String oid, String type) {
        String StartUse_OrderClose = ConfigCache.getCache(oid, "StartUse_OrderClose");
        String StartUse_OrderRationality = ConfigCache.getCache(oid, "StartUse_OrderRationality");
        String StartUse_OrderComplete = ConfigCache.getCache(oid, "StartUse_OrderComplete");
        String StartUse_AllergyList = ConfigCache.getCache(oid, "StartUse_AllergyList");
        String StartUse_AlikeEmr = ConfigCache.getCache(oid, "StartUse_AlikeEmr");
        String StartUse_HidePatKeyMessage = ConfigCache.getCache(oid, "StartUse_HidePatKeyMessage");
        String CivVisit_CalcNum = ConfigCache.getCache(oid, "CivVisit_CalcNum");
        String status = "false";
        //获取各个模块的启用状态
        if ("ORDER_CLOSE".equals(type)) {
            status = StartUse_OrderClose;
        } else if ("ORDER_RATIONALITY".equals(type)) {
            status = StartUse_OrderRationality;
        } else if ("ORDER_COMPLETE".equals(type)) {
            status = StartUse_OrderComplete;
        } else if ("ALLERGY_LIST".equals(type)) {
            status = StartUse_AllergyList;
        } else if ("ALIKE_EMR".equals(type)) {
            status = StartUse_AlikeEmr;
        } else if ("HIDE_PAT_KMSG".equals(type)) {
            status = StartUse_HidePatKeyMessage;
        } else if ("CIVVISIT_CALCNUM".equals(type)) {
            status = CivVisit_CalcNum;
        }
        return status;
    }

    public static Map<String, String> getOCLOuterURL(String oid) {
        Map<String, String> map = new HashMap<String, String>();
        String OCLOuterURL = ConfigCache.getCache(oid, "OCLOuterURL");
        map.putAll(Utils.StrSplitToMap(OCLOuterURL, ";", ","));
        return map;
    }

    /**
     * 医嘱合理性中诊断根据编码或者名称来匹配
     *
     * @return code name
     */
    public static String getORDiagMathType(String oid) {
        String ORDiagMathType = ConfigCache.getCache(oid, "ORDiagMathType");
        return StringUtils.isNotBlank(ORDiagMathType) ? ORDiagMathType : "code";
    }

    /**
     * 获取闭环调用的WS地址
     *
     * @return
     */
    public static String getOCLWSURL(String oid) {
        String OCLWSURL = ConfigCache.getCache(oid, "OCLWSURL");
        return StringUtils.isNotBlank(OCLWSURL) ? OCLWSURL : "";
    }

    /**
     * 获取检验报告明细排序
     *
     * @return
     */
    public static String getHDR_LAB_REPORT_DETAIL_SORT(String oid) {
        String sort = ConfigCache.getCache(oid, "HDR_LAB_REPORT_DETAIL_SORT");
        return StringUtils.isNotBlank(sort) ? sort : "TEST_NO";
    }

    /**
     * 获取医嘱状态
     * 配置示例：撤销,废弃
     *
     * @return
     */
    public static String getOrderStatus(String oid) {
        String orderStatus = ConfigCache.getCache(oid, "ORDER_STATUS");
        return StringUtils.isNotBlank(orderStatus) ? orderStatus : "";
    }

    /**
     * 获取闭环调用的WS命名空间
     *
     * @return
     */
    public static String getOCLWSNameSpace(String oid) {
        String OCLWSNameSpace = ConfigCache.getCache(oid, "OCLWSNameSpace");
        return StringUtils.isNotBlank(OCLWSNameSpace) ? OCLWSNameSpace : "http://ws.edi.hdr.goodwill.com/";
    }

    /**
     * 获取主诊断配置
     *
     * @return MDTABLE：诊断表名，MDCOLUMN：主诊断类型列名，MDVALUE：主诊断类型值，MDIsUseSubNum 是否使用诊断子序号来确定主诊断
     */
    public static Map<String, String> getMainDiagConfig(String oid) {
        String MDTABLE = ConfigCache.getCache(oid, "MDTABLE");
        String MDCOLUMN = ConfigCache.getCache(oid, "MDCOLUMN");
        String MDVALUE = ConfigCache.getCache(oid, "MDVALUE");
        String MDIsUseSubNum = ConfigCache.getCache(oid, "MDIsUseSubNum");
        Map<String, String> map = new HashMap<String, String>();
        map.put("MDTABLE", Utils.objToStr(MDTABLE, "HDR_EMR_CONTENT_DIAG"));
        map.put("MDCOLUMN", Utils.objToStr(MDCOLUMN, "DIAGNOSIS_PROPERTY_CODE"));
        map.put("MDVALUE", Utils.objToStr(MDVALUE, "2"));
        map.put("MDIsUseSubNum", Utils.objToStr(MDIsUseSubNum, "TRUE"));
        return map;
    }

    /**
     * 获取医嘱分类
     *
     * @param type EXAM,LAB,DRUG,OPER,NURSE,PATHOLOGY,TREAT,
     *             ORDERCLOSE_DRUG_KF,ORDERCLOSE_DRUG_JMZS,ORDERCLOSE_EXAM,ORDERCLOSE_LAB,ORDERCLOSE_OPER
     * @return 1, 放射类;2,检查类;19,其他检查类;
     * 0,定量检验;3,化验类;20,其他检验类;z,其他类;
     * c,片剂药品;d,大输液;e,成组医嘱;f,针剂药品;s,自备药类18，病理类;
     */
    public static List<String> getOrderClass(String oid, String type) {
        List<String> examList = new ArrayList<String>();
        String configString = "";
        String OrderClass_EXAM = ConfigCache.getCache(oid, "OrderClass_EXAM");
        String OrderClass_LAB = ConfigCache.getCache(oid, "OrderClass_LAB");
        String OrderClass_DRUG = ConfigCache.getCache(oid, "OrderClass_DRUG");
        String OrderClass_OPER = ConfigCache.getCache(oid, "OrderClass_OPER");
        String OrderClass_NURSE = ConfigCache.getCache(oid, "OrderClass_NURSE");
        String OrderClass_PATHOLOGY = ConfigCache.getCache(oid, "OrderClass_PATHOLOGY");
        String OrderClass_TREAT = ConfigCache.getCache(oid, "OrderClass_TREAT");
        String OrderClose_DRUG_KF = ConfigCache.getCache(oid, "OrderClose_DRUG_KF");
        String OrderClose_DRUG_JMZS = ConfigCache.getCache(oid, "OrderClose_DRUG_JMZS");
        String OrderClose_EXAM = ConfigCache.getCache(oid, "OrderClose_EXAM");
        String OrderClose_LAB = ConfigCache.getCache(oid, "OrderClose_LAB");
        String OrderClose_OPER = ConfigCache.getCache(oid, "OrderClose_OPER");
        switch (type) {
            case "EXAM":
                configString = OrderClass_EXAM;
                break;
            case "LAB":
                configString = OrderClass_LAB;
                break;
            case "DRUG":
                configString = OrderClass_DRUG;
                break;
            case "OPER":
                configString = OrderClass_OPER;
                break;
            case "NURSE":
                configString = OrderClass_NURSE;
                break;
            case "PATHOLOGY":
                configString = OrderClass_PATHOLOGY;
                break;
            case "TREAT":
                configString = OrderClass_TREAT;
                break;
            case "ORDERCLOSE_DRUG_KF":
                configString = OrderClose_DRUG_KF;
                break;
            case "ORDERCLOSE_DRUG_JMZS":
                configString = OrderClose_DRUG_JMZS;
                break;
            case "ORDERCLOSE_EXAM":
                configString = OrderClose_EXAM;
                break;
            case "ORDERCLOSE_LAB":
                configString = OrderClose_LAB;
                break;
            case "ORDERCLOSE_OPER":
                configString = OrderClose_OPER;
                break;
            default:
                break;
        }
        if (StringUtils.isNotBlank(configString)) {
            String[] items = configString.split(",");
            for (String item : items) {
                examList.add(item);
            }
        }
        return examList;
    }

    /**
     * 规则中的判断标准转换为检验结果中的检验项结果状态
     *
     * @param judgStandard
     * @return
     */
    public static String JudgStandardToResultStatusCode(String oid, String judgStandard) {
        String RESULTSTATUSCODE_UP = ConfigCache.getCache(oid, "RESULTSTATUSCODE_UP");
        String RESULTSTATUSCODE_DOWN = ConfigCache.getCache(oid, "RESULTSTATUSCODE_DOWN");
        String resultString = "";
        if (judgStandard.equals("1")) {
            resultString = RESULTSTATUSCODE_UP;
        }
        if (judgStandard.equals("2")) {
            resultString = RESULTSTATUSCODE_DOWN;
        }
        return resultString;
    }

    /**
     * 获取检验状态升高值
     *
     * @return
     */
    public static String getResultStatusCodeUp(String oid) {
        String RESULTSTATUSCODE_UP = ConfigCache.getCache(oid, "RESULTSTATUSCODE_UP");
        return Utils.objToStr(RESULTSTATUSCODE_UP, "H");
    }

    /**
     * 获取检验状态降低值
     *
     * @return
     */
    public static String getResultStatusCodeDown(String oid) {
        String RESULTSTATUSCODE_DOWN = ConfigCache.getCache(oid, "RESULTSTATUSCODE_DOWN");
        return Utils.objToStr(RESULTSTATUSCODE_DOWN, "L");
    }

    /**
     * 获取医嘱字典前缀
     *
     * @param type OPER
     * @return
     */
    public static String getOrderDictCode(String oid, String type) {
        String OPER_CODE = ConfigCache.getCache(oid, "OPER_CODE");
        return Utils.objToStr(OPER_CODE, "1.2.156.112636.1.1.2.1.4.13");
    }

    /**
     * 获取体检视图单次体检表头配置
     *
     * @return
     */
    public static String getMedicalTableConfig(String oid) {
        String MEDICAL_TABLE_CONFIG = ConfigCache.getCache(oid, "MEDICAL_TABLE_CONFIG");
        return StringUtils.isNotBlank(MEDICAL_TABLE_CONFIG) ? MEDICAL_TABLE_CONFIG
                : "首页,summary,summary;一般体检,1000,common_check;内科,2000,in_check;外科,3000,out_check;眼科,4000,eye_check;耳鼻喉科,5000,ent;体检报告,D,lab;检查报告,E,exam";
    }

    public static String getMEDICAL_COMMON_CHECK_CONFIG(String oid) {
        String medical_common_check_config = ConfigCache.getCache(oid, "MEDICAL_COMMON_CHECK_CONFIG");
        return StringUtils.isNotBlank(medical_common_check_config) ? medical_common_check_config
                : "身高,体重,血压,脉率,体重指数;MEDICAL_ITEM_NAME|in|身高,体重,血压,脉率,体重指数";
    }

    public static String getMEDICAL_IN_CHECK_CONFIG(String oid) {
        String value = ConfigCache.getCache(oid, "MEDICAL_IN_CHECK_CONFIG");
        return StringUtils.isNotBlank(value) ? value
                : "既往史,巩膜黄染,心界,心脏,心律,杂音,肺部,腹部,肝,脾,双肾,神经系统,一般状况,其他;MEDICAL_ITEM_NAME|in|既往史,巩膜黄染,心界,心脏,心律,杂音,肺部,腹部,肝,脾,双肾,神经系统,一般状况,其他";
    }

    public static String getMEDICAL_OUT_CHECK_CONFIG(String oid) {
        String value = ConfigCache.getCache(oid, "MEDICAL_OUT_CHECK_CONFIG");
        return StringUtils.isNotBlank(value) ? value
                : "病史,皮肤,淋巴结,甲状腺,乳腺,脊柱,四肢关节,外生殖器,前列腺,肛门,其他;MEDICAL_ITEM_NAME|in|病史,皮肤,淋巴结,甲状腺,乳腺,脊柱,四肢关节,外生殖器,前列腺,肛门,其他";
    }

    public static String getMEDICAL_EYE_CHECK_CONFIG(String oid) {
        String value = ConfigCache.getCache(oid, "MEDICAL_EYE_CHECK_CONFIG");
        return StringUtils.isNotBlank(value) ? value
                : "视力(左),视力(右),矫正视力左,矫正视力右,辨色力,眼脸结膜,角膜,晶状体,眼底检查,其他;MEDICAL_ITEM_NAME|in|视力(左),视力(右),矫正视力左,矫正视力右,辨色力,眼脸结膜,角膜,晶状体,眼底检查,其他";
    }

    public static String getMEDICAL_ENT_CHECK_CONFIG(String oid) {
        String value = ConfigCache.getCache(oid, "MEDICAL_ENT_CHECK_CONFIG");
        return StringUtils.isNotBlank(value) ? value
                : "耳,右耳听力,外耳通,鼓膜,鼻,咽喉,扁桃体,其他;MEDICAL_ITEM_NAME|in|耳,右耳听力,外耳通,鼓膜,鼻,咽喉,扁桃体,其他";
    }

    public static String getDictTableNameString(String oid) {
        String DICTTABLENAME = ConfigCache.getCache(oid, "DICTTABLENAME");
        return Utils.objToStr(DICTTABLENAME, "HDR_DICT_ITEM");
    }



    /**
     * 获取男性编码
     *
     * @return
     */
    public static String getSexMaleCode(String oid) {
        String SEX_MALE_CODE = ConfigCache.getCache(oid, "SEX_MALE_CODE");
        return Utils.objToStr(SEX_MALE_CODE, "1");
    }

    /**
     * 获取女性编码
     *
     * @return
     */
    public static String getSexFeMaleCode(String oid) {
        String SEX_FEMALE_CODE = ConfigCache.getCache(oid, "SEX_FEMALE_CODE");
        return Utils.objToStr(SEX_FEMALE_CODE, "2");
    }

    /**
     * 获取住院静脉输液药品医嘱查询条件
     *
     * @return
     */
    public static String getOCLJMZSFilter(String oid) {
        String OCL_JMZSFilter = ConfigCache.getCache(oid, "OCL_JMZSFilter");
        return Utils.objToStr(OCL_JMZSFilter, "");
    }

    /**
     * @return
     * @Description 方法描述: 获取住院口服药品医嘱查询条件
     */
    public static String getOCLKFFilter(String oid) {
        String OCL_KFFilter = ConfigCache.getCache(oid, "OCL_KFFilter");
        return Utils.objToStr(OCL_KFFilter, "");
    }

    /**
     * @return
     * @Description 方法描述: 获取住院其他药品医嘱查询条件
     */
    public static String getOCLQTFilter(String oid) {
        String OCL_QTFilter = ConfigCache.getCache(oid, "OCL_QTFilter");
        return Utils.objToStr(OCL_QTFilter, "");
    }

    /**
     * 获取门诊静脉输液药品医嘱查询条件
     *
     * @return
     */
    public static String getOclMzJmzsfilter(String oid) {
        String OCL_MZ_JMZSFilter = ConfigCache.getCache(oid, "OCL_MZ_JMZSFilter");
        return Utils.objToStr(OCL_MZ_JMZSFilter, "");
    }

    /**
     * @return
     * @Description 方法描述: 获取门诊口服药品医嘱查询条件
     */
    public static String getOclMzKffilter(String oid) {
        String OCL_MZ_KFFilter = ConfigCache.getCache(oid, "OCL_MZ_KFFilter");
        return Utils.objToStr(OCL_MZ_KFFilter, "");
    }

    /**
     * @return
     * @Description 方法描述: 获取门诊口服药品医嘱查询条件
     */
    public static String getOclMzQTfilter(String oid) {
        String OCL_MZ_QTFilter = ConfigCache.getCache(oid, "OCL_MZ_QTFilter");
        return Utils.objToStr(OCL_MZ_QTFilter, "");
    }

    /**
     * @return
     * @Description 方法描述: 获取闭环远程调用的wsdl
     */
    public static String getOCLWSDL(String oid) {
        String OCL_WS_WSDL = ConfigCache.getCache(oid, "OCL_WS_WSDL");
        return Utils.objToStr(OCL_WS_WSDL, "http://**************:8888/ocl/ws/queryOcl?wsdl");
    }

    /**
     * @return
     * @Description 方法描述: 获取闭环远程调用的命名空间
     */
    public static String getOCLNAMESPACE(String oid) {
        String OCL_WS_NAMESPACE = ConfigCache.getCache(oid, "OCL_WS_NAMESPACE");
        return Utils.objToStr(OCL_WS_NAMESPACE, "http://webservice.ocl.goodwill.com/");
    }

    public static String getMDFilter2(String oid) {
        String MDFilter2 = ConfigCache.getCache(oid, "MDFilter2");
        //默认返回诊断序号为1的数据
        if (StringUtils.isBlank(MDFilter2)) {
            return "DIAGNOSIS_NUM|=|1";
        }
        return MDFilter2;
    }

    /**
     * 获取输血闭环查询配置
     *
     * @return
     */
    public static String getOCLBloodListFilter(String oid) {
        String OCL_BloodListFilter = ConfigCache.getCache(oid, "OCL_BloodListFilter");
        return Utils.objToStr(OCL_BloodListFilter, "");
    }

    public static String getInpSummaryTableName(String oid) {
        String inpSummaryTableName = ConfigCache.getCache(oid, "inpSummaryTableName");
        if (StringUtils.isBlank(inpSummaryTableName)) {
            return "HDR_INP_SUMMARY";
        } else {
            return inpSummaryTableName;
        }
    }

    /**
     * 获取孕妇诊断
     *
     * @return
     */
    public static String getPMDiag(String oid) {
        String PMDiag = ConfigCache.getCache(oid, "PMDiag");
        return Utils.objToStr(PMDiag, "O26.501,O36.102");
    }

    /**
     * @return
     * @Description 方法描述: 获取过敏类型
     */
    public static String getAllergyTypes(String oid) {
        String ALLERGY_TYPES = ConfigCache.getCache(oid, "ALLERGY_TYPE");
        return Utils.objToStr(ALLERGY_TYPES, "1|一般药物过敏;2|特殊药物过敏;3|药物成分过敏;4|食物过敏;5|海鲜过敏;6|花粉过敏;7|不明");
    }

    /**
     * @return
     * @Description 方法描述: 获取过敏类型
     */
    public static String getAllergySeverity(String oid) {
        String allergy_severity = ConfigCache.getCache(oid, "ALLERGY_SEVERITY");
        return Utils.objToStr(allergy_severity, "1|轻度;2|中度;3|严重");
    }

    /**
     * @return
     * @Description 方法描述: 获取过敏类型
     */
    public static String getAllergySeverityFieldName(String oid) {
        String allergy_severity = ConfigCache.getCache(oid, "ALLERGY_SEVERITY_FIELD_NAME");
        return Utils.objToStr(allergy_severity, "ALLERGY_SEVERITY");
    }

    /**
     * @return 配置字符串
     * @Description 方法描述: 获取病历文书类型配置
     */
    public static String getEmrTypeList(String oid) {
        String emr_type_list = ConfigCache.getCache(oid, "EMR_TYPE_LIST");
        return Utils.objToStr(emr_type_list, "1|病历记录;2|手术记录;3|出院记录;4|检查记录");
    }

    /**
     * @return
     * @Description 方法描述: 获取时间轴周类型
     */
    public static List<String> getWeekTypes(String oid) {
        if (WEEK_TYPE.size() == 0) {
            WEEK_TYPE.add("第一周");
            WEEK_TYPE.add("第二周");
            WEEK_TYPE.add("第三周");
            WEEK_TYPE.add("第四周");
            WEEK_TYPE.add("第五周");
            WEEK_TYPE.add("第六周");
            WEEK_TYPE.add("第七周");
            WEEK_TYPE.add("第八周");
            WEEK_TYPE.add("第九周");
            WEEK_TYPE.add("第十周");
        }
        return WEEK_TYPE;
    }

    /**
     * 获取页面名称
     *
     * @param value
     * @return
     */
    public static String getPageName(String oid, String value) {
        String key = "CIV_PAGE_" + value;
        String result = ConfigCache.getCache(oid, key);
        return result;
    }

    /**
     * 电子病历是否外部调用
     *
     * @param VISIT_CODE
     * @return
     */
    public static Boolean getEMR_ISCALL(String oid, String VISIT_CODE) {
        String CIV_EMR_IN_ISCALL = ConfigCache.getCache(oid, "CIV_EMR_IN_ISCALL");
        String CIV_EMR_OUT_ISCALL = ConfigCache.getCache(oid, "CIV_EMR_OUT_ISCALL");
        String key = VISIT_CODE.equals("02") ? CIV_EMR_IN_ISCALL : CIV_EMR_OUT_ISCALL;
        String value = StringUtils.isNotBlank(key) && "true".equals(key) ? "true" : "false";
        return Boolean.valueOf(value);
    }

    public static String getEMR_ISCALL_TYPE(String oid, String VISIT_CODE) {
        String CIV_EMR_IN_ISCALL_TYPE = ConfigCache.getCache(oid, "CIV_EMR_IN_ISCALL_TYPE");
        String CIV_EMR_OUT_ISCALL_TYPE = ConfigCache.getCache(oid, "CIV_EMR_OUT_ISCALL_TYPE");
        String key = VISIT_CODE.equals("02") ? CIV_EMR_IN_ISCALL_TYPE : CIV_EMR_OUT_ISCALL_TYPE;
        return StringUtils.isNotBlank(key) ? key : "";
    }

    public static String getEMR_ISCALL_URL(String oid, String VISIT_CODE) {
        String CIV_EMR_IN_ISCALL_URL = ConfigCache.getCache(oid, "CIV_EMR_IN_ISCALL_URL");
        String CIV_EMR_OUT_ISCALL_URL = ConfigCache.getCache(oid, "CIV_EMR_OUT_ISCALL_URL");
        String key = VISIT_CODE.equals("02") ? CIV_EMR_IN_ISCALL_URL : CIV_EMR_OUT_ISCALL_URL;
        return StringUtils.isNotBlank(key) ? key : "";
    }

    /**
     * 门诊就诊查询条件
     *
     * @param
     * @return
     */
    public static String getCIV_OUT_VISIT_FILTER(String oid) {
        String CIV_OUT_VISIT_FILTER = ConfigCache.getCache(oid, "CIV_OUT_VISIT_FILTER");
        return StringUtils.isNotBlank(CIV_OUT_VISIT_FILTER) ? CIV_OUT_VISIT_FILTER : "";
    }
    /**
     * 住院就诊查询条件
     *
     * @param
     * @return
     */
    public static String getCIV_IN_VISIT_FILTER(String oid) {
        String CIV_IN_VISIT_FILTER = ConfigCache.getCache(oid, "CIV_IN_VISIT_FILTER");
        return StringUtils.isNotBlank(CIV_IN_VISIT_FILTER) ? CIV_IN_VISIT_FILTER : "";
    }

    /**
     * 检查报告查看URL
     *
     * @return
     */
    public static String getPERSONAL_HOSPITAL_FLAG(String oid) {
        String PERSONAL_HOSPITAL_FLAG = ConfigCache.getCache(oid, "PERSONAL_HOSPITAL_FLAG");
        return StringUtils.isBlank(PERSONAL_HOSPITAL_FLAG) ? "" : PERSONAL_HOSPITAL_FLAG;
    }

    public static String getCIV_ORDER_EXAM_MATCHTYPE(String oid) {
        String CIV_ORDER_EXAM_MATCHTYPE = ConfigCache.getCache(oid, "CIV_ORDER_EXAM_MATCHTYPE");
        return StringUtils.isBlank(CIV_ORDER_EXAM_MATCHTYPE) ? "=" : CIV_ORDER_EXAM_MATCHTYPE;
    }

    public static String getVISIT_VIEW_OPERATION_CONFIG(String oid) {
        String VISIT_VIEW_OPERATION_CONFIG = ConfigCache.getCache(oid, "VISIT_VIEW_OPERATION_CONFIG");
        return VISIT_VIEW_OPERATION_CONFIG;
    }

    /**
     * 获取配置通用方法：此方法无默认值
     *
     * @param config
     * @return
     */
    public static String getConfigValue(String oid, String config) {
        String value = ConfigCache.getCache(oid, config);
        return value;
    }

    /**
     * 以list形式获取配置通用方法：此方法无默认值
     *
     * @param config
     * @return
     */
    public static List getConfigValueAsList(String oid, String config) {
        String value = ConfigCache.getCache(oid, config);
        List<String> result = new ArrayList<>();
        if (StringUtils.isNotBlank(value)) {
            String[] values = value.split(",");
            for (String val : values) {
                result.add(val);
            }
        }
        return result;
    }

    public static String getCIV_RPC_DEFAULT_PAGE(String oid) {
        String value = ConfigCache.getCache(oid, "CIV_RPC_DEFAULT_PAGE");
        return StringUtils.isNotBlank(value) ? value : "civ/jsp/visitView/visitView.jsp";
    }

    /**
     * 获取配置通用方法：
     *
     * @param config
     * @param defaultValue
     * @return
     */
    public static String getConfigValue(String oid, String config, String defaultValue) {
        String value = ConfigCache.getCache(oid, config);
        if (StringUtils.isBlank(value)) {
            value = defaultValue;
        }
        return value;
    }

    /**
     * 就诊试图住院医嘱手术条件
     *
     * @return
     */
    public static void setVisitViewOperationFilter(String oid, List<PropertyFilter> filters) {
        String VISIT_VIEW_OPERATION_CONFIG = ConfigCache.getCache(oid, "VISIT_VIEW_OPERATION_CONFIG");
        if (StringUtils.isNotBlank(VISIT_VIEW_OPERATION_CONFIG)) {
            String[] values = VISIT_VIEW_OPERATION_CONFIG.split("\\|");
            if (null != values && values.length > 0) {
                PropertyFilter filter1 = new PropertyFilter();
                filter1.setMatchType(values[1]);
                filter1.setPropertyName(values[0]);
                filter1.setPropertyValue(values[2]);
                //filter1.setPropertyType("STRING");
                filters.add(filter1);
            }
        }
    }

    /**
     * 检查报告状态条件
     *
     * @return
     */
    public static void setExamStatusFilter(String oid, List<PropertyFilter> filters) {
        String EXAM_STATUS_CONFIG = ConfigCache.getCache(oid, "EXAM_STATUS_CONFIG");
        if (StringUtils.isNotBlank(EXAM_STATUS_CONFIG)) {
            String[] configs = EXAM_STATUS_CONFIG.split(";");
            for (String config : configs) {
                String[] values = config.split("\\|");
                if (null != values && values.length > 0) {
                    PropertyFilter filter1 = new PropertyFilter();
                    filter1.setMatchType(values[1]);
                    filter1.setPropertyName(values[0]);
                    filter1.setPropertyValue(values[2]);
                    //filter1.setPropertyType("STRING");
                    filters.add(filter1);
                }
            }
        }
    }

    /**
     * 获取默认打开的页面
     *
     * @return
     */
    public static String getCIV_DEFAULT_PAGE(String oid) {
        String value = ConfigCache.getCache(oid, "CIV_DEFAULT_PAGE");
        return StringUtils.isNotBlank(value) ? value : "pages/jsp/visitView/visitView.jsp";
    }


    /**
     * @return
     */
    public static Map<String, String> getCIV_CATEGARY_ORDER_SHOW_CONFIG(String oid) {
        String value = ConfigCache.getCache(oid, "CIV_CATEGARY_ORDER_SHOW_CONFIG");
        if (StringUtils.isBlank(value)) {
            value = "1";
        }
        Map<String, String> map = new HashMap<String, String>();
        map.put("showOrder", value);
        return map;
    }

    /**
     * 患者统一视图logo信息
     *
     * @return
     */
    public static Map<String, Object> getLogoInfo(String oid) {
        String isUseLogo = ConfigCache.getCache(oid, "IF_LOGO_SWITCH");
        String logoFileName = ConfigCache.getCache(oid, "HOSPITAL_LOGO_FILE_NAME");
        String hospitalName = ConfigCache.getCache(oid, "HOSPITAL_LOGO_NAME");
        Map<String, Object> res = new HashMap<>();
        res.put("isUseLogo", "true".equals(isUseLogo) ? true : false);
        res.put("fileName", logoFileName);
        res.put("hospitalName", hospitalName);
        return res;
    }

    /**
     * 过敏程度过滤条件是否在页面展示
     * 默认展示，ALLERGY_FILTER_SWITCH=false 不展示
     *
     * @return
     */
    public static Map<String, Object> getAllergyFilter(String oid) {
        String allergyFilterSwitch = ConfigCache.getCache(oid, "ALLERGY_FILTER_SWITCH");
        Map<String, Object> res = new HashMap<>();
        res.put("result", "1");
        if ("false".equalsIgnoreCase(allergyFilterSwitch)) {
            res.put("result", "0");
        }

        return res;
    }

    /**
     * 切换患者按钮配置项
     * 默认展示，PATIENT_LIST_SWITCH=false 不展示
     *
     * @return
     */
    public static Map<String, Object> getExchangePatient(String oid) {
        String patientSwitch = ConfigCache.getCache(oid, "PATIENT_LIST_SWITCH");
        Map<String, Object> res = new HashMap<>();
        res.put("result", "1");
        if ("false".equalsIgnoreCase(patientSwitch)) {
            res.put("result", "0");
        }
        return res;
    }

    /**
     * 护理记录调用第三方的url 护理类型配置
     *
     * @return
     */
    public static Map<String, Object> getCIV_NURSE_URL_TYPES(String oid) {
        Map<String, Object> mapRes = new HashMap<String, Object>();
        String value = ConfigCache.getCache(oid, "CIV_NURSE_URL_TYPES");
        String[] values = value.split("\\|");
        List<Map<String, String>> rs = new ArrayList<Map<String, String>>();
        if (null != values && values.length > 1) {
            mapRes.put("field", values[0]);
            String[] type = values[1].split(";");
            for (String typeOne : type) {
                String[] typeOness = typeOne.split(",");
                if (null != typeOness && typeOness.length > 1) {
                    Map<String, String> map = new HashMap<String, String>();
                    map.put("code", typeOness[0]);
                    map.put("name", typeOness[1]);
                    rs.add(map);
                }
            }
        }
        mapRes.put("data", rs);
        return mapRes;
    }

    /**
     * 护理记录调用第三方的url 护理类型配置ziduan
     *
     * @return
     */
    public static String getCIV_NURSE_URL_FIELD(String oid) {
        String value = ConfigCache.getCache(oid, "CIV_NURSE_URL_TYPES");
        String[] values = value.split("\\|");
        if (null != values && values.length > 0) {
            return values[0];
        }
        return "";
    }

    /**
     * 获取检查报告 查看检查报告，影响浏览等url配置
     *
     * @return
     */
    public static List<Map<String, String>> getCIV_CHECK_REPORT_URL_CONFIG(String oid) {
        List<Map<String, String>> list = new ArrayList<Map<String, String>>();
        String value = ConfigCache.getCache(oid, "CIV_CHECK_REPORT_URL_CONFIG");
        if (StringUtils.isBlank(value)) {
            value = "查看检查报告,PACS_URL,iframe";
        }
        String[] values = value.split("\\|");
        for (String config : values) {
            String[] configs = config.split(",");
            if (null != configs && configs.length > 1) {
                Map<String, String> map = new HashMap<String, String>();
                map.put("name", configs[0]);
                map.put("field", configs[1]);
                map.put("linkType", configs[2]);
                list.add(map);
            }
        }
        return list;
    }

    public static String getCIV_CHECK_REPORT_URL_PARAM_CONFIG(String oid) {
        String value = ConfigCache.getCache(oid, "CIV_CHECK_REPORT_URL_PARAM_CONFIG");
        return StringUtils.isBlank(value) ? "false" : value;
    }

    /**
     * 获取病理报告 查看病理报告，影响浏览等url配置
     *
     * @return
     */
    public static List<Map<String, String>> getCIV_PATHOLOGY_REPORT_URL_CONFIG(String oid) {
        List<Map<String, String>> list = new ArrayList<Map<String, String>>();
        String value = ConfigCache.getCache(oid, "CIV_PATHOLOGY_REPORT_URL_CONFIG");
        if (StringUtils.isBlank(value)) {
            value = "查看病理报告,PACS_URL,iframe";
        }
        String[] values = value.split("\\|");
        for (String config : values) {
            String[] configs = config.split(",");
            if (null != configs && configs.length > 1) {
                Map<String, String> map = new HashMap<String, String>();
                map.put("name", configs[0]);
                map.put("field", configs[1]);
                map.put("linkType", configs[2]);
                list.add(map);
            }
        }
        return list;
    }

    public static String[] getCIV_PATHOLOGY_REPORT_URL_CONFIG_FIELDS(String oid) {
        String value = ConfigCache.getCache(oid, "CIV_PATHOLOGY_REPORT_URL_CONFIG");
        if (StringUtils.isBlank(value)) {
            value = "查看病理报告,PACS_URL";
        }
        String[] values = value.split("\\|");
        String[] result = new String[values.length];
        for (int i = 0; i < values.length; i++) {
            String config = values[i];
            String[] configs = config.split(",");
            if (null != configs && configs.length > 1) {
                result[i] = configs[1];
            }
        }
        return result;
    }


    /**
     * #检验报告详情查询字段配置
     *
     * @return
     */
    public static String[] getCIV_LAB_REPORT_DETAIL_FIELD(String oid) {
        String value = ConfigCache.getCache(oid, "CIV_LAB_REPORT_DETAIL_FIELD");
        if (StringUtils.isBlank(value)) {
            value = "ROWKEY,LAB_SUB_ITEM_CODE,LAB_SUB_ITEM_NAME,RANGE,LAB_RESULT_UNIT,LAB_RESULT_VALUE,LAB_QUAL_RESULT,RESULT_STATUS_CODE,TEST_NO";
        }
        return value.split(",");
    }

    /**
     * #外院检验报告详情查询字段配置
     *
     * @return
     */
    public static String[] getCIV_LAB_REPORT_DETAIL_FIELD_WY(String oid) {
        String value = ConfigCache.getCache(oid, "CIV_LAB_REPORT_DETAIL_FIELD_WY");
        if (StringUtils.isBlank(value)) {
            value = "ROWKEY,LAB_SUB_ITEM_CODE,LAB_SUB_ITEM_NAME,RANGE,LAB_RESULT_UNIT,LAB_RESULT_VALUE,LAB_QUAL_RESULT,RESULT_STATUS_CODE,WYHR_TYPE,HOSPITAL_NAME,REPORT_URL,TEST_NO";
        }
        return value.split(",");
    }

    /**
     * #检验报告详情显示列配置
     *
     * @return
     */
    public static String getCIV_LAB_REPORT_DETAIL_HEAD(String oid) {
        String value = ConfigCache.getCache(oid, "CIV_LAB_REPORT_DETAIL_HEAD");
        if (StringUtils.isBlank(value)) {
            value = "[{\"name\":\"index\",\"display\":\"序号\"},{\"name\":\"labSubItemCode\",\"display\":\"代码\"},{\"name\":\"labSubItemName\",\"display\":\"项目名称\"},{\"name\":\"labResultValue\",\"display\":\"结果\"},{\"name\":\"range\",\"display\":\"参考范围\"},{\"name\":\"labResultUnit\",\"display\":\"单位\"},{\"name\":\"operation\",\"display\":\"操作\"},{\"name\":\"testNo\",\"display\":\"TEST_NO\"}]";
        }
        return value;
    }

    public static String  getCIV_LAB_REPORT_DETAIL_HEAD_TAB(String oid,String tabCode) {
        String value =  ConfigCache.getCache(oid,"CIV_" + tabCode + "_LAB_REPORT_DETAIL_HEAD");
        if (StringUtils.isBlank(value)) {
            value = "[{\"name\":\"index\",\"display\":\"序号\"},{\"name\":\"labSubItemCode\",\"display\":\"代码\"},{\"name\":\"labSubItemName\",\"display\":\"项目名称\"},{\"name\":\"labResultValue\",\"display\":\"结果\"},{\"name\":\"range\",\"display\":\"参考范围\"},{\"name\":\"labResultUnit\",\"display\":\"单位\"},{\"name\":\"operation\",\"display\":\"操作\"},{\"name\":\"testNo\",\"display\":\"TEST_NO\"}]";
        }
        return value;
    }

    /**
     * 外院检验表头
     * @param oid
     * @param tabCode
     * @return
     */
    public static String  getCIV_LAB_REPORT_DETAIL_HEAD_WY_TAB(String oid,String tabCode) {
        String value =  ConfigCache.getCache(oid,"CIV_" + tabCode + "_LAB_REPORT_DETAIL_HEAD_WY");
        if (StringUtils.isBlank(value)) {
            value = "[{\"name\":\"index\",\"display\":\"序号\"},{\"name\":\"labSubItemCode\",\"display\":\"代码\"},{\"name\":\"labSubItemName\",\"display\":\"项目名称\"},{\"name\":\"labResultValue\",\"display\":\"结果\"},{\"name\":\"range\",\"display\":\"参考范围\"},{\"name\":\"labResultUnit\",\"display\":\"单位\"},{\"name\":\"labResultUnit\",\"display\":\"来源医院\"},{\"name\":\"operation\",\"display\":\"操作\"},{\"name\":\"testNo\",\"display\":\"TEST_NO\"}]";
        }
        return value;
    }

    /**
     * 护理详情查询字段配置
     *
     * @return
     */
    public static String[] getCIV_NURSE_TABLE_FIELD(String oid) {
        String value = ConfigCache.getCache(oid, "CIV_NURSE_TABLE_FIELD");
        if (StringUtils.isBlank(value)) {
            value = "NR_NAME,CAPTION_DATE_TIME,CREATOR_NAME,NR_CONTENT_HTML,CREATE_DATE_TIME,LAST_MODIFY_DATE_TIME,URL,FILE_CLASS,DOCUMENT_ID";
        }
        return value.split(",");
    }

    /**
     * 获取护理单表头设置
     *
     * @return
     */
    public static List<Map<String, String>> getCIV_NURSE_TABLE_HEAD(String oid) {
        String value = ConfigCache.getCache(oid, "CIV_NURSE_TABLE_HEAD");
        if (StringUtils.isBlank(value)) {
            value = "nrName,文书类型;captionDateTime,标题时间;createDateTime,记录时间;creatorName,记录者;nrContentHtml,记录内容";
        }
        List<Map<String, String>> list = new ArrayList<Map<String, String>>();
        for (String config : value.split(";")) {
            String[] configs = config.split(",");
            if (null != configs && configs.length > 1) {
                Map<String, String> map = new HashMap<String, String>();
                map.put("name", configs[0]);
                map.put("display", configs[1]);
                list.add(map);
            }
        }
        return list;
    }

    /**
     * 护理记录是否区分住院和门诊url
     *
     * @return
     */
    public static boolean getCIV_NURSE_URL_OUT_OR_IN(String oid) {
        String value = StringUtils.isBlank(ConfigCache.getCache(oid, "CIV_NURSE_URL_OUT_OR_IN")) ? "false"
                : ConfigCache.getCache(oid, "CIV_NURSE_URL_OUT_OR_IN");
        return value.equalsIgnoreCase("false") ? false : true;
    }

    /**
     * 病历展示優先数据源配置：先取病历表的HTML字段还是  拼接病历章节表数据
     * DG 表示拼接病历章节表数据
     * HTML 表示取病历表的HTML字段
     *
     * @return
     */
    public static String getCIV_EMR_FIRST_DATA_SOURCE(String oid) {
        String value = ConfigCache.getCache(oid, "CIV_EMR_FIRST_DATA_SOURCE");
        return value.equalsIgnoreCase("DG") ? "DG" : "HTML";
    }

    /**
     * 患者列表页是否过滤当前登录用户的科室权限
     *
     * @return
     */
    public static boolean getUserQueryDeptFlag(String oid) {
        String value = ConfigCache.getCache(oid, "CIV_QUERY_CONDITION_DEPT_FLAG");
        return value.equalsIgnoreCase("true") ? true : false;
    }

    /**
     * 患者电子病历文书内容是否脱敏配置
     *
     * @return
     */
    public static String getEMRHiddenConfig(String oid) {
        String value = ConfigCache.getCache(oid, "CIV_XH_EMR_HIDDEN_DATA_CONFIG");
        return value.equalsIgnoreCase("y") ? "Y" : "N";
    }

    /**
     * 获取输血申请查询配置
     *
     * @return
     */
    public static String getBloodApplyFilter(String oid) {
        String booldApplyFilter = ConfigCache.getCache(oid, "BLOOD_APPLY_FILTER");
        return Utils.objToStr(booldApplyFilter, "");
    }

    /**
     * 获取病历文书PDF下载路径
     *
     * @return
     */
    public static String getCIV_EMR_PDF_PATH(String oid) {
        String value = ConfigCache.getCache(oid, "CIV_EMR_PDF_PATH");
        return Utils.objToStr(value, "");
    }
    /**
     * 患者列表排除条件
     *
     * @return
     */
    public static String getHZLBFilter(String oid) {
        String HZLBFilter = ConfigCache.getCache(oid, "HZLBFilter");
        return Utils.objToStr(HZLBFilter, "");
    }
    /**
     * 患者列表是否显示一级科室
     *
     * @return
     */
    public static String getIS_SHOW_PARENT_DEPT(String oid) {
        String IS_SHOW_PARENT_DEPT = ConfigCache.getCache(oid, "IS_SHOW_PARENT_DEPT");
        return Utils.objToStr(IS_SHOW_PARENT_DEPT, "");
    }

    /**
     * 就诊列表是否显示就诊状态
     *
     * @return
     */
    public static String getIS_SHOW_VISIT_STATUS(String oid) {
        String IS_SHOW_VISIT_STATUS = ConfigCache.getCache(oid, "IS_SHOW_VISIT_STATUS");
        return Utils.objToStr(IS_SHOW_VISIT_STATUS, "");
    }
    public static boolean getCheckSort(String oid) {
        String flag = ConfigCache.getCache(oid, "IS_CHECK_SORT");
        return flag.equals("true") ? true : false;
    }
    public static String getCheckSortConfig(String oid) {
        String CHECK_SORT_CONFIG = ConfigCache.getCache(oid, "CHECK_SORT_CONFIG");
        return Utils.objToStr(CHECK_SORT_CONFIG, "");
    }
    public static String getSingleloginUser(String oid) {
        String SINGLELOGIN_URL = ConfigCache.getCache(oid, "SINGLELOGIN_URL");
        return Utils.objToStr(SINGLELOGIN_URL, "");
    }


    /**
     * 获取检验报告 查看检验报告，影响浏览等url配置
     *
     * @return
     */
    public static List<Map<String, String>> getCIV_LAB_REPORT_URL_CONFIG(String oid) {
        List<Map<String, String>> list = new ArrayList<Map<String, String>>();
        String value = ConfigCache.getCache(oid, "CIV_LAB_REPORT_URL_CONFIG");
        if (StringUtils.isBlank(value)) {
            value = "查看检验报告,PACS_URL,iframe";
        }
        String[] values = value.split("\\|");
        for (String config : values) {
            String[] configs = config.split(",");
            if (null != configs && configs.length > 1) {
                Map<String, String> map = new HashMap<String, String>();
                map.put("name", configs[0]);
                map.put("field", configs[1]);
                map.put("linkType", configs[2]);
                list.add(map);
            }
        }
        return list;
    }

    public static String getCIV_LAB_REPORT_URL_PARAM_CONFIG(String oid) {
        String value = ConfigCache.getCache(oid, "CIV_LAB_REPORT_URL_PARAM_CONFIG");
        return StringUtils.isBlank(value) ? "false" : value;
    }

    public static List<Map<String, String>> getSummaryOutpFields(String oid, String configNmae) {
        List<Map<String, String>> list = new ArrayList<>();
        String CIV_VISIT_OUTP_SUNNARY = ConfigCache.getCache(oid, configNmae);
        if (StringUtils.isNotBlank(CIV_VISIT_OUTP_SUNNARY)) {
            String[] str = CIV_VISIT_OUTP_SUNNARY.split(";");
            for (int i = 0; i < str.length; i++) {
                Map<String, String> map = new HashMap<>();
                String[] config = str[i].split(",");
                map.put("name",config[0]);
                map.put("code",config[1]);
                list.add(map);
            }
        }
        return list;
    }

    public static String getSummaryOutpDiagFields(String oid) {
        String VISIT_OUTP_SUMMARY_DIAG_COLUMN = ConfigCache.getCache(oid, "VISIT_OUTP_SUMMARY_DIAG_COLUMN");
        return Utils.objToStr(VISIT_OUTP_SUMMARY_DIAG_COLUMN, "");
    }

    public static String getSummaryOutpOperFields(String oid) {
        String VISIT_OUTP_SUMMARY_OPER_COLUMN = ConfigCache.getCache(oid, "VISIT_OUTP_SUMMARY_OPER_COLUMN");
        return Utils.objToStr(VISIT_OUTP_SUMMARY_OPER_COLUMN, "");
    }

    public static String getSummaryOutpFeeDisplayFields(String oid) {
        String VISIT_OUTP_SUMMARY_FEE_DISPLAY_COLUMN = ConfigCache.getCache(oid, "VISIT_OUTP_SUMMARY_FEE_DISPLAY_COLUMN");
        return Utils.objToStr(VISIT_OUTP_SUMMARY_FEE_DISPLAY_COLUMN, "");
    }

}
