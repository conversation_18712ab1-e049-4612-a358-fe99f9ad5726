package com.goodwill.hdr.civ.controller;


import com.goodwill.hdr.civ.entity.*;
import com.goodwill.hdr.civ.enums.DictType;
import com.goodwill.hdr.civ.service.SpecialtyViewNewService;
import com.goodwill.hdr.civ.service.SpecialtyViewPowerService;
import com.goodwill.hdr.core.orm.Page;
import com.goodwill.hdr.security.utils.SecurityCommonUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Description 类描述：
 * @modify 修改记录：
 */
@RequestMapping("/svpower")
@RestController
@Api(tags = "新版专科视图权限配置")
public class SpecialtyViewPowerAction {
    @Autowired
    private SpecialtyViewNewService specialtyViewNewService;

    @Autowired
    private SpecialtyViewPowerService specialtyViewPowerService;


    /**
     * 获取疾病列表--管理员
     * String userCode = SecurityUtils.getCurrentUserName(); //在线用户
     */
    @ApiOperation(value = "获取疾病列表", notes = "获取疾病列表", httpMethod = "POST")
    @RequestMapping(value = "/getSicknessList", method = RequestMethod.POST)
    public Page<Sickness> getSicknessList(String keyWord, int pageNo, int pageSize) {
        Page<Sickness> page = specialtyViewNewService.getSicknessList(keyWord, pageNo, pageSize);
        return page;
    }

    /**
     * 获取科室列表--管理员
     * String userCode = SecurityUtils.getCurrentUserName(); 在线用户
     */
    @ApiOperation(value = "获取科室列表", notes = "获取科室列表", httpMethod = "POST")
    @RequestMapping(value = "/getDeptList", method = RequestMethod.POST)
    public Page<SpecialtyDept> getDeptList(String keyWord, int pageNo, int pageSize) {
        Page<SpecialtyDept> page = specialtyViewNewService.getDeptList(keyWord, pageNo, pageSize);
        return page;
    }

    /**
     * 疾病列表删除疾病
     */
    @ApiOperation(value = "疾病列表删除疾病", notes = "疾病列表删除疾病", httpMethod = "POST")
    @RequestMapping(value = "/delSickness", method = RequestMethod.POST)
    public int delSickness(String oid, String scikness_code) {
        int status = specialtyViewPowerService.delSickness(oid, scikness_code);
        return status;
    }

    /**
     * 科室列表删除科室配置
     */
    @ApiOperation(value = "科室列表删除科室配置", notes = "科室列表删除科室配置", httpMethod = "POST")
    @RequestMapping(value = "/delDeptConfig", method = RequestMethod.POST)
    public int delDeptConfig(String oid, String dept_code) {
        int status = specialtyViewPowerService.delDeptConfig(oid, dept_code);
        return status;
    }

    /**
     * 从Hbase字典表里新增数据--诊断疾病
     */
    @ApiOperation(value = "从Hbase字典表里新增数据--诊断疾病", notes = "从Hbase字典表里新增数据--诊断疾病", httpMethod = "POST")
    @RequestMapping(value = "/getSickness", method = RequestMethod.POST)
    public Page<Map<String, String>> getSickness(String oid,String visitId, String keyWord, String isDisposed, int pageNo, int pageSize) {
        if (StringUtils.isBlank(keyWord)) {
            keyWord = "";
        }
        //管理员配置模板诊断
        Page<Sickness> pageDiag = specialtyViewNewService.getSicknessList("", 0, 0);
        //当前用户配置的
        List<Map<String, String>> list = specialtyViewPowerService.getSicknessList(oid);
        //是否只显示已配置模板诊断
        if (StringUtils.isNotBlank(isDisposed) && "1".equals(isDisposed)) {
            Page<Map<String, String>> page = specialtyViewPowerService.removeNoConfigData(oid, pageDiag, list);
            return page;
        } else {
            //查询HBASE数据
            Page<Map<String, String>> page = specialtyViewPowerService.getDictDiagData(oid, visitId,keyWord, pageNo, pageSize, DictType.DIAG);
            page = specialtyViewPowerService.executeDuplicates(oid, page, pageDiag);
            if (!"admin".equals(SecurityCommonUtil.getLoginUserCode())) {
                //非管理员用户可能配置管理员未设置模板的诊断，需要去重
                page = specialtyViewPowerService.executeDuplicatesList(oid, page, list);
            }
            return page;
        }
    }

    /**
     * 从mysql查询科室以供管理员添加配置
     */
    @ApiOperation(value = "查询科室以供管理员添加配置", notes = "查询科室以供管理员添加配置", httpMethod = "POST")
    @RequestMapping(value = "/getSpecialtyDepts", method = RequestMethod.POST)
    public Page<Map<String, String>> getSpecialtyDepts(String keyWord,  int pageNo, int pageSize) {
        //首先查询管理员已经配置过的科室svpower
        Page<Map<String, String>> page = specialtyViewNewService.getDeptListToAdd(keyWord, pageNo, pageSize);
        return page;
    }


    /**
     * 从Hbase字典表里新增数据--生命体征
     */
    @ApiOperation(value = "从Hbase字典表里新增数据--生命体征", notes = "从Hbase字典表里新增数据--生命体征", httpMethod = "POST")
    @RequestMapping(value = "/getSicknessSMTZ", method = RequestMethod.POST)
    public Page<Map<String, String>> getHealth(String oid, String keyWord, String code, String configType, int pageNo, int pageSize) {
        if (StringUtils.isBlank(keyWord)) {
            keyWord = "";
        }
        //读取配置的生命体征
        Page<Map<String, String>> page = new Page<Map<String, String>>();
        if ("diagnose".equals(configType)) {
            page = specialtyViewPowerService.getHealthData(oid, code, keyWord, pageNo, pageSize);
        } else if ("dept".equals(configType)) {
            page = specialtyViewPowerService.getDeptHealthData(oid, code, keyWord, pageNo, pageSize);
        }
        return page;
    }

    /**
     * 从Hbase字典表里新增数据--检验明细
     */
    @ApiOperation(value = "从Hbase字典表里新增数据--检验明细", notes = "从Hbase字典表里新增数据--检验明细", httpMethod = "POST")
    @RequestMapping(value = "/getLabDetails", method = RequestMethod.POST)
    public Page<Map<String, String>> getLabDetail(String oid,String visitId, String keyWord, String code, String configType, int pageNo, int pageSize) {
        if (StringUtils.isBlank(keyWord)) {
            keyWord = "";
        }
        Page<Map<String, String>> page = new Page<Map<String, String>>();
        if ("diagnose".equals(configType)) {
            page = specialtyViewPowerService.getDictData(oid,visitId, code, "LabDetails", keyWord, pageNo, pageSize, DictType.LABSUB);
        } else if ("dept".equals(configType)) {
            page = specialtyViewPowerService.getDeptDictData(oid, visitId,code, "LabDetails", keyWord, pageNo, pageSize, DictType.LABSUB);
        }
        return page;
    }

    /**
     * 从Hbase字典表里新增数据--检查
     */
    @ApiOperation(value = "从Hbase字典表里新增数据--检查", notes = "从Hbase字典表里新增数据--检查", httpMethod = "POST")
    @RequestMapping(value = "/getExams", method = RequestMethod.POST)
    public Page<Map<String, String>> getExam(String oid,String visitId, String keyWord, String code, String configType, int pageNo, int pageSize) {
        if (StringUtils.isBlank(keyWord)) {
            keyWord = "";
        }
        Page<Map<String, String>> page = new Page<Map<String, String>>();
        if ("diagnose".equals(configType)) {
            page = specialtyViewPowerService.getDictData(oid,visitId, code, "Exams", keyWord, pageNo, pageSize, DictType.EXAM);
        } else if ("dept".equals(configType)) {
            page = specialtyViewPowerService.getDeptDictData(oid,visitId, code, "Exams", keyWord, pageNo, pageSize, DictType.EXAM);
        }
        return page;
    }

    /**
     * 从Hbase字典表里新增数据--药品
     */
    @ApiOperation(value = "从Hbase字典表里新增数据--药品", notes = "从Hbase字典表里新增数据--药品", httpMethod = "POST")
    @RequestMapping(value = "/getDrugs", method = RequestMethod.POST)
    public Page<Map<String, String>> getDrag(String oid,String visitId, String keyWord, String code, String configType, int pageNo, int pageSize) {
        if (StringUtils.isBlank(keyWord)) {
            keyWord = "";
        }
        Page<Map<String, String>> page = new Page<Map<String, String>>();
        if ("diagnose".equals(configType)) {
            page = specialtyViewPowerService.getDictData(oid,visitId, code, "Drugs", keyWord, pageNo, pageSize, DictType.DRUG);
        } else if ("dept".equals(configType)) {
            page = specialtyViewPowerService.getDeptDictData(oid,visitId, code, "Drugs", keyWord, pageNo, pageSize, DictType.DRUG);
        }
        return page;
    }

    /**
     * 从Hbase字典表里新增数据--护理及饮食
     */
    @ApiOperation(value = "从Hbase字典表里新增数据--护理及饮食", notes = "从Hbase字典表里新增数据--护理及饮食", httpMethod = "POST")
    @RequestMapping(value = "/getNurseAndEat", method = RequestMethod.POST)
    public Page<Map<String, String>> getNurseAndEat(String oid, String visitId,String keyWord, String code, String configType, int pageNo, int pageSize) {
        if (StringUtils.isBlank(keyWord)) {
            keyWord = "";
        }
        Page<Map<String, String>> page = new Page<Map<String, String>>();
        if ("diagnose".equals(configType)) {
            page = specialtyViewPowerService.getDictData(oid,visitId, code, "NurseAndEat", keyWord, pageNo, pageSize, DictType.NURSE);
        } else if ("dept".equals(configType)) {
            page = specialtyViewPowerService.getDeptDictData(oid, visitId,code, "NurseAndEat", keyWord, pageNo, pageSize, DictType.NURSE);
        }
        return page;
    }

    /**
     * 从Hbase字典表里新增数据--检验
     */
    @ApiOperation(value = "从Hbase字典表里新增数据--检验", notes = "从Hbase字典表里新增数据--检验", httpMethod = "POST")
    @RequestMapping(value = "/getLabs", method = RequestMethod.POST)
    public Page<Map<String, String>> getLab(String oid,String visitId, String keyWord, String code, String configType, int pageNo, int pageSize) {
        if (StringUtils.isBlank(keyWord)) {
            keyWord = "";
        }
        Page<Map<String, String>> page = new Page<Map<String, String>>();
        if ("diagnose".equals(configType)) {
            page = specialtyViewPowerService.getDictData(oid, visitId,code, "Labs", keyWord, pageNo, pageSize, DictType.LAB);
        } else if ("dept".equals(configType)) {
            page = specialtyViewPowerService.getDeptDictData(oid,visitId, code, "Labs", keyWord, pageNo, pageSize, DictType.LAB);
        }
        return page;
    }


    /**
     * 获取诊断下的大项
     */
    @ApiOperation(value = "获取诊断下的大项", notes = "获取诊断下的大项", httpMethod = "POST")
    @RequestMapping(value = "/getItemList", method = RequestMethod.POST)
    public List<SpecialtyConfig> getItemList(String oid, String main_diag) {
        List<SpecialtyConfig> list = specialtyViewPowerService.getItemList(oid, main_diag);
        return list;
    }

    /**
     * 获取专科科室下的大项
     */
    @ApiOperation(value = "获取专科科室下的大项", notes = "获取专科科室下的大项", httpMethod = "POST")
    @RequestMapping(value = "/getSpecityaltyDeptItemList", method = RequestMethod.POST)
    public List<DeptSpecialtyConfig> getSpecityaltyDeptItemList(String oid, String dept_code) {
        List<DeptSpecialtyConfig> list = specialtyViewPowerService.getDeptItemList(oid, dept_code);
        return list;
    }

    /**
     * 获取诊断明细数据
     */
    @ApiOperation(value = "获取诊断明细数据", notes = "获取诊断明细数据", httpMethod = "POST")
    @RequestMapping(value = "/getItemDetail", method = RequestMethod.POST)
    public Page<SpecialtyIndicatorConfigEntity> getItemDetail(String oid, String main_diag, String type_code, int pageNo, int pageSize) {
        Page<SpecialtyIndicatorConfigEntity> page = specialtyViewPowerService.getDetailData(oid, main_diag, type_code, pageNo, pageSize);
        return page;
    }

    /**
     * 添加指标
     */
    @ApiOperation(value = "添加诊断明细", notes = "添加诊断明细", httpMethod = "POST")
    @RequestMapping(value = "/addInditorConfig", method = RequestMethod.POST)
    public Map<String, Integer> addInditorConfig(String oid,String sickness_code,String sickness_name,String item_code,String item_name,String itemIndicatorCode,String itemIndicatorName) {
        Map<String, String> map = new HashMap<String, String>();
        map.put("mainDiag", sickness_code);
        map.put("mainDiagName", sickness_name);
        map.put("itemCode", item_code);
        map.put("itemName", item_name);
        map.put("itemClassCode", "");
        map.put("itemClassName", "");
        map.put("itemIndicatorCode", itemIndicatorCode);
        map.put("itemIndicatorName", itemIndicatorName);
        int num = specialtyViewPowerService.addInditorConfig(oid,map);
        Map<String, Integer> mapRetrun = new HashMap<String, Integer>();
        mapRetrun.put("status", num);
        return mapRetrun ;
    }
    /**
     * 添加科室明细
     */
    @ApiOperation(value = "添加科室明细", notes = "添加科室明细", httpMethod = "POST")
    @RequestMapping(value = "/addDeptInditorConfig", method = RequestMethod.POST)
    public Map<String, Integer> addDeptInditorConfig(String oid,String dept_code,String dept_name,String item_code,String item_name,String itemIndicatorCode,String itemIndicatorName) {
        Map<String, String> map = new HashMap<String, String>();
        map.put("deptCode", dept_code);
        map.put("deptName", dept_name);
        map.put("itemCode", item_code);
        map.put("itemName", item_name);
        map.put("itemClassCode", "");
        map.put("itemClassName", "");
        map.put("itemIndicatorCode", itemIndicatorCode);
        map.put("itemIndicatorName", itemIndicatorName);
        int num = specialtyViewPowerService.addDeptInditorConfig(oid,map);
        Map<String, Integer> mapRetrun = new HashMap<String, Integer>();
        mapRetrun.put("status", num);
        return mapRetrun ;
    }
    /**
     * 获取科室明细数据
     */
    @ApiOperation(value = "获取科室明细数据", notes = "获取科室明细数据", httpMethod = "POST")
    @RequestMapping(value = "/getDeptItemDetail", method = RequestMethod.POST)
    public  Page<Map<String, Object>> getDeptItemDetail(String oid,String dept_code,String type_code,int pageNo,int pageSize) {
        Page<Map<String, Object>> page = specialtyViewPowerService.getDeptDetailData(oid,dept_code, type_code, pageNo, pageSize);
       return  page;
    }
    /**
     * 保存诊断数据到mysql
     */
    @ApiOperation(value = "保存诊断数据到mysql", notes = "保存诊断数据到mysql", httpMethod = "POST")
    @RequestMapping(value = "/addSickness", method = RequestMethod.POST)
    public Map<String, String> addSickness(String oid,String code,String name) {
        Map<String, String> result = new HashMap<String, String>();
        int res = 0;
        if (StringUtils.isNotBlank(code) && StringUtils.isNotBlank(name)) {
            res = specialtyViewPowerService.addSickness(oid,code, name);
        }
        result.put("status",String.valueOf(res));
        return result;
    }
    /**
     * 添加科室配置到mysql
     */
    @ApiOperation(value = "添加科室配置到mysql", notes = "添加科室配置到mysql", httpMethod = "POST")
    @RequestMapping(value = "/addConfigDept", method = RequestMethod.POST)
    public Map<String, String> addConfigDept(String oid,String code,String name) {
        Map<String, String> result = new HashMap<String, String>();
        int res = 0;
        if (StringUtils.isNotBlank(code) && StringUtils.isNotBlank(name)) {
            res = specialtyViewPowerService.addSecurityDept(oid,code, name);
        }
        result.put("status",String.valueOf(res));
        return result;
    }
    /**
     * 根据id删除诊断明细
     */
    @ApiOperation(value = "根据id删除诊断明细", notes = "根据id删除诊断明细", httpMethod = "POST")
    @RequestMapping(value = "/delInditorConfig", method = RequestMethod.POST)
    public Map<String, Integer> delInditorConfig(String oid,String id) {
        int num = specialtyViewPowerService.delInditorConfig(oid,id);
        Map<String, Integer> map = new HashMap<String, Integer>();
        map.put("status", num);
        return map;
    }
    /**
     * 根据id删除科室明细
     */
    @ApiOperation(value = "根据id删除科室明细", notes = "根据id删除科室明细", httpMethod = "POST")
    @RequestMapping(value = "/delDeptInditorConfig", method = RequestMethod.POST)
    public  Map<String, Integer> delDeptInditorConfig(String oid,String id) {
        int num = specialtyViewPowerService.delDeptInditorConfig(oid,id);
        Map<String, Integer> map = new HashMap<String, Integer>();
        map.put("status", num);
        return map;
    }

    /**
     * 获取医生自定义的疾病列表
     */
    @ApiOperation(value = "获取医生自定义的疾病列表", notes = "获取医生自定义的疾病列表", httpMethod = "POST")
    @RequestMapping(value = "/getConfigSickness", method = RequestMethod.POST)
    public List<Map<String, String>> getConfigSickness(String oid,int pageSize,int pageNo) {
        List<Map<String, String>> list = specialtyViewPowerService.getSicknessList(oid);
        Page<Map<String, String>> page = new Page<Map<String, String>>();
        page.setPageSize(pageSize);
        page.setPageNo(pageNo);
        page.setTotalCount(list.size());
        page.setResult(list);
        return list;
    }
}
