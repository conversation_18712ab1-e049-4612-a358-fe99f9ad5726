package com.goodwill.hdr.civ.enums;


import com.goodwill.hdr.core.enums.EnumType;

/**
 * <AUTHOR>
 * @Description 类描述：常用的变量枚举
 * @Date 2016年1月29日
 * @modify 修改记录：
 */
public enum HdrConstantEnum implements EnumType {
    HOSPITAL_BYSY {
        @Override
        public String getCode() {
            return "1.2.156.112636";
        }

        @Override
        public String getLabel() {
            return "北京大学第三医院";
        }
    },
    HOSPITAL_XNYY {
        @Override
        public String getCode() {
            return "600012";
        }

        @Override
        public String getLabel() {
            return "陆军军医大学附属西南医院";
        }
    },
    HOSPITAL_DPYY {
        @Override
        public String getCode() {
            return "600014";
        }

        @Override
        public String getLabel() {
            return "陆军军医大学附属大坪医院";
        }
    },
    HOSPITAL_XQYY {
        @Override
        public String getCode() {
            return "600013";
        }

        @Override
        public String getLabel() {
            return "陆军军医大学附属新桥医院";
        }
    },
    HOSPITAL_HBHS {
        @Override
        public String getCode() {
            return "42008321X";
        }

        @Override
        public String getLabel() {
            return "湖北黄石中心医院";
        }
    },
    HOSPITAL_NYSY {
        @Override
        public String getCode() {
            return "45586249-1";
//			return "1.2.156.112636";
        }

        @Override
        public String getLabel() {
            return "南方医科大学第三医院";
        }
    },
    HOSPITAL_NNEY {
        @Override
        public String getCode() {
            return "49852133-9";
        }

        @Override
        public String getLabel() {
            return "南宁二院";
        }
    },
    HOSPITAL_WHET {
        @Override
        public String getCode() {
            return "1242010044162664X7";
        }

        @Override
        public String getLabel() {
            return "武汉儿童医院";
        }
    },
    HOSPITAL_QFS {
        @Override
        public String getCode() {
            return "49557358-X";
        }

        @Override
        public String getLabel() {
            return "千佛山";
        }
    },
    HOSPITAL_ZDFB {
        @Override
        public String getCode() {
            return "47000327-3";
        }

        @Override
        public String getLabel() {
            return "浙大妇保";
        }
    },
    HOSPITAL_BJXH {
        @Override
        public String getCode() {
            return "400012916";
        }

        @Override
        public String getLabel() {
            return "北京协和";
        }
    },
    HOSPITAL_SJT {
        @Override
        public String getCode() {
            return "400003235";
        }

        @Override
        public String getLabel() {
            return "首都医科大学附属北京世纪坛医院";
        }
    },
    HOSPITAL_JDF_HF {
        @Override
        public String getCode() {
            return "H34017300026";
        }

        @Override
        public String getLabel() {
            return "京东方合肥院区";
        }
    },
    HOSPITAL_JDF_SZ {
        @Override
        public String getCode() {
            return "H32050902566";
        }

        @Override
        public String getLabel() {
            return "京东方苏州院区";
        }
    },
    HOSPITAL_JDF_CD {
        @Override
        public String getCode() {
            return "H51011605598";
        }

        @Override
        public String getLabel() {
            return "京东方成都院区";
        }
    },
    HOSPITAL_BJTR {
        @Override
        public String getCode() {
            return "400686347";
        }

        @Override
        public String getLabel() {
            return "北京同仁医院";
        }
    },
    HOSPITAL_KMFY {
        @Override
        public String getCode() {
            return "H53010200215";
        }

        @Override
        public String getLabel() {
            return "昆明市妇幼保健院";
        }
    },
    HOSPITAL_ZQZYY {
        @Override
        public String getCode() {
            return "12370181493120325G";
        }

        @Override
        public String getLabel() {
            return "济南市章丘区中医医院(新院区)";
        }
    },
    HOSPITAL_BJYA {
        @Override
        public String getCode() {
            return "400686486";
        }

        @Override
        public String getLabel() {
            return "首都医科大学附属北京佑安医院";
        }
    },

    /**
     * 申请科室
     */
    HDR_APPLY_DEPTT {
        @Override
        public String getCode() {
            return "HDR_APPLY_DEPTT";
        }

        @Override
        public String getLabel() {
            return "申请科室";
        }

    },
    VISIT_TYPE_OUTPV_CODE {
        @Override
        public String getCode() {
            return "01";
        }

        @Override
        public String getLabel() {
            return "门诊访问方式编码";
        }
    },
    VISIT_TYPE_CODE {
        @Override
        public String getCode() {
            return "VISIT_TYPE_CODE";
        }

        @Override
        public String getLabel() {
            return "访问方式编码字段";
        }
    },
    VISIT_TYPE_OUTPV {
        @Override
        public String getCode() {
            return "OUTPV";
        }

        @Override
        public String getLabel() {
            return "VISIT_TYPE_OUTPV";
        }

    },
    VISIT_TYPE_INPV {
        @Override
        public String getCode() {
            return "INPV";
        }

        @Override
        public String getLabel() {
            return "VISIT_TYPE_INPV";
        }

    }

}
