package com.goodwill.hdr.civ.dao.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.goodwill.hdr.civ.dao.VisitReportsOverTimeDao;
import com.goodwill.hdr.civ.entity.CmrReportTimeDuration;
import com.goodwill.hdr.civ.mapper.CmrReportTimeDurationMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @Description 类描述：
 * @modify 修改记录：
 */
@Repository
public class VisitReportsOverTimeDaoImpl implements VisitReportsOverTimeDao {
    @Autowired
    CmrReportTimeDurationMapper cmrReportTimeDurationMapper;

    @Override
    public List<CmrReportTimeDuration> getOverTimeHour(String orderItemCode, String reportType) {
        QueryWrapper<CmrReportTimeDuration> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("order_item_code", orderItemCode)
                .eq("report_type", reportType);
        List<CmrReportTimeDuration> cmrReportTimeDurations = cmrReportTimeDurationMapper.selectList(queryWrapper);
        return cmrReportTimeDurations;

//        List<CmrReportTimeDuration> list = new ArrayList<CmrReportTimeDuration>();
//        try {
//            String sql = "SELECT * FROM cmr_report_time_duration WHERE order_item_code = ? AND report_type = ? ";
//            Query query = createSQLQuery(CmrReportTimeDuration.class, sql, orderItemCode, reportType);
//            list = query.list();
//        } catch (Exception e) {
//            e.printStackTrace();
//            return list;
//        }
    }

}
