package com.goodwill.hdr.civ.controller;


import com.goodwill.hdr.civ.config.Config;
import com.goodwill.hdr.civ.service.PatientListService;
import com.goodwill.hdr.civ.vo.NameAndCodeVo;
import com.goodwill.hdr.core.orm.Page;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.*;

/**
 * <AUTHOR>
 * @Description 类描述：患者列表
 * @Date 2018年6月11日
 * @modify 修改记录：
 */
@RequestMapping("/patlist")
@RestController
@Api(tags = "患者列表页查询")
public class PatientListAction {


    @Autowired
    private PatientListService patientListService;


    /**
     * @Description 患者列表 列表样式
     */
    @ApiOperation(value = "查询患者列表", notes = "查询患者列表", httpMethod = "POST")
    @RequestMapping(value = "/getPatientList_List", method = RequestMethod.POST)
    public Page<Map<String, String>> getPatientList_List(String patientId, String visitId, String patientName, String card_Id, String visit_Type,
                                                         String visitDept, String visitBTime, String visitETime, String dischargeBTime,
                                                         String dischargeETime, String birthdayBTime, String birthdayETime, String district,
                                                         int pageNo, int pageSize, String oid, String visitStatus,String inpvDays,String inpvDaysType,
                                                         String hospital_second,String diagName,String isCollect,String eid) {

            //查询中台solr
        return patientListService.getPatientList_ListByJHDCP(oid, patientId, visitId,
                    patientName, card_Id, visit_Type, visitDept, visitBTime, visitETime, dischargeBTime, dischargeETime, birthdayBTime, birthdayETime,
                    district, visitStatus, pageNo, pageSize,inpvDays,inpvDaysType,hospital_second,diagName,isCollect,eid);
    }


    @PostMapping("/getVisitStatus")
    public List<NameAndCodeVo> getVisitStatus() {
        return patientListService.getVisitStatus();
    }


    /**
     * @Description 获取所有授权科室
     */

    @RequestMapping(value = "/getAllDept", method = RequestMethod.POST)
    public Page<Map<String, String>> getAllDept(String keyWord, String oid,int pageNo, int pageSize) {


        Page<Map<String, String>> authorisedDept = patientListService.getAuthorisedDept(keyWord, oid, pageNo, pageSize);

        return authorisedDept;
    }

    /**
     * @Description 获取所有部门
     */
    @ApiOperation(value = "获取所有部门", notes = "获取所有部门", httpMethod = "POST")
    @RequestMapping(value = "/getAllDistricts", method = RequestMethod.POST)
    public Page<Map<String, String>> getAllDistricts(String oid, String keyWord, String typeCode, String pageNo, String pageSize) {
//		String keyWord = getParameter("keyWord");
//		String typeCode = getParameter("typeCode");
        int pageNo1 = Integer.parseInt(pageNo);
        int pageSize1 = Integer.parseInt(pageSize);
        Page<Map<String, String>> page;

            page = patientListService.getAllFacetDataByJHDCP(oid, keyWord, typeCode, "DISTRICT_ADMISSION_TO_CODE", "DISTRICT_ADMISSION_TO_NAME", pageNo1, pageSize1);

        return page;
    }


    /**
     * 获取当前登录用户查看患者详情的权限
     */
    @ApiOperation(value = "获取当前登录用户查看患者详情的权限", notes = "获取当前登录用户查看患者详情的权限", httpMethod = "POST")
    @RequestMapping(value = "/getUserAuthority", method = RequestMethod.POST)
    public Map<String, String> getUserAuthority(String oid, String deptCode) {
        Map<String, String> map = patientListService.getUserAuthority(oid, deptCode);
        return map;
    }

    /**
     * 获取当前登录用户查看患者详情的权限
     */

    @PostMapping(value = "/getAuthorisedOid")
    public Page<Map<String, String>> getAuthorisedOid(String oids) {

        Page<Map<String, String>> page = new Page<>(1, 100);
        List<String> oidList = new ArrayList<>();
        if (StringUtils.isNotBlank(oids)) {
            String[] split = oids.split(",");
            Collections.addAll(oidList, split);
        }
        page.setResult(patientListService.getAuthorisedOid(oidList));
        return page;

    }

    /**
     * 收藏患者
     * @return
     */
    @PostMapping(value = "/collectPatient")
    public Map<String, String> collectPatient(String patientId, String visitId, String visitTypeCode, String oid, String isCollect) {
        String result = patientListService.collectPatient( patientId, visitId, visitTypeCode,  oid,  isCollect);
        Map<String, String> map = new HashMap<String, String>();
        map.put("status", result);
        return map;
    }

}
