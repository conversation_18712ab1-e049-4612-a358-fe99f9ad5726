package com.goodwill.hdr.civ.service;


import com.goodwill.hdr.core.orm.Page;

import java.util.List;
import java.util.Map;

/**
 * 输血记录接口
 *
 * <AUTHOR>
 */
public interface BloodService {
    public Page<Map<String, String>> getPageBloodApplyView(
            Map<String, Object> queryMap, String orderBy, String orderDir,
            int pageNo, int pageSize);

    public Page<Map<String, String>> getPageBloodMatchView(
            Map<String, Object> queryMap, String orderBy, String orderDir,
            int pageNo, int pageSize);

    public Page<Map<String, String>> getPageBloodView(
            Map<String, Object> queryMap, String orderBy, String orderDir,
            int pageNo, int pageSize);

    Page<Map<String, String>> getBloodApplyByVisit(String oid, String patientId, String visitId, String visitType, int pageNo, int pageSize);

    List<Map<String, String>> getBloodApplyNode(String oid, String patientId, String visitId, String visitType, String applyNo);

    Page<Map<String, String>> getBloodApplyNodeList(String oid, String patientId, String visitId, String visitType, String applyNo, String nameNode);
}
