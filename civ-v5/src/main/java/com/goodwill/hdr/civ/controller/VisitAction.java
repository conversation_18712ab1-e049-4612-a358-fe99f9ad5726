package com.goodwill.hdr.civ.controller;


import com.goodwill.hdr.civ.config.Config;
import com.goodwill.hdr.civ.service.*;
import com.goodwill.hdr.core.orm.Page;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Description 类描述：门诊就诊Action
 * @Date 2018年4月19日
 * @modify 修改记录：
 */
@RequestMapping("/visit")
@RestController
@Api(tags = "就诊信息查询")
public class VisitAction {


    @Autowired
    private VisitService visitService;
    @Autowired
    private SummaryService summaryService;
    @Autowired
    private OrderService orderService;

    @Autowired
    private InspectReportService inspectReportService;
    @Autowired
    private CheckReportService checkReportService;
    @Autowired
    private PathologyReportService pathologyReportService;
    @Autowired
    private MedicalRecordService medicalRecordService;
    @Autowired
    private NursingService nursingService;
    @Autowired
    private OperService operService;
    @Autowired
    private AllergyService allergyService;
    @Autowired
    private CurerecordService curerecordService;
    @Autowired
    private OCLService oclService;





    @RequestMapping(value = "/getPatientVisitsList", method = RequestMethod.POST)
    public Map<String, Object> getPatientVisitsList(String oid, String outPatientId, String visitDept, String deptType, String visitStatus, String timeStart, String timeEnd, String visitDate) {
        //获取 患者编号 就诊类型 日期类型 科室  开始时间  结束时间

        Map<String, Object> result = visitService.getPatientVisitsList(oid, outPatientId, visitDept, deptType, visitStatus, timeStart, timeEnd, visitDate);

        //响应
        return result;
    }


    @RequestMapping(value = "/getPatientVisitDeptList", method = RequestMethod.POST)
    public Map<String, Object> getPatientVisitDeptList(String oid, String outPatientId, String visitStatus, String visitDept, String timeStart, String timeEnd, String visitDate,String patientId,String visitType) {
        //获取 患者编号 就诊类型 日期类型 科室  开始时间  结束时间

        Map<String, Object> result = visitService.getPatientVisitDeptList(oid, outPatientId, visitStatus, visitDept, timeStart, timeEnd, visitDate,patientId,visitType);

        //响应
        return result;
    }





    /**
     * @Description 获取患者病案首页
     */
    @ApiOperation(value = "获取患者病案首页", notes = "获取患者病案首页", httpMethod = "POST")
    @RequestMapping(value = "/getPatientInpSummary", method = RequestMethod.POST)
    public List<String> getPatientInpSummary(String oid, String patientId, String visitId) {
        //获取 患者编号  就诊次数
        List<String> result = summaryService.getInpSummary(oid, patientId, visitId);
        //响应
        return result;
    }
    /**
     * @Description 获取患者病案首页
     */
    @ApiOperation(value = "获取患者病案首页(编目前)", notes = "获取患者病案首页(编目前)", httpMethod = "POST")
    @RequestMapping(value = "/getPatientInpSummaryBefore", method = RequestMethod.POST)
    public List<String> getPatientInpSummaryBefore(String oid, String patientId, String visitId) {
        //获取 患者编号  就诊次数
        List<String> result = summaryService.getInpSummaryBefore(oid, patientId, visitId);
        //响应
        return result;
    }
    /**
     * @Description 获取患者病案首页
     */
    @ApiOperation(value = "获取患者病案首页", notes = "获取患者病案首页", httpMethod = "POST")
    @RequestMapping(value = "/getPatientInpSummaryBeforeInfo", method = RequestMethod.POST)
    public List<String> getPatientInpSummaryBeforeInfo(String oid, String patientId, String visitId) {
        //获取 患者编号  就诊次数
        List<String> result = summaryService.getInpSummaryBeforeInfo(oid, patientId, visitId);
        //响应
        return result;
    }
    /**
     * @Description 获取病案首页诊断
     */
    @ApiOperation(value = "获取病案首页诊断", notes = "获取病案首页诊断", httpMethod = "POST")
    @RequestMapping(value = "/getPatientInpSummaryBeforeDiag", method = RequestMethod.POST)
    public Map<String, Object> getPatientInpSummaryBeforeDiag(String oid, String patientId, String visitId) {
        Map<String, Object> result = summaryService.getInpSummaryBeforeDiag(oid, patientId, visitId);
        //响应
        return result;
    }
    /**
     * @Description 获取病案首页手术
     */
    @ApiOperation(value = "获取病案首页手术", notes = "获取病案首页手术", httpMethod = "POST")
    @RequestMapping(value = "/getPatientInpSummaryBeforeOpera", method = RequestMethod.POST)
    public Map<String, Object> getPatientInpSummaryBeforeOpera(String oid, String patientId, String visitId) {
        Map<String, Object> result = summaryService.getInpSummaryBeforeOperation(oid, patientId, visitId);
        //响应
        return result;
    }

    /**
     * @Description 获取患者病案首页
     */
    @ApiOperation(value = "获取患者病案首页", notes = "获取患者病案首页", httpMethod = "POST")
    @RequestMapping(value = "/getPatientInpSummaryInfo", method = RequestMethod.POST)
    public List<String> getPatientInpSummaryInfo(String oid, String patientId, String visitId) {
        //获取 患者编号  就诊次数
        List<String> result = summaryService.getInpSummaryInfo(oid, patientId, visitId);
        //响应
        return result;
    }

    /**
     * @Description 获取病案首页诊断
     */
    @ApiOperation(value = "获取病案首页诊断", notes = "获取病案首页诊断", httpMethod = "POST")
    @RequestMapping(value = "/getPatientInpSummaryDiag", method = RequestMethod.POST)
    public Map<String, Object> getPatientInpSummaryDiag(String oid, String patientId, String visitId) {
        Map<String, Object> result = summaryService.getInpSummaryDiag(oid, patientId, visitId);
        //响应
        return result;
    }

    /**
     * @Description 获取病案首页手术
     */
    @ApiOperation(value = "获取病案首页手术", notes = "获取病案首页手术", httpMethod = "POST")
    @RequestMapping(value = "/getPatientInpSummaryOpera", method = RequestMethod.POST)
    public Map<String, Object> getPatientInpSummaryOpera(String oid, String patientId, String visitId) {
        Map<String, Object> result = summaryService.getInpSummaryOperation(oid, patientId, visitId);
        //响应
        return result;
    }

    /**
     * @Description 获取病案首页费用 分类统计
     */
    @ApiOperation(value = "获取病案首页费用分类统计", notes = "获取病案首页费用分类统计", httpMethod = "POST")
    @RequestMapping(value = "/getPatientInpSummaryFee", method = RequestMethod.POST)
    public Map<String, Object> getPatientInpSummaryFee(String oid, String patientId, String visitId) {
        Map<String, Object> result = summaryService.getInpSummaryFee(oid, patientId, visitId);
        //响应
        return result;
    }

    /**
     * 费用明细类型
     */
    @ApiOperation(value = "获取费用明细类型", notes = "获取费用明细类型", httpMethod = "POST")
    @RequestMapping(value = "/getPatientInpSummaryFeeTypes", method = RequestMethod.POST)
    public Page<Map<String, String>> getPatientInpSummaryFeeTypes(String oid, String patientId, String visitId, String visitType, int pageNo, int pageSize) {
        Page<Map<String, String>> result = summaryService.getPatientInpSummaryFeeTypes(oid, patientId, visitId, visitType,
                pageNo, pageSize);
        //响应
        return result;
    }

    /**
     * 费用明细table展示字段
     */
    @ApiOperation(value = "获取费用明细table展示字段", notes = "获取费用明细table展示字段", httpMethod = "POST")
    @RequestMapping(value = "/getPatientInpSummaryFeeTable", method = RequestMethod.POST)
    public String getPatientInpSummaryFeeTable(String oid, String visitType) {
        String result = summaryService.getPatientFeeTable(oid, visitType);
        //响应
        return result;
    }

    /**
     * 费用明细数据
     */
    @ApiOperation(value = "获取费用明细数据", notes = "获getPatientInpSummaryFeeDetail取费用明细数据", httpMethod = "POST")
    @RequestMapping(value = "/getPatientInpSummaryFeeDetail", method = RequestMethod.POST)
    public Page<Map<String, String>> getPatientInpSummaryFeeDetail(String feeType, String oid, String patientId, String visitId, String visitType, int pageNo, int pageSize) {
//        String feeType = getParameter("feeType");
        Page<Map<String, String>> result = summaryService.getPatientInpSummaryFeeData(oid, patientId, visitId, visitType,
                feeType, pageNo, pageSize);
        //响应
        return result;
    }

    /**
     * @Description 获取患者某次门诊的首页信息   就诊信息+门诊诊断
     */
    @ApiOperation(value = "获取患者某次门诊的首页信息", notes = "获取患者某次门诊的首页信息", httpMethod = "POST")
    @RequestMapping(value = "/getPatientOutpSummary", method = RequestMethod.POST)
    public Map<String, Object> getPatientOutpSummary(String oid, String patientId, String visitId) {
        Map<String, Object> result = summaryService.getPatientOutpSummary(oid, patientId, visitId);
        //响应
        return result;
    }

    /**
     * @Description 获取患者末次就诊的信息
     */
    @ApiOperation(value = "获取患者末次就诊的信息", notes = "获取患者末次就诊的信息", httpMethod = "POST")
    @RequestMapping(value = "/getPatientFinalVisit", method = RequestMethod.POST)
    public Map<String, Object> getPatientFinalVisit(String oid, String outPatientId,String patientId,String visitType) {

        Map<String, String> result = new HashMap<>();
        Map<String, String> config = new HashMap<>();
        //20241105 选中某个时间段无就诊数据时末次就诊信息为空白，因此修改为展示所有就诊次的末次就诊信息
        if(StringUtils.isBlank(outPatientId)){
            Map<String, Object> idsMap = visitService.getOutOrInPatientId("ALL",oid, patientId, visitType,"allTime", "allDept", "", "", "all");
            outPatientId=String.valueOf(idsMap.get("ids"));
        }
        //if (StringUtils.isNotBlank(outPatientId)) {
            result = this.visitService.getCurrentPatientInfo(oid, outPatientId);
            config = this.orderService.getPatLastInfoViewConfig(oid);
        //}
        Map<String, Object> map = new HashMap<>();
        map.put("viewConfig", config);
        map.put("data", result);
        return map;
    }

    /**
     * @Description 通过pid，vid和visitType获取患者就诊信息
     */
    @ApiOperation(value = "通过pid，vid和visitType获取患者就诊信息", notes = "通过pid，vid和visitType获取患者就诊信息", httpMethod = "POST")
    @RequestMapping(value = "/getPatientVisit", method = RequestMethod.POST)
    public Map<String, Object> getPatientVisit(String oid, String patientId, String visitId, String visitType) {
        Map<String, String> result = new HashMap<String, String>();
        Map<String, String> config = new HashMap<String, String>();
        if (StringUtils.isNotBlank(patientId)) {
            result = visitService.getPatientInfo(oid, patientId, visitId, visitType);
            config = orderService.getPatLastInfoViewConfig(oid);
        }
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("viewConfig", config);
        map.put("data", result);
        //响应
        return map;
    }

    /**
     * 获取末次就诊的科室信息
     */
    @ApiOperation(value = "获取末次就诊的科室信息", notes = "获取末次就诊的科室信息", httpMethod = "POST")
    @RequestMapping(value = "/getPatientFinalVisitDept", method = RequestMethod.POST)
    public Map<String, String> getPatientFinalVisitDept(String oid, String patientId, String visitType,String outPatientId) {
//        String outPatientId = getParameter("outPatientId");
        Map<String, String> result = new HashMap<String, String>();
        if (StringUtils.isNotBlank(patientId)) {
            Map<String, String> data = visitService.getCurrentPatientInfo(oid,outPatientId);
            result.put("deptCode", data.get("stay_dept_code"));
            result.put("deptName", data.get("stay_dept_name"));
        }
        //响应
        return result;
    }

    /**
     * @Description 获取患者末次就诊的信息
     */
    @ApiOperation(value = "获取患者末次就诊的信息", notes = "获取患者末次就诊的信息", httpMethod = "POST")
    @RequestMapping(value = "/getPatientInFinalVisit", method = RequestMethod.POST)
    public Map<String, String> getPatientInFinalVisit(String this_oid, String oid, String patientId, String outPatientId, String visitType) {
//        String outPatientId = getParameter("outPatientId");
        Map<String, String> result = visitService.getPatientInFinalVisit(this_oid, oid, patientId, outPatientId, visitType);
        //响应
        return result;
    }

    /**
     * @Description 判断是否存在与当前患者编号不一致的患者标识，若存在，则返回
     */
    @ApiOperation(value = "获取当前患者其他pid", notes = "获取当前患者其他pid", httpMethod = "POST")
    @RequestMapping(value = "/getDifferencePids", method = RequestMethod.POST)
    public Map<String, Object> getDifferencePids(String oid, String initOid, String patientId, String visitType) {
        Map<String, Object> result = new HashMap<String, Object>();
        if (StringUtils.isNotBlank(patientId))
            result = visitService.getOutOrInPatientId(oid, initOid, patientId, visitType,"","","","","");
        //响应
        return result;
    }
    /**
     * @Description 判断是否存在与当前患者编号不一致的患者标识，若存在，则返回
     */
    @ApiOperation(value = "获取当前患者其他pid(分类视图)", notes = "获取当前患者其他pid(分类视图)", httpMethod = "POST")
    @RequestMapping(value = "/getCategoryDifferencePids", method = RequestMethod.POST)
    public Map<String, Object> getCategoryDifferencePids(String oid,String initOid, String patientId, String visitType,String visitDate, String visitDept, String timeStart, String timeEnd, String deptType) {
        Map<String, Object> result = new HashMap<String, Object>();
        if (StringUtils.isNotBlank(patientId))
            result = visitService.getOutOrInPatientId(oid,initOid, patientId, visitType,visitDate, visitDept, timeStart, timeEnd, deptType);
        //响应
        return result;
    }

    /**
     * @Description 某次就诊的各项数量   医嘱，检验报告，检查报告，病历文书，手术记录，护理记录，过敏记录
     */
    @PostMapping("/getVisitItemCount")
    public Map<String, Object> getVisitItemCount(String this_oid, String oid, String patientId, String visitId, String visitType) {
        Map<String, Object> result = new HashMap<String, Object>();
        //查询某次就诊的各项数量
        long orderCount = orderService.getOrderCount(oid, patientId, visitId, visitType);
        long inspectCount = inspectReportService.getInspectCount(oid, patientId, visitId, visitType,"");
        long checkCount = checkReportService.getCheckCount(oid, patientId, visitId, visitType);
        long pathologyCount = pathologyReportService.getPathologyCount(oid, patientId, visitId, visitType);
        long mrCount = medicalRecordService.getMRCount(oid, patientId, visitId, visitType);
        long operCount = operService.getOperCount(oid, patientId, visitId, visitType);
        long allergyCount = allergyService.getAllergyCount(oid, patientId, visitId, visitType);
        long hdCount = (long) curerecordService.getReportsByPat(oid, patientId, visitType, visitId);
        result.put("orderCount", orderCount); //医嘱
        result.put("inspectCount", inspectCount); //检验报告
        result.put("pathologyCount", pathologyCount); //病理报告
        result.put("checkCount", checkCount); //检查报告
        result.put("mrCount", mrCount); //病历文书
        result.put("operCount", operCount); //手术记录
        result.put("allergyCount", allergyCount); //过敏记录
        result.put("hdCount", hdCount); //血透报告
        //暂时在这里判断是否配置了url
        result.put("nurseCount", nursingService.getVisitNurseNum(oid, patientId, visitId, visitType));
        //响应
        return result;
    }

    /**
     * @Description 某次就诊的各类医嘱数量
     */
    @ApiOperation(value = "获取某次就诊的各类医嘱数量", notes = "获取某次就诊的各类医嘱数量", httpMethod = "POST")
    @RequestMapping(value = "/getVisitOrderCount", method = RequestMethod.POST)
    public Map<String, Object> getVisitOrderCount(String oid, String patientId, String visitId, String visitType) {
        Map<String, Object> result = orderService.getTypeOrderCount(oid, patientId, visitId, visitType);
        return result;
    }


    /**
     * 获取末次就诊信息显示配置
     */
    @ApiOperation(value = "获取末次就诊信息显示配置", notes = "获取末次就诊信息显示配置", httpMethod = "POST")
    @RequestMapping(value = "/getPatLastInfoView", method = RequestMethod.POST)
    public Map<String, String> getPatLastInfoView(String oid) {
        Map<String, String> config = orderService.getPatLastInfoViewConfig(oid);
        return config;
    }

    /**
     * 获取配置的医嘱闭环url
     */
    @ApiOperation(value = "获取配置的医嘱闭环url", notes = "获取配置的医嘱闭环url", httpMethod = "POST")
    @RequestMapping(value = "/getOclUrl", method = RequestMethod.POST)
    public Map<String, String> getOclUrl(String oid, String patientId, String visitId, String id, HttpServletRequest request) {

        String token = request.getHeader("Authorization");
        //获取医嘱闭环配置url
        Map<String, String> map = oclService.getOclUrl(oid, patientId, visitId, id, token);
//        Map<String, String> map = commonURLService.getCommonUrl(oid, patientId, visitId, "OCL",
//                new HashMap<String, String>());
        return map;
    }

    /**
     * 就诊试图的科室筛选
     */
    @ApiOperation(value = "就诊试图的科室筛选", notes = "就诊试图的科室筛选", httpMethod = "POST")
    @RequestMapping(value = "/getShowConfig", method = RequestMethod.POST)
    public Map<String, String> getShowConfig(String oid) {
        String deptShow = Config.getVisitDeptSelectConfig(oid);
        Map<String, String> map = new HashMap<String, String>();
        map.put("config", deptShow);
        return map;
    }

    /**
     * 默认打开的页面配置
     */
    @ApiOperation(value = "默认打开的页面配置", notes = "默认打开的页面配置", httpMethod = "POST")
    @RequestMapping(value = "/getDefaultPage", method = RequestMethod.POST)
    public Map<String, String> getDefaultPage(String oid) {
        String defaultPage = Config.getCIV_DEFAULT_PAGE(oid);
        Map<String, String> map = new HashMap<String, String>();
        map.put("page", defaultPage);
        return map;
    }

    /**
     * 根据身份证号+患者姓名关联免登陆跳转地址
     */
    @ApiOperation(value = "根据身份证号+患者姓名关联免登陆跳转地址", notes = "根据身份证号+患者姓名关联免登陆跳转地址", httpMethod = "POST")
    @RequestMapping(value = "/getPatientinfo", method = RequestMethod.POST)
    public Map<String, String> getPatientinfo(String idCardNo, String oid) {
        Map<String, String> result = new HashMap<String, String>();

        result = visitService.getPatientinfo(oid, idCardNo);
        return result;
    }

    /**
     * 根据身份证号+患者姓名关联免登陆跳转地址
     */

    @RequestMapping(value = "/getRpcUrlByInpNoAndVisitNo", method = RequestMethod.POST)
    public Map<String,String> getRpcUrlByInpNoAndVisitNo(String inpNo, String visitNo,String oid) {
        Map<String,String> errorResult=new HashMap<>();
        errorResult.put("status","0");
        if(StringUtils.isBlank(oid)){
            return errorResult;
        }

        if (StringUtils.isNotBlank(inpNo)) {
           return visitService.getRpcUrlByInpNo(oid,inpNo);
        } else if (StringUtils.isNotBlank(visitNo)) {
            return visitService.getRpcUrlByVisitNo(oid,visitNo);
        }

        return errorResult;
    }

    /**
     * @Description 获取患者某次门诊的首页信息   就诊信息+门诊诊断
     * 北京佑安医院门诊首页
     */
    @ApiOperation(value = "获取患者某次门诊的首页信息", notes = "获取患者某次门诊的首页信息", httpMethod = "POST")
    @RequestMapping(value = "/getPatientOutpSummaryYouAn", method = RequestMethod.POST)
    public Map<String, Object> getPatientOutpSummaryYouAn(String oid, String patientId, String visitId) {
        Map<String, Object> result = summaryService.getPatientOutpSummaryYouAn(oid, patientId, visitId);
        //响应
        return result;
    }


}
