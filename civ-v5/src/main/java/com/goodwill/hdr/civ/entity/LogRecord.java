package com.goodwill.hdr.civ.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.io.Serializable;
import java.time.LocalDate;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2022-03-03
 */
@TableName("civ_log_record")
public class LogRecord implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id_pk", type = IdType.AUTO)
    private Integer idPk;

    private String deptname;

    private String deptcode;

    private String username;

    private String usercode;

    private String pagename;

    private String pagecode;

    private LocalDate accesstime;

    private String ip;
    private String oid;

    public Integer getIdPk() {
        return idPk;
    }

    public void setIdPk(Integer idPk) {
        this.idPk = idPk;
    }

    public String getDeptname() {
        return deptname;
    }

    public void setDeptname(String deptname) {
        this.deptname = deptname;
    }

    public String getDeptcode() {
        return deptcode;
    }

    public void setDeptcode(String deptcode) {
        this.deptcode = deptcode;
    }

    public String getUsername() {
        return username;
    }

    public void setUsername(String username) {
        this.username = username;
    }

    public String getUsercode() {
        return usercode;
    }

    public void setUsercode(String usercode) {
        this.usercode = usercode;
    }

    public String getPagename() {
        return pagename;
    }

    public void setPagename(String pagename) {
        this.pagename = pagename;
    }

    public String getPagecode() {
        return pagecode;
    }

    public void setPagecode(String pagecode) {
        this.pagecode = pagecode;
    }

    public LocalDate getAccesstime() {
        return accesstime;
    }

    public void setAccesstime(LocalDate accesstime) {
        this.accesstime = accesstime;
    }

    public String getIp() {
        return ip;
    }

    public void setIp(String ip) {
        this.ip = ip;
    }


    public String getOid() {
        return oid;
    }

    public void setOid(String oid) {
        this.oid = oid;
    }

    @Override
    public String toString() {
        return "LogRecord{" +
                "idPk=" + idPk +
                ", deptname='" + deptname + '\'' +
                ", deptcode='" + deptcode + '\'' +
                ", username='" + username + '\'' +
                ", usercode='" + usercode + '\'' +
                ", pagename='" + pagename + '\'' +
                ", pagecode='" + pagecode + '\'' +
                ", accesstime=" + accesstime +
                ", ip='" + ip + '\'' +
                ", oid='" + oid + '\'' +
                '}';
    }
}
