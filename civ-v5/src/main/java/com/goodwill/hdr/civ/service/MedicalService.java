package com.goodwill.hdr.civ.service;



import com.goodwill.hdr.core.orm.Page;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Description 类描述：
 * @modify 修改记录：
 */
public interface MedicalService {
    /**
     * 体检患者信息
     *
     * @param patientId
     * @param visitType
     * @return
     */
    Map<String, String> getPatientInfo(String oid, String patientId, String visitType);

    /**
     * 获取体检列表
     *
     * @param cardNo
     * @param dateBegin
     * @param dateEnd
     * @param pageNo
     * @param pageSize
     * @return
     */
    List<Map<String, String>> getMedicalListInfo(String oid, String cardNo, String dateBegin, String dateEnd, String visitDate, int pageNo, int pageSize);

    /**
     * 获取体检视图单次体检的表头配置
     */
    List<Map<String, String>> getMedicalTableHead(String oid);

    /**
     * 体检视图首页
     *
     * @return
     */
    Map<String, Object> getMedicalSummaryHomePage(String visitId,String medicalReportNo, String oid, String patientId, String cardNo);

    /**
     * 体检视图 一般检查 内科 外科 眼科 耳鼻喉科
     *
     * @return
     */
    Map<String, Object> getMedicalCommonCheck(String oid,String visitId, String medicalReportNo, String medicalItemClass, String checkTyp ,String patientId);

    /**
     * 体检视图检验报告列表
     *
     * @param medicalReportNo
     * @param medicalItemClass
     * @return
     */
    Page<Map<String, String>> getMedicalLabList(String oid, String visitId, String medicalReportNo, String medicalItemClass, String orderBy, String orderDir, int pageNo, int pageSize, String patientId);

    /**
     * 体检视图检验报告明细
     *
     * @param medicalReportNo
     * @param applyNo
     * @return
     */
    Map<String, Object> getMedicalLabDetail(String oid,String visitId, String cardNo, String medicalReportNo, String applyNo, String showUnNormal,String patientId);

    /**
     * 体检视图检查报告列表
     *
     * @param medicalReportNo
     * @param medicalItemClass
     * @return
     */
    Page<Map<String, String>> getMedicalExamList(String oid,String visitId, String medicalReportNo, String medicalItemClass, String orderBy, String orderDir, int pageNo, int pageSize,String patientId);


    /**
     * 体检视图检验报告明细
     *
     * @param medicalReportNo
     * @param applyNo
     * @return
     */
    Map<String, Object> getMedicalExamDetail(String oid, String visitId,String medicalReportNo, String applyNo,String patientId);

    /**
     * 获取医院信息
     *
     * @return
     */
    Map<String, String> getMedicalHospialInfo(String oid);

    /**
     * 获取数量
     * @return
     */
    Map<String, String> getReportNum(String oid,String visitId, String medicalReportNo, String labClass, String examClass,String patientId);

    Map<String, Object> getReportTrendData(String oid, String patientId, String medicalItemCode, int numIndex, String beginTime, String endTime);
}
