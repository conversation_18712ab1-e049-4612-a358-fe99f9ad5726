package com.goodwill.hdr.civ.utils;


import com.goodwill.hdr.civ.entity.Config;
import com.goodwill.hdr.civ.service.ConfigService;

import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @Description 类描述：
 * @modify 修改记录：
 */
@Component
public class ConfigUtil  {


    public static List<Config> getConfigBycode(String oid, String configCode) {
//        ConfigService configService = SpringContextHolder.getBean("configService");
//        List<Config> list = configService.getConfigBycode(oid,configCode);
        ConfigService configService = SpringContextUtil.getBean("configService",ConfigService.class);
        List<Config> list = configService.getConfigBycode(oid,configCode);
        return list;
    }

}
