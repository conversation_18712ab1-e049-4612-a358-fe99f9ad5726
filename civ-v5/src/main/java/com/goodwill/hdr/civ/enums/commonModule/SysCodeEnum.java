package com.goodwill.hdr.civ.enums.commonModule;

/**
 * 后端判断模块逻辑处理方式所需的枚举量
 *
 * <AUTHOR>
 * @since 4.6.8.3
 */
public enum SysCodeEnum {
    URL("url", "普通模块"),
    LIST("list", "带列表的模块"),
    DES("des", "des参数加密"),
    BASE64("base64", "base64参数加密"),
    SM4("sm4", "sm4参数加密"),

    RSA("rsa", "rsa公钥参数加密"),

    ZGSY("zgsy", "自贡四院"),

    LZ("lz", "联众病案"),
    AES("aes", "AES身份证号加密"),
    EHR("ehr", "电子健康档案"),
    CEMSMD5("cemsmd5", "北京影研创新md5加密"),
    SYSENC("sysenc", "系统全局加密"),
    TABLE("table", "纯列表的模块"),
    LIST_TABLE("list_table", "右侧列表+左侧列表"),
    URL_TABLE("url_table", "带url的表格"),
    WEB_EMR("WEBEMR", "WEB版电子病历"),
    ANAES("ANAES", "WEB版手术记录"),
    ICU("ICU", "WEB版重症"),
    MZEMR("MZEMR", "门诊病历"),
    ZYBH("ZYBH", "住院闭环"),
    MZBH("MZBH", "门诊闭环"),
    ZDBH("ZDBH", "重点业务闭环");


    private final String code;
    private final String label;

    SysCodeEnum(String code, String label) {
        this.code = code;
        this.label = label;
    }


    public String getCode() {
        return code;
    }


    public String getLabel() {
        return label;
    }
}
