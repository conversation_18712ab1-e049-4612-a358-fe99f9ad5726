package com.goodwill.hdr.civ.handle;

import com.goodwill.hdr.web.common.vo.ResultVO;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestControllerAdvice;

import java.util.ArrayList;
import java.util.List;

@RestControllerAdvice
public class GlobalExceptionHandler {
    @ResponseBody
    @ExceptionHandler(Exception.class)
    public ResultVO<?> processException(Exception exception) {
        ResultVO<Object> resultVO = new ResultVO<>();
        StackTraceElement[] stackTrace = exception.getStackTrace();
        List<StackTraceElement> goodwillTrace = new ArrayList<>();
        for (StackTraceElement s : stackTrace) {
            if (s.getClassName().startsWith("com.goodwill.hdr")) {
                goodwillTrace.add(s);
            }

        }
        resultVO.fail(exception.toString(), goodwillTrace);
        return resultVO;
    }

}
