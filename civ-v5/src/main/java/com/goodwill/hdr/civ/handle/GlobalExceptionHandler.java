package com.goodwill.hdr.civ.handle;

import com.goodwill.hdr.web.common.vo.ResultVO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.dao.DataAccessException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.method.annotation.MethodArgumentTypeMismatchException;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.http.converter.HttpMessageNotReadableException;

import java.io.PrintWriter;
import java.io.StringWriter;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@RestControllerAdvice
public class GlobalExceptionHandler {

    private static final Logger logger = LoggerFactory.getLogger(GlobalExceptionHandler.class);

    @ResponseBody
    @ExceptionHandler(Exception.class)
    public ResultVO<?> processException(Exception exception) {
        // 记录完整的异常信息到日志
        logger.error("全局异常处理器捕获异常: ", exception);

        ResultVO<Object> resultVO = new ResultVO<>();

        // 构建详细的错误信息
        Map<String, Object> errorDetails = new HashMap<>();

        // 异常基本信息
        errorDetails.put("exceptionType", exception.getClass().getSimpleName());
        errorDetails.put("message", exception.getMessage());
        errorDetails.put("localizedMessage", exception.getLocalizedMessage());

        // 获取完整堆栈信息
        StringWriter sw = new StringWriter();
        PrintWriter pw = new PrintWriter(sw);
        exception.printStackTrace(pw);
        String fullStackTrace = sw.toString();
        errorDetails.put("fullStackTrace", fullStackTrace);

        // 获取关键堆栈信息（前10行）
        StackTraceElement[] stackTrace = exception.getStackTrace();
        List<String> keyStackTrace = new ArrayList<>();
        int maxLines = Math.min(10, stackTrace.length);
        for (int i = 0; i < maxLines; i++) {
            keyStackTrace.add(stackTrace[i].toString());
        }
        errorDetails.put("keyStackTrace", keyStackTrace);

        // 获取goodwill相关的堆栈信息
        List<String> goodwillTrace = new ArrayList<>();
        for (StackTraceElement s : stackTrace) {
            if (s.getClassName().startsWith("com.goodwill")) {
                goodwillTrace.add(s.toString());
            }
        }
        errorDetails.put("goodwillTrace", goodwillTrace);

        // 如果有cause异常，也记录下来
        if (exception.getCause() != null) {
            Throwable cause = exception.getCause();
            Map<String, Object> causeInfo = new HashMap<>();
            causeInfo.put("causeType", cause.getClass().getSimpleName());
            causeInfo.put("causeMessage", cause.getMessage());
            errorDetails.put("cause", causeInfo);
        }

        // 构建用户友好的错误消息
        String userMessage = buildUserFriendlyMessage(exception);

        resultVO.fail(userMessage, errorDetails);
        return resultVO;
    }

    /**
     * 处理数据库相关异常
     */
    @ResponseBody
    @ExceptionHandler(DataAccessException.class)
    public ResultVO<?> handleDataAccessException(DataAccessException exception) {
        logger.error("数据库访问异常: ", exception);

        ResultVO<Object> resultVO = new ResultVO<>();
        Map<String, Object> errorDetails = buildErrorDetails(exception);

        String userMessage = "数据库操作失败：" +
            (exception.getMessage() != null ? exception.getMessage() : "请检查数据连接或联系管理员");

        resultVO.fail(userMessage, errorDetails);
        return resultVO;
    }

    /**
     * 处理参数验证异常
     */
    @ResponseBody
    @ExceptionHandler(MethodArgumentNotValidException.class)
    public ResultVO<?> handleValidationException(MethodArgumentNotValidException exception) {
        logger.error("参数验证异常: ", exception);

        ResultVO<Object> resultVO = new ResultVO<>();
        Map<String, Object> errorDetails = buildErrorDetails(exception);

        StringBuilder errorMsg = new StringBuilder("参数验证失败：");
        exception.getBindingResult().getFieldErrors().forEach(error -> {
            errorMsg.append(error.getField()).append(" ").append(error.getDefaultMessage()).append("; ");
        });

        resultVO.fail(errorMsg.toString(), errorDetails);
        return resultVO;
    }

    /**
     * 处理参数类型不匹配异常
     */
    @ResponseBody
    @ExceptionHandler(MethodArgumentTypeMismatchException.class)
    public ResultVO<?> handleTypeMismatchException(MethodArgumentTypeMismatchException exception) {
        logger.error("参数类型不匹配异常: ", exception);

        ResultVO<Object> resultVO = new ResultVO<>();
        Map<String, Object> errorDetails = buildErrorDetails(exception);

        String userMessage = String.format("参数类型错误：参数 '%s' 的值 '%s' 无法转换为 %s 类型",
            exception.getName(),
            exception.getValue(),
            exception.getRequiredType() != null ? exception.getRequiredType().getSimpleName() : "未知");

        resultVO.fail(userMessage, errorDetails);
        return resultVO;
    }

    /**
     * 处理HTTP消息不可读异常（通常是JSON格式错误）
     */
    @ResponseBody
    @ExceptionHandler(HttpMessageNotReadableException.class)
    public ResultVO<?> handleHttpMessageNotReadableException(HttpMessageNotReadableException exception) {
        logger.error("HTTP消息不可读异常: ", exception);

        ResultVO<Object> resultVO = new ResultVO<>();
        Map<String, Object> errorDetails = buildErrorDetails(exception);

        String userMessage = "请求数据格式错误：" +
            (exception.getMessage() != null && exception.getMessage().contains("JSON") ?
                "JSON格式不正确，请检查请求体" : "请求数据无法解析");

        resultVO.fail(userMessage, errorDetails);
        return resultVO;
    }

    /**
     * 构建错误详情
     */
    private Map<String, Object> buildErrorDetails(Exception exception) {
        Map<String, Object> errorDetails = new HashMap<>();

        // 异常基本信息
        errorDetails.put("exceptionType", exception.getClass().getSimpleName());
        errorDetails.put("message", exception.getMessage());
        errorDetails.put("localizedMessage", exception.getLocalizedMessage());

        // 获取关键堆栈信息（前5行）
        StackTraceElement[] stackTrace = exception.getStackTrace();
        List<String> keyStackTrace = new ArrayList<>();
        int maxLines = Math.min(5, stackTrace.length);
        for (int i = 0; i < maxLines; i++) {
            keyStackTrace.add(stackTrace[i].toString());
        }
        errorDetails.put("keyStackTrace", keyStackTrace);

        // 获取goodwill相关的堆栈信息
        List<String> goodwillTrace = new ArrayList<>();
        for (StackTraceElement s : stackTrace) {
            if (s.getClassName().startsWith("com.goodwill")) {
                goodwillTrace.add(s.toString());
            }
        }
        errorDetails.put("goodwillTrace", goodwillTrace);

        return errorDetails;
    }

    /**
     * 构建用户友好的错误消息
     */
    private String buildUserFriendlyMessage(Exception exception) {
        String message = exception.getMessage();
        String exceptionType = exception.getClass().getSimpleName();

        // 根据异常类型提供更友好的消息
        if (exception instanceof NullPointerException) {
            return "系统内部错误：空指针异常，请联系管理员";
        } else if (exception instanceof IllegalArgumentException) {
            return "参数错误：" + (message != null ? message : "请检查输入参数");
        } else if (exception instanceof RuntimeException) {
            return "运行时错误：" + (message != null ? message : "系统处理异常");
        } else if (message != null && !message.trim().isEmpty()) {
            return message;
        } else {
            return "系统异常：" + exceptionType + "，请联系管理员";
        }
    }
}
