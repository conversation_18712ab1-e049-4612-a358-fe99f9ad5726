package com.goodwill.hdr.civ.dao;


import com.goodwill.hdr.civ.entity.SpecialtyIndicatorConfigEntity;

import java.util.List;
import java.util.Map;

public interface SprcialtyViewNewDao {
    //    List<SpecialtyConfigEntity> getSpecityConfig(String doctor, String main_diag);
//
//    /**
//     * 查询科室下的大项
//     * @param deptCode
//     * @return
//     */
//    List<SpecialtyDeptConfigEntity> getDeptSpecityConfig(String  deptCode);
//
//    List<SpecialtyConfigEntity> getSpecityConfigMainDiag(String doctor);
//
//    List<SpecialtyIndicatorConfigEntity> getSpecityIndicatorConfig(String doctorCode, String main_diag);
//
//    List<SicknessEntity> getSicknessList(String maindiag,int pageNo, int pageSize);
//
    List<SpecialtyIndicatorConfigEntity> getConfigByUserCodeMainDaig(String doctorCode, String maindaig, String itemCode);
//
//    /**
//     * 获取科室下的明细项
//     * @param deptCode
//     * @param itemCode
//     * @return
//     */
//    List<SpecialtyDeptIndicatorConfig> getConfigByDept(String deptCode,String itemCode);
//
    int updateSicknessByCode(String maindaig, String status);
//
    int updateSicknessIndicatorConfig(String userCode, String maindaig, String status);
//
    int addSickness(String sicknessCode, String sicknessName);
//
//    /**
//     * 添加专科科室
//     * @param deptCode
//     * @param deptName
//     * @return
//     */
    int addSpecityDept(String deptCode, String deptName);
//
    int addSicknessIndicatorConfig(String userCode, String userName, String sicknessCode, String sicknessName);
    /**
//     * 初始化专科科室设置civ_dept_specialty_config数据
//     */
    int addDeptSpecityConfig(String deptCode, String deptName);
//    /**
//     * 删除诊断明细
//     * @param id
//     * @return
//     */
    int delIndicatorConfig(String id);
//
//    /**
//     * 删除科室明细
//     * @param id
//     * @return
//     */
    int delDeptIndicatorConfig(String id);
//
//    /**
//     * 添加诊断细项
//     * @param map
//     * @return
//     */
    int addInditorConfig(Map<String,String> map);
//    /**
//     * 添加科室细项
//     * @param map
//     * @return
//     */
    int addDeptInditorConfig(Map<String,String> map);
//
//
//    List<SpecialtyIndicatorConfigEntity> getDiagSpecityIndicatorConfig(String doctorCode);

//    /**
//     * 查询已添加配置的 科室列表
//     */
 //   List<SpecialtyDeptEntity> getDeptList(String dept, int pageNo, int pageSize);
//
//    /**
//     * 查询科室以供添加配置
//     * @param dept
//     * @param pageNo
//     * @param pageSize
//     * @return
//     */
 //   List<Map<String,String>> getDeptListToAdd(String keyWord, int pageNo, int pageSize);
//
//    /**
//     * 删除科室
//     * @param deptCode
//     * @param status
//     * @return
//     */
 //   int deleteDeptByCode(String deptCode);
//    /**
//     * 更新科室指标信息
//     */
 //   int deleteDeptIndicatorConfig( String dept_code);
//
//    /**
//     * 删除科室中间表配置
//     * @param deptCode
//     * @return
//     */
 //   int delDeptSpecialtyConfig(String deptCode);
//
//    /**
//     * 查找已經配置的最大索引數，即配置個數
//     * @param deptCode
//     * @param itemCode
//     * @return
//     */
     String getDeptInditorMaxIndex(String deptCode,String itemCode);
//    /**
//     * 查找已經配置的最大索引數，即配置個數
//     * @param diagCode
//     * @param itemCode
//     * @return
//     */
     String getInditorMaxIndex(String userCode,String diagCode,String itemCode);
}
