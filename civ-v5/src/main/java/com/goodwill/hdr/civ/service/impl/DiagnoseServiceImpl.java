package com.goodwill.hdr.civ.service.impl;


import com.goodwill.hdr.civ.config.ConfigCache;
import com.goodwill.hdr.civ.enums.HdrConstantEnum;
import com.goodwill.hdr.civ.enums.HdrTableEnum;
import com.goodwill.hdr.civ.service.DiagnoseService;
import com.goodwill.hdr.civ.utils.CivUtils;
import com.goodwill.hdr.civ.utils.Utils;
import com.goodwill.hdr.core.orm.MatchType;
import com.goodwill.hdr.core.orm.Page;
import com.goodwill.hdr.hbase.bo.PropertyFilter;
import com.goodwill.hdr.hbase.dto.responseVo.PageResultVo;
import com.goodwill.hdr.hbase.dto.responseVo.ResultVo;
import com.goodwill.hdr.hbaseQueryClient.builder.PageRequestBuilder;
import com.goodwill.hdr.hbaseQueryClient.service.HbaseQueryClient;
import com.goodwill.hdr.rest.client.transmission.JhdcpHttpSender;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
public class DiagnoseServiceImpl implements DiagnoseService {

    Logger logger = LoggerFactory.getLogger(DiagnoseServiceImpl.class);


    @Autowired
    JhdcpHttpSender jhdcpHttpSender;
    private final HbaseQueryClient hbaseQueryClient;

    public DiagnoseServiceImpl(HbaseQueryClient hbaseQueryClient) {
        this.hbaseQueryClient = hbaseQueryClient;
    }

    @Override
    public List<Map<String, Object>> getDiagsList(String outPatientId, String startDate, String endDate) {
        // TODO Auto-generated method stub
        List<Map<String, Object>> diagsResult = new ArrayList<Map<String, Object>>();


        //2.获取就诊列表
        List<Map<String, Object>> diagsList = getAllVisits(outPatientId, startDate, endDate);
        //3.时间排序
        Utils.sortListByDate(diagsList, "start_Date", Page.Sort.DESC);
        //4.以年分组
        Map<String, Object> result = new HashMap<String, Object>();
        CivUtils.groupByYearDate(result, diagsList, "start_Date");
        //5.转换格式
        for (String year : result.keySet()) {
            Map<String, Object> map = new HashMap<String, Object>();
            map.put("year", year);
            map.put("data", result.get(year));
            diagsResult.add(map);
        }
        //6.重新排序
        Utils.sortListByDate(diagsResult, "year", Page.Sort.DESC);

        return diagsResult;
    }




    /**
     * @return
     * @Description 方法描述: 通过EID或PATIENT_ID查询患者所有的就诊信息，优先EID
     */
    private List<Map<String, Object>> getAllVisits(String outPatientId, String startDate, String endDate) {


        String[] split = outPatientId.split(",");

        //就诊列表
        List<Map<String, Object>> visits = new ArrayList<Map<String, Object>>();

        //根据主索引查询 Solr 处理门诊患者标识和住院患者标识不一致情况


        //根据上述筛选后的患者编号和就诊类型   获取所有就诊列表
        for (String s: split) {
            String[] vtpid = s.split("\\|");
            getVisitsAndAddToVisits(visits, vtpid[2], vtpid[1], vtpid[0], vtpid[3], startDate, endDate);
        }

        return visits;
    }

    /**
     * @param visits    存储就诊列表的集合
     * @param patientId 患者编号
     * @param visitType 就诊类型
     * @return 返回类型： void
     * @Description 方法描述: 根据患者编号和就诊类型查询该类型下的就诊列表，并将数据填充到visits
     */
    private void getVisitsAndAddToVisits(List<Map<String, Object>> visits, String oid, String patientId, String visitType, String visitId, String startDate, String endDate) {
        List<PropertyFilter> transFilter = new ArrayList<PropertyFilter>();
        //住院
        if ("02".equals(visitType)) {
//            if(StringUtils.isNotBlank(visitId)){
//                transFilter.add(new PropertyFilter("VISIT_ID", "STRING", MatchType.EQ.getOperation(), visitId));
//            }
            if (StringUtils.isNotBlank(startDate)) {
                transFilter.add(new PropertyFilter("ADMISSION_TIME", MatchType.GE.getOperation(), startDate + " 00:00:00"));
            }
            if (StringUtils.isNotBlank(endDate)) {
                transFilter.add(new PropertyFilter("ADMISSION_TIME", MatchType.LE.getOperation(), endDate + " 23:59:59"));
            }

            transFilter.add(new PropertyFilter("TRANS_NO", MatchType.EQ.getOperation(), "0")); //仅取入出院，不取转科
            List<Map<String, String>> patAdts = new ArrayList<>();
            ResultVo<PageResultVo<Map<String, String>>> resultVo = hbaseQueryClient.getPageByCondition(
                    PageRequestBuilder.init()
                            .tableName(HdrTableEnum.HDR_PAT_ADT.getCode())
                            .patientId(patientId)
                            .oid(oid)
                            .visitId(visitId)
                            .visitTypeCode(visitType)
                            .filters(transFilter)
                            .pageNo(0)
                            .pageSize(0)
                            .orderBy("")
                            .desc()
                            .column("DEPT_DISCHARGE_FROM_CODE", "DEPT_DISCHARGE_FROM_NAME",
                                    "ADMISSION_TIME", "DISCHARGE_TIME", "VISIT_ID", "DEPT_ADMISSION_TO_NAME",
                                    "DEPT_ADMISSION_TO_CODE", "INP_NO", "IN_PATIENT_ID", "TRANSFER_TIME", "ORG_NO", "ORG_NAME")
                            .build());
            if(resultVo.isSuccess()){
                patAdts = resultVo.getContent().getResult();
            }
//            List<Map<String, String>> patAdts = hbaseDao.findConditionByPatient(HdrTableEnum.HDR_PAT_ADT.getCode(), oid,
//                    patientId, transFilter, new String[]{"DEPT_DISCHARGE_FROM_CODE", "DEPT_DISCHARGE_FROM_NAME",
//                            "ADMISSION_TIME", "DISCHARGE_TIME", "VISIT_ID", "DEPT_ADMISSION_TO_NAME",
//                            "DEPT_ADMISSION_TO_CODE", "INP_NO", "IN_PATIENT_ID", "TRANSFER_TIME", "ORG_NO", "ORG_NAME"});
            for (Map<String, String> adt : patAdts) {
                Map<String, Object> covert = new HashMap<String, Object>();
                covert.put("dept_Name", adt.get("DEPT_ADMISSION_TO_NAME")); //入院科室
                covert.put("dept_Code", adt.get("DEPT_ADMISSION_TO_CODE"));
                covert.put("start_Date", adt.get("ADMISSION_TIME")); //入院时间
                covert.put("end_Date", adt.get("DISCHARGE_TIME")); //出院时间
                covert.put("visit_Id", adt.get("VISIT_ID")); //就诊次数
                covert.put("orgNo", adt.get("ORG_NO")); //医疗机构标识
                covert.put("orgName", adt.get("ORG_NAME")); //医疗机构名称
                //查询本次住院诊断  主诊断
                String visitIdTmp = adt.get("VISIT_ID");
                getPatDiagInp(covert, oid, patientId, visitIdTmp);

                //若就诊时间没有，用挂号时间代替
                String registing_time = adt.get("TRANSFER_TIME");
                if (StringUtils.isBlank(adt.get("ADMISSION_TIME"))) {
                    covert.put("start_Date", registing_time);
                }
                if (!covert.isEmpty()) {
                    covert.put("visit_Type", "2");//住院
                    visits.add(covert);
                }
            }
        } else {
//            if(StringUtils.isNotBlank(visitId)){
//                transFilter.add(new PropertyFilter("VISIT_ID", "STRING", MatchType.EQ.getOperation(), visitId));
//            }
            if (StringUtils.isNotBlank(startDate)) {
                transFilter.add(new PropertyFilter("VISIT_TIME", MatchType.GE.getOperation(), startDate + " 00:00:00"));
            }
            if (StringUtils.isNotBlank(endDate)) {
                transFilter.add(new PropertyFilter("VISIT_TIME", MatchType.LE.getOperation(), endDate + " 23:59:59"));
            }
            List<Map<String, String>> outVisits = new ArrayList<>();
            ResultVo<PageResultVo<Map<String, String>>> resultVo = hbaseQueryClient.getPageByCondition(
                    PageRequestBuilder.init()
                            .tableName(HdrTableEnum.HDR_OUT_VISIT.getCode())
                            .patientId(patientId)
                            .oid(oid)
                            .visitId(visitId)
                            .visitTypeCode(visitType)
                            .filters(transFilter)
                            .pageNo(0)
                            .pageSize(0)
                            .orderBy("")
                            .desc()
                            .column("REGISTING_TIME", "VISIT_TIME", "VISIT_ID", "VISIT_DEPT_NAME",
                                    "VISIT_DEPT_CODE", "VISIT_FLAG", "OUT_PATIENT_ID", "EMERGENCY_VISIT_IND", "ORG_NO", "ORG_NAME")
                            .build());
            if(resultVo.isSuccess()){
                outVisits = resultVo.getContent().getResult();
            }

//            List<Map<String, String>> outVisits = hbaseDao.findConditionByPatient(HdrTableEnum.HDR_OUT_VISIT.getCode(), oid,
//                    patientId, transFilter,new String[]{"REGISTING_TIME", "VISIT_TIME", "VISIT_ID", "VISIT_DEPT_NAME",
//                            "VISIT_DEPT_CODE", "VISIT_FLAG", "OUT_PATIENT_ID", "EMERGENCY_VISIT_IND", "ORG_NO", "ORG_NAME"});
            for (Map<String, String> outVisit : outVisits) {
                //北医三院特殊处理：1-挂号未就诊 9-退号，忽略这两类门诊信息
                if (HdrConstantEnum.HOSPITAL_BYSY.getCode().equals(ConfigCache.getCache(oid, "org_oid"))) {
                    String visit_flag = outVisit.get("VISIT_FLAG");
                    if (visit_flag != null && "1,9,".indexOf(visit_flag + ",") > -1) {
                        continue;
                    }
                }
                Map<String, Object> covert = new HashMap<String, Object>();
                covert.put("dept_Name", outVisit.get("VISIT_DEPT_NAME")); //就诊科室
                covert.put("dept_Code", outVisit.get("VISIT_DEPT_CODE"));
                covert.put("start_Date", outVisit.get("VISIT_TIME")); //就诊时间
                covert.put("visit_Id", outVisit.get("VISIT_ID")); //就诊次数
                covert.put("orgNo", outVisit.get("ORG_NO")); //医疗机构标识
                covert.put("orgName", outVisit.get("ORG_NAME")); //医疗机构名称
                //查询本次门诊诊断
                String visitIdTmp = outVisit.get("VISIT_ID");
                getPatDiagOutp(covert, oid, patientId, visitIdTmp);

                //若就诊时间没有，用挂号时间代替
                String registing_time = outVisit.get("REGISTING_TIME");
                if (StringUtils.isBlank(outVisit.get("VISIT_TIME"))) {
                    covert.put("start_Date", registing_time);
                }
                if (!covert.isEmpty()) {
                    if (outVisit.get("EMERGENCY_VISIT_IND") != null && "true".equalsIgnoreCase(outVisit.get("EMERGENCY_VISIT_IND"))) {
                        covert.put("visit_Type", "3");
                    } else {
                        covert.put("visit_Type", "1");
                    }
                    visits.add(covert);
                }
            }
        }
    }

    /**
     * @param patId   患者编号
     * @param visitId 就诊次数
     * @return
     * @Description 方法描述: 查询患者某次住院的诊断
     */
    public void getPatDiagInp(Map<String, Object> covert, String oid, String patId, String visitId) {
        //获取患者住院诊断:初步诊断、主诊断
        String tableName = "HDR_EMR_CONTENT_DIAG";
//		String preRowkey = HbaseCURDUtils.getRowkeyPrefix(patId) + "|" + visitId + "|";
        List<PropertyFilter> filters = new ArrayList<PropertyFilter>();
        List<Map<String, String>> list = new ArrayList<>();
        ResultVo<PageResultVo<Map<String, String>>> resultVo = hbaseQueryClient.getPageByCondition(
                PageRequestBuilder.init()
                        .tableName(tableName)
                        .patientId(patId)
                        .oid(oid)
                        .visitId(visitId)
                        .visitTypeCode("")
                        .filters(filters)
                        .pageNo(0)
                        .pageSize(0)
                        .orderBy("DIAGNOSIS_TIME")
                        .desc()
                        .column("DIAGNOSIS_CODE", "DIAGNOSIS_NAME", "DIAGNOSIS_TIME", "DIAGNOSIS_DOCTOR_CODE", "DIAGNOSIS_DOCTOR_NAME",
                                "DIAGNOSIS_DESC", "DIAGNOSIS_NUM", "DIAGNOSIS_TYPE_NAME")
                        .build());

        if(resultVo.isSuccess()){
            list = resultVo.getContent().getResult();
        }
//        List<Map<String, String>> list = hbaseDao.findConditionByPatientVisitId(tableName, oid, patId, visitId, filters, new String[]{
//                "DIAGNOSIS_CODE", "DIAGNOSIS_NAME", "DIAGNOSIS_TIME", "DIAGNOSIS_DOCTOR_CODE", "DIAGNOSIS_DOCTOR_NAME",
//                "DIAGNOSIS_DESC", "DIAGNOSIS_NUM", "DIAGNOSIS_TYPE_NAME"});
        //诊断时间降序
        Utils.sortListByDate(list, "DIAGNOSIS_TIME", Page.Sort.DESC);

        List<Map<String, String>> qtzd = new ArrayList<Map<String, String>>();
        if (list.size() == 0) {
            covert.put("diag", "");
            covert.put("code", "");
        }
        for (Map<String, String> map : list) {
            if ("1".equals(map.get("DIAGNOSIS_NUM"))) {
                //主诊断code  name
                covert.put("diag", map.get("DIAGNOSIS_NAME") == null ? "" : map.get("DIAGNOSIS_NAME"));
                covert.put("code", map.get("DIAGNOSIS_CODE") == null ? "" : map.get("DIAGNOSIS_CODE"));
                covert.put("time", map.get("DIAGNOSIS_TIME") == null ? "" : map.get("DIAGNOSIS_TIME"));
                covert.put("doctor_name", map.get("DIAGNOSIS_DOCTOR_NAME") == null ? "" : map.get("DIAGNOSIS_DOCTOR_NAME"));
            } else {
                Map<String, String> else_diag = new HashMap<String, String>();
                else_diag.put("else_type", map.get("DIAGNOSIS_TYPE_NAME") == null ? "" : map.get("DIAGNOSIS_TYPE_NAME"));
                else_diag.put("else_name", map.get("DIAGNOSIS_NAME") == null ? "" : map.get("DIAGNOSIS_NAME"));
                else_diag.put("else_time", map.get("DIAGNOSIS_TIME") == null ? "" : map.get("DIAGNOSIS_TIME"));
                else_diag.put("else_doctor_name", map.get("DIAGNOSIS_DOCTOR_NAME") == null ? "" : map.get("DIAGNOSIS_DOCTOR_NAME"));
                qtzd.add(else_diag);
            }
        }
        covert.put("else_diag", qtzd);
    }


    /**
     * @param patId   患者编号
     * @param visitId 就诊次数
     * @return
     * @Description 方法描述: 查询患者某次门诊的诊断
     */
    public void getPatDiagOutp(Map<String, Object> covert, String oid, String patId, String visitId) {

        List<PropertyFilter> filters = new ArrayList<PropertyFilter>();
        List<Map<String, String>> list = new ArrayList<>();
        ResultVo<PageResultVo<Map<String, String>>> resultVo = hbaseQueryClient.getPageByCondition(
                PageRequestBuilder.init()
                        .tableName( HdrTableEnum.HDR_OUT_VISIT_DIAG.getCode())
                        .patientId(patId)
                        .oid(oid)
                        .visitId(visitId)
                        .visitTypeCode("")
                        .filters(filters)
                        .pageNo(0)
                        .pageSize(0)
                        .orderBy("DIAGNOSIS_TIME")
                        .desc()
                        .column("DIAGNOSIS_CODE",
                                "DIAGNOSIS_NAME", "DIAGNOSIS_TIME", "DIAGNOSIS_DOCTOR_CODE", "DIAGNOSIS_DOCTOR_NAME",
                                "DIAGNOSIS_DESC", "DIAGNOSIS_NUM", "DIAGNOSIS_TYPE_NAME")
                        .build());

        if(resultVo.isSuccess()){
            list = resultVo.getContent().getResult();
        }
//        List<Map<String, String>> list = hbaseDao.findConditionByPatientVisitId(
//                HdrTableEnum.HDR_OUT_VISIT_DIAG.getCode(), oid, patId, visitId, filters, new String[]{"DIAGNOSIS_CODE",
//                        "DIAGNOSIS_NAME", "DIAGNOSIS_TIME", "DIAGNOSIS_DOCTOR_CODE", "DIAGNOSIS_DOCTOR_NAME",
//                        "DIAGNOSIS_DESC", "DIAGNOSIS_NUM", "DIAGNOSIS_TYPE_NAME"});
        //诊断时间降序
        Utils.sortListByDate(list, "DIAGNOSIS_TIME", Page.Sort.DESC);
        if (list != null && list.size() > 0) {
            //主诊断
            covert.put("diag", list.get(0).get("DIAGNOSIS_NAME") == null ? "" : list.get(0).get("DIAGNOSIS_NAME"));
            covert.put("name", list.get(0).get("DIAGNOSIS_NAME") == null ? "" : list.get(0).get("DIAGNOSIS_NAME"));
            covert.put("code", list.get(0).get("DIAGNOSIS_CODE") == null ? "" : list.get(0).get("DIAGNOSIS_CODE"));
            covert.put("time", list.get(0).get("DIAGNOSIS_TIME") == null ? "" : list.get(0).get("DIAGNOSIS_TIME"));
            covert.put("doctor_name", list.get(0).get("DIAGNOSIS_DOCTOR_NAME") == null ? "" : list.get(0).get("DIAGNOSIS_DOCTOR_NAME"));
        } else {
            covert.put("diag", "");
            covert.put("name", "");
            covert.put("code", "");
        }

    }

    @Override
    public void getDiagsListNum(String outPatientId, Map<String, Object> resultMap) {

        //获取就诊记录

        //2.获取就诊列表
        int num = getAllVisitsNum(outPatientId);
        resultMap.put("num", num);
    }

    /**
     * @param patientId 患者编号
     * @param visitType 就诊类型
     * @return 返回类型： void
     * @Description 方法描述: 根据患者编号和就诊类型查询该类型下的就诊数量
     */
    private int getVisitsAndAddToVisitsNum(String oid, String patientId, String visitType,String visitId) {
        List<PropertyFilter> transFilter = new ArrayList<PropertyFilter>();
        //住院
        if ("02".equals(visitType)) {
            List<Map<String, String>> patAdts = new ArrayList<Map<String, String>>();
//            if (StringUtils.isNotBlank(visitId)){
//                transFilter.add(new PropertyFilter("VISIT_ID", "STRING", MatchType.EQ.getOperation(), visitId));
//            }
            transFilter.add(new PropertyFilter("TRANS_NO", MatchType.EQ.getOperation(), "0")); //仅取入出院，不取转科
            ResultVo<PageResultVo<Map<String, String>>> resultVo = hbaseQueryClient.getPageByCondition(
                    PageRequestBuilder.init()
                            .tableName(HdrTableEnum.HDR_PAT_ADT.getCode())
                            .patientId(patientId)
                            .oid(oid)
                            .visitId(visitId)
                            .visitTypeCode(visitType)
                            .filters(transFilter)
                            .pageNo(0)
                            .pageSize(0)
                            .orderBy("")
                            .desc()
                            .column("VISIT_ID")
                            .build());



            if(resultVo.isSuccess()){
                patAdts = resultVo.getContent().getResult();
            }
//            patAdts = hbaseDao.findConditionByPatient(HdrTableEnum.HDR_PAT_ADT.getCode(), oid,
//                    patientId, transFilter, new String[]{"VISIT_ID"});
            return patAdts.size();
        } else {

            List<Map<String, String>> outVisits = new ArrayList<Map<String, String>>();
//            if (StringUtils.isNotBlank(visitId)){
//                transFilter.add(new PropertyFilter("VISIT_ID", "STRING", MatchType.EQ.getOperation(), visitId));
//            }
            ResultVo<PageResultVo<Map<String, String>>> resultVo = hbaseQueryClient.getPageByCondition(
                    PageRequestBuilder.init()
                            .tableName(HdrTableEnum.HDR_OUT_VISIT.getCode())
                            .patientId(patientId)
                            .oid(oid)
                            .visitId(visitId)
                            .visitTypeCode(visitType)
                            .filters(transFilter)
                            .pageNo(0)
                            .pageSize(0)
                            .orderBy("")
                            .desc()
                            .column("VISIT_ID", "VISIT_FLAG")
                            .build());



            if(resultVo.isSuccess()){
                outVisits = resultVo.getContent().getResult();
            }
//            outVisits = hbaseDao.findConditionByPatient(HdrTableEnum.HDR_OUT_VISIT.getCode(), oid,
//                    patientId,transFilter, new String[]{"VISIT_ID", "VISIT_FLAG"});
            int num = 0;
            for (Map<String, String> outVisit : outVisits) {
                //北医三院特殊处理：1-挂号未就诊 9-退号，忽略这两类门诊信息
                if (HdrConstantEnum.HOSPITAL_BYSY.getCode().equals(ConfigCache.getCache(oid, "org_oid"))) {
                    String visit_flag = outVisit.get("VISIT_FLAG");
                    if (visit_flag != null && "1,9,".indexOf(visit_flag + ",") > -1) {
                        continue;
                    }
                }
                num++;
            }
            return num;
        }
    }

    /**
     * @return
     * @Description 方法描述: 通过EID或PATIENT_ID查询患者所有的就诊信息，优先EID
     */
    private int getAllVisitsNum(String outPatientId) {

        String[] split = outPatientId.split(",");
        //根据上述筛选后的患者编号和就诊类型   获取所有就诊列表
        int num = 0;
        for (String s:split) {
            String[] vtpid = s.split("\\|");
            num = num + getVisitsAndAddToVisitsNum(vtpid[2], vtpid[1], vtpid[0],vtpid[3]);
        }

        return num;
    }

    /**
     * 返回医技视图诊断信息
     *
     * @param this_oid
     * @param oid
     * @param patientId
     * @param visitId
     * @param visitType
     * @return
     */
    @Override
    public com.goodwill.hdr.web.common.vo.ResultVO<Map<String, Object>> getDiags(String this_oid, String oid, String patientId, String visitId, String visitType) {
        com.goodwill.hdr.web.common.vo.ResultVO<Map<String, Object>> resultVO = new com.goodwill.hdr.web.common.vo.ResultVO<>();
        Map<String, Object> covert = new HashMap<>();
        if (visitType.equals("02")) {
            if (StringUtils.isNotBlank(this_oid)) {
                if (this_oid.equals(oid) || "ALL".equals(this_oid)) {
                    this_oid = oid;
                }
                getPatDiagInp(covert, this_oid, patientId, visitId);
            }
        } else {
            if (StringUtils.isNotBlank(this_oid)) {
                if (this_oid.equals(oid) || "ALL".equals(this_oid)) {
                    this_oid = oid;
                }
                getPatDiagOutp(covert, this_oid, patientId, visitId);
            }
        }
        resultVO.success("查询成功", covert);
        return resultVO;
    }

    @Override
    public com.goodwill.hdr.web.common.vo.ResultVO<List<Map<String, Object>>> getDiagLists(String this_oid, String oid, String outPatientId) {
        com.goodwill.hdr.web.common.vo.ResultVO<List<Map<String, Object>>> resultVO = new com.goodwill.hdr.web.common.vo.ResultVO<>();
        List<Map<String, Object>> diagsResult = new ArrayList<>();
        List<Map<String, Object>> visits = new ArrayList<>();
        String[] vTypeAndPidAndOidArray = outPatientId.split(",");
        String oidTmp = "";
        for (String s : vTypeAndPidAndOidArray) {
            String[] strings = s.split("\\|");
            String visitType = strings[0];
            String patientId = strings[1];
            String oidInCoordinates = strings[2];
            String visitId = strings[3];

            /*在为ALL时查询所有，否则仅查询当前选中的oid*/
            if (this_oid.equalsIgnoreCase("ALL")) {
                oidTmp = oidInCoordinates;
            } else {
                oidTmp = this_oid;
            }
            if (oidInCoordinates.equals(oidTmp)) {
                getVisitsAndAddToVisits(visits, oidInCoordinates, patientId, visitType, visitId, "", "");
            }
        }
        //时间排序
        Utils.sortListByDate(visits, "start_Date", Page.Sort.DESC);
        //以年分组
        Map<String, Object> result = new HashMap<String, Object>();
        CivUtils.groupByYearDate(result, visits, "start_Date");
        //转换格式
        for (String year : result.keySet()) {
            Map<String, Object> map = new HashMap<String, Object>();
            map.put("year", year);
            map.put("data", result.get(year));
            diagsResult.add(map);
        }
        //重新排序
        Utils.sortListByDate(diagsResult, "year", Page.Sort.DESC);
        if (diagsResult.isEmpty()) {
            resultVO.fail("无历次诊断信息");
            return resultVO;
        }
        resultVO.success("查询成功", diagsResult);
        return resultVO;
    }
}