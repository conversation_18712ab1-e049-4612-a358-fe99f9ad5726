package com.goodwill.hdr.civ.controller;

import com.goodwill.hdr.civ.service.ConsultService;
import io.swagger.annotations.Api;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;

/**
 * @PROJECT_NAME: civ-5.2
 * @DESCRIPTION:
 * @USER: xiaohuya
 * @DATE: 28/07/2025 17:39
 */
@RequestMapping("/consult")
@RestController
@Api(tags = "会诊记录")
public class ConsultAction {
    @Autowired
    private ConsultService consultService;
    @RequestMapping("/getConsultList")
    public List<Map<String, String>> getConsultList(String oid, String patientId, String visitId, String visitType) {
        return consultService.getConsultList( oid, patientId, visitId, visitType);
    }
    @RequestMapping("/getConsultDetails")
    public Map<String, String> getConsultDetails(String oid, String patientId, String visitId, String visitType,String consultNo) {
        return consultService.getConsultDetails( oid, patientId, visitId, visitType,consultNo);
    }
}
