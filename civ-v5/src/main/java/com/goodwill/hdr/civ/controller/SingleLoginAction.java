package com.goodwill.hdr.civ.controller;

import com.alibaba.fastjson.JSONObject;
import com.goodwill.hdr.civ.service.SingleLoginService;
import io.swagger.annotations.Api;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 20250424 对接第三方单点登录 此方法做的通用化配置
 * 说明：此类开发是针对现场版本低，不想升级，而采取的打补丁措施
 */
@RequestMapping("/singleLogin")
@RestController
@Api(tags = "第三方单点登录")
public class SingleLoginAction {
    private static Logger logger = LoggerFactory.getLogger(SingleLoginAction.class);

    @Autowired
    SingleLoginService singleLoginService;

    @PostMapping("/getSingleloginUserByOther")
    public JSONObject getSingleloginUserByOther(String oid, String param) {
        String s = singleLoginService.getSingleloginUserByOther(oid, param);
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("userCode", s);
        logger.info("jsonObject==" + jsonObject);
        return jsonObject;
    }

}
