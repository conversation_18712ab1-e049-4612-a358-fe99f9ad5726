package com.goodwill.hdr.civ.dao.impl;


import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.goodwill.hdr.civ.dao.SprcialtyViewNewDao;
import com.goodwill.hdr.civ.entity.*;
import com.goodwill.hdr.civ.mapper.*;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;
import org.springframework.web.bind.annotation.DeleteMapping;

import java.util.HashMap;
import java.util.List;
import java.util.Map;


/**
 * <AUTHOR>
 * @Description 类描述： 专科视图dao实现类
 * @modify 修改记录：
 */
@Repository
public class SprcialtyViewDaoNewImpl implements SprcialtyViewNewDao {
    @Autowired
    SpecialtyIndicatorConfigMapper specialtyIndicatorConfigMapper;
    @Autowired
    DeptSpecialtyIndicatorMapper deptSpecialtyIndicatorMapper;
    @Autowired
    SicknessMapper sicknessMapper;
    @Autowired
    SpecialtyDeptMapper specialtyDeptMapper;
    @Autowired
    DeptSpecialtyConfigMapper deptSpecialtyConfigMapper;
    @Autowired
    SpecialtyConfigMapper specialtyConfigMapper;

//    /**
//     * 通过用户编码查询用户是否有自定义设置，其中admin用户代表系统初始设置模板
//     *
//     * @param doctorCode
//     * @return
//     */
//    @Override
//    public List<SpecialtyIndicatorConfigEntity> getSpecityIndicatorConfig(String doctorCode, String maindaig) {
//        String sql = "select * from  civ_specialty_indicator_config where doctor_code = ? and  sickness_code = ? and is_inuse = 'Y' " +
//                " group by sickness_code,sickness_name,item_code,item_name,item_indicator_code,item_indicator_name order by  array_index asc";
//        Query query = createSQLQuery(SpecialtyIndicatorConfigEntity.class, sql, doctorCode, maindaig);
//        List<SpecialtyIndicatorConfigEntity> list = query.list();
//        return list;
//    }
//
//    @Override
//    public List<SpecialtyIndicatorConfigEntity> getDiagSpecityIndicatorConfig(String doctorCode) {
//        String sql = "select  sickness_code,sickness_name from  civ_specialty_indicator_config where is_inuse = 'Y' ";
//        if (StringUtils.isNotBlank(doctorCode)) {
//            sql += " and doctor_code = '" + doctorCode + "'";
//        }
//        sql += " group  by sickness_code";
//        Query query = createSqlQuery(sql);
//        List<SpecialtyIndicatorConfigEntity> listEntity = new ArrayList<SpecialtyIndicatorConfigEntity>();
//        List<Object> list = query.list();
//        for (Object obj : list) {
//            SpecialtyIndicatorConfigEntity entity = new SpecialtyIndicatorConfigEntity();
//            Object[] objArr = (Object[]) obj;
//            entity.setSicknessCode(objArr[0].toString());
//            entity.setSicknessName(objArr[1].toString());
//            listEntity.add(entity);
//        }
//        return listEntity;
//    }
//
//    @Override
//    public List<SpecialtyConfigEntity> getSpecityConfig(String doctorCode, String maindaig) {
//        String sql = "select *  from  civ_specialty_config where  is_inuse = 'Y'   and doctor_code = ?  and  sickness_code = ? " +  //and  sickness_code = ? and
//                " group by sickness_code,sickness_name,item_code,item_name order by  array_index asc";
//        Query query = createSqlQuery(sql, doctorCode, maindaig);
//        List<Object> list = query.list();
//        List<SpecialtyConfigEntity> listRes = new ArrayList<SpecialtyConfigEntity>();
//        for (Object obj : list) {
//            SpecialtyConfigEntity entity = new SpecialtyConfigEntity();
//            Object[] objArr = (Object[]) obj;
//            entity.setId(Integer.parseInt(objArr[0].toString()));
//            entity.setDoctorCode(objArr[1].toString());
//            entity.setDoctorName(objArr[2].toString());
//            entity.setSicknessCode(objArr[3].toString());
//            entity.setSicknessName(objArr[4].toString());
//            entity.setItemCode(objArr[5].toString());
//            entity.setItemName(objArr[6].toString());
//            entity.setDataType(objArr[7].toString());
//            entity.setIsInuse(objArr[8].toString());
//            entity.setArrayIndex(objArr[9].toString());
//            listRes.add(entity);
//        }
//        return listRes;
//    }
//
//    /**
//     * 查询科室下的大项
//     * @param deptCode
//     * @return
//     */
//    @Override
//    public List<SpecialtyDeptConfigEntity> getDeptSpecityConfig(String deptCode) {
//        String sql = "select  * from  civ_dept_specialty_config where  is_inuse = 'Y'  ";
//                if(StringUtils.isNotBlank(deptCode)){
//                    sql += " and dept_code = ?";
//                }
//        sql +=  " group by dept_code,dept_name,item_code,item_name order by  array_index asc";
//        SQLQuery query = (SQLQuery)createSqlQuery(sql, deptCode);
//        List<SpecialtyDeptConfigEntity> list = query.addEntity(SpecialtyDeptConfigEntity.class).list();
//        return list;
//    }
//
//    @Override
//    public List<SpecialtyConfigEntity> getSpecityConfigMainDiag(String doctorCode) {
//        String sql = "select *  from  civ_specialty_config where  is_inuse = 'Y'   and doctor_code = ?  " +  //and  sickness_code = ? and
//                " group by sickness_code,sickness_name,item_code,item_name order by  array_index asc";
//        Query query = createSqlQuery(sql, doctorCode);
//        List<Object> list = query.list();
//        List<SpecialtyConfigEntity> listRes = new ArrayList<SpecialtyConfigEntity>();
//        for (Object obj : list) {
//            SpecialtyConfigEntity entity = new SpecialtyConfigEntity();
//            Object[] objArr = (Object[]) obj;
//            entity.setId(Integer.parseInt(objArr[0].toString()));
//            entity.setDoctorCode(objArr[1].toString());
//            entity.setDoctorName(objArr[2].toString());
//            entity.setSicknessCode(objArr[3].toString());
//            entity.setSicknessName(objArr[4].toString());
//            entity.setItemCode(objArr[5].toString());
//            entity.setItemName(objArr[6].toString());
//            entity.setDataType(objArr[7].toString());
//            entity.setIsInuse(objArr[8].toString());
//            entity.setArrayIndex(objArr[9].toString());
//            listRes.add(entity);
//        }
//        return listRes;
//    }
//
//    /**
//     * 查询疾病集合
//     *
//     * @param maindiag
//     * @return
//     */
//    public List<SicknessEntity> getSicknessList(String maindiag,int pageNo, int pageSize) {
//        String sql = "select * from civ_sickness where is_inuse = 'Y'  and  sickness_code is not null and sickness_name is not null  ";
//        if (StringUtils.isNotBlank(maindiag)) {
//            sql += " and  sickness_name like '%" + maindiag + "%'";
//        }
//        if (pageNo > 0 && pageSize > 0) {
//            sql += "  limit " + (pageNo - 1) * pageSize + "," + pageSize;
//        }
//        Query query = createSqlQuery(sql);
//        List<Object> listSql = query.list();
//        List<SicknessEntity> listRes = new ArrayList<SicknessEntity>();
//        for (Object obj : listSql) {
//            SicknessEntity entity = new SicknessEntity();
//            Object[] objArr = (Object[]) obj;
//            entity.setId(Integer.parseInt(objArr[0] == null ? "" : objArr[0].toString()));
//            entity.setSicknessCode(objArr[1] == null ? "" : objArr[1].toString());
//            entity.setSicknessName(objArr[2] == null ? "" : objArr[2].toString());
//            entity.setIsInUse(objArr[3] == null ? "" : objArr[3].toString());
//            listRes.add(entity);
//        }
//        return listRes;
//    }
//
//    /**
//     *
//     * 查询科室集合
//     *
//     * @param
//     * @return
//     */
//    public List<SpecialtyDeptEntity> getDeptList(String dept, int pageNo, int pageSize) {
//        String sql = "select * from civ_specialty_dept where is_inuse = 'Y' ";
//        if (StringUtils.isNotBlank(dept)) {
//            sql += " and  ( dept_name like '%" + dept + "%'  or  dept_code like   '%"+dept+"%')";
//        }
//        if (pageNo > 0 && pageSize > 0) {
//            sql += "  limit " + (pageNo - 1) * pageSize + "," + pageSize;
//        }
//        SQLQuery query = (SQLQuery)createSqlQuery(sql);
//        List<SpecialtyDeptEntity> listRes  = query.addEntity(SpecialtyDeptEntity.class).list();
//        return listRes;
//    }
//
//    /**
//     * 查询科室
//     * @param keyWord
//     * @param pageNo
//     * @param pageSize
//     * @return
//     */
//    @Override
//    public List<Map<String,String>> getDeptListToAdd(String keyWord, int pageNo, int pageSize) {
//        String sql = "select deptcode,deptname from security_dept where enabled = '1'  ";
//        if (StringUtils.isNotBlank(keyWord)) {
//            sql += " and  ( deptname like '%" + keyWord + "%'  or  deptcode like   '%"+keyWord+"%')";
//        }
//        if (pageNo > 0 && pageSize > 0) {
//            sql += "  limit " + (pageNo - 1) * pageSize + "," + pageSize;
//        }
//        Query query = createSqlQuery(sql);
//        List<Object> listSql = query.list();
//        List<Map<String,String>> list = new ArrayList<Map<String,String>>();
//        for (Object obj : listSql) {
//            Map<String,String> map = new HashMap<String, String>();
//            Object[] objArr = (Object[]) obj;
//            map.put("code",objArr[0] == null ? "" : objArr[0].toString());
//            map.put("name",objArr[1] == null ? "" : objArr[1].toString());
//            list.add(map);
//        }
//        return list;
//    }
//

    /**
     * 通过用户编码查询用户是否有自定义设置，其中admin用户代表系统初始设置模板
     *
     * @param doctorCode
     * @return
     */
    @Override
    public List<SpecialtyIndicatorConfigEntity> getConfigByUserCodeMainDaig(String doctorCode, String mainDaig, String itemCode) {
        QueryWrapper<SpecialtyIndicatorConfigEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.select("sickness_code", "sickness_name", "item_code", "item_name", "item_indicator_code", "item_indicator_name")
                .eq("doctor_code", doctorCode)
                .eq(" sickness_code", mainDaig)
                .eq("is_inuse", "Y")
                .eq("item_code", itemCode)
                .groupBy("sickness_code", "sickness_name", "item_code", "item_name", "item_indicator_code", "item_indicator_name","array_index")
                .orderByAsc("array_index");
        List<SpecialtyIndicatorConfigEntity> list = specialtyIndicatorConfigMapper.selectList(queryWrapper);

        return list;
    }
//    /**
//     * 获取科室下的明细项
//     * @param deptCode
//     * @param itemCode
//     * @return
//     */
//    @Override
//    public List<SpecialtyDeptIndicatorConfig> getConfigByDept(String deptCode, String itemCode) {
//        String sql = "select *  from  civ_dept_specialty_indicator where dept_code = ? and is_inuse = 'Y' and item_code = ? " +
//                " group by dept_code,dept_name,item_code,item_name,sub_item_code,sub_item_name order by  array_index asc";
//        SQLQuery query = (SQLQuery)createSqlQuery(sql, deptCode, itemCode);
//        List<SpecialtyDeptIndicatorConfig> listRes  = query.addEntity(SpecialtyDeptIndicatorConfig.class).list();
//        return listRes;
//    }
//
//    /**
//     * 删除疾病表
//     */
    @Override
    public int updateSicknessByCode(String sicknessCode, String status) {
        UpdateWrapper<Sickness> updateWrapper = new UpdateWrapper<>();
        updateWrapper.eq("sickness_code",sicknessCode);
        Sickness sickness = new Sickness();
        sickness.setIsInuse(status);
        int result  = sicknessMapper.update(sickness,updateWrapper);
//        String sql = "update   civ_sickness set  is_inuse = ? where sickness_code = ? ";
//        Query query = createSqlQuery(sql, status, sicknessCode);
//        int result = query.executeUpdate();
        return result;
    }
//    /**
//     * 删除科室表
//     */
//    @Override
//    public int deleteDeptByCode(String deptCode) {
//        String sql = "delete from     civ_specialty_dept  where dept_code = ? ";
//        Query query = createSqlQuery(sql, deptCode);
//        int result = query.executeUpdate();
//        return result;
//    }
//
//
//    /**
//     * 更新诊断-各明细项配置
//     */
    @Override
    public int updateSicknessIndicatorConfig(String userCode, String sicknessCode, String status) {
        UpdateWrapper<SpecialtyIndicatorConfigEntity> updateWrapper = new UpdateWrapper<>();
        updateWrapper.eq("doctor_code",userCode);
        updateWrapper.eq("sickness_code",sicknessCode);
        SpecialtyIndicatorConfigEntity specialtyIndicatorConfig = new SpecialtyIndicatorConfigEntity();
        specialtyIndicatorConfig.setIsInuse(status);
        int result  = specialtyIndicatorConfigMapper.update(specialtyIndicatorConfig,updateWrapper);
        return result;
    }
//
//    /**
//     * 更新科室-各明细项配置
//     */
//    @Override
//    public int deleteDeptIndicatorConfig( String dept_code) {
//        String sql = "delete from    civ_dept_specialty_indicator  where dept_code = ? ";
//        Query query = createSqlQuery(sql,dept_code);
//        int result = query.executeUpdate();
//        return result;
//    }
//
//    /**
//     * 添加疾病诊断到mysql数据库
//     *
//     * @param sicknessCode
//     * @param sicknessName
//     */
    @Override
    public int addSickness(String sicknessCode, String sicknessName) {
        Sickness sickness = new Sickness();
        sickness.setIsInuse("Y");
        sickness.setSicknessName(sicknessName);
        sickness.setSicknessCode(sicknessCode);
        int i = sicknessMapper.insert(sickness);
        return i;
    }
//
//    /**
//     * 添加专科科室
//     * @param deptCode
//     * @param deptName
//     * @return
//     */
    @Override
    public int addSpecityDept(String deptCode, String deptName) {
        SpecialtyDept specialtyDept = new SpecialtyDept();
        specialtyDept.setDeptCode(deptCode);
        specialtyDept.setDeptName(deptName);
        specialtyDept.setIsInuse("Y");
        int i = specialtyDeptMapper.insert(specialtyDept);
        return i;
    }
//
//    /**
//     * 添加疾病诊断明细配置到mysql数据库
//     *
//     * @param sicknessCode
//     * @param sicknessName
//     */
    @Override
    public int addSicknessIndicatorConfig(String userCode, String userName, String sicknessCode, String sicknessName) {
        SpecialtyConfig healthsc = new SpecialtyConfig();
        healthsc.setDoctorCode(userCode);
        healthsc.setDoctorName(userName);
        healthsc.setSicknessCode(sicknessCode);
        healthsc.setSicknessName(sicknessName);
        healthsc.setArrayIndex(1);
        healthsc.setIsInuse("Y");
        healthsc.setDataType("FoldLine");
        healthsc.setItemCode("SicknessSMTZ");
        healthsc.setItemName("重点生命体征");

        SpecialtyConfig labsc = new SpecialtyConfig();
        labsc.setDoctorCode(userCode);
        labsc.setDoctorName(userName);
        labsc.setSicknessCode(sicknessCode);
        labsc.setSicknessName(sicknessName);
        labsc.setArrayIndex(6);
        labsc.setIsInuse("Y");
        labsc.setDataType("List");
        labsc.setItemCode("Labs");
        labsc.setItemName("重点检验");

        SpecialtyConfig labDetailssc = new SpecialtyConfig();
        labDetailssc.setDoctorCode(userCode);
        labDetailssc.setDoctorName(userName);
        labDetailssc.setSicknessCode(sicknessCode);
        labDetailssc.setSicknessName(sicknessName);
        labDetailssc.setArrayIndex(2);
        labDetailssc.setIsInuse("Y");
        labDetailssc.setDataType("FoldLine");
        labDetailssc.setItemCode("LabDetails");
        labDetailssc.setItemName("重点检验结果");

        SpecialtyConfig dragsc = new SpecialtyConfig();
        labDetailssc.setDoctorCode(userCode);
        labDetailssc.setDoctorName(userName);
        labDetailssc.setSicknessCode(sicknessCode);
        labDetailssc.setSicknessName(sicknessName);
        dragsc.setArrayIndex(5);
        dragsc.setIsInuse("Y");
        dragsc.setDataType("Table");
        dragsc.setItemCode("Drags");
        dragsc.setItemName("重点用药");

        SpecialtyConfig examsc = new SpecialtyConfig();
        examsc.setDoctorCode(userCode);
        examsc.setDoctorName(userName);
        examsc.setSicknessCode(sicknessCode);
        examsc.setSicknessName(sicknessName);
        examsc.setArrayIndex(4);
        examsc.setIsInuse("Y");
        examsc.setDataType("List");
        examsc.setItemCode("Exams");
        examsc.setItemName("重点检查报告");

//        SpecialtyConfig nurseAndEatsc = new SpecialtyConfig();
//        nurseAndEatsc.setDoctorCode(userCode);
//        nurseAndEatsc.setDoctorName(userName);
//        nurseAndEatsc.setSicknessCode(sicknessCode);
//        nurseAndEatsc.setSicknessName(sicknessName);
//        nurseAndEatsc.setArrayIndex(3);
//        nurseAndEatsc.setIsInuse("Y");
//        nurseAndEatsc.setDataType("Table");
//        nurseAndEatsc.setItemCode("NurseAndEat");
//        nurseAndEatsc.setItemName("护理及饮食");
//        String sqlHealth = " insert into civ_specialty_config(doctor_code,doctor_name,sickness_code,sickness_name,item_code,item_name,data_type,is_inuse,array_index) values(?,?,?,?,'Health','重点生命体征','FoldLine','Y','1')";
//        String sqlLab = " insert into civ_specialty_config(doctor_code,doctor_name,sickness_code,sickness_name,item_code,item_name,data_type,is_inuse,array_index) values(?,?,?,?,'Lab','重点检验','List','Y','6')";
//        String sqlLabDetail = " insert into civ_specialty_config(doctor_code,doctor_name,sickness_code,sickness_name,item_code,item_name,data_type,is_inuse,array_index) values(?,?,?,?,'LabDetail','重点检验结果','FoldLine','Y','2')";
//        String sqlDrag = " insert into civ_specialty_config(doctor_code,doctor_name,sickness_code,sickness_name,item_code,item_name,data_type,is_inuse,array_index) values(?,?,?,?,'Drag','重点用药','Table','Y','5')";
//        String sqlExam = " insert into civ_specialty_config(doctor_code,doctor_name,sickness_code,sickness_name,item_code,item_name,data_type,is_inuse,array_index) values(?,?,?,?,'Exam','重点检查报告','List','Y','4')";
//        String sqlNurseAndEat = " insert into civ_specialty_config(doctor_code,doctor_name,sickness_code,sickness_name,item_code,item_name,data_type,is_inuse,array_index) values(?,?,?,?,'NurseAndEat','护理及饮食','Table','Y','3')";

//        Query query = createSqlQuery(sqlHealth, userCode, userName, sicknessCode, sicknessName);
//        query.executeUpdate();
//        query = createSqlQuery(sqlLab, userCode, userName, sicknessCode, sicknessName);
//        query.executeUpdate();
//        query = createSqlQuery(sqlLabDetail, userCode, userName, sicknessCode, sicknessName);
//        query.executeUpdate();
//        query = createSqlQuery(sqlDrag, userCode, userName, sicknessCode, sicknessName);
//        query.executeUpdate();
//        query = createSqlQuery(sqlExam, userCode, userName, sicknessCode, sicknessName);
//        query.executeUpdate();
//        query = createSqlQuery(sqlNurseAndEat, userCode, userName, sicknessCode, sicknessName);
        int i1 = specialtyConfigMapper.insert(healthsc);
        int i2 = specialtyConfigMapper.insert(labsc);
        int i3 = specialtyConfigMapper.insert(labDetailssc);
        int i4 = specialtyConfigMapper.insert(dragsc);
        int i5 = specialtyConfigMapper.insert(examsc);
        //int i6 = specialtyConfigMapper.insert(nurseAndEatsc);
        return i5;
    }
//
//    /**
//     * 删除科室中间表配置
//     * @param deptCode
//     * @return
//     */
//    @Override
//    public int delDeptSpecialtyConfig(String deptCode) {
//        String sql = "delete from    civ_dept_specialty_config  where dept_code = ? ";
//        Query query = createSqlQuery(sql,deptCode);
//        int result = query.executeUpdate();
//        return result;
//    }
//    /**
//     * 初始化专科科室设置civ_dept_specialty_config数据
//     * @param deptCode
//     * @param deptName
//     * @return
//     */
    @Override
    public int addDeptSpecityConfig(String deptCode, String deptName) {
        DeptSpecialtyConfig healthDeptConfig = new DeptSpecialtyConfig();
        healthDeptConfig.setDeptCode(deptCode);
        healthDeptConfig.setDeptName(deptName);
        healthDeptConfig.setArrayIndex("1");
        healthDeptConfig.setIsInuse("Y");
        healthDeptConfig.setDataType("FoldLine");
        healthDeptConfig.setItemCode("SicknessSMTZ");
        healthDeptConfig.setItemName("重点生命体征");

        DeptSpecialtyConfig labDeptConfig = new DeptSpecialtyConfig();
        labDeptConfig.setDeptCode(deptCode);
        labDeptConfig.setDeptName(deptName);
        labDeptConfig.setArrayIndex("6");
        labDeptConfig.setIsInuse("Y");
        labDeptConfig.setDataType("List");
        labDeptConfig.setItemCode("Lab");
        labDeptConfig.setItemName("重点检验");

        DeptSpecialtyConfig LabDetailsDeptConfig = new DeptSpecialtyConfig();
        LabDetailsDeptConfig.setDeptCode(deptCode);
        LabDetailsDeptConfig.setDeptName(deptName);
        LabDetailsDeptConfig.setArrayIndex("2");
        LabDetailsDeptConfig.setIsInuse("Y");
        LabDetailsDeptConfig.setDataType("FoldLine");
        LabDetailsDeptConfig.setItemCode("LabDetails");
        LabDetailsDeptConfig.setItemName("重点检验结果");

        DeptSpecialtyConfig dragDeptConfig = new DeptSpecialtyConfig();
        dragDeptConfig.setDeptCode(deptCode);
        dragDeptConfig.setDeptName(deptName);
        dragDeptConfig.setArrayIndex("5");
        dragDeptConfig.setIsInuse("Y");
        dragDeptConfig.setDataType("Table");
        dragDeptConfig.setItemCode("Drags");
        dragDeptConfig.setItemName("重点用药");

        DeptSpecialtyConfig examDeptConfig = new DeptSpecialtyConfig();
        examDeptConfig.setDeptCode(deptCode);
        examDeptConfig.setDeptName(deptName);
        examDeptConfig.setArrayIndex("4");
        examDeptConfig.setIsInuse("Y");
        examDeptConfig.setDataType("List");
        examDeptConfig.setItemCode("Exams");
        examDeptConfig.setItemName("重点检查报告");

//        DeptSpecialtyConfig nurseAndEatDeptConfig = new DeptSpecialtyConfig();
//        nurseAndEatDeptConfig.setDeptCode(deptCode);
//        nurseAndEatDeptConfig.setDeptName(deptName);
//        nurseAndEatDeptConfig.setArrayIndex("3");
//        nurseAndEatDeptConfig.setIsInuse("Y");
//        nurseAndEatDeptConfig.setDataType("Table");
//        nurseAndEatDeptConfig.setItemCode("NurseAndEat");
//        nurseAndEatDeptConfig.setItemName("护理及饮食");

//        String sqlHealth = " insert into civ_dept_specialty_config(dept_code,dept_name,item_code,item_name,data_type,is_inuse,array_index) values(?,?,'Health','重点生命体征','FoldLine','Y','1')";
//        String sqlLab = " insert into civ_dept_specialty_config(dept_code,dept_name,item_code,item_name,data_type,is_inuse,array_index) values(?,?,'Lab','重点检验','List','Y','6')";
//        String sqlLabDetail = " insert into civ_dept_specialty_config(dept_code,dept_name,item_code,item_name,data_type,is_inuse,array_index) values(?,?,'LabDetail','重点检验结果','FoldLine','Y','2')";
//        String sqlDrag = " insert into civ_dept_specialty_config(dept_code,dept_name,item_code,item_name,data_type,is_inuse,array_index) values(?,?,'Drag','重点用药','Table','Y','5')";
//        String sqlExam = " insert into civ_dept_specialty_config(dept_code,dept_name,item_code,item_name,data_type,is_inuse,array_index) values(?,?,'Exam','重点检查报告','List','Y','4')";
//        String sqlNurseAndEat = " insert into civ_dept_specialty_config(dept_code,dept_name,item_code,item_name,data_type,is_inuse,array_index) values(?,?,'NurseAndEat','护理及饮食','Table','Y','3')";
        int i1 = deptSpecialtyConfigMapper.insert(healthDeptConfig);
        int i2 = deptSpecialtyConfigMapper.insert(labDeptConfig);
        int i3 = deptSpecialtyConfigMapper.insert(LabDetailsDeptConfig);
        int i4 = deptSpecialtyConfigMapper.insert(dragDeptConfig);
        int i5 = deptSpecialtyConfigMapper.insert(examDeptConfig);
        //int i6 = deptSpecialtyConfigMapper.insert(nurseAndEatDeptConfig);
//        Query query = createSqlQuery(sqlHealth, deptCode, deptName);
//        query.executeUpdate();
//        query = createSqlQuery(sqlLab, deptCode, deptName);
//        query.executeUpdate();
//        query = createSqlQuery(sqlLabDetail, deptCode, deptName);
//        query.executeUpdate();
//        query = createSqlQuery(sqlDrag,deptCode, deptName);
//        query.executeUpdate();
//        query = createSqlQuery(sqlExam,deptCode, deptName);
//        query.executeUpdate();
//        query = createSqlQuery(sqlNurseAndEat, deptCode, deptName);
        return i1;
    }
//
//    /**
//     * 根据id删除诊断明细
//     *
//     * @param id
//     */
    @Override
    public int delIndicatorConfig(String id) {
        Map<String, Object> condition = new HashMap<>();
        condition.put("id", id);
        int i = specialtyIndicatorConfigMapper.deleteByMap(condition);
        return i ;
    }
//    /**
//     * 根据id删除科室明细
//     *
//     * @param id
//     */
    public int delDeptIndicatorConfig(String id) {
        Map<String, Object> condition = new HashMap<>();
        condition.put("id", id);
        int i = specialtyIndicatorConfigMapper.deleteByMap(condition);
        return i;

    }
//
//
//    /**
//     * 新增配置
//     */
    @Override
    public int addInditorConfig(Map<String, String> map) {
        SpecialtyIndicatorConfigEntity s = new SpecialtyIndicatorConfigEntity();
        s.setDoctorCode(map.get("userCode"));
        s.setDoctorName(map.get("userName"));
        s.setSicknessCode(map.get("mainDiag"));
        s.setSicknessName(map.get("mainDiagName"));
        s.setItemCode(map.get("itemCode"));
        s.setItemName(map.get("itemName"));
        s.setItemClassCode(map.get("itemClassCode"));
        s.setItemClassName(map.get("itemClassName"));
        s.setItemIndicatorCode(map.get("itemIndicatorCode"));
        s.setItemIndicatorName(map.get("itemIndicatorName"));
        s.setIsInuse("Y");
        s.setArrayIndex(Integer.valueOf(map.get("arrayIndex")));
        return specialtyIndicatorConfigMapper.insert(s);
    }
//
//    /**
//     * 添加科室细项
//     * @param map
//     * @return
//     */
    @Override
    public int addDeptInditorConfig(Map<String, String> map) {
        DeptSpecialtyIndicator deptSpecialtyIndicator = new DeptSpecialtyIndicator();
        deptSpecialtyIndicator.setDeptCode(map.get("deptCode"));
        deptSpecialtyIndicator.setDeptName(map.get("deptName"));
        deptSpecialtyIndicator.setItemCode(map.get("itemCode"));
        deptSpecialtyIndicator.setItemName(map.get("itemName"));
        deptSpecialtyIndicator.setItemClassCode(map.get("itemClassCode"));
        deptSpecialtyIndicator.setItemClassName(map.get("itemClassName"));
        deptSpecialtyIndicator.setSubItemCode(map.get("itemIndicatorCode"));
        deptSpecialtyIndicator.setSubItemName(map.get("itemIndicatorName"));
        deptSpecialtyIndicator.setIsInuse("Y");

        return deptSpecialtyIndicatorMapper.insert(deptSpecialtyIndicator);
    }
//    /**
//     * 查找已經配置的最大索引數，即配置個數
//     * @param deptCode
//     * @param itemCode
//     * @return
//     */
     @Override
     public String getDeptInditorMaxIndex(String deptCode, String itemCode) {
         QueryWrapper<DeptSpecialtyIndicator> queryWrapper = new QueryWrapper<>();
         queryWrapper.eq("doctor_code", deptCode)
                 .eq("is_inuse", "Y")
                 .eq("item_code", itemCode)
                 .select("COALESCE(max(array_index),0)+1 as array_index");
         List<DeptSpecialtyIndicator> list = deptSpecialtyIndicatorMapper.selectList(queryWrapper);
         Integer  index = 1;
         for(DeptSpecialtyIndicator deptSpecialtyIndicator : list){
             index = deptSpecialtyIndicator.getArrayIndex() +1;
         }
         if(StringUtils.isBlank(index.toString())||"null".equals(index)){
             index = 1;
         }
         return index.toString();
    }
//    /**
//     * 查找已經配置的最大索引數，即配置個數
//     * @param diagCode
//     * @param itemCode
//     * @return
//     */
    @Override
    public String getInditorMaxIndex(String userCode,String diagCode, String itemCode) {
        QueryWrapper<SpecialtyIndicatorConfigEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("doctor_code", userCode)
                .eq(" sickness_code", diagCode)
                .eq("is_inuse", "Y")
                .eq("item_code", itemCode)
                .select("COALESCE(max(array_index),0)+1 as array_index");
        List<SpecialtyIndicatorConfigEntity> list = specialtyIndicatorConfigMapper.selectList(queryWrapper);
        Integer  index = 1;
       for(SpecialtyIndicatorConfigEntity specialtyIndicatorConfigEntity : list){
         index = specialtyIndicatorConfigEntity.getArrayIndex() +1;
       }
        if(StringUtils.isBlank(index.toString())||"null".equals(index)){
            index = 1;
        }
        return index.toString();
    }
}
