package com.goodwill.hdr.civ.vo;

import java.util.List;

public class TreeNodeVo {
    private String name;
    private List<NameAndCodeVo> children;

    public TreeNodeVo() {
    }

    public TreeNodeVo(String name, List<NameAndCodeVo> children) {
        this.name = name;
        this.children = children;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public List<NameAndCodeVo> getChildren() {
        return children;
    }

    public void setChildren(List<NameAndCodeVo> children) {
        this.children = children;
    }
}
