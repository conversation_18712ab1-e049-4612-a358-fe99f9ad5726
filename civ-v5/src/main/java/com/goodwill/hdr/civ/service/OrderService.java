package com.goodwill.hdr.civ.service;


import com.goodwill.hdr.civ.vo.ResultVo;
import com.goodwill.hdr.core.orm.Page;
import com.goodwill.hdr.web.common.vo.ResultVO;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Description 类描述：医嘱服务接口
 * @Date 2018年4月27日
 * @modify 修改记录：
 */
public interface OrderService {

    /**
     * @param patId         患者编号
     * @param visitId       就诊次数
     * @param visitType     就诊类型
     * @param orderStatus   医嘱状态
     * @param orderProperty 医嘱性质
     * @param orderBy
     * @param orderDir
     * @param kfShowConfig
     * @param pageNo        页码
     * @param pageSize      分页单位
     * @return
     * @Description 方法描述: 某次就诊的口服药品医嘱
     */
    public Page<Map<String, String>> getDrugListKF(String oid, String patId, String visitId, String visitType, String orderStatus,
                                                   String orderProperty, String mainDiag, String orderType, String deptCode, String orderItemName, String orderBy, String orderDir, List<Map<String, Object>> kfShowConfig, int pageNo, int pageSize);

    /**
     * @param patId         患者编号
     * @param visitId       就诊次数
     * @param visitType     就诊类型
     * @param orderStatus   医嘱状态
     * @param orderProperty 医嘱性质
     * @param orderBy
     * @param orderDir
     * @param jmShowConfig
     * @param pageNo        页码
     * @param pageSize      分页单位
     * @return
     * @Description 方法描述: 某次就诊的静脉注射药品医嘱
     */
    public Page<Map<String, String>> getDrugListJMZS(String oid, String patId, String visitId, String visitType, String orderStatus,
                                                     String orderProperty, String mainDiag, String orderCode, String deptCode, String orderItemName, String orderBy, String orderDir, List<Map<String, Object>> jmShowConfig,int pageNo, int pageSize);

    /**
     * @param patId         患者编号
     * @param visitId       就诊次数
     * @param visitType     就诊类型
     * @param orderStatus   医嘱状态
     * @param orderProperty 医嘱性质
     * @param pageNo        页码
     * @param pageSize      分页单位
     * @return
     * @Description 方法描述: 某次就诊的其他药品医嘱
     */


    Page<Map<String, String>> getDrugListQTYP(String oid, String patId, String visitId, String visitType, String orderStatus,
                                              String orderProperty, String orderItemName, String orderBy, String orderDir, int pageNo, int pageSize);

    /**
     * @param patientId     患者编号
     * @param visitId       就诊次数
     * @param orderType     医嘱类型
     * @param visitType     就诊类型
     * @param orderStatus   医嘱状态
     * @param orderProperty 医嘱性质
     * @param orderBy
     * @param orderDir
     * @param pageNo        页码
     * @param pageSize      分页单位
     * @return
     * @Description 方法描述: 当前视图 - 药品医嘱
     */
    public Page<Map<String, String>> getCVDrugList(String oid, String patientId, String visitId, String orderType, String visitType,
                                                   String orderStatus, String orderProperty, String mainDiag, String deptCode, String orderCode, String orderItemName, String orderBy, String orderDir, int pageNo,
                                                   int pageSize);

    /**
     * @param patId     患者编号
     * @param visitId   就诊次数
     * @param visitType 就诊类型
     * @param orderBy
     * @param orderDir
     * @param pageNo    页码
     * @param pageSize  分页单位
     * @return
     * @Description 方法描述: 某次就诊的检验医嘱
     */
    public Page<Map<String, String>> getLabList(String oid, String patId, String visitId, String visitType, String mainDiag,
                                                String orderCode, String deptCode, String orderItemName, String orderBy, String orderDir, int pageNo, int pageSize);

    /**
     * @param patId     患者编号
     * @param visitId   就诊次数
     * @param visitType 就诊类型
     * @param orderBy
     * @param orderDir
     * @param pageNo    页码
     * @param pageSize  分页单位
     * @return
     * @Description 方法描述: 某次就诊的检查医嘱
     */
    public Page<Map<String, String>> getExamList(String oid, String patId, String visitId, String visitType, String mainDiag,
                                                 String orderCode, String deptCode, String orderItemName, String orderBy, String orderDir, int pageNo, int pageSize);

    /**
     * @param patId     患者编号
     * @param visitId   就诊次数
     * @param visitType 就诊类型
     * @param orderBy
     * @param orderDir
     * @param pageNo    页码
     * @param pageSize  分页单位
     * @return
     * @Description 方法描述: 某次就诊的手术医嘱
     */
    public Page<Map<String, String>> getOperList(String oid, String patId, String visitId, String visitType, String orderItemName, String orderBy, String orderDir, int pageNo,
                                                 int pageSize);

    /**
     * @param patId    患者编号
     * @param visitId  就诊次数
     * @param pageNo   页码
     * @param pageSize 分页单位
     * @return
     * @Description 方法描述: 某次就诊的用血申请
     */
    public Page<Map<String, String>> getBloodList(String oid, String patId, String visitId, int pageNo, int pageSize);

    /**
     * @param patId    患者编号
     * @param visitId  就诊次数
     * @param orderNo  医嘱号
     * @param pageNo   页码
     * @param pageSize 分页单位
     * @return
     * @Description 方法描述: 医嘱执行记录
     */
    public Page<Map<String, String>> getOrderExeList(String oid, String patId, String visitId, String orderNo, String orderType, int pageNo,
                                                     int pageSize);

    /**
     * @param patId    患者编号
     * @param visitId  就诊次数
     * @param orderNo  医嘱号
     * @param pageNo   页码
     * @param pageSize 分页单位
     * @return
     * @Description 方法描述: 药品医嘱发药审核数据
     */
    public Page<Map<String, String>> getDrugCheckList(String oid, String patId, String visitId, String orderNo,String orderType, int pageNo,
                                                      int pageSize,String visitType);


    /**
     * @param patId        患者号
     * @param visitId      住院次
     * @param visitType    就诊类型
     * @param types        医嘱类型
     * @param filterString filterString 查询字符串 column|in|080,079,004,035,088,134;a|=|b
     * @param orderReporte 指定返回的报告类型 lab,exam
     * @param orderby      排序字段
     * @param orderdir     排序规则
     * @param configMap
     * @param pageNo       页码
     * @param pageSize     分页单位
     * @return 分页对象
     * @Description 方法描述: 查询医嘱列表
     */
    public Page<Map<String, String>> getOrderList(String oid, String patId, String visitId, String visitType, List<String> types,
                                                  String filterString, String orderReporte, String orderby, String orderdir, String mainDiag,
                                                  String orderType, String deptCode, List<Map<String, Object>> configMap, int pageNo, int pageSize,String pharmacyWay);

    /**
     * @param patId     患者编号
     * @param visitId   就诊次数
     * @param visitType 就诊类型
     * @param types     医嘱类别
     * @param orderNo   医嘱号
     * @param orderBy   排序字段
     * @param orderDir  排序规则
     * @param pageNo    页码
     * @param pageSize  分页单位
     * @return
     * @Description 方法描述: 查询手术医嘱列表
     */
    public Page<Map<String, String>> getOperOrderList(String oid, String patId, String visitId, String visitType,
                                                      List<String> types, String orderNo, String orderBy, String orderDir, String orderIteam, int pageNo, int pageSize);


    /**
     * @param patId       患者编号
     * @param visitId     就诊次数
     * @param types       医嘱类型   口服，静脉，检查，检验...
     * @param filterStr   附加条件  column|in|080,079,004,035,088,134;a|=|b
     * @param orderReport 检查，检验医嘱是否出报告
     * @param orderBy     排序字段
     * @param orderDir    排序规则
     * @param pageNo      页码
     * @param pageSize    分页单位
     * @return
     * @Description 方法描述: 查询北医三院门诊医嘱
     */
    public Page<Map<String, String>> getOrderListMZ(String oid, String patId, String visitId, List<String> types, String filterStr,
                                                    String orderReport, String orderBy, String orderDir, int pageNo, int pageSize);

    /**
     * @param patId     患者编号
     * @param visitId   就诊次数
     * @param types     医嘱类型
     * @param filterStr 附加条件  column|in|080,079,004,035,088,134;a|=|b
     * @param orderBy   排序字段
     * @param orderDir  排序规则
     * @param pageNo    页码
     * @param pageSize  分页单位
     * @return
     * @Description 方法描述: 查询北医三院门诊手术医嘱
     */
    public Page<Map<String, String>> getOperOrderListMZ(String oid, String patId, String visitId, List<String> types,
                                                        String filterStr, String orderBy, String orderDir, int pageNo, int pageSize);

    /**
     * @param patId    患者编号
     * @param visitId  就诊次数
     * @param orderBy  排序字段
     * @param orderDir 排序规则
     * @param pageNo   页码
     * @param pageSize 分页单位
     * @return
     * @Description 方法描述: 查询用血申请列表
     */
    public Page<Map<String, String>> getBloodApplyList(String oid, String patId, String visitId, String orderBy, String orderDir,
                                                       int pageNo, int pageSize);

    /**
     * @param patId
     * @param orderNo
     * @return
     * @Description 方法描述: 根据检验医嘱号查询检验报告详情
     */
    public Page<Map<String, String>> getLabReportDetails(String oid, String patId, String visitType, String visitId, String field, String orderNo,
                                                         int pageNo, int pageSize);

    /**
     * @param patId
     * @param visitType
     * @param orderNo
     * @return
     * @Description 方法描述: 根据检查医嘱号查询检查报告详情
     */
    public Map<String, String> getExamReportDetails(String oid, String patId, String visitType, String visitId, String field, String orderNo);

    /**
     * @param patientId 患者编号
     * @param visitId   就诊次数
     * @param visitType 就诊类型
     * @return
     * @Description 方法描述: 患者某次就诊的医嘱数量
     */
    public long getOrderCount(String oid, String patientId, String visitId, String visitType);

    /**
     * @param patientId 患者编号
     * @param visitId   就诊次数
     * @param visitType 就诊类型
     * @return
     * @Description 方法描述: 患者某次就诊的各类医嘱的数量
     */
    public Map<String, Object> getTypeOrderCount(String oid, String patientId, String visitId, String visitType);


    /**
     * @param patId          患者编号
     * @param orderStatus    医嘱状态    （下达，审核，开始，撤销，停止）
     * @param orderProperty  医嘱性质    （长期，临时）
     * @param orderType      医嘱类型  （药品，检查，检验，手术）
     * @param orderTimeBegin 开立开始时间
     * @param orderTimeEnd   开立结束时间
     * @param orderName      药品名称
     * @param orderby        排序字段
     * @param orderdir       排序规则
     * @param pageNo         页码
     * @param pageSize       分页单位
     * @return
     * @Description 方法描述: 查询患者医嘱(分类视图)
     */
    public Page<Map<String, String>> getOrders(String oid, String patId, String orderStatus, String orderProperty, String orderType,
                                               String orderTimeBegin, String orderTimeEnd, String orderName, String orderby, String orderdir, int pageNo,
                                               int pageSize, String outPatientId, String visitType,String drugType , String drugProperty);

    /**
     * @param resultMap 数量集合
     * @return
     * @Description 方法描述: 查询患者医嘱(分类视图)数量
     */
    public void getOrdersNum( Map<String, Object> resultMap, String orderType, String outPatientId);


    /**
     * 护理医嘱查询
     *
     * @param patId
     * @param visitId
     * @param visitType
     * @param orderStatus
     * @param orderProperty
     * @param pageNo
     * @param pageSize
     * @return
     */
//    Page<Map<String, String>> getNurseOrderList(String oid, String patId, String visitId, String visitType, String orderStatus,
//                                                String orderProperty, int pageNo, int pageSize);

    /**
     * 其他医嘱查询
     *
     * @param patId
     * @param visitId
     * @param visitType
     * @param orderStatus
     * @param orderProperty
     * @param orederBy
     * @param orderDir
     * @param pageNo
     * @param pageSize
     * @return
     */
    Page<Map<String, String>> getOthersOrderList(String oid, String patId, String visitId, String visitType, String orderStatus,
                                                 String orderProperty, String orderItemName, String orederBy, String orderDir, int pageNo, int pageSize);


    /**
     * 获取患者末次就诊显示配置
     */
    Map<String, String> getPatLastInfoViewConfig(String oid);

    /**
     * 获取每次就诊全部医嘱
     *
     * @param patientId
     * @param visitId
     * @param visitType
     * @param orderStatus
     * @param orderProperty
     * @param orderType
     * @param orderBy
     * @param orderDir
     * @param pageNo
     * @param pageSize
     * @return
     */
    Page<Map<String, String>> getVisitPageView(String oid, String patientId, String visitId, String visitType, String orderStatus,
                                               String orderProperty, String orderItemName, String orderType, String orderBy, String orderDir, int pageNo, int pageSize);


    /**
     * 获取医技视图末次就诊手术医嘱
     *
     * @param this_oid
     * @param oid
     * @param patientId
     * @param visitId
     * @param visitType
     * @return
     */
    ResultVO<List<Map<String, String>>> getOperApplyList(String this_oid, String oid, String patientId, String visitId, String visitType);

    ResultVo<Map<String, List<Map<String, String>>>> getOclTableHead(String oid, String orderType);

    /**
     * 调用单医嘱节点闭环
     * @param oid
     * @param patientId
     * @param visitId
     * @param visitTypeCode
     * @param orderProperty
     * @param orderType
     * @return
     */
    Map<String, String> getSingleOcl(String oid,String patientId, String visitId, String visitTypeCode,String orderProperty,String orderType,String orderNo);

}
