package com.goodwill.hdr.civ.controller;

import com.baomidou.dynamic.datasource.toolkit.StringUtils;


import com.goodwill.hdr.civ.enums.DictType;
import com.goodwill.hdr.civ.service.DictHbaseService;
import com.goodwill.hdr.core.orm.Page;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Description 类描述：hbase字典查询类
 * @Date 2018年7月10日
 * @modify 修改记录：
 */
@RequestMapping("/dict")
@RestController
@Api(tags = "当前视图")
public class DictHbaseAction {

    @Autowired
    private DictHbaseService dictHbaseService;

    /**
     * @throws Exception
     * @Description 获取检查分类字典
     */
    @ApiOperation(value = "获取检查分类字典", notes = "获取检查分类字典", httpMethod = "POST")
    @RequestMapping(value = "/getExamClassDict", method = RequestMethod.POST)
    public Page<Map<String, String>> getExamClassDict(String column, String keyword, String visitId,String pageSize, String pageNo, String oid)  {
        //设置页面和页码
        Page<Map<String, String>> page = new Page<Map<String, String>>();
        page.setPageNo(Integer.parseInt(pageNo));
        page.setPageSize(Integer.parseInt(pageSize));
        Page<Map<String, String>> result = dictHbaseService.getDict(oid,visitId, DictType.EXAMCLASS, column == null ? "" : column,
                keyword == null ? "" : keyword, page);
        return result;
    }

    /**
     * @Description 获取检验明细项字典
     */
    @ApiOperation(value = "获取检验明细项字典", notes = "获取检验明细项字典", httpMethod = "POST")
    @RequestMapping(value = "/getLabSubDict", method = RequestMethod.POST)
    public Page<Map<String, String>> getLabSubDict(String column, String keyword,String visitId, String pageSize, String pageNo, String oid) {
        //设置页面和页码
        Page<Map<String, String>> page = new Page<Map<String, String>>();
        page.setPageNo(Integer.parseInt(pageNo));
        page.setPageSize(Integer.parseInt(pageSize));
        Page<Map<String, String>> result = dictHbaseService.getDict(oid, visitId,DictType.LABSUB, column == null ? "" : column,
                keyword == null ? "" : keyword, page);
        return result;
    }

    /**
     * @Description 获取科室字典
     */
    @ApiOperation(value = "获取科室字典", notes = "获取科室字典", httpMethod = "POST")
    @RequestMapping(value = "/getDeptDict", method = RequestMethod.POST)
    public Page<Map<String, String>> getDeptDict(String column, String keyword ,String visitId,String pageSize, String pageNo, String oid) {
        //设置页面和页码
        Page<Map<String, String>> page = new Page<Map<String, String>>();
        page.setPageNo(Integer.parseInt(pageNo));
        page.setPageSize(Integer.parseInt(pageSize));
        Page<Map<String, String>> result = dictHbaseService.getDict(oid, visitId,DictType.DEPT, column == null ? "" : column,
                keyword == null ? "" : keyword, page);
        return result;
    }

    /**
     * @return 返回类型： void
     * @throws Exception
     * @Description 方法描述:查询护理医嘱字典
     */
    @ApiOperation(value = "查询护理医嘱字典", notes = "查询护理医嘱字典", httpMethod = "POST")
    @RequestMapping(value = "/getNurseDict", method = RequestMethod.POST)
    public Page<Map<String, String>> getNurseDict(String column, String keyword, String visitId, String pageSize, String pageNo, String oid) throws Exception {
        //设置页面和页码
        Page<Map<String, String>> page = new Page<Map<String, String>>();
        page.setPageNo(Integer.parseInt(pageNo));
        page.setPageSize(Integer.parseInt(pageSize));
        Page<Map<String, String>> result = dictHbaseService.getDict(oid,visitId, DictType.NURSE, column == null ? "" : column,
                keyword == null ? "" : keyword, page);
        return result;
    }

    /**
     * @return 返回类型： void
     * @throws Exception
     * @Description 方法描述:查询手术医嘱字典
     */
    @ApiOperation(value = "查询手术医嘱字典", notes = "查询手术医嘱字典", httpMethod = "POST")
    @RequestMapping(value = "/getOperDict", method = RequestMethod.POST)
    public Page<Map<String, String>> getOperDict(String column, String keyword,String visitId, String pageSize, String pageNo, String oid) throws Exception {
        //设置页面和页码
        Page<Map<String, String>> page = new Page<Map<String, String>>();
        page.setPageNo(Integer.parseInt(pageNo));
        page.setPageSize(Integer.parseInt(pageSize));
        Page<Map<String, String>> result = dictHbaseService.getDict(oid,visitId, DictType.OPER, column == null ? "" : column,
                keyword == null ? "" : keyword, page);
        return result;
    }

    /**
     * @return 返回类型： void
     * @throws Exception
     * @Description 方法描述: 查询手术ICD9字典
     */
    @ApiOperation(value = "询手术ICD9字典", notes = "询手术ICD9字典", httpMethod = "POST")
    @RequestMapping(value = "/getOperICD9Dict", method = RequestMethod.POST)
    public Page<Map<String, String>> getOperICD9Dict(String column, String keyword,String visitId, String pageSize, String pageNo, String oid) throws Exception {
        //设置页面和页码
        Page<Map<String, String>> page = new Page<Map<String, String>>();
        page.setPageNo(Integer.parseInt(pageNo));
        page.setPageSize(Integer.parseInt(pageSize));
        Page<Map<String, String>> result = dictHbaseService.getDict(oid,visitId, DictType.OPERICD9, column == null ? "" : column,
                keyword == null ? "" : keyword, page);
        return result;
    }

    /**
     * @return 返回类型： void
     * @Description 方法描述: 查询诊断字典
     */
    @ApiOperation(value = "查询诊断字典", notes = "查询诊断字典", httpMethod = "POST")
    @RequestMapping(value = "/getDiagDict", method = RequestMethod.POST)
    public Page<Map<String, String>> getDiagDict(String column, String keyword,String visitId, String pageSize, String pageNo, String oid) {
        //设置页面和页码
        Page<Map<String, String>> page = new Page<Map<String, String>>();
        page.setPageNo(Integer.parseInt(pageNo));
        page.setPageSize(Integer.parseInt(pageSize));
        Page<Map<String, String>> result = dictHbaseService.getDict(oid,visitId, DictType.DIAG, column == null ? "" : column,
                keyword == null ? "" : keyword, page);
        return result;
    }

    /**
     * @return 返回类型： void
     * @Description 方法描述: 查询药品医嘱字典
     */
    @ApiOperation(value = "查询药品医嘱字典", notes = "查询药品医嘱字典", httpMethod = "POST")
    @RequestMapping(value = "/getDrugDict", method = RequestMethod.POST)
    public Page<Map<String, String>> getDrugDict(String column, String keyword, String visitId ,String pageSize, String pageNo, String oid) {
        //设置页面和页码
        Page<Map<String, String>> page = new Page<Map<String, String>>();
        page.setPageNo(Integer.parseInt(pageNo));
        page.setPageSize(Integer.parseInt(pageSize));
        Page<Map<String, String>> result = dictHbaseService.getDict(oid,visitId, DictType.DRUG, column == null ? "" : column,
                keyword == null ? "" : keyword, page);
        return result;
    }

    /**
     * @return 返回类型： void
     * @Description 方法描述:查询检查医嘱字典
     */
    @ApiOperation(value = "查询检查医嘱字典", notes = "查询检查医嘱字典", httpMethod = "POST")
    @RequestMapping(value = "/getExamDict", method = RequestMethod.POST)
    public Page<Map<String, String>> getExamDict(String column, String keyword, String visitId,String pageSize, String pageNo, String oid) {
        //设置页面和页码
        Page<Map<String, String>> page = new Page<Map<String, String>>();
        page.setPageNo(Integer.parseInt(pageNo));
        page.setPageSize(Integer.parseInt(pageSize));
        Page<Map<String, String>> result = dictHbaseService.getDict(oid, visitId,DictType.EXAM, column == null ? "" : column,
                keyword == null ? "" : keyword, page);
        return result;
    }

    /**
     * @return 返回类型： void
     * @Description 方法描述:查询门诊检查医嘱字典
     */
    @ApiOperation(value = "查询门诊检查医嘱字典", notes = "查询门诊检查医嘱字典", httpMethod = "POST")
    @RequestMapping(value = "/getOutExamDict", method = RequestMethod.POST)
    public Page<Map<String, String>> getOutExamDict(String column, String keyword,  String visitId,String pageSize, String pageNo, String oid) {
        //设置页面和页码
        Page<Map<String, String>> page = new Page<Map<String, String>>();
        page.setPageNo(Integer.parseInt(pageNo));
        page.setPageSize(Integer.parseInt(pageSize));
        Page<Map<String, String>> result = dictHbaseService.getOutExamDict(oid, visitId,DictType.EXAM_MZ,
                column == null ? "" : column, keyword == null ? "" : keyword, page);
        return result;
    }

    /**
     * @return 返回类型： void
     * @Description 方法描述: 查询检验医嘱字典
     */
    @ApiOperation(value = "查询检验医嘱字典", notes = "查询检验医嘱字典", httpMethod = "POST")
    @RequestMapping(value = "/getLabDict", method = RequestMethod.POST)
    public Page<Map<String, String>> getLabDict(String column, String keyword, String visitId,String pageSize, String pageNo, String oid) {
        //设置页面和页码
        Page<Map<String, String>> page = new Page<Map<String, String>>();
        page.setPageNo(Integer.parseInt(pageNo));
        page.setPageSize(Integer.parseInt(pageSize));
        Page<Map<String, String>> result = dictHbaseService.getDict(oid, visitId,DictType.LAB, column == null ? "" : column,
                keyword == null ? "" : keyword, page);
        return result;
    }

    /**
     * 获取门诊检验字典编码
     *
     * @return
     */
    @ApiOperation(value = "获取门诊检验字典编码", notes = "获取门诊检验字典编码", httpMethod = "POST")
    @RequestMapping(value = "/getLabgetOutLabDictDict", method = RequestMethod.POST)
    public Page<Map<String, String>> getOutLabDict(String column, String keyword,String visitId, String pageSize, String pageNo, String oid) {
        //设置页面和页码
        Page<Map<String, String>> page = new Page<Map<String, String>>();
        page.setPageNo(Integer.parseInt(pageNo));
        page.setPageSize(Integer.parseInt(pageSize));
        Page<Map<String, String>> result = dictHbaseService.getDict(oid, visitId,DictType.LAB_MZ, column == null ? "" : column,
                keyword == null ? "" : keyword, page);
        return result;
    }

    /**
     * @return 返回类型： void
     * @Description 方法描述:查询检验报告明细字典
     */
    @ApiOperation(value = "查询检验报告明细字典", notes = "查询检验报告明细字典", httpMethod = "POST")
    @RequestMapping(value = "/getLabSubDict_temp", method = RequestMethod.POST)
    public Page<Map<String, String>> getLabSubDict_temp(String column ,String visitId,String keyword, String pageSize, String pageNo, String oid) {
        //设置页面和页码
        Page<Map<String, String>> page = new Page<Map<String, String>>();
        page.setPageNo(Integer.parseInt(pageNo));
        page.setPageSize(Integer.parseInt(pageSize));
        Page<Map<String, String>> result = dictHbaseService.getDictSubLab(oid, visitId,column == null ? "" : column,
                keyword == null ? "" : keyword, page);
        return result;
    }

    /**
     * @Description 根据code获取诊断编码名称
     */
    @ApiOperation(value = "根据code获取诊断编码名称", notes = "根据code获取诊断编码名称", httpMethod = "POST")
    @RequestMapping(value = "/viewDiagByCode", method = RequestMethod.POST)
    public Page<Map<String, String>> viewDiagByCode(String keyword,String visitId, String pageSize, String pageNo, String oid) {
        //设置页面和页码
        Page<Map<String, String>> page = new Page<Map<String, String>>();
        page.setPageNo(Integer.parseInt(pageNo));
        page.setPageSize(Integer.parseInt(pageSize));
        List<String> codeList = new ArrayList<String>();
        if (StringUtils.isNotBlank(keyword)) {
            return dictHbaseService.getNamebyCode(oid,visitId ,DictType.DIAG, keyword, page, false);
        } else {
            return page;
        }
    }

    /**
     * @Description 根据code获取手术编码名称
     */
    @ApiOperation(value = "根据code获取手术编码名称", notes = "根据code获取手术编码名称", httpMethod = "POST")
    @RequestMapping(value = "/viewOperByCode", method = RequestMethod.POST)
    public Page<Map<String, String>> viewOperByCode(String keyword,String visitId, String pageSize, String pageNo, String oid) {
        //设置页面和页码
        Page<Map<String, String>> page = new Page<Map<String, String>>();
        page.setPageNo(Integer.parseInt(pageNo));
        page.setPageSize(Integer.parseInt(pageSize));
        List<String> codeList = new ArrayList<String>();
        if (StringUtils.isNotBlank(keyword)) {
            return dictHbaseService.getNamebyCode(oid,visitId, DictType.OPER, keyword, page, false);
        } else {
            return page;
        }
    }

    /**
     * @Description 根据code获取手术编码名称
     */
    @ApiOperation(value = "根据code获取手术编码名称", notes = "根据code获取手术编码名称", httpMethod = "POST")
    @RequestMapping(value = "/viewNurseByCode", method = RequestMethod.POST)
    public Page<Map<String, String>> viewNurseByCode(String keyword,String visitId, String pageSize, String pageNo, String oid) {
        //设置页面和页码
        Page<Map<String, String>> page = new Page<Map<String, String>>();
        page.setPageNo(Integer.parseInt(pageNo));
        page.setPageSize(Integer.parseInt(pageSize));
        List<String> codeList = new ArrayList<String>();
        if (StringUtils.isNotBlank(keyword)) {
            return dictHbaseService.getNamebyCode(oid,visitId, DictType.NURSE, keyword, page, false);
        } else {
            return page;
        }
    }

    /**
     * @return 返回类型： void
     * @Description 方法描述:根据编码获取检查医嘱字典
     */
    @ApiOperation(value = "根据编码获取检查医嘱字典", notes = "根据code获取手术编码名称", httpMethod = "POST")
    @RequestMapping(value = "/viewExamByCode", method = RequestMethod.POST)
    public Page<Map<String, String>> viewExamByCode(String keyword, String visitId,String pageSize, String pageNo, String oid) {
        //设置页面和页码
        Page<Map<String, String>> page = new Page<Map<String, String>>();
        page.setPageNo(Integer.parseInt(pageNo));
        page.setPageSize(Integer.parseInt(pageSize));
        List<String> codeList = new ArrayList<String>();
        if (StringUtils.isNotBlank(keyword)) {
            return dictHbaseService.getNamebyCode(oid, visitId,DictType.EXAM, keyword, page, false);
        } else {
            return page;
        }
    }

    /**
     * @return 返回类型： void
     * @Description 方法描述:根据编码获取检验字典
     */
    @ApiOperation(value = "根据编码获取检验字典", notes = "根据code获取手术编码名称", httpMethod = "POST")
    @RequestMapping(value = "/viewLabByCode", method = RequestMethod.POST)
    public Page<Map<String, String>> viewLabByCode(String keyword, String visitId ,String pageSize, String pageNo, String oid) {
        //设置页面和页码
        Page<Map<String, String>> page = new Page<Map<String, String>>();
        page.setPageNo(Integer.parseInt(pageNo));
        page.setPageSize(Integer.parseInt(pageSize));
        List<String> codeList = new ArrayList<String>();
        if (StringUtils.isNotBlank(keyword)) {
            return dictHbaseService.getNamebyCode(oid,visitId, DictType.LAB, keyword, page, false);
        } else {
            return page;
        }
    }

    /**
     * @return 返回类型： void
     * @Description 方法描述:根据编码获取检验明细字典
     */
    @ApiOperation(value = "根据编码获取检验明细字典", notes = "根据编码获取检验明细字典", httpMethod = "POST")
    @RequestMapping(value = "/viewLabSubByCode", method = RequestMethod.POST)
    public Page<Map<String, String>> viewLabSubByCode(String keyword, String visitId,String pageSize, String pageNo, String oid) {
        //设置页面和页码
        Page<Map<String, String>> page = new Page<Map<String, String>>();
        page.setPageNo(Integer.parseInt(pageNo));
        page.setPageSize(Integer.parseInt(pageSize));
        List<String> codeList = new ArrayList<String>();
        if (StringUtils.isNotBlank(keyword)) {
            return dictHbaseService.getNamebyCode(oid, visitId,DictType.LABSUB, keyword, page, false);
        } else {
            return page;
        }
    }

    /**
     * @return 返回类型： void
     * @Description 方法描述:根据编码获取药品医嘱字典
     */
    @ApiOperation(value = "根据编码获取药品医嘱字典", notes = "根据编码获取药品医嘱字典", httpMethod = "POST")
    @RequestMapping(value = "/viewDrugByCode", method = RequestMethod.POST)
    public Page<Map<String, String>> viewDrugByCode(String keyword, String visitId,String pageSize, String pageNo, String oid) {
        //设置页面和页码
        Page<Map<String, String>> page = new Page<Map<String, String>>();
        page.setPageNo(Integer.parseInt(pageNo));
        page.setPageSize(Integer.parseInt(pageSize));
        List<String> codeList = new ArrayList<String>();
        if (StringUtils.isNotBlank(keyword)) {
            return dictHbaseService.getNamebyCode(oid,visitId, DictType.DRUG, keyword, page, true);
        } else {
            return page;
        }
    }

    @ApiOperation(value = "时间轴获取检验指标", notes = "时间轴获取检验指标", httpMethod = "POST")
    @RequestMapping(value = "/getLabSubType", method = RequestMethod.POST)
    public Page<Map<String, String>> getLabSubType(String keyword, int pageSize, int pageNo, String oid,String patientId, String visitId, String visitType) {
        Page<Map<String, String>> page = new Page<Map<String, String>>();
        page = dictHbaseService.getLabSubTypeList(oid, keyword, pageNo, pageSize,patientId,visitId,visitType);

        return page;
    }
}
