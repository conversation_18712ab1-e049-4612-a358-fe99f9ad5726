package com.goodwill.hdr.civ.service;


import com.goodwill.hdr.civ.entity.TimeaxisConfig;
import com.goodwill.hdr.core.orm.Page;

import java.util.List;
import java.util.Map;

public interface TimeAxisViewService {

	/**
	 * @param patientId
	 * @param pageSize
	 * @param pageNo
	 * @return
	 * @Description 获取就诊列表
	 */
	public Page<Map<String, String>> getVisitDeptList(String oid, String patientId, String idCardNo, String deptName, int pageSize, int pageNo);

	/**
	 * @Description
	 * 获取就诊时间
	 * @return
	 */
	public List<Map<String, String>> getVisitTimeList(String oid, String patientId,
                                                      String visitId);

	/**
	 * @Description
	 * 获取体温单
	 * @return
	 */
	public List<Map<String, Object>> getVisitTimeTpList(String oid, String patientId, String visitId,
                                                        String admissionTime, int week, int cols);

	/**
	 * @Description
	 * 获取体温和脉搏
	 * @return
	 */
	public Map<String, Object> getVisitTimeBloodAndPulse(String oid, String patientId, String visitId,
                                                         String admissionTime, int week, int cols);

	/**
	 * @Description
	 * 获取检验
	 * @return
	 */
	public Map<String, Object> getVisitTimeLabList(String oid, String patientId, String visitId,
                                                   String admissionTime, String mainDiag, String deptCode, int week, int cols);

	/**
	 * @Description
	 * 获取检查
	 * @return
	 */
	public Map<String, Object> getVisitTimeExamList(String oid, String patientId, String visitId,
                                                    String admissionTime, String mainDiag, String deptCode, int week, int cols);

	/**
	 * @Description
	 * 获取诊疗记录
	 * @return
	 */
	public Map<String, Object> getVisitTimeClinicList(String oid, String patientId, String visitId,
                                                      String admissionTime, int week, int cols);

	/**
	 * @Description
	 * 获取手术
	 * @return
	 */
	public Map<String, Object> getVisitTimeOperList(String oid, String patientId ,String visitId,String admissionTime, int week, int cols);

	public void getVisitTimeOperList(String oid, String patientId,String visitId,String admissionTime, int week, int cols, List<Map<String, Object>> rs);
	/**
	 * @Description
	 * 获取药品
	 * @return
	 */
	public Map<String, Object> getVisitTimeDrugList(String oid, String patientId, String visitId, String admissionTime, String mainDiag, String deptCode, String showMainIndicator, int week, int cols);

	/**
	 * @Description 获取治疗
	 * @return
	 */
	public Map<String, Object> getVisitTimeCureList(String oid, String patientId, String visitId, String admissionTime, String mainDiag, String deptCode, int week, int cols);

	/**
	 * @Description 入出转
	 * @return
	 */
	public List<Map<String, Object>> getVisitTimePat_AdtList(String oid, String patientId, String visitId);

	/**
	 * @Description 获取列表
	 */
	public List<Map<String, String>> getConfigList(String oid, String usercode, String patientid, String visittype, String visitid);

	/**
	 * 获取

	 * @return
	 */
	List<Map<String, String>> getConfigConfigList(String oid, String mainDiag, String deptCode);

	/**
	 * @Description 插入配置
	 */
	public Map<String, String> insertConfig(String oid, TimeaxisConfig config);

	/**
	 * @Description 删除配置
	 */
	public boolean deleteConfig(String oid, String id);

	/**
	 * @Description 查询检验结果指标
	 */
	public Map<String, Object> getInspectItemLine(String oid, String patientid, String visittype, String visitid, String subitemcode, String admissionTime, int week, int cols);

	/**
	 * 获取优化版手术
	 * @param patientId
	 * @param visitId
	 * @param admissionTime
	 * @param week
	 * @param cols
	 * @return
	 */
	List<Map<String, Object>> getVisitOperTimeList(String oid, String patientId,
                                                   String visitId, String admissionTime, int week, int cols);
}
