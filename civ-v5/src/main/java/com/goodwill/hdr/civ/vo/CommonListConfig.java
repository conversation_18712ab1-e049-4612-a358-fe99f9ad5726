package com.goodwill.hdr.civ.vo;

import com.goodwill.hdr.hbase.bo.PropertyFilter;

import java.util.List;

public class CommonListConfig {
    private String tableName;
    private List<PropertyFilter> conditionList;
    private List<ColumnConfig> columnList;
    private List<String> paramList;
    private String sortColumn;
    private String sortType;


    public static class ColumnConfig {
        private Boolean display;
        private String column;

        public String getColumn() {
            return column;
        }

        public void setColumn(String column) {
            this.column = column;
        }

        public Boolean getDisplay() {
            return display;
        }

        public void setDisplay(Boolean display) {
            this.display = display;
        }
    }

    public String getTableName() {
        return tableName;
    }

    public void setTableName(String tableName) {
        this.tableName = tableName;
    }

    public List<PropertyFilter> getConditionList() {
        return conditionList;
    }

    public void setConditionList(List<PropertyFilter> conditionList) {
        this.conditionList = conditionList;
    }

    public List<ColumnConfig> getColumnList() {
        return columnList;
    }

    public void setColumnList(List<ColumnConfig> columnList) {
        this.columnList = columnList;
    }

    public String getSortColumn() {
        return sortColumn;
    }

    public void setSortColumn(String sortColumn) {
        this.sortColumn = sortColumn;
    }

    public String getSortType() {
        return sortType;
    }

    public void setSortType(String sortType) {
        this.sortType = sortType;
    }

    public List<String> getParamList() {
        return paramList;
    }

    public void setParamList(List<String> paramList) {
        this.paramList = paramList;
    }
}
