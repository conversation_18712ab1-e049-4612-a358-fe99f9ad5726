package com.goodwill.hdr.civ.service.impl;


import com.goodwill.hdr.civ.config.Config;
import com.goodwill.hdr.civ.config.ConfigCache;
import com.goodwill.hdr.civ.enums.HdrConstantEnum;
import com.goodwill.hdr.civ.service.CurrentViewService;
import com.goodwill.hdr.civ.service.MedicalRecordService;
import com.goodwill.hdr.civ.service.SpecialtyViewPowerService;
import com.goodwill.hdr.civ.utils.CivUtils;
import com.goodwill.hdr.civ.utils.EMRUtils;

import com.goodwill.hdr.civ.utils.Utils;
import com.goodwill.hdr.core.orm.MatchType;
import com.goodwill.hdr.core.orm.Page;
import com.goodwill.hdr.hbase.bo.PropertyFilter;
import com.goodwill.hdr.hbase.dto.responseVo.PageResultVo;
import com.goodwill.hdr.hbase.dto.responseVo.ResultVo;
import com.goodwill.hdr.hbaseQueryClient.builder.PageRequestBuilder;
import com.goodwill.hdr.hbaseQueryClient.service.HbaseQueryClient;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.text.SimpleDateFormat;
import java.util.*;

@Service
public class CurrentViewServiceImpl implements CurrentViewService {


    private static final Logger log = LoggerFactory.getLogger(CurrentViewServiceImpl.class);
    private final SpecialtyViewPowerService specialtyViewPowerService;

    private final MedicalRecordService medicalRecordService;
    private final HbaseQueryClient hbaseQueryClient;

    public CurrentViewServiceImpl(SpecialtyViewPowerService specialtyViewPowerService, MedicalRecordService medicalRecordService, HbaseQueryClient hbaseQueryClient) {
        this.specialtyViewPowerService = specialtyViewPowerService;
        this.medicalRecordService = medicalRecordService;
        this.hbaseQueryClient = hbaseQueryClient;
    }

    @Override
    public Map<String, String> getCVMain(String oid, String patientId, String visitId) {
        //1:查询病历文书表  2：查询病历章节表
        if (Config.getHDR_EMR_CONTENT_DG_OR_NOT(oid).equals("1")) {
            return getEMRContent(oid, patientId, visitId, "02", "1");
        } else {
            return getEmrContentDg( oid, patientId, visitId, "02", "1");
        }
    }

    @Override
    public Map<String, String> getCVNowHis(String oid, String patientId, String visitId) {
        //1:查询病历文书表  2：查询病历章节表
        if (Config.getHDR_EMR_CONTENT_DG_OR_NOT(oid).equals("1")) {
            return getEMRContent(oid, patientId, visitId, "02", "2");
        } else {
            return getEmrContentDg( oid, patientId, visitId, "02", "2");
        }
    }



    /**
     * @param patientId 患者编号
     * @param visitId   就诊次数
     * @param visitType 就诊类型
     * @param type      内容类型  1:主诉  2：现病史
     * @return
     * @Description 方法描述: 解析某次就诊的最新病历，获取取指定内容
     */
    public Map<String, String> getEMRContent(String oid, String patientId, String visitId, String visitType, String type) {
        Map<String, String> result = new HashMap<String, String>();
        //获取末次住院的病历 只取入院记录
        String mrText = medicalRecordService.getMrText(oid, patientId, visitId, visitType);

        //未找到病历内容，中断执行
        if (StringUtils.isBlank(mrText)) {
            result.put("content", "-");
            return result;
        }
        //解析病历内容
        Map<String, String> contents = EMRUtils.EMRElement(oid, mrText);
        if ("1".equals(type)) { //主诉
            String mainField = Config.getCIV_CV_MAIN(oid);
            Utils.checkAndPutToMap(result, "content", contents.get(mainField), "-", false);
        } else if ("2".equals(type)) { //现病史
            String nowHisField = Config.getCIV_CV_NOW_HIS(oid);
            Utils.checkAndPutToMap(result, "content", contents.get(nowHisField), "-", false);
        }
        return result;
    }


    /**
     * 查询病例章节内容
     *
     * @return
     */
    private Map<String, String> getEmrContentDg( String oid, String patientId, String visitId, String visitType, String type) {
        Map<String, String> map = new HashMap<String, String>();
        //获取末次住院的病历章节 只取入院记录
        String tableName = "HDR_EMR_CONTENT_DG";

        List<PropertyFilter> filters = new ArrayList<PropertyFilter>();
        filters.add(new PropertyFilter("DELETE_FLAG", MatchType.EQ.getOperation(), "0"));
        //HbaseService.createPropertyFilter("DELETE_FLAG", "0", MatchType.EQ.getOperation(), filters);
        if (StringUtils.isNotBlank(visitType)) {
            filters.add(new PropertyFilter("VISIT_TYPE_CODE", MatchType.EQ.getOperation(), visitType));
        }
        if (StringUtils.isNotBlank(Config.getEMR_RAN_MRCLASSCODE(oid))) {
            filters.add(new PropertyFilter("MR_CLASS_CODE", MatchType.IN.getOperation(), Config.getEMR_RAN_MRCLASSCODE(oid)));
            //HbaseService.createPropertyFilter("MR_CLASS_CODE", Config.getEMR_RAN_MRCLASSCODE(oid), MatchType.IN.getOperation(), filters);
        }
        //1主诉 2现病史
        if (type.equals("1")) {
            if (StringUtils.isNotBlank(Config.getMAIN_DG_CODE(oid))) {
                filters.add(new PropertyFilter("DG_CODE", MatchType.EQ.getOperation(), Config.getMAIN_DG_CODE(oid)));
                //HbaseService.createPropertyFilter("DG_CODE", Config.getMAIN_DG_CODE(oid), MatchType.EQ.getOperation(), filters);
            }
        } else {
            if (StringUtils.isNotBlank(Config.getNOW_HIS_DG_CODE(oid))) {
                filters.add(new PropertyFilter("DG_CODE", MatchType.EQ.getOperation(), Config.getNOW_HIS_DG_CODE(oid)));
                //HbaseService.createPropertyFilter("DG_CODE", Config.getNOW_HIS_DG_CODE(oid), MatchType.EQ.getOperation(), filters);
            }
        }
        List<Map<String, String>> list = new ArrayList<>();
        if (StringUtils.isNotBlank(oid) || "ALL".equals(oid)) {
            ResultVo<PageResultVo<Map<String, String>>> resultVo = hbaseQueryClient.getPageByCondition(
                    PageRequestBuilder.init()
                            .tableName(tableName)
                            .patientId(patientId)
                            .oid(oid)
                            .visitId(visitId)
                            .visitTypeCode(visitType)
                            .filters(filters)
                            .pageNo(0)
                            .pageSize(0)
                            .orderBy("")
                            .desc()
                            .column("DG_PLAIN_CONTENT", "CREATE_DATE_TIME")
                            .build());
            if(resultVo.isSuccess()){
                list = resultVo.getContent().getResult();
            }
//            list = hbaseDao.findConditionByPatientVisitId(tableName, oid, patientId, visitId, filters,
//                    "DG_PLAIN_CONTENT", "CREATE_DATE_TIME");
        }

        if (list != null && list.size() > 0) {
            //按时间排序来查找出最后一条数据
            Date currentDate = Utils.strToDate(list.get(0).get("CREATE_DATE_TIME"), "yyyy-MM-dd hh:mm:ss");
            map = list.get(0);
            for (int i = 1; i < list.size(); i++) {
                Date tmpDate = Utils.strToDate(list.get(i).get("CREATE_DATE_TIME"), "yyyy-MM-dd hh:mm:ss");
                if (Utils.dateCompare(tmpDate, currentDate)) {
                    map = list.get(i);
                }
            }
        }
        Map<String, String> result = new HashMap<>();
        result.put("content", StringUtils.isNotBlank(map.get("DG_PLAIN_CONTENT")) ? map.get("DG_PLAIN_CONTENT") : "-");
        return result;
    }

    @Override
    public Map<String, Object> getCVDiag(String oid, String patientId, String visitId) {
        Map<String, Object> result = new HashMap<String, Object>();
        List<Map<String, String>> list = new ArrayList<Map<String, String>>();
        if (StringUtils.isNotBlank(patientId) && StringUtils.isNotBlank(oid)  || "ALL".equals(oid)) {
            List<PropertyFilter> filters = new ArrayList<PropertyFilter>();
            //诊断类型  必须加上
            //		filters.add(new PropertyFilter("DIAGNOSIS_PROPERTY_CODE", "STRING", MatchType.IN.getOperation(), "2,3"));
            CivUtils.strToFilter(Config.getCIV_CVDIAGFILTER(oid), filters, ";");
            //本次住院所有诊断
            ResultVo<PageResultVo<Map<String, String>>> resultVo = hbaseQueryClient.getPageByCondition(
                    PageRequestBuilder.init()
                            .tableName("HDR_EMR_CONTENT_DIAG")
                            .patientId(patientId)
                            .oid(oid)
                            .visitId(visitId)
                            .visitTypeCode("")
                            .filters(filters)
                            .pageNo(0)
                            .pageSize(0)
                            .orderBy("")
                            .desc()
                            .column("DIAGNOSIS_CODE", "DIAGNOSIS_NAME", "DIAGNOSIS_TIME", "DIAGNOSIS_DOCTOR_CODE", "DIAGNOSIS_DOCTOR_NAME",
                                    "DIAGNOSIS_DESC", "DIAGNOSIS_SUB_NUM", "DIAGNOSIS_NUM")
                            .build());
            if(resultVo.isSuccess()){
                list = resultVo.getContent().getResult();
            }
//            list = hbaseDao.findConditionByPatientVisitId("HDR_EMR_CONTENT_DIAG", oid, patientId, visitId, filters,
//                    "DIAGNOSIS_CODE", "DIAGNOSIS_NAME", "DIAGNOSIS_TIME", "DIAGNOSIS_DOCTOR_CODE", "DIAGNOSIS_DOCTOR_NAME",
//                    "DIAGNOSIS_DESC", "DIAGNOSIS_SUB_NUM", "DIAGNOSIS_NUM");
        }
        //处理数据  筛选主诊断和其他诊断
        List<String> others = new ArrayList<String>();
        String date = "-";
        String mainDiag = "-";
        String mainDiagCode = "-";
        for (Map<String, String> map : list) {
            if ("1".equals(map.get("DIAGNOSIS_NUM")) && "0".equals(map.get("DIAGNOSIS_SUB_NUM"))) {
                if (StringUtils.isNotBlank(map.get("DIAGNOSIS_TIME"))) {
                    date = map.get("DIAGNOSIS_TIME");
                } else {
                    date = "-";
                }
                if (StringUtils.isNotBlank(map.get("DIAGNOSIS_NAME"))) {
                    mainDiag = map.get("DIAGNOSIS_NAME");
                } else {
                    mainDiag = "-";
                }
                if (StringUtils.isNotBlank(map.get("DIAGNOSIS_CODE"))) {
                    mainDiagCode = map.get("DIAGNOSIS_CODE");
                } else {
                    mainDiagCode = "-";
                }
            } else {
                if (StringUtils.isNotBlank(map.get("DIAGNOSIS_NAME"))) {
                    others.add(map.get("DIAGNOSIS_NAME"));
                }
            }
        }
        //主诊断 + 其他诊断
        result.put("date", date); //诊断时间
        result.put("main_diag", mainDiag); //诊断名称
        result.put("main_diag_code", mainDiagCode); //疾病编码
        result.put("else_diag", others);
        return result;
    }

    @Override
    public List<Map<String, String>> getPatVitalSign(String oid, String patientId, String visitId, String mainDiag, String deptCode) {
        List<Map<String, String>> result = new ArrayList<Map<String, String>>();
        //仅取近两天的数据
        List<PropertyFilter> filters = new ArrayList<PropertyFilter>();
		/*String date = Utils.CurrDateDiff("pre", 2) + " 00:00:00";
		filters.add(new PropertyFilter("RECORD_TIME", "STRING", MatchType.GE.getOperation(), date));*/
//		String  vitalConfig = Config.getConfigValue("CIV_VITAL_SIGN_CONFIG");
//		if(StringUtils.isNotBlank(vitalConfig)){
//			String[] config = vitalConfig.split("\\|");
//			filters.add(new PropertyFilter(config[0], "STRING",config[1] , config[2]));
//		}
        List<Map<String, String>> patvsList = new ArrayList<>();
        if (StringUtils.isNotBlank(oid)) {
            ResultVo<PageResultVo<Map<String, String>>> resultVo = hbaseQueryClient.getPageByCondition(
                    PageRequestBuilder.init()
                            .tableName("HDR_VITAL_MEASURE")
                            .patientId(patientId)
                            .oid(oid)
                            .visitId(visitId)
                            .visitTypeCode("")
                            .filters(filters)
                            .pageNo(0)
                            .pageSize(0)
                            .orderBy("MEASURING_TIME")
                            .asc()
                            .column("VITAL_TYPE_NAME","VITAL_SIGN_VALUE","VITAL_SIGN2_VALUE",
                                    "MEASURING_TIME","MAX_RESULT","MIN_RESULT")
                            .build());
            if(resultVo.isSuccess()){
                patvsList = resultVo.getContent().getResult();
            }
            //patvsList = hbaseDao.findConditionByPatientVisitId("HDR_VITAL_MEASURE", oid, patientId, visitId, filters);
        }
        log.info("patvsList:{}", patvsList);

        //去重，取每个测量项的最新数据
        Map<String, Map<String, String>> deduplicationMap = new HashMap<String, Map<String, String>>();
        for (Map<String, String> map : patvsList) {
            //异常判断
            String max = map.get("MAX_RESULT");
            String min = map.get("MIN_RESULT");
            String v = map.get("VITAL_SIGN_VALUE");
            //默认正常
            map.put("TREND", "normal");
            if (Utils.isNumber(max) && Utils.isNumber(v)) {
                if (Double.parseDouble(v) > Double.parseDouble(max)) {
                    map.put("TREND", "rise"); //过高
                }
            }
            if (Utils.isNumber(min) && Utils.isNumber(v)) {
                if (Double.parseDouble(v) < Double.parseDouble(min)) {
                    map.put("TREND", "fall"); //过低
                }
            }
            String keyString = map.get("VITAL_TYPE_NAME");
            map.put("VITAL_SIGN_VALUE", Utils.getRealData(map.get("VITAL_SIGN_VALUE")));
            if (StringUtils.isNotBlank(map.get("VITAL_SIGN2_VALUE"))) {
                map.put("VITAL_SIGN2_VALUE", Utils.getRealData(map.get("VITAL_SIGN2_VALUE")));
            }
            //过滤掉正常值，保留异常值
            if (deduplicationMap.containsKey(keyString)) {
                Map<String, String> currMap = deduplicationMap.get(keyString);
                //取最大报告时间数据
                String currTimeString = currMap.get("MEASURING_TIME");
                String newTimeString = map.get("MEASURING_TIME");
                //新时间比原有时间大
                if (newTimeString.compareTo(currTimeString) > 0) {
                    deduplicationMap.put(keyString, map);
                }
            } else {
                deduplicationMap.put(keyString, map);
            }
        }
        log.info("deduplicationMap:{}", deduplicationMap);
        //查询专科视图 生命体征 重要匹配
        List<Map<String, String>> list = new ArrayList<Map<String, String>>();
        if (StringUtils.isNotBlank(mainDiag) || StringUtils.isNotBlank(deptCode)) {
            list = specialtyViewPowerService.getSpecialtyConfig(oid, mainDiag, "Health", deptCode);
        }
        StringBuffer subItemName = new StringBuffer();
        //首先统计专科视图顺序
        for (Map<String, String> map : list) {
            subItemName.append(map.get("subItemName") + ";");
            setPatVitalSignValue(result,oid,subItemName.toString(),deduplicationMap);
            /*if (Config.getCIV_VITAL_TYPE_NAME(oid, "体温").equals(map.get("subItemName"))) {
                //体温
                Map<String, String> m1 = new HashMap<String, String>();
                m1.put("code", "1");
                m1.put("name", "体温");
                m1.put("unit", "℃");
                Map<String, String> tMap = deduplicationMap.get(Config.getCIV_VITAL_TYPE_NAME(oid, "体温"));
                if (null != tMap) {
                    m1.put("value", tMap.get("VITAL_SIGN_VALUE"));
                    m1.put("trend", tMap.get("TREND"));
                } else {
                    m1.put("value", "-");
                    m1.put("trend", "-");
                }
                result.add(m1);
            }
            if (Config.getCIV_VITAL_TYPE_NAME(oid, "脉搏").equals(map.get("subItemName"))) {
                Map<String, String> m2 = new HashMap<String, String>();
                m2.put("code", "2");
                m2.put("name", "脉搏");
                m2.put("unit", "次/分");
                Map<String, String> mMap = deduplicationMap.get(Config.getCIV_VITAL_TYPE_NAME(oid, "脉搏"));
                if (null != mMap) {
                    m2.put("value", mMap.get("VITAL_SIGN_VALUE"));
                    m2.put("trend", mMap.get("TREND"));
                } else {
                    m2.put("value", "-");
                    m2.put("trend", "-");
                }
                result.add(m2);
            }
            if (Config.getCIV_VITAL_TYPE_NAME(oid, "心率").equals(map.get("subItemName"))) {
                Map<String, String> m3 = new HashMap<String, String>();
                m3.put("code", "3");
                m3.put("name", "心率");
                m3.put("unit", "次/分");
                Map<String, String> xMap = deduplicationMap.get(Config.getCIV_VITAL_TYPE_NAME(oid, "心率"));
                if (null != xMap) {
                    m3.put("value", xMap.get("VITAL_SIGN_VALUE"));
                    m3.put("trend", xMap.get("TREND"));
                } else {
                    m3.put("value", "-");
                    m3.put("trend", "-");
                }
                result.add(m3);
            }
            if (Config.getCIV_VITAL_TYPE_NAME(oid, "呼吸").equals(map.get("subItemName"))) {
                Map<String, String> m4 = new HashMap<String, String>();
                m4.put("code", "4");
                m4.put("name", "呼吸");
                m4.put("unit", "次/分");
                Map<String, String> hMap = deduplicationMap.get(Config.getCIV_VITAL_TYPE_NAME(oid, "呼吸"));
                if (null != hMap) {
                    m4.put("value", hMap.get("VITAL_SIGN_VALUE"));
                    m4.put("trend", hMap.get("TREND"));
                } else {
                    m4.put("value", "-");
                    m4.put("trend", "-");
                }
                result.add(m4);
            }
            if (Config.getCIV_VITAL_TYPE_NAME(oid, "血压").equals(map.get("subItemName"))) {
                Map<String, String> m5 = new HashMap<String, String>();
                m5.put("code", "5");
                m5.put("name", "血压");
                m5.put("unit", "mmHg");
                Map<String, String> xyMap = deduplicationMap.get(Config.getCIV_VITAL_TYPE_NAME(oid, "血压"));
                if (null != xyMap) {
                    String v1 = StringUtils.isBlank(xyMap.get("VITAL_SIGN_VALUE")) ? "-" : xyMap.get("VITAL_SIGN_VALUE");
                    String v2 = StringUtils.isBlank(xyMap.get("VITAL_SIGN2_VALUE")) ? "-" : xyMap.get("VITAL_SIGN2_VALUE");
                    m5.put("value", v1 + "/" + v2);
                    m5.put("trend", xyMap.get("TREND"));
                } else {
                    m5.put("value", "-");
                    m5.put("trend", "-");
                }
                result.add(m5);
            }
            if (Config.getCIV_VITAL_TYPE_NAME(oid, "血氧").equals(map.get("subItemName"))) {
                Map<String, String> m6 = new HashMap<String, String>();
                m6.put("code", "6");
                m6.put("name", "血氧");
                m6.put("unit", "%");
                Map<String, String> yMap = deduplicationMap.get(Config.getCIV_VITAL_TYPE_NAME(oid, "血氧"));
                if (null != yMap) {
                    m6.put("value", yMap.get("VITAL_SIGN_VALUE"));
                    m6.put("trend", yMap.get("TREND"));
                } else {
                    m6.put("value", "-");
                    m6.put("trend", "-");
                }
                result.add(m6);
            }*/
        }
        String subItemNameStr = subItemName.toString();
        setPatVitalSignValue(result,oid,subItemNameStr,deduplicationMap);
        /*if (!subItemNameStr.contains(Config.getCIV_VITAL_TYPE_NAME(oid, "体温"))) {
            //体温
            Map<String, String> m1 = new HashMap<String, String>();
            m1.put("code", "1");
            m1.put("name", "体温");
            m1.put("unit", "℃");
            Map<String, String> tMap = deduplicationMap.get(Config.getCIV_VITAL_TYPE_NAME(oid, "体温"));
            if (null != tMap) {
                m1.put("value", tMap.get("VITAL_SIGN_VALUE"));
                m1.put("trend", tMap.get("TREND"));
            } else {
                m1.put("value", "-");
                m1.put("trend", "-");
            }
            result.add(m1);
        }

        if (!subItemNameStr.contains(Config.getCIV_VITAL_TYPE_NAME(oid, "脉搏"))) {
            Map<String, String> m2 = new HashMap<String, String>();
            m2.put("code", "2");
            m2.put("name", "脉搏");
            m2.put("unit", "次/分");
            Map<String, String> mMap = deduplicationMap.get(Config.getCIV_VITAL_TYPE_NAME(oid, "脉搏"));
            if (null != mMap) {
                m2.put("value", mMap.get("VITAL_SIGN_VALUE"));
                m2.put("trend", mMap.get("TREND"));
            } else {
                m2.put("value", "-");
                m2.put("trend", "-");
            }
            result.add(m2);
        }

        if (!subItemNameStr.contains(Config.getCIV_VITAL_TYPE_NAME(oid, "心率"))) {
            Map<String, String> m3 = new HashMap<String, String>();
            m3.put("code", "3");
            m3.put("name", "心率");
            m3.put("unit", "次/分");
            Map<String, String> xMap = deduplicationMap.get(Config.getCIV_VITAL_TYPE_NAME(oid, "心率"));
            if (null != xMap) {
                m3.put("value", xMap.get("VITAL_SIGN_VALUE"));
                m3.put("trend", xMap.get("TREND"));
            } else {
                m3.put("value", "-");
                m3.put("trend", "-");
            }
            result.add(m3);
        }

        if (!subItemNameStr.contains(Config.getCIV_VITAL_TYPE_NAME(oid, "呼吸"))) {
            Map<String, String> m4 = new HashMap<String, String>();
            m4.put("code", "4");
            m4.put("name", "呼吸");
            m4.put("unit", "次/分");
            Map<String, String> hMap = deduplicationMap.get(Config.getCIV_VITAL_TYPE_NAME(oid, "呼吸"));
            if (null != hMap) {
                m4.put("value", hMap.get("VITAL_SIGN_VALUE"));
                m4.put("trend", hMap.get("TREND"));
            } else {
                m4.put("value", "-");
                m4.put("trend", "-");
            }
            result.add(m4);
        }
        if (!HdrConstantEnum.HOSPITAL_BJXH.getCode().equalsIgnoreCase(ConfigCache.getCache(oid, "org_oid"))) {
            if (!subItemNameStr.contains(Config.getCIV_VITAL_TYPE_NAME(oid, "血压"))) {
                Map<String, String> m5 = new HashMap<String, String>();
                m5.put("code", "5");
                m5.put("name", "血压");
                m5.put("unit", "mmHg");
                Map<String, String> xyMap = deduplicationMap.get(Config.getCIV_VITAL_TYPE_NAME(oid, "血压"));
                if (null != xyMap) {
                    String v1 = StringUtils.isBlank(xyMap.get("VITAL_SIGN_VALUE")) ? "-" : xyMap.get("VITAL_SIGN_VALUE");
                    String v2 = StringUtils.isBlank(xyMap.get("VITAL_SIGN2_VALUE")) ? "-" : xyMap.get("VITAL_SIGN2_VALUE");
                    m5.put("value", v1 + "/" + v2);
                    m5.put("trend", xyMap.get("TREND"));
                } else {
                    m5.put("value", "-");
                    m5.put("trend", "-");
                }
                result.add(m5);
            }
        } else {
            if (!subItemNameStr.contains(Config.getCIV_VITAL_TYPE_NAME(oid, "收缩压"))) {
                Map<String, String> m5 = new HashMap<String, String>();
                m5.put("code", "5");
                m5.put("name", "收缩压");
                m5.put("unit", "mmHg");
                Map<String, String> xyMap = deduplicationMap.get(Config.getCIV_VITAL_TYPE_NAME(oid, "收缩压"));
                if (null != xyMap) {
                    String v1 = StringUtils.isBlank(xyMap.get("VITAL_SIGN_VALUE")) ? "-" : xyMap.get("VITAL_SIGN_VALUE");
                    //String v2 = StringUtils.isBlank(xyMap2.get("VITAL_SIGN_VALUE")) ? "-" : xyMap.get("VITAL_SIGN_VALUE");
                    m5.put("value", v1);
                    m5.put("trend", xyMap.get("TREND"));
                } else {
                    m5.put("value", "-");
                    m5.put("trend", "-");
                }
                result.add(m5);
            }
            if (!subItemNameStr.contains(Config.getCIV_VITAL_TYPE_NAME(oid, "舒张压"))) {
                Map<String, String> m6 = new HashMap<String, String>();
                m6.put("code", "5");
                m6.put("name", "舒张压");
                m6.put("unit", "mmHg");
                Map<String, String> yMap = deduplicationMap.get(Config.getCIV_VITAL_TYPE_NAME(oid, "舒张压"));
                if (null != yMap) {
                    m6.put("value", yMap.get("VITAL_SIGN_VALUE"));
                    m6.put("trend", yMap.get("TREND"));
                } else {
                    m6.put("value", "-");
                    m6.put("trend", "-");
                }
                result.add(m6);
            }
        }


        if (!subItemNameStr.contains(Config.getCIV_VITAL_TYPE_NAME(oid, "血氧"))) {
            Map<String, String> m6 = new HashMap<String, String>();
            m6.put("code", "6");
            m6.put("name", "血氧");
            m6.put("unit", "%");
            Map<String, String> yMap = deduplicationMap.get(Config.getCIV_VITAL_TYPE_NAME(oid, "血氧"));
            if (null != yMap) {
                m6.put("value", yMap.get("VITAL_SIGN_VALUE"));
                m6.put("trend", yMap.get("TREND"));
            } else {
                m6.put("value", "-");
                m6.put("trend", "-");
            }
            result.add(m6);
        }*/
        return result;
    }

    /**
     * 体征指标配置映射
     * 20204-09-09
     * @param result
     * @param oid
     * @param subItemNameStr
     * @param deduplicationMap
     */
    public void setPatVitalSignValue(List<Map<String, String>> result,String oid,String subItemNameStr,Map<String, Map<String, String>> deduplicationMap){
        String CIV_VITAL_TYPE_NAME = ConfigCache.getCache(oid, "CIV_VITAL_TYPE_NAME");
        if(StringUtils.isBlank(CIV_VITAL_TYPE_NAME)){
            log.warn("请先配置CIV_VITAL_TYPE_NAME，格式：1,体温,体温|TW;....");
            return;
        }
        String[] vitals=CIV_VITAL_TYPE_NAME.trim().split(";");
        List<String[]> list=new ArrayList<>();
        for(String s:vitals){
            String[] tempS=s.split(",");
            list.add(tempS);
        }
        for (String[] s: list) {
            String code = StringUtils.isNotBlank(s[0]) ? s[0].trim() : "";
            String itemName = s[1];
            String[] values=s[2].split("\\|");
            if(values.length>1){
                String itemCode=values[1].replace('+', ',');
                if (!subItemNameStr.contains(itemName)) {
                    Map<String, String> m1 = new HashMap<String, String>();
                    m1.put("code", code);
                    m1.put("itemCode", itemCode);
                    m1.put("itemName", itemName);
                    if (itemName.contains("体温")) {
                        m1.put("unit", "℃");
                    } else if (itemName.contains("脉搏") || itemName.contains("心率") || itemName.contains("呼吸")) {
                        m1.put("unit", "次/分");
                    } else if (itemName.contains("血压") || itemName.contains("收缩压") || itemName.contains("舒张压")) {
                        m1.put("unit", "mmHg");
                    } else if (itemName.contains("血氧")) {
                        m1.put("unit", "%");
                    }
                    Map<String, String> tMap = deduplicationMap.get(itemName);
                    if (null != tMap) {
                        if (itemName.contains("血压")) {
                            String v1 = StringUtils.isBlank(tMap.get("VITAL_SIGN_VALUE")) ? "-" : tMap.get("VITAL_SIGN_VALUE");
                            String v2 = StringUtils.isBlank(tMap.get("VITAL_SIGN2_VALUE")) ? "-" : tMap.get("VITAL_SIGN2_VALUE");
                            m1.put("value", v1 + "/" + v2);
                        } else {
                            String v1 = StringUtils.isBlank(tMap.get("VITAL_SIGN_VALUE")) ? "-" : tMap.get("VITAL_SIGN_VALUE");
                            m1.put("value", v1);
                        }
                        m1.put("trend", tMap.get("TREND"));
                    } else {
                        m1.put("value", "-");
                        m1.put("trend", "-");
                    }
                    result.add(m1);
                }
            }
        }
    }

    @Override
    public List<Map<String, String>> getPatNurseTask(String oid, String patientId, String visitId) {
        List<PropertyFilter> filters = new ArrayList<PropertyFilter>();
        //CivUtils.strToFilter("VISIT_TYPE_CODE|=|02", filters, ";");
        List<Map<String, String>> list = new ArrayList<>();
        ResultVo<PageResultVo<Map<String, String>>> resultVo = hbaseQueryClient.getPageByCondition(
                PageRequestBuilder.init()
                        .tableName("HDR_HLRW")
                        .patientId(patientId)
                        .oid(oid)
                        .visitId(visitId)
                        .visitTypeCode("02")
                        .filters(filters)
                        .pageNo(0)
                        .pageSize(0)
                        .orderBy("TASKDATETIME")
                        .desc()
                        .column("TASKDATETIME",
                                "TASKDESC", "TASKSTATUS_NAME", "TASKTYPE_NAME")
                        .build());
        if(resultVo.isSuccess()){
            list = resultVo.getContent().getResult();
        }
//        List<Map<String, String>> list = hbaseDao.findConditionByPatientVisitId("HDR_HLRW", oid, patientId, visitId, filters, "TASKDATETIME",
//                "TASKDESC", "TASKSTATUS_NAME", "TASKTYPE_NAME");
        List<Map<String, String>> listSort = Utils.sortList(list, "TASKDATETIME", "desc");
        return listSort;
    }

    @Override
    public Page<Map<String, String>> getPatRiskAssess(String oid, String patientId, String visitId, int pageNo, int pageSize) {
        Page<Map<String, String>> page = new Page<Map<String, String>>();
        page.setPageNo(pageNo);
        page.setPageSize(pageSize);

        List<PropertyFilter> filters = new ArrayList<PropertyFilter>();
        //CivUtils.strToFilter("VISIT_TYPE_CODE|=|02", filters, ";");
        Page<Map<String, String>> list = new Page<>();
        ResultVo<PageResultVo<Map<String, String>>> resultVo = hbaseQueryClient.getPageByCondition(
                PageRequestBuilder.init()
                        .tableName("HDR_FXPG")
                        .patientId(patientId)
                        .oid(oid)
                        .visitId(visitId)
                        .visitTypeCode("02")
                        .filters(filters)
                        .pageNo(0)
                        .pageSize(0)
                        .orderBy("RECORD_TIME")
                        .desc()
                        .column("RECORD_TIME", "ASSESS_ITEM_NAME", "ASSESS_ITEM_VALUE")
                        .build());
        if(resultVo.isSuccess()){
            list.setResult(resultVo.getContent().getResult());
            list.setTotalCount(resultVo.getContent().getTotal());
        }
//        Page<Map<String, String>> list = hbaseDao.findPageConditionByPatientVisitId("HDR_FXPG", oid, patientId, visitId, page, filters,
//                "RECORD_TIME", "ASSESS_ITEM_NAME", "ASSESS_ITEM_VALUE");
//        //		List<Map<String, String>> listSort = Utils.sortList(list, "RECORD_TIME", "desc");
        return list;
    }

    @Override
    public String getPatAssessPlan(String oid, String patientId, String visitId) {
        String result = "";
        if (StringUtils.isNotBlank(patientId)) {
            List<PropertyFilter> filters = new ArrayList<PropertyFilter>();
            //CivUtils.strToFilter("DG_CODE|=|E2005;VISIT_TYPE_CODE|=|02", filters, ";");
            List<Map<String, String>> list = new ArrayList<>();
            ResultVo<PageResultVo<Map<String, String>>> resultVo = hbaseQueryClient.getPageByCondition(
                    PageRequestBuilder.init()
                            .tableName("HDR_EMR_CONTENT_DG")
                            .patientId(patientId)
                            .oid(oid)
                            .visitId(visitId)
                            .visitTypeCode("02")
                            .filters(filters)
                            .pageNo(0)
                            .pageSize(0)
                            .orderBy("")
                            .desc()
                            .column("DG_PLAIN_CONTENT")
                            .build());
            if(resultVo.isSuccess()){
                list = resultVo.getContent().getResult();
            }
//            List<Map<String, String>> list = hbaseDao.findConditionByPatientVisitId("HDR_EMR_CONTENT_DG", oid, patientId, visitId, filters,
//                    "DG_PLAIN_CONTENT");
            if (list.size() > 0) {
                result = Utils.objToStr(list.get(0).get("DG_PLAIN_CONTENT"));
            }
        }
        return result;
    }

    @Override
    public Page<Map<String, String>> getPatCriticalValues(String oid, String patientId,
                                                          String visitId, int pageNo, int pageSize) {
        // TODO Auto-generated method stub
        Page<Map<String, String>> page = new Page<Map<String, String>>();
        page.setPageNo(pageNo);
        page.setPageSize(pageSize);

        List<Map<String, String>> rs = new ArrayList<Map<String, String>>();
        List<PropertyFilter> filters = new ArrayList<PropertyFilter>();
        //注释掉时间条件，危急值没有时间有效性
//		String time=getLastDay();
//		createPropertyFilter("REPORT_TIME", time, MatchType.GE.getOperation(), filters);
        //createPropertyFilter("VISIT_ID", visitId, MatchType.EQ.getOperation(), filters);
        //INFECTED_DIAG,INFECTED_POSITION,INFECTED_TIME,INFECTED_BACTERIA,INFECTED_TIMES,INFECTED_SOURCE,SUSPICIOUS_DIAG
        ResultVo<PageResultVo<Map<String, String>>> resultVo = hbaseQueryClient.getPageByCondition(
                PageRequestBuilder.init()
                        .tableName("HDR_CRITICAL_VALUES")
                        .patientId(patientId)
                        .oid(oid)
                        .visitId(visitId)
                        .visitTypeCode("")
                        .filters(filters)
                        .pageNo(0)
                        .pageSize(0)
                        .orderBy("")
                        .desc()
                        .column("LAB_ITEM_NAME", "LAB_SUB_ITEM_NAME", "LAB_SUB_ITEM_CODE", "CRISIS_VALUE", "RANGE", "REPORT_TIME",
                                "OPERATOR_NAME", "OPERATE_TIME", "CRISIS_FLAG", "ORDER_NO")
                        .build());
        if(resultVo.isSuccess()){
            page.setResult(resultVo.getContent().getResult());
        }
//        page = hbaseDao.findByConditionPage("HDR_CRITICAL_VALUES", oid, patientId, filters, page,
//                new String[]{"LAB_ITEM_NAME", "LAB_SUB_ITEM_NAME", "LAB_SUB_ITEM_CODE", "CRISIS_VALUE", "RANGE", "REPORT_TIME",
//                        "OPERATOR_NAME", "OPERATE_TIME", "CRISIS_FLAG", "ORDER_NO"});
        if (page.getResult().size() > 0) {
            for (Map<String, String> map : page.getResult()) {
                filters = new ArrayList<PropertyFilter>();
                createPropertyFilter("LAB_SUB_ITEM_CODE", map.get("LAB_SUB_ITEM_CODE"), MatchType.EQ.getOperation(), filters);
                createPropertyFilter("ORDER_NO", map.get("ORDER_NO"), MatchType.EQ.getOperation(), filters);
                //INFECTED_DIAG,INFECTED_POSITION,INFECTED_TIME,INFECTED_BACTERIA,INFECTED_TIMES,INFECTED_SOURCE,SUSPICIOUS_DIAG
                List<Map<String, String>> subs = new ArrayList<>();
                ResultVo<PageResultVo<Map<String, String>>> resultVo1 = hbaseQueryClient.getPageByCondition(
                        PageRequestBuilder.init()
                                .tableName("HDR_LAB_REPORT_DETAIL")
                                .patientId(patientId)
                                .oid(oid)
                                .visitId(visitId)
                                .visitTypeCode("")
                                .filters(filters)
                                .pageNo(0)
                                .pageSize(0)
                                .orderBy("")
                                .desc()
                                .column("LAB_ITEM_NAME", "LAB_SUB_ITEM_NAME")
                                .build());



                if(resultVo1.isSuccess()){
                    subs = resultVo1.getContent().getResult();
                }
//                List<Map<String, String>> subs = hbaseDao.findConditionByPatientVisitId("HDR_LAB_REPORT_DETAIL", oid, patientId, visitId, filters,
//                        new String[]{"LAB_ITEM_NAME", "LAB_SUB_ITEM_NAME"});
                if (subs.size() > 0) {
                    map.put("LAB_ITEM_NAME", subs.get(0).get("LAB_ITEM_NAME"));
                }
                rs.add(map);
            }
            Utils.sortListByDate(rs, "OPERATE_TIME", Page.Sort.DESC);
        }
        page.setResult(rs);
        page.setTotalCount(rs.size());
        return page;
    }

    @Override
    public Page<Map<String, String>> getPatInfectionWarn(String oid, String patientId,
                                                         String visitId, int pageNo, int pageSize) {
        // TODO Auto-generated method stub
        Page<Map<String, String>> page = new Page<Map<String, String>>();
        page.setPageNo(pageNo);
        page.setPageSize(pageSize);

        List<PropertyFilter> filters = new ArrayList<PropertyFilter>();
        //注释掉时间条件，院感现已没有时间有效性
//		String time=getLastDay();
//		createPropertyFilter("INFECTED_TIME", time, MatchType.GE.getOperation(), filters);
        //createPropertyFilter("VISIT_ID", visitId, MatchType.EQ.getOperation(), filters);
        //INFECTED_DIAG,INFECTED_POSITION,INFECTED_TIME,INFECTED_BACTERIA,INFECTED_TIMES,INFECTED_SOURCE,SUSPICIOUS_DIAG
        ResultVo<PageResultVo<Map<String, String>>> resultVo = hbaseQueryClient.getPageByCondition(
                PageRequestBuilder.init()
                        .tableName("HDR_INFECTION_WARN")
                        .patientId(patientId)
                        .oid(oid)
                        .visitId(visitId)
                        .visitTypeCode("")
                        .filters(filters)
                        .pageNo(0)
                        .pageSize(0)
                        .orderBy("")
                        .desc()
                        .column("INFECTED_DIAG", "INFECTED_POSITION", "INFECTED_TIME", "INFECTED_BACTERIA",
                                "INFECTED_TIMES", "INFECTED_SOURCE", "SUSPICIOUS_DIAG")
                        .build());



        if(resultVo.isSuccess()){
            page.setResult(resultVo.getContent().getResult());
            page.setTotalCount(resultVo.getContent().getTotal());
        }
//        page = hbaseDao.findByConditionPage("HDR_INFECTION_WARN", oid, patientId, filters, page,
//                new String[]{"INFECTED_DIAG", "INFECTED_POSITION", "INFECTED_TIME", "INFECTED_BACTERIA",
//                        "INFECTED_TIMES", "INFECTED_SOURCE", "SUSPICIOUS_DIAG"});
        return page;
    }

    public static String getLastDay() {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd hh:mm:ss");
        Calendar calendar = Calendar.getInstance();
        Date date = new Date();
        calendar.setTime(date);
        int day = calendar.get(Calendar.DATE);
        calendar.set(Calendar.DATE, day - 1);
        String lastDay = sdf.format(calendar.getTime());
        return lastDay;
    }

    /**
     * 创建Filter
     *
     * @param columnName
     * @param keyword
     * @param filters
     */
    public static void createPropertyFilter(String columnName, String keyword, String MatchType,
                                            List<PropertyFilter> filters) {
        if (StringUtils.isNotBlank(keyword)) {
            PropertyFilter filter1 = new PropertyFilter();
            filter1.setMatchType(MatchType);
            filter1.setPropertyName(columnName);
            filter1.setPropertyValue(keyword);
            filters.add(filter1);
        }
    }

}
