package com.goodwill.hdr.civ.vo;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

public class DeptListWithinUser implements Serializable {
    private static final long serialVersionUID = -2020425991335014171L;
    private String deptCode;
    private String deptName;
    private List<UserVo> user = new ArrayList<>();

    public DeptListWithinUser() {
    }

    public DeptListWithinUser(String deptCode, String deptName) {
        this.deptCode = deptCode;
        this.deptName = deptName;
    }

    public String getDeptCode() {
        return deptCode;
    }

    public void setDeptCode(String deptCode) {
        this.deptCode = deptCode;
    }

    public String getDeptName() {
        return deptName;
    }

    public void setDeptName(String deptName) {
        this.deptName = deptName;
    }

    public List<UserVo> getUser() {
        return user;
    }

    public void setUser(List<UserVo> user) {
        this.user = user;
    }
}
