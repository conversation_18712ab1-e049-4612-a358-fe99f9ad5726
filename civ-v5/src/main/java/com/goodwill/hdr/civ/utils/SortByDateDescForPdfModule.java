package com.goodwill.hdr.civ.utils;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Comparator;
import java.util.Date;
import java.util.Map;

public class SortByDateDescForPdfModule implements Comparator<Map<String, String>> {


    @Override
    public int compare(Map<String, String> o1, Map<String, String> o2) {

        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String str0 = o1.get("createDateTime");
        String str1 = o2.get("createDateTime");
        Date date1 = null;
        Date date2 = null;
        try {

            date1 = sdf.parse(str0);
            date2 = sdf.parse(str1);

        } catch (ParseException e) {
            e.printStackTrace();
        }
        assert date1 != null;
        if (date1.before(date2)) {
            return 1;
        } else if (date1.equals(date2)) {
            return 0;
        } else {
            return -1;
        }

    }


}