package com.goodwill.hdr.civ.mapper;


import com.baomidou.dynamic.datasource.annotation.DS;
import com.goodwill.hdr.security.priority.entity.SecurityCommonDept;
import com.goodwill.hdr.web.core.mapper.HdrBaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2022/03/22
 * @Description security_dept表mapper
 */
@Mapper
@DS("security")
public interface SecurityDeptMapper extends HdrBaseMapper<SecurityCommonDept> {

    @Select("select distinct deptcode as deptCode,deptname as deptName from security_dept where pk_dept in (${pkDept})")
    List<Map<String, String>> selectDeptCodeNameByPkDept(String pkDept);
}
