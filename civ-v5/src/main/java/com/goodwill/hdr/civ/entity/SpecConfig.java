package com.goodwill.hdr.civ.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.io.Serializable;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2022-03-03
 */
@TableName("civ_spec_config")
public class SpecConfig implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 自动生成自增主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    private String usercode;

    /**
     * 科室编码
     */
    private String deptcode;

    /**
     * 配置类型（lab，exam，drugtype）
     */
    private String type;

    /**
     * 项编码
     */
    private String itemcode;

    /**
     * 项名称
     */
    private String itemname;

    /**
     * 最后修改时间
     */
    private String lastupdatetime;

    private String createBy;

    private String createTime;

    private String modifyBy;

    private String modifyTime;

    private String modifyNote;

    private String dataSource;

    private String dataStatus;

    private String nameOne;

    private String nameAbrev;

    private String nameFull;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getUsercode() {
        return usercode;
    }

    public void setUsercode(String usercode) {
        this.usercode = usercode;
    }

    public String getDeptcode() {
        return deptcode;
    }

    public void setDeptcode(String deptcode) {
        this.deptcode = deptcode;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getItemcode() {
        return itemcode;
    }

    public void setItemcode(String itemcode) {
        this.itemcode = itemcode;
    }

    public String getItemname() {
        return itemname;
    }

    public void setItemname(String itemname) {
        this.itemname = itemname;
    }

    public String getLastupdatetime() {
        return lastupdatetime;
    }

    public void setLastupdatetime(String lastupdatetime) {
        this.lastupdatetime = lastupdatetime;
    }

    public String getCreateBy() {
        return createBy;
    }

    public void setCreateBy(String createBy) {
        this.createBy = createBy;
    }

    public String getCreateTime() {
        return createTime;
    }

    public void setCreateTime(String createTime) {
        this.createTime = createTime;
    }

    public String getModifyBy() {
        return modifyBy;
    }

    public void setModifyBy(String modifyBy) {
        this.modifyBy = modifyBy;
    }

    public String getModifyTime() {
        return modifyTime;
    }

    public void setModifyTime(String modifyTime) {
        this.modifyTime = modifyTime;
    }

    public String getModifyNote() {
        return modifyNote;
    }

    public void setModifyNote(String modifyNote) {
        this.modifyNote = modifyNote;
    }

    public String getDataSource() {
        return dataSource;
    }

    public void setDataSource(String dataSource) {
        this.dataSource = dataSource;
    }

    public String getDataStatus() {
        return dataStatus;
    }

    public void setDataStatus(String dataStatus) {
        this.dataStatus = dataStatus;
    }

    public String getNameOne() {
        return nameOne;
    }

    public void setNameOne(String nameOne) {
        this.nameOne = nameOne;
    }

    public String getNameAbrev() {
        return nameAbrev;
    }

    public void setNameAbrev(String nameAbrev) {
        this.nameAbrev = nameAbrev;
    }

    public String getNameFull() {
        return nameFull;
    }

    public void setNameFull(String nameFull) {
        this.nameFull = nameFull;
    }

    @Override
    public String toString() {
        return "SpecConfig{" +
                "id=" + id +
                ", usercode=" + usercode +
                ", deptcode=" + deptcode +
                ", type=" + type +
                ", itemcode=" + itemcode +
                ", itemname=" + itemname +
                ", lastupdatetime=" + lastupdatetime +
                ", createBy=" + createBy +
                ", createTime=" + createTime +
                ", modifyBy=" + modifyBy +
                ", modifyTime=" + modifyTime +
                ", modifyNote=" + modifyNote +
                ", dataSource=" + dataSource +
                ", dataStatus=" + dataStatus +
                ", nameOne=" + nameOne +
                ", nameAbrev=" + nameAbrev +
                ", nameFull=" + nameFull +
                "}";
    }
}
