package com.goodwill.hdr.civ.controller;



import com.alibaba.fastjson.JSONObject;
import com.goodwill.hdr.civ.config.Config;
import com.goodwill.hdr.civ.dao.OrganizationDao;
import com.goodwill.hdr.civ.entity.SysConfig;
import com.goodwill.hdr.civ.service.PowerService;
import com.goodwill.hdr.civ.utils.WsUtil;
import com.goodwill.hdr.civ.vo.NameAndCodeVo;
import com.goodwill.hdr.core.orm.Page;
import com.goodwill.hdr.security.priority.entity.UserEntity;
import com.goodwill.hdr.security.utils.SecurityCommonUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.text.SimpleDateFormat;
import java.util.*;

/**
 * <AUTHOR>
 * @Description 类描述：患者系统权限设置
 * @Date 2018年6月11日
 * @modify 修改记录：
 */
@RequestMapping("/power")
@RestController
@Api(tags = "权限相关")
public class PowerAction {
    private static Logger log = LoggerFactory.getLogger(PowerAction.class);


    @Autowired
    private PowerService powerService;
    @Autowired
    private OrganizationDao organizationDao;


    //判断是否是管理员用户

    /**
     * @Description 判断是否是管理员用户
     */
    @PostMapping("/checkSysAdmin")
    public Map<String, String> checkSysAdmin(String oid, String userCode) {
        return powerService.getCheckAdmin(oid, userCode);
    }

    //全局设置

    /**
     * @Description 获取全局设置
     */
    @PostMapping("/getSysConfig")
    public List<SysConfig> getSysConfig() {
        Map<String, String> orgInfo = organizationDao.getLoginOrgInfo();
        return powerService.getSysConfig(orgInfo.get("code"));
    }

    /**
     * @Description 修改全局设置
     */
    @PostMapping("/updateSysConfig")
    public Map<String, String> updateSysConfig(String oid, String configCode, String configValue) {
        boolean b = powerService.updateSysConfig(oid, configCode, configValue);
        Map<String, String> rs = new HashMap<String, String>();
        if (b) {
            rs.put("result", "1");
        } else {
            rs.put("result", "0");
        }
        return rs;
    }

    //权限设置

    /**
     * @Description 获得分类视图权限
     */
    @PostMapping("/getPowerConfigByCategory")
    public List<Map<String, Object>> getPowerConfigByCategory(String oid, String userCode) {
        List<Map<String, Object>> list = powerService.getPowerConfigByCategory(oid, userCode);
        return list;
    }

    /**
     * @Description 获得就诊视图权限
     */
    @PostMapping("/getPowerConfigByVisit")
    public List<Map<String, Object>> getPowerConfigByVisit(String oid, String userCode) {
        List<Map<String, Object>> list = powerService.getPowerConfigByVisit(oid, userCode);
        return list;
    }

    /**
     * @Description 是否计算就诊视图Tab页签数据数量
     */
    @PostMapping("/getPowerConfigByTabNum")
    public Map<String, String> getPowerConfigByTabNum(String oid) {
        Map<String, String> map = powerService.getSysConfigByType(oid, "CivVisit_CalcNum");
        return map;
    }

    /**
     * @Description 获得当前视图权限
     */
    @PostMapping("/getPowerConfigByPage")
    public Map<String, String> getPowerConfigByPage(String oid, String userCode) {
        Map<String, String> map = powerService.getPowerConfigByPage(oid, userCode);
        return map;
    }

    /**
     * @Description 获得Hide权限
     */
    @PostMapping("/getPowerConfigByHidePat")
    public Map<String, String> getPowerConfigByHidePat(String oid) {
        Map<String, String> map = powerService.getSysConfigByType("all", "StartUse_HidePatKeyM");
        return map;
    }

    /**
     * @Description 获得OrderClose权限
     */
    @GetMapping("/getPowerConfigByOrderClose")
    public Map<String, String> getPowerConfigByOrderClose(String oid) {
        Map<String, String> map = powerService.getSysConfigByType(oid, "StartUse_OrderClose");
        return map;
    }

    /**
     * @Description 获得Allergy权限
     */
    @GetMapping("/getPowerConfigByAllergy")
    public Map<String, String> getPowerConfigByAllergy(String oid) {
        Map<String, String> map = powerService.getSysConfigByType(oid, "StartUse_Allergy");
        return map;
    }

    /**
     * @Description 获得用户所有权限
     */
    @PostMapping("/getPowerConfigByUser")
    public Map<String, Object> getPowerConfigByUser(String targetOid, String userCode, String deptCode) {
        Map<String, Object> map = new HashMap<String, Object>();
        if (StringUtils.isNotBlank(deptCode)) {
            map = powerService.getPowerConfigByDept(targetOid, deptCode);
        } else {
            map = powerService.getPowerConfigByUser(targetOid, userCode);
        }
        return map;
    }

    /**
     * @Description 修改用户权限
     */
    @PostMapping("/updatePowerConfigByUser")
    public Map<String, String> updatePowerConfigByUser(String targetOid, String userCode, String deptCode, String Current,
                                                       String Specialty, String TimeAxis, String Visit, String Category, String Mr, String Exam,
                                                       String Pathology, String specialtyTimeAxis, String medicalView, String userOid) {

        Map<String, String> rs = new HashMap<String, String>();
        if (StringUtils.isNotBlank(deptCode)) {
            rs = powerService.updatePowerConfigByDept(targetOid, deptCode, Current, Specialty, TimeAxis, Visit, Category, Mr, Exam,
                    Pathology, specialtyTimeAxis, medicalView);
        } else {
            rs = powerService.updatePowerConfigByUser(targetOid, userCode, Current, Specialty, TimeAxis, Visit, Category, Mr, Exam,
                    Pathology, specialtyTimeAxis, medicalView, userOid);
        }
        return rs;
    }

    /**
     * @Description 获得用户列表
     */
    @PostMapping("/getUserList")
    public Map<String, Object> getUserList(String oid, String userName, String deptCode, int pageNo,
                                           int pageSize) {
        Map<String, Object> map = powerService.getUserList(oid, userName, deptCode, pageNo,
                pageSize);
        return map;
    }

    /**
     * @Description 获得医生列表
     */
    @PostMapping("/getDoctorList")
    public Map<String, Object> getDoctorList(String oid, String userName, String deptCode, String pageNo, String pageSize) {

        Map<String, Object> map = powerService.getUserList(oid, userName, deptCode, Integer.valueOf(pageNo),
                Integer.valueOf(pageSize));
        return map;

    }

    /**
     * @Description 获得部门列表
     */
    @PostMapping("/getDeptList")
    public Page<Map<String, Object>> getDeptCodeList(String oid, String deptName, String pageNo, String pageSize) {
        Page<Map<String, Object>> map = powerService.getDeptCodeList(oid, deptName, Integer.valueOf(pageNo),
                Integer.valueOf(pageSize));
        return map;
    }

    /**
     * @Description 初始化所有科室权限
     */
    @PostMapping("/initDeptPower")
    public Map<String, String> initDeptPower(String oid) {
        Map<String, String> map = powerService.initDeptPower(oid);
        return map;
    }

    /**
     * 获取脱敏字段配置
     *
     * @return
     */
    @PostMapping("/getInfoHiddenScope")
    public List<Map<String, Object>> getInfoHiddenScope(String oid) {
        List<Map<String, Object>> list = powerService.getInfoHiddenField(null, "");
        return list;
    }
    @PostMapping("/getInfoHiddenDict")
    public  Page<Map<String, String>> getInfoHiddenDict(String oid,String keyWord,int pageNo,int pageSize) {
        Page<Map<String, String>> page = powerService.getInfoHiddenDict(null, keyWord,pageNo,pageSize);
        return page;
    }


    /**
     * 更新选择状态
     */
    @PostMapping("/updateSysHideConfig")
    public Map<String, String> updateSysHideConfig(String oid, String configCode, String configValue, String enabled, String fields, String maskRuleCode, String maskRuleName) {
        boolean result = powerService.updateSysHideConfig(oid, configCode, configValue, enabled, fields, maskRuleCode, maskRuleName);
        Map<String, String> map = new HashMap<String, String>();
        if (result) {
            map.put("status", "1");
        } else {
            map.put("status", "0");
        }
        return map;
    }

    /**
     * 新增 选择状态
     */
    @PostMapping("/insertSysHideConfig")
    public Map<String, String> insertSysHideConfig( String configName, String configValue, String fields, String maskRuleCode, String maskRuleName) {
        boolean result = powerService.insertSysHideConfig( configName, configValue, fields, maskRuleCode, maskRuleName);
        Map<String, String> map = new HashMap<String, String>();
        if (result) {
            map.put("status", "1");
        }
        return map;
    }

    /**
     * 删除病历文书脱敏字段
     *
     * @param configCode
     */
    @PostMapping("/deleteSysHideConfig")
    public Map<String, String> deleteSysHideConfig(String configCode,String delFields) {
        boolean result = powerService.deleteSysHideConfig(configCode,delFields);
        Map<String, String> map = new HashMap<String, String>();
        if (result) {
            map.put("status", "1");
        } else {
            map.put("status", "0");
        }
        return map;
    }


    /**
     * 获取通用配置
     */

    @PostMapping("/getCommonConfig")
    public Map<String, Object> getCommonConfig() {

        Map<String, Object> res = powerService.getCommonConfig();
        return res;
    }

    /**
     * 获取患者列表的配置
     */
    @ApiOperation(value = "获取患者列表配置", notes = "获取患者列表配置", httpMethod = "POST")
    @RequestMapping(value = "/getPatListConfig", method = RequestMethod.POST)
    public Map<String, Object> getPatListConfig() {
        Map<String, Object> res = powerService.getPatListConfig();
        return res;
    }

    /**
     * 获取当前视图的配置
     */
    @PostMapping("/getCurrentConfig")
    public Map<String, Object> getCurrentConfig(String oid) {
        Map<String, Object> res = powerService.getCurrentConfig(oid);
        return res;
    }


    /**
     * 获取就诊试图配置
     */
    @PostMapping("/getVisitConfig")
    public Map<String, Object> getVisitConfig(String userCode, String oid) {
        Map<String, Object> res = powerService.getVisitConfig(oid, userCode);
        return res;
    }

    /**
     * 获取分类视图配置
     */
    @PostMapping("/getCatagoryConfig")
    public Map<String, Object> getCatagoryConfig(String patientId, String visitId, String userCode) {
        Map<String, Object> res = powerService.getCatagoryConfig(userCode, patientId, visitId);
        return res;
    }


    /**
     * 获取水印配置和展示信息
     */
    @PostMapping("/getWaterMark")
    public Map<String, String> getWaterMark(String oid) {
        UserEntity currentUser = SecurityCommonUtil.getCurrentLoginUser();
//        CustomUserDetails currentUser = (CustomUserDetails) currentLoginUser;
        String username = currentUser.getUsername();
        String name = currentUser.getUsercode();
        String isShowWaterMark = Config.getIS_SHOW_WATER_MARK(oid);
        Date date1 = new Date();
        SimpleDateFormat sd = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String date = sd.format(date1);
        Map<String, String> map = new HashMap<>();
        map.put("result", isShowWaterMark);
        map.put("waterMark", username + "  " + name);
        map.put("date", date);
        return map;
    }

    @PostMapping("/getOidConfigByUser")
    public List<Map<String, String>> getOidConfigByUser(String userCode, String deptCode) {
        if (StringUtils.isNotBlank(userCode)) {
            return powerService.getOidConfigByUserCode(userCode);
        } else if (StringUtils.isNotBlank(deptCode)) {
            return powerService.getOidConfigByDeptCode(deptCode);
        }
        return new ArrayList<>();

    }

    @GetMapping("/getVisitStatusDict")
    public List<NameAndCodeVo> getVisitStatusDict() {
        return powerService.getVisitStatusDict();

    }

    /**
     * 京东方单点登录接口
     */
    @PostMapping ("/getSingleloginUser")
    public String getSingleloginUser(String oid, String jhTicket) {
        String userCode = powerService.getSingleloginUser(oid,jhTicket);
        return userCode;
    }
    /**
     * VIP权限获取
     */
    @ApiOperation(value = "VIP权限获取", notes = "VIP权限获取", httpMethod = "POST")
    @RequestMapping(value = "/getVipPatientDataRight", method = RequestMethod.POST)
    public List<Map<String, String>> getVipPatientDataRight(String oid, String deptCode, String userCode) {
        List<Map<String, String>> map = new ArrayList<>();
        if (StringUtils.isNotBlank(deptCode)) {
            map = powerService.getVipConfigByDept(oid, deptCode);
        } else {
            map = powerService.getVipConfigByUser(oid, userCode);
        }
        return map;
    }
    /**
     * VIP权限修改
     */
    @ApiOperation(value = "VIP权限修改", notes = "VIP权限修改", httpMethod = "POST")
    @RequestMapping(value = "/updateVipPatientDataRight", method = RequestMethod.POST)
    public Map<String, String> updateVipPatientDataRight(String oid, String userCode,String configValue,String deptCode) {
        Map<String, String> map = new HashMap<>();
        if (StringUtils.isNotBlank(deptCode) && !"undefined".equals(deptCode)) {
            map =  powerService.updateVipPatientDataRight(oid, "",configValue,deptCode);
        } else {
            map = powerService.updateVipPatientDataRight(oid, userCode,configValue,"");
        }
        return map;
    }

    /**
     * 获取脱敏规则
     * 20241210 脱敏设置目前为全局设置 故oid固定ALL
     */
    @PostMapping("/getMaskRules")
    public Map<String, List<Map<String, Object>>> getMaskRules(String oid) {
        oid="ALL";
        return powerService.getMaskRules(oid);
    }


    /**
     * @Description 获取全局设置
     */
    @PostMapping("/getSysConfigForHide")
    public List<SysConfig> getSysConfigForHide() {
        Map<String, String> orgInfo = organizationDao.getLoginOrgInfo();
        return powerService.getSysConfigForHide(orgInfo.get("code"));
    }

    /**
     * 获取用户脱敏数据
     * 20241105 由于目前用户脱敏不分院区，故oid固定设置为ALL
     */
    @PostMapping("/getSysHideConfigByUser")
    public Map<String, Object> getSysHideConfigByUser(String oid,String userCode) {
        oid="ALL";
        return powerService.getSysHideConfigByUser(oid, userCode);
    }

    /**
     * 保存用户与脱敏字段的映射关系
     */
   @PostMapping("/insertFiledUser")
    public Map<String, String> insertFiledUser(String userList,String dataMasking) {
        boolean result = powerService.insertFiledUser( userList, dataMasking);
        Map<String, String> map = new HashMap<String, String>();
        if (result) {
            map.put("status", "1");
        }else{
            map.put("status", "0");
        }
        return map;
    }

    /**
     * 20250117 对接第三方单点登录
     * 说明：因306特色医学中心需求添加 但此方法做的通用配置化的
     */
    @PostMapping("/getSingleloginUserByOther")
    public JSONObject getSingleloginUserByOther(String oid, String param) {
        String s = powerService.getSingleloginUserByOther(oid, param);
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("userCode", s);
        return jsonObject;
    }

    /**
     * 20250219 对接第三方单点登录 登出get请求
     */
    @GetMapping("/loginOut")
    public String loginOut(String url, String param) {
        url = url + "?" + param;
        log.info("进入loginOut..........." + url);
        return WsUtil.requestByGet(url);
    }

}
