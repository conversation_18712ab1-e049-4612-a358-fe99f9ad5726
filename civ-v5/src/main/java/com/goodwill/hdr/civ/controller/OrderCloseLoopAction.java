package com.goodwill.hdr.civ.controller;


import com.goodwill.hdr.civ.service.OCLService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

/**
 * 医嘱闭环Action
 *
 * <AUTHOR>
 * @Date 2018年4月25日
 */
@RequestMapping("/orderclose")
@RestController
@Api(tags = "医嘱闭环")
public class OrderCloseLoopAction {

    @Autowired
    private OCLService oclService;

    /**
     * @Description 口服药和静脉药闭环
     */
    @ApiOperation(value = "口服药和静脉药闭环", notes = "口服药和静脉药闭环", httpMethod = "POST")
    @RequestMapping(value = "/drugOCL", method = RequestMethod.POST)
    public String drugOCL(String oid, String patientId, String visitId, String orderNo) {
        //医嘱编号
//		String orderno = getParameter("orderNo");
        if (StringUtils.isBlank(patientId) || StringUtils.isBlank(visitId)) {
            return null;
        }
        //获取结果数据
        String result = oclService.getDrugOCL(oid, patientId, visitId, orderNo);
        return result;
    }

    /**
     * @Description 检验医嘱闭环
     */
    @ApiOperation(value = "检验医嘱闭环", notes = "检验医嘱闭环", httpMethod = "POST")
    @RequestMapping(value = "/labOCL", method = RequestMethod.POST)
    public String labOCL(String oid, String patientId, String visitId, String orderNo) {
        if (StringUtils.isBlank(patientId) || StringUtils.isBlank(visitId)) {
            return null;
        }
        //获取结果数据
        String result = oclService.getLabOCL(oid, patientId, visitId, orderNo);
        return result;
    }

    /**
     * @Description 检查医嘱闭环
     */
    @ApiOperation(value = "检查医嘱闭环", notes = "检查医嘱闭环", httpMethod = "POST")
    @RequestMapping(value = "/examOCL", method = RequestMethod.POST)
    public String examOCL(String oid, String patientId, String visitId, String orderNo) {
        if (StringUtils.isBlank(patientId) || StringUtils.isBlank(visitId)) {
            return null;
        }
        //获取结果数据
        String result = oclService.getExamOCL(oid, patientId, visitId, orderNo);
        return result;
    }

    /**
     * @Description 获取手术医嘱闭环
     */
    @ApiOperation(value = "获取手术医嘱闭环", notes = "获取手术医嘱闭环", httpMethod = "POST")
    @RequestMapping(value = "/operOCL", method = RequestMethod.POST)
    public String operOCL(String oid, String patientId, String visitId, String orderNo) {
        if (StringUtils.isBlank(patientId) || StringUtils.isBlank(visitId)) {
            return null;
        }
        //获取结果数据
        String result = oclService.getOperOCL(oid, patientId, visitId, orderNo);
        return result;
    }

    /**
     * @Description 当前视图 - 末次住院手术医嘱闭环
     */
    @ApiOperation(value = "末次住院手术医嘱闭环", notes = "末次住院手术医嘱闭环", httpMethod = "POST")
    @RequestMapping(value = "/getCvOCL", method = RequestMethod.POST)
    public String getCvOCL(String oid, String patientId, String visitId) {
        String result = "";
        if (StringUtils.isNotBlank(patientId)) {
            result = oclService.getCVOperOCL(oid, patientId, visitId);
        }
        return StringUtils.isBlank(result) ? "[]" : result;
    }

    /**
     * @Description 输血闭环
     */
    @ApiOperation(value = "输血闭环", notes = "输血闭环", httpMethod = "POST")
    @RequestMapping(value = "/bloodOCL", method = RequestMethod.POST)
    public String bloodOCL(String oid, String patientId, String visitId, String timesNo) {
        //用血次数编号
        if (StringUtils.isBlank(patientId) || StringUtils.isBlank(visitId)) {
            return null;
        }
        //获取结果数据
        String result = oclService.getBloodOCL(oid, patientId, visitId, timesNo);
        return result;
    }

}
