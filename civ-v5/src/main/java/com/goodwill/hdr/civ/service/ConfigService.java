package com.goodwill.hdr.civ.service;


import com.goodwill.hdr.civ.entity.Config;
import com.goodwill.hdr.civ.entity.FeedbackSuggestions;
import com.goodwill.hdr.civ.vo.TreeNodeVo;
import com.goodwill.hdr.core.orm.Page;

import java.util.List;
import java.util.Map;

public interface ConfigService  {

    Page<Map<String, Object>> queryConfigs(String oid, String keyWord, String configScopePage, String code, int pageNo, int pageSize);

    Map<String, String> updateOrSaveConfig(String oid, String id, String value);



    List<TreeNodeVo> getHospitalOid();

    List<Config> getConfigBycode(String oid, String configCode);

//    Map<String, String> updateOrSaveConfigBycode(String oid, String sysCode, String value);

    Map<String, String> insertConfig(String code, String oid, String configCode, String configName, String configValue, String configDesc, String configType, String groupId, String groupName, String groupScopePage);

    Map<String, String> isOnlyConfigCode(String code, String oid, String configCode);

    Map<String, String> saveSuggestion(String oid, String dateId, String relevantUser, String relevantEmail, String phone, String type, String description, byte[] bytes, String imagPath, String submitTime, String fileFileName);

    Map<String, String> saveSuggestionImage(String oid, String dateId, String imagePath, byte[] bytes, String fileFileName);

    FeedbackSuggestions querySuggestionImageById(String id);

    Page<Map<String, String>> querySuggestionByPageNo(String oid, int pageNo, int pageSize);

    Map<String, String> deleteSuggestionImage(String oid, String dateId);


    Page<Map<String, String>> getDeptPage(String oid, String keyWord, int pageNo, int pageSize);

    Page<Map<String, Object>> queryUser(String oid, String deptCode, String keyword, int pageNo, int pageSize, List<TreeNodeVo> oids);

    /**
     * 获取院区以及全部用户 不包含科室
     * @param oid
     * @param keyWord
     * @param pageNo
     * @param pageSize
     * @param oids
     * @return
     */
    Page<Map<String, Object>> queryUserOrRole(String oid, String deptCode, String keyWord, int pageNo, int pageSize, List<TreeNodeVo> oids);

}
