package com.goodwill.hdr.civ.entity;

import com.baomidou.mybatisplus.annotation.TableName;

import java.io.Serializable;

@TableName("hdr_md_platform_dict_item")
public class HdrMdPlatformDictItem implements Serializable {

    private static final long serialVersionUID = 1L;

    private String code;

    private String match_codes;

    private String dict_code;

    private String data_status;

    public static long getSerialVersionUID() {
        return serialVersionUID;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getMatch_codes() {
        return match_codes;
    }

    public void setMatch_codes(String match_codes) {
        this.match_codes = match_codes;
    }

    public String getDict_code() {
        return dict_code;
    }

    public void setDict_code(String dict_code) {
        this.dict_code = dict_code;
    }

    public String getData_status() {
        return data_status;
    }

    public void setData_status(String data_status) {
        this.data_status = data_status;
    }

    @Override
    public String toString() {
        return "HdrMdPlatformDictItem{" +
                "code='" + code + '\'' +
                ", match_codes='" + match_codes + '\'' +
                ", dict_code='" + dict_code + '\'' +
                ", data_status='" + data_status + '\'' +
                '}';
    }
}
