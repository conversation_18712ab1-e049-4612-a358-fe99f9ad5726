package com.goodwill.hdr.civ.controller;


import com.goodwill.hdr.civ.config.Config;
import com.goodwill.hdr.civ.config.ConfigCache;
import com.goodwill.hdr.civ.service.OrderService;
import com.goodwill.hdr.civ.vo.OrderShowConfigVo;
import com.goodwill.hdr.civ.vo.ResultVo;
import com.goodwill.hdr.core.orm.Page;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Description 类描述：医嘱Action
 * @Date 2018年6月6日
 * @modify 修改记录：
 */
@RestController
@RequestMapping("/order")
public class OrderAction {


    @Autowired
    private OrderService orderService;

    /**
     * 当前视图 - 药品医嘱
     *
     * @param orderStatus   医嘱状态   1-下达  2-审核  3-开始  4-撤销  5-停止
     * @param orderProperty 医嘱性质   1-临时    2-长期
     * @param orderType     医嘱类型  1-口服   2-静脉
     * @param mainDiag      诊断类型
     * @param dept_code     末次科室
     */
    @PostMapping("/getCVDrugList")
    public Page<Map<String, String>> getCVDrugList(String patientId, String oid, String visitId, int pageNo, int pageSize,
                                                   String orderStatus, String orderProperty,
                                                   String orderType, String mainDiag, String dept_code, String orderItemName, String orderBy, String orderDir) {
        Page<Map<String, String>> result = new Page<Map<String, String>>();

        if (StringUtils.isNotBlank(patientId)) {
            result = orderService.getCVDrugList(oid, patientId, visitId, orderType, "INPV", orderStatus, orderProperty,
                    mainDiag, dept_code, "Drag", orderItemName, orderBy, orderDir, pageNo, pageSize);
        }
        return result;
    }

    /**
     * @param orderStatus   医嘱状态   1-下达  2-审核  3-开始  4-撤销  5-停止
     * @param orderProperty 医嘱性质   1-临时    2-长期
     * @Description 口服药品医嘱
     */
    @PostMapping("/getDrugListKF")
    public Page<Map<String, String>> getDrugListKF(String oid, String patientId, String visitId, String visitType,
                                                   String orderStatus, String orderProperty, String orderItemName, String orderBy, String orderDir, int pageNo, int pageSize) {
        List<Map<String, Object>> kfShowConfig = OrderShowConfigVo.getKfShowConfig(oid);
        if (StringUtils.isBlank(orderBy)) {
            orderBy = "orderTime";
        }
        Page<Map<String, String>> result = orderService.getDrugListKF(oid, patientId, visitId, visitType, orderStatus,
                orderProperty, "", "", "", orderItemName, orderBy, orderDir,kfShowConfig , pageNo, pageSize);
        return result;
    }

    /**
     * @Description 静脉药品医嘱
     */
    @PostMapping("/getDrugListJMZS")
    public Page<Map<String, String>> getDrugListJMZS(String oid, String patientId, String visitId, String visitType,
                                                     String orderStatus, String orderProperty, String orderItemName, String orderBy, String orderDir, int pageNo, int pageSize) {
        List<Map<String, Object>> jmShowConfig = OrderShowConfigVo.getJmShowConfig(oid);
        Page<Map<String, String>> result = orderService.getDrugListJMZS(oid, patientId, visitId, visitType, orderStatus,
                orderProperty, "", "", "", orderItemName, orderBy, orderDir, jmShowConfig, pageNo, pageSize);
        return result;
    }

    /**
     * @Description 其他药品医嘱
     */
    @PostMapping("/getDrugListQTYP")
    public Page<Map<String, String>> getDrugListQTYP(String oid, String patientId, String visitId, String visitType,
                                                     String orderStatus, String orderProperty, String orderItemName, String orderBy, String orderDir, int pageNo, int pageSize) {

        Page<Map<String, String>> result = orderService.getDrugListQTYP(oid, patientId, visitId, visitType, orderStatus,
                orderProperty, orderItemName, orderBy, orderDir, pageNo, pageSize);
        return result;
    }

//    /**
//     * 获取护理医嘱
//     */
//    @PostMapping("/getNurseOrder")
//    public Page<Map<String, String>> getNurseOrder(String oid, String patientId, String visitId,
//                                                   String visitType, String orderStatus, String orderProperty,
//                                                   int pageNo, int pageSize) {
//
//        Page<Map<String, String>> result = orderService.getNurseOrderList(oid, patientId, visitId, visitType, orderStatus,
//                orderProperty, pageNo, pageSize);
//        return result;
//    }

    /**
     * 获取其他医嘱
     */
    @PostMapping("/getOthersOrder")
    public Page<Map<String, String>> getOthersOrder(String oid, String patientId, String visitId, String orderItemName,
                                                    String visitType, String orderStatus, String orderProperty, String orderBy, String orderDir,
                                                    int pageNo, int pageSize) {

        Page<Map<String, String>> result = orderService.getOthersOrderList(oid, patientId, visitId, visitType, orderStatus,
                orderProperty, orderItemName, orderBy, orderDir, pageNo, pageSize);
        return result;
    }

    /**
     * @Description 检验医嘱
     */
    @PostMapping("/getLabList")
    public Page<Map<String, String>> getLabList(String oid, String patientId, String visitId, String visitType, String orderItemName, String orderBy, String orderDir, int pageNo, int pageSize) {
        Page<Map<String, String>> result = orderService.getLabList(oid, patientId, visitId, visitType, "", "", "", orderItemName, orderBy, orderDir,
                pageNo, pageSize);
        return result;
    }

    /**
     * @Description 检查医嘱
     */
    @PostMapping("/getExamList")
    public Page<Map<String, String>> getExamList(String oid, String patientId, String visitId,
                                                 String visitType, String orderItemName, String orderBy, String orderDir, int pageNo, int pageSize) {
        Page<Map<String, String>> result = orderService.getExamList(oid, patientId, visitId, visitType, "", "", "", orderItemName, orderBy, orderDir,
                pageNo, pageSize);
        return result;

    }


    /**
     * @Description 手术医嘱
     */
    @PostMapping("/getOperList")
    public Page<Map<String, String>> getOperList(String oid, String patientId, String visitId,
                                                 String visitType, String orderItemName, String orderBy, String orderDir, int pageNo, int pageSize) {
        Page<Map<String, String>> result = orderService.getOperList(oid, patientId, visitId, visitType, orderItemName, orderBy, orderDir, pageNo, pageSize);
        return result;
    }

    /**
     * @Description 用血申请
     */
    @PostMapping("/getBloodList")
    public Page<Map<String, String>> getBloodList(String oid, String patientId, String visitId, int pageNo, int pageSize) {
        Page<Map<String, String>> result = orderService.getBloodList(oid, patientId, visitId, pageNo, pageSize);
        return result;
    }

    /**
     * @Description 医嘱执行记录
     */
    @PostMapping("/getOrderExeList")
    public Page<Map<String, String>> getOrderExeList(String oid, String patientId, String visitId, int pageNo, int pageSize, String orderNo, String orderType) {
        Page<Map<String, String>> result = orderService.getOrderExeList(oid, patientId, visitId, orderNo,orderType, pageNo, pageSize);
        return result;
    }

    /**
     * @Description 药品医嘱发药审核
     */
    @PostMapping("/getDrugCheckList")
    public Page<Map<String, String>> getDrugCheckList(String oid, String patientId, String visitId, int pageNo, int pageSize, String orderNo, String orderType,String visitType) {
        Page<Map<String, String>> result = orderService.getDrugCheckList(oid, patientId, visitId, orderNo,orderType, pageNo, pageSize,visitType);
        return result;
    }

    /**
     * @Description 检验医嘱详情
     */
    @PostMapping("/getLabReportDetails")
    public Page<Map<String, String>> getLabReportDetails(String oid, String patientId, String visitId, String visitType, int pageNo, int pageSize, HttpServletRequest httpServletRequest) {
        String field = Config.getOrderCloseLabJoinField(oid);
        if("INPV".equals(visitType)){
            field = Config.getOrderCloseZYLabJoinField(oid);
        }
        String[] fieldValue = field.split("\\|");
        String orderno = httpServletRequest.getParameter(fieldValue[1]);
        Page<Map<String, String>> result = orderService.getLabReportDetails(oid, patientId, visitType,visitId, fieldValue[0],
                orderno, pageNo, pageSize);
        return result;
    }

    /**
     * @Description 检查医嘱详情
     */
    @PostMapping("/getExamReportDetails")
    public Map<String, String> getExamReportDetails(String oid, String patientId,String visitId, String visitType, HttpServletRequest httpServletRequest) {
        String field = Config.getOrderCloseJoinField(oid);
        String[] fieldValue = field.split("\\|");
        String orderno = httpServletRequest.getParameter(fieldValue[1]);
        //		String orderNo = getParameter("orderNo");
        Map<String, String> result = orderService.getExamReportDetails(oid, patientId, visitType,visitId, fieldValue[0], orderno);
        return result;
    }

    /**
     * @Description 查询患者医嘱
     */
    @PostMapping("/getOrders")
    public Page<Map<String, String>> getOrders(String oid, String patientId, String orderStatus,
                                               String orderProperty, String orderType, HttpServletRequest servletRequest,
                                               String orderTimeBegin, String orderTimeEnd, String outPatientId, String visitType,
                                               String orderBy, String orderDir,String drugType , String drugProperty,
                                               int pageNo, int pageSize) {

        String orderName = servletRequest.getParameter("keyWord").replace("(", "").replace(")", "");
        Page<Map<String, String>> result = orderService.getOrders(oid, patientId, orderStatus, orderProperty, orderType,
                orderTimeBegin, orderTimeEnd, orderName, orderBy, orderDir, pageNo, pageSize, outPatientId, visitType,drugType,drugProperty);
        //响应
        return result;
    }

    /**
     * @Description 医嘱闭环显示配置
     */
    @PostMapping("/getOrderCloseShowConfig")
    public Map<String, String> getOrderCloseShowConfig(String oid) {
        Map<String, String> result = Config.getCIV_CATEGARY_ORDER_SHOW_CONFIG(oid);
        return result;
    }

    /**
     * @Description 每次就诊全部医嘱
     */
    @PostMapping("/getVisitView")
    public Page<Map<String, String>> getVisitView(String patientId, String visitId, String visitType,
                                                  String orderStatus, String orderProperty, String orderType, String orderItemName,
                                                  String orderDir, String oid, String orderBy, int pageNo, int pageSize) {

        Page<Map<String, String>> result = orderService.getVisitPageView(oid, patientId, visitId, visitType, orderStatus,
                orderProperty, orderType, orderItemName, orderBy, orderDir, pageNo, pageSize);
        return result;
    }

    /**
     * 获取药品确认单和医嘱执行历史的表头
     */
    @PostMapping("/getOclTableHeadJson")
    public ResultVo<Map<String, List<Map<String, String>>>> getOclTableHeadJson(String oid,String orderType ) {

        ResultVo<Map<String, List<Map<String, String>>>>  result = orderService.getOclTableHead(oid,orderType);
        return result;
    }

    @PostMapping("/getSingleOcl")
    public Map<String, String> getSingleOcl(String oid, String patientId, String visitId, String visitType, String orderProperty, String orderType,String orderNo) {
        Map<String, String> map = orderService.getSingleOcl(oid, patientId, visitId, visitType, orderProperty, orderType,orderNo);
        return map;
    }

    /**
     * @Description 获取其他医嘱中需要展示操作列的配置
     */
    @PostMapping("/getShowOrderClassCodeForOthers")
    public Map<String, String> getShowOrderClassCodeForOthers(String oid) {
        String value = ConfigCache.getCache(oid, "CIV_ORDER_CLASS_CODE_FOR_OTHERS");
        Map<String, String> map = new HashMap<String, String>();
        map.put("showOrderClassCode", StringUtils.isNotBlank(value) ? value.trim() : "");
        return map;
    }

}
