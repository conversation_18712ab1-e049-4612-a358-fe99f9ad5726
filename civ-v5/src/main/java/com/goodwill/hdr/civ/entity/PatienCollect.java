package com.goodwill.hdr.civ.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.io.Serializable;

/**
 * 患者关注收藏表(PatienCollect)实体类
 *
 * <AUTHOR>
 * @since 2024-12-26 14:40:32
 */
@TableName("civ_patien_collect")
public class PatienCollect implements Serializable {
    private static final long serialVersionUID = 409797399941373308L;

    @TableId(value = "id", type = IdType.ASSIGN_UUID)
    private String id;
    @TableField("patient_id")
    private String patientId;
    @TableField("visit_id")
    private String visitId;
    @TableField("visit_type_code")
    private String visitTypeCode;
    @TableField("user_code")
    private String userCode;
    /**
     * 1：收藏，0取消收藏
     */
    @TableField("is_collect")
    private String isCollect;
    @TableField("oid")
    private String oid;


    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getPatientId() {
        return patientId;
    }

    public void setPatientId(String patientId) {
        this.patientId = patientId;
    }

    public String getVisitId() {
        return visitId;
    }

    public void setVisitId(String visitId) {
        this.visitId = visitId;
    }

    public String getVisitTypeCode() {
        return visitTypeCode;
    }

    public void setVisitTypeCode(String visitTypeCode) {
        this.visitTypeCode = visitTypeCode;
    }

    public String getUserCode() {
        return userCode;
    }

    public void setUserCode(String userCode) {
        this.userCode = userCode;
    }

    public String getIsCollect() {
        return isCollect;
    }

    public void setIsCollect(String isCollect) {
        this.isCollect = isCollect;
    }

    public String getOid() {
        return oid;
    }

    public void setOid(String oid) {
        this.oid = oid;
    }

}

