package com.goodwill.hdr.civ.utils;

import org.apache.commons.codec.digest.DigestUtils;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @Description 类描述：南医三院护理记录参数获取工具类
 * @modify 修改记录：
 */
public class GenUtils {

    private static String username = "hip";

    private static String password = "hip123456";

    public static String setUrl(String url, String patientId, String visitId, Map<String, String> paramMap) {
        url = url.replace("#{patientId}", patientId).replace("#{visitId}", visitId);
        String timestamp = "" + System.currentTimeMillis();
        String nonce = GenUtils.genNonce();
        url = url.replace("#{nonce}", nonce).replace("#{timestamp}", timestamp);
        url = url.replace("#{sign}", md5(username + password + nonce + timestamp));
        url = url.replace("#{viewType}",paramMap.get("viewType"));
        return url;
    }

    /**
     * 南医三院MD5加密直接返回的字符串
     * @param str
     * @return
     */
    public static String md5(String str) {
        //TODO 生成
        return DigestUtils.md5Hex(str);
    }

    public static String genNonce() {
        //TODO 自定义随机器生成
        String random = String.valueOf((int) ((Math.random() * 9 + 1) * 100000));
        return random;
    }

    public static void main(String[] args) {
        System.out.println(genNonce());
        System.out.println(DigestUtils.md5Hex("sssaas"));
        String SD_URL = "http://************:9801/crNursing/nursingDoc?viewType=doc&patientId=#{patientId}&visitId=#{visitId}&userName=hip&nonce=#{nonce}&timestamp=#{timestamp}&sign=#{sign}";
        System.out.println(setUrl(SD_URL, "pid", "vid",new HashMap<String, String>()));

    }
}
