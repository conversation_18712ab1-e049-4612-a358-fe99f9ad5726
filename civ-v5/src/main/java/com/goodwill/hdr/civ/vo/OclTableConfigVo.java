package com.goodwill.hdr.civ.vo;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.goodwill.hdr.civ.config.ConfigCache;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.util.*;

@Component
public class OclTableConfigVo implements ApplicationContextAware {
    private static final Logger log = LoggerFactory.getLogger(OclTableConfigVo.class);
    private static ObjectMapper objectMapper;
    private static final Map<String, List<String>> oclTableConfigMap = new HashMap<>();
    private static final Map<String, List<Map<String, String>>> oclTableDefaultMap = new HashMap<>();

    static {
//        口服


        oclTableConfigMap.put("KF", Arrays.asList("OCL_KF_EXE_TABLE_CONFIG", "OCL_KF_CHECK_TABLE_CONFIG"));
        paddingDefaultMap("OCL_KF_EXE_TABLE_CONFIG", "计划执行时间", "planPrescTime", "PLAN_PRESC_TIME");
        paddingDefaultMap("OCL_KF_EXE_TABLE_CONFIG", "实际执行时间", "prescTime", "PRESC_TIME");
        paddingDefaultMap("OCL_KF_EXE_TABLE_CONFIG", "计划执行护士", "planPrescNurseName", "PLAN_PRESC_NURSE_NAME");
        paddingDefaultMap("OCL_KF_EXE_TABLE_CONFIG", "执行护士", "prescNurseName", "PRESC_NURSE_NAME");
        paddingDefaultMap("OCL_KF_EXE_TABLE_CONFIG", "完成时间", "finishTime", "FINISH_TIME");
        paddingDefaultMap("OCL_KF_EXE_TABLE_CONFIG", "完成护士", "finishNurseName", "FINISH_NURSE_NAME");

        paddingDefaultMap("OCL_KF_CHECK_TABLE_CONFIG", "药单号", "detailSn", "DETAIL_SN");
        paddingDefaultMap("OCL_KF_CHECK_TABLE_CONFIG", "审核时间", "chargeConfirmTime", "CHARGE_CONFIRM_TIME");
        paddingDefaultMap("OCL_KF_CHECK_TABLE_CONFIG", "审核人", "chargeConfirmerName", "CHARGE_CONFIRMER_NAME");
        paddingDefaultMap("OCL_KF_CHECK_TABLE_CONFIG", "计划执行时间", "occTime", "OCC_TIME");


//        静脉注射
        oclTableConfigMap.put("JM", Arrays.asList("OCL_JM_EXE_TABLE_CONFIG", "OCL_JM_CHECK_TABLE_CONFIG"));
        paddingDefaultMap("OCL_JM_EXE_TABLE_CONFIG", "计划执行时间", "planPrescTime", "PLAN_PRESC_TIME");
        paddingDefaultMap("OCL_JM_EXE_TABLE_CONFIG", "实际执行时间", "prescTime", "PRESC_TIME");
        paddingDefaultMap("OCL_JM_EXE_TABLE_CONFIG", "计划执行护士", "planPrescNurseName", "PLAN_PRESC_NURSE_NAME");
        paddingDefaultMap("OCL_JM_EXE_TABLE_CONFIG", "执行护士", "prescNurseName", "PRESC_NURSE_NAME");
        paddingDefaultMap("OCL_JM_EXE_TABLE_CONFIG", "完成时间", "finishTime", "FINISH_TIME");
        paddingDefaultMap("OCL_JM_EXE_TABLE_CONFIG", "完成护士", "finishNurseName", "FINISH_NURSE_NAME");

        paddingDefaultMap("OCL_JM_CHECK_TABLE_CONFIG", "药单号", "detailSn", "DETAIL_SN");
        paddingDefaultMap("OCL_JM_CHECK_TABLE_CONFIG", "审核时间", "chargeConfirmTime", "CHARGE_CONFIRM_TIME");
        paddingDefaultMap("OCL_JM_CHECK_TABLE_CONFIG", "审核人", "chargeConfirmerName", "CHARGE_CONFIRMER_NAME");
        paddingDefaultMap("OCL_JM_CHECK_TABLE_CONFIG", "计划执行时间", "occTime", "OCC_TIME");

//        其他用药
        oclTableConfigMap.put("QT", Arrays.asList("OCL_QT_EXE_TABLE_CONFIG", "OCL_QT_CHECK_TABLE_CONFIG"));
        paddingDefaultMap("OCL_QT_EXE_TABLE_CONFIG", "计划执行时间", "planPrescTime", "PLAN_PRESC_TIME");
        paddingDefaultMap("OCL_QT_EXE_TABLE_CONFIG", "实际执行时间", "prescTime", "PRESC_TIME");
        paddingDefaultMap("OCL_QT_EXE_TABLE_CONFIG", "计划执行护士", "planPrescNurseName", "PLAN_PRESC_NURSE_NAME");
        paddingDefaultMap("OCL_QT_EXE_TABLE_CONFIG", "执行护士", "prescNurseName", "PRESC_NURSE_NAME");
        paddingDefaultMap("OCL_QT_EXE_TABLE_CONFIG", "完成时间", "finishTime", "FINISH_TIME");
        paddingDefaultMap("OCL_QT_EXE_TABLE_CONFIG", "完成护士", "finishNurseName", "FINISH_NURSE_NAME");

        paddingDefaultMap("OCL_QT_CHECK_TABLE_CONFIG", "药单号", "detailSn", "DETAIL_SN");
        paddingDefaultMap("OCL_QT_CHECK_TABLE_CONFIG", "审核时间", "chargeConfirmTime", "CHARGE_CONFIRM_TIME");
        paddingDefaultMap("OCL_QT_CHECK_TABLE_CONFIG", "审核人", "chargeConfirmerName", "CHARGE_CONFIRMER_NAME");
        paddingDefaultMap("OCL_QT_CHECK_TABLE_CONFIG", "计划执行时间", "occTime", "OCC_TIME");


//        检验
        oclTableConfigMap.put("JY", Collections.singletonList("OCL_JY_EXE_TABLE_CONFIG"));
        paddingDefaultMap("OCL_JY_EXE_TABLE_CONFIG", "项目代码", "labSubItemCode", "LAB_SUB_ITEM_CODE");
        paddingDefaultMap("OCL_JY_EXE_TABLE_CONFIG", "项目名称", "labSubItemName", "LAB_SUB_ITEM_NAME");
        paddingDefaultMap("OCL_JY_EXE_TABLE_CONFIG", "定性结果", "labQualResult", "LAB_QUAL_RESULT");
        paddingDefaultMap("OCL_JY_EXE_TABLE_CONFIG", "定量结果", "labResultValue", "LAB_RESULT_VALUE");
        paddingDefaultMap("OCL_JY_EXE_TABLE_CONFIG", "参考范围", "range", "RANGE");
        paddingDefaultMap("OCL_JY_EXE_TABLE_CONFIG", "单位", "labResultUnit", "LAB_RESULT_UNIT");
//        检查
        oclTableConfigMap.put("JC", Collections.singletonList("OCL_JC_EXE_TABLE_CONFIG"));
        paddingDefaultMap("OCL_JC_EXE_TABLE_CONFIG", "检查特征", "examFeature", "EXAM_FEATURE");
        paddingDefaultMap("OCL_JC_EXE_TABLE_CONFIG", "检查诊断", "examDiag", "EXAM_DIAG");


    }


    private static void paddingDefaultMap(String configName, String display, String name, String field) {

        List<Map<String, String>> tmpList = oclTableDefaultMap.get(configName);
        if (tmpList == null) {
            tmpList = new ArrayList<>();
        }
        Map<String, String> tmpMap = new HashMap<>();
        tmpMap.put("display", display);
        tmpMap.put("name", name);
        tmpMap.put("field", field);
        tmpList.add(tmpMap);
        oclTableDefaultMap.put(configName, tmpList);
    }


    public static List<Map<String, String>> getKfExeHead(String oid) {
        Optional<List<Map<String, String>>> kf = getHead(oid,"KF", 0);
        return kf.orElse(oclTableDefaultMap.get("OCL_KF_EXE_TABLE_CONFIG"));
    }

    public static List<Map<String, String>> getKfCheckHead(String oid) {
        Optional<List<Map<String, String>>> kf = getHead(oid,"KF", 1);
        return kf.orElse(oclTableDefaultMap.get("OCL_KF_CHECK_TABLE_CONFIG"));
    }

    public static List<Map<String, String>> getJmExeHead(String oid) {
        Optional<List<Map<String, String>>> kf = getHead(oid,"JM", 0);
        return kf.orElse(oclTableDefaultMap.get("OCL_JM_EXE_TABLE_CONFIG"));
    }

    public static List<Map<String, String>> getJmCheckHead(String oid) {
        Optional<List<Map<String, String>>> kf = getHead(oid,"JM", 1);
        return kf.orElse(oclTableDefaultMap.get("OCL_JM_CHECK_TABLE_CONFIG"));
    }

    public static List<Map<String, String>> getQtExeHead(String oid) {
        Optional<List<Map<String, String>>> kf = getHead(oid,"QT", 0);
        return kf.orElse(oclTableDefaultMap.get("OCL_QT_EXE_TABLE_CONFIG"));
    }

    public static List<Map<String, String>> getQtCheckHead(String oid) {
        Optional<List<Map<String, String>>> kf = getHead(oid,"QT", 1);
        return kf.orElse(oclTableDefaultMap.get("OCL_QT_CHECK_TABLE_CONFIG"));
    }

    public static List<Map<String, String>> getJyExeHead(String oid) {
        Optional<List<Map<String, String>>> kf = getHead(oid,"JY", 0);
        return kf.orElse(oclTableDefaultMap.get("OCL_JY_EXE_TABLE_CONFIG"));
    }

    public static List<Map<String, String>> getJcExeHead(String oid) {
        Optional<List<Map<String, String>>> kf = getHead(oid,"JC", 0);
        return kf.orElse(oclTableDefaultMap.get("OCL_JC_EXE_TABLE_CONFIG"));
    }

    private static Optional<List<Map<String, String>>> getHead(String oid,String orderType, int index) {
        List<String> configs = oclTableConfigMap.get(orderType);
        String configName = configs.get(index);
        String json = ConfigCache.getCache(oid,configName);
        Optional<List<Map<String, String>>> optional = Optional.empty();
        if (StringUtils.isNotBlank(json)) {
            try {
                List<Map<String, String>> headMapList = objectMapper.readValue(json, new TypeReference<List<Map<String, String>>>() {
                });
                optional = Optional.ofNullable(headMapList);
            } catch (IOException e) {
                log.error("{}解析失败", configName);
            }
        }

        return optional;
    }

    public static ResultVo<Map<String, List<Map<String, String>>>> buildKfResultVo(String oid) {
        return buildResultVo(oid,"KF");
    }

    public static ResultVo<Map<String, List<Map<String, String>>>> buildJmResultVo(String oid) {
        return buildResultVo(oid,"JM");
    }

    public static ResultVo<Map<String, List<Map<String, String>>>> buildQtResultVo(String oid) {
        return buildResultVo(oid,"QT");
    }

    public static ResultVo<Map<String, List<Map<String, String>>>> buildLabResultVo(String oid) {
        return buildResultVo(oid,"JY");
    }

    public static ResultVo<Map<String, List<Map<String, String>>>> buildExamResultVo(String oid) {
        return buildResultVo(oid,"JC");
    }


    private static ResultVo<Map<String, List<Map<String, String>>>> buildResultVo(String oid,String orderType) {
        List<String> configs = oclTableConfigMap.get(orderType);
        Map<String, List<Map<String, String>>> res = new HashMap<>();
        boolean exceptionFlag = false;
        StringBuilder msg = new StringBuilder();
        for (String configName : configs) {
            String json = ConfigCache.getCache(oid,configName);

            if (StringUtils.isNotBlank(json)) {
                try {
                    List<Map<String, String>> headMapList = objectMapper.readValue(json, new TypeReference<List<Map<String, String>>>() {
                    });
                    res.put(configName, headMapList);
                } catch (IOException e) {
                    exceptionFlag = true;
                    msg.append(configName).append("配置有误，采用默认配置;");
                    res.put(configName, oclTableDefaultMap.get(configName));

                }
            } else {
                res.put(configName, oclTableDefaultMap.get(configName));
            }
        }
        if (exceptionFlag) {
            return ResultVo.error(msg.toString(), res);
        }
        return ResultVo.success(res);
    }


    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        objectMapper = applicationContext.getBean(ObjectMapper.class);
    }
}
