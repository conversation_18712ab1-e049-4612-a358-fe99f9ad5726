package com.goodwill.hdr.civ.dao.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.goodwill.hdr.civ.dao.SpecialtyDeptConfigDao;
import com.goodwill.hdr.civ.entity.DeptSpecialtyIndicator;
import com.goodwill.hdr.civ.mapper.DeptSpecialtyIndicatorMapper;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @Description 类描述：
 * @modify 修改记录：
 */
@Repository
public class SpecialtyDeptConfigDaoImpl implements SpecialtyDeptConfigDao {

    @Autowired
    DeptSpecialtyIndicatorMapper deptSpecialtyIndicatorMapper;

    /**
     * 通过code查询科室的转科视图配置
     *
     * @param dept
     * @param itemCode
     * @param subItemCode
     * @return
     */
    @Override
    public List<DeptSpecialtyIndicator> getDeptSpecialtyConfig(String dept, String itemCode, String subItemCode) {
        QueryWrapper<DeptSpecialtyIndicator> queryWrapper = new QueryWrapper<>();
        queryWrapper.select("distinct sub_item_code", "sub_item_name", "item_code", "item_name","array_index")
                .eq("is_inuse", "Y")
                .in((StringUtils.isNotBlank(dept) && dept.length() > 2), "dept_code", dept)
                .eq(StringUtils.isNotBlank(itemCode), "item_code", itemCode)
                .eq(StringUtils.isNotBlank(subItemCode), "sub_item_code", subItemCode)
                .orderByAsc("array_index");
        List<DeptSpecialtyIndicator> deptSpecialtyIndicators = deptSpecialtyIndicatorMapper.selectList(queryWrapper);

        return deptSpecialtyIndicators;
//        String sql = " select distinct sub_item_code,sub_item_name,item_code,item_name  from  civ_dept_specialty_indicator where is_inuse = 'Y' ";
//        if(StringUtils.isNotBlank(dept) && dept.length() > 2){
//            sql +=   " and dept_code in  "+dept;
//        }
//        if (StringUtils.isNotBlank(itemCode)) {
//            sql += " and item_code= '" + itemCode + "'";
//        }
//        if (StringUtils.isNotBlank(subItemCode)) {
//            sql += " and sub_item_code= '" + subItemCode + "'";
//        }
//        sql += " order by array_index asc ";
//        logger.info("查询sql为:"+ sql );
//        Query query = createSqlQuery(sql);
//        List<Object> listobj = query.list();
//        List<DeptSpecialtyIndicator> list = new ArrayList<SpecialtyDeptIndicatorConfig>();
//        for (Object obj : listobj) {
//            SpecialtyDeptIndicatorConfig entity =new SpecialtyDeptIndicatorConfig();
//            Object[] objArr = (Object[]) obj;
//            entity.setSubItemCode(objArr[0]==null?"":objArr[0].toString());
//            entity.setSubItemName(objArr[1]==null?"":objArr[1].toString());
//            entity.setItemCode(objArr[2]==null?"":objArr[2].toString());
//            entity.setItemName(objArr[3]==null?"":objArr[3].toString());
//            list.add(entity);
//        }
//        return list;
    }


}
