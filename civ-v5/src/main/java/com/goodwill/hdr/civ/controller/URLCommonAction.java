package com.goodwill.hdr.civ.controller;


import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;

import com.goodwill.hdr.civ.config.ConfigCache;
import com.goodwill.hdr.civ.enums.commonModule.SysCodeEnum;
import com.goodwill.hdr.civ.service.CommonURLService;
import com.goodwill.hdr.civ.utils.WsUtil;
import com.goodwill.hdr.civ.vo.*;
import com.goodwill.hdr.core.orm.Page;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Description 类描述：
 * @Date 2019-12-09 15:03
 * @modify 修改记录：
 */
@RestController
@RequestMapping("/dialysis")
public class URLCommonAction {

    private final CommonURLService commonURLService;
    private final List<String> urlModuleList;
    private final ObjectMapper objectMapper;

    public URLCommonAction(CommonURLService commonURLService, ObjectMapper objectMapper) {
        this.commonURLService = commonURLService;
        this.objectMapper = objectMapper;
        this.urlModuleList = new ArrayList<>();
        urlModuleList.add(SysCodeEnum.URL.getCode());
        urlModuleList.add(SysCodeEnum.DES.getCode());
        urlModuleList.add(SysCodeEnum.LZ.getCode());
        urlModuleList.add(SysCodeEnum.ZGSY.getCode());
        urlModuleList.add(SysCodeEnum.BASE64.getCode());
        urlModuleList.add(SysCodeEnum.AES.getCode());
        urlModuleList.add(SysCodeEnum.CEMSMD5.getCode());
        urlModuleList.add(SysCodeEnum.RSA.getCode());
    }

    /**
     * @Description 获取web电子病历url
     */
    @PostMapping("/getWebEmrUrl")
    public Object getWebEmrUrl(String id, String sysCode, String end_time, String id_card_no, String oid, String patientId, String visitId, String visit_type_code, Integer pageNo, Integer pageSize) {


        if (urlModuleList.contains(sysCode)) {

            Map<String, String> rs = commonURLService.getCommonUrl(oid, patientId, visitId, visit_type_code, id, sysCode);
            return rs;
        } else if (SysCodeEnum.LIST.getCode().equals(sysCode)) {
            pageNo = pageNo == null ? 0 : pageNo;
            pageSize = pageSize == null ? 10 : pageSize;
            Page<Map<String, String>> rs = commonURLService.getUrlListFromHbaseForVisit(oid, patientId, visitId, visit_type_code, id, pageNo, pageSize);
            return rs;
        } else if ((SysCodeEnum.EHR.getCode().equals(sysCode))) {
            Map<String, String> rs = commonURLService.getEhrUrl(oid, id_card_no, id, sysCode);
            return rs;
        } else if ((SysCodeEnum.SYSENC.getCode().equals(sysCode))) {
            Map<String, String> rs = commonURLService.getSysencUrl(oid, patientId, visitId, visit_type_code, id, sysCode);
            return rs;
        } else if (SysCodeEnum.WEB_EMR.getCode().equals(sysCode) || SysCodeEnum.ANAES.getCode().equals(sysCode)
        || SysCodeEnum.ICU.getCode().equals(sysCode) || SysCodeEnum.MZEMR.getCode().equals(sysCode)
                || SysCodeEnum.ZYBH.getCode().equals(sysCode) || SysCodeEnum.MZBH.getCode().equals(sysCode)
                || SysCodeEnum.ZDBH.getCode().equals(sysCode)) {
            Map<String, String> map = new HashMap<>();
            map.put("end_time", end_time);
            map.put("id_card_no", id_card_no);
            Map<String, String> rs = commonURLService.getCommonWebUrl(oid, patientId, visitId, visit_type_code, id, sysCode, map);
            return rs;
        }
        return null;

    }

    @PostMapping("/getCommonTable")
    public Page<Map<String, String>> getCommonTable(String id, String oid, String patientId, String visitId, String visitType, String outPatientId, String paramMap, Integer pageNo, Integer pageSize) {


        List<KeyValueDto> paramList = new ArrayList<>();
        if (StringUtils.isNotBlank(paramMap)) {
            try {
                paramList = objectMapper.readValue(paramMap, new TypeReference<List<KeyValueDto>>() {
                });
            } catch (JsonProcessingException e) {
                throw new RuntimeException(e);
            }
        }
        Page<Map<String, String>> rs;
        if (StringUtils.isBlank(outPatientId)) {
            rs = commonURLService.getTableCommonModule(oid, patientId, visitId, visitType, id, paramList, pageNo, pageSize);

        } else {
            rs = commonURLService.getCategoryTableTemplate(outPatientId, id, paramList, pageNo, pageSize);
        }
        return rs;

    }

    @PostMapping("/getCommonTableConfig")
    public List<ColumnConfig> getCommonTableConfig(String id, String oid) {

        return commonURLService.getCommonTableConfig(oid, id);

    }


    @PostMapping("/getCommonList")
    public List<Map<String,String>> getCommonList(String id, String oid, String patientId, String visitId, String visitType, String outPatientId) {


        if (StringUtils.isBlank(outPatientId)) {
            return commonURLService.getCommonListForVisit(oid, patientId, visitId, visitType, id);
        } else {
            return commonURLService.getCommonListForCategory(outPatientId, id);
        }


    }

    @PostMapping("/getCommonListConfig")
    public CommonListConfigResponse getCommonListConfig(String id, String oid) {

        return commonURLService.getCommonListConfig(oid, id);

    }

    @PostMapping("/getCommonTab")
    public List<NameAndCodeVo> getCommonTab(String id, String oid) {
        return commonURLService.getCommonTab(oid, id);

    }

    /**
     * 获取嘉和电子病历时间戳方法
     * 说明： 20250102 因萍乡市妇幼保健院切入嘉和云病历需要 预防跨域的情况后端开发
     */
    @PostMapping("/getTimeStamp")
    public String getTimeStamp(String oid) {
        String timeStampUrl= ConfigCache.getCache(oid, "JH_EMR_TIMESTAMP_URL");
        String s=WsUtil.getPost(timeStampUrl,"");
        JSONObject jsonObject = JSONObject.parseObject(s);
        JSONObject bodyObject = jsonObject.getJSONObject("body");
        return bodyObject.toJSONString();
    }

}
