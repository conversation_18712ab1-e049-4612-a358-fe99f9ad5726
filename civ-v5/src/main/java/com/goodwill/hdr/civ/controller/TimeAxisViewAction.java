package com.goodwill.hdr.civ.controller;


import com.goodwill.hdr.civ.entity.TimeaxisConfig;
import com.goodwill.hdr.civ.service.TimeAxisViewService;
import com.goodwill.hdr.core.orm.Page;
import com.goodwill.hdr.security.utils.SecurityCommonUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Description 类描述：时间轴
 * @Date 2018年4月11日
 * @modify 修改记录：
 */
@RequestMapping("/tav")
@RestController
@Api(tags = "时间轴")
public class TimeAxisViewAction {


    @Autowired
    private TimeAxisViewService axisViewService;

    /**
     * @Description 就诊列表
     */
    @ApiOperation(value = "获取就诊列表", notes = "获取就诊列表", httpMethod = "POST")
    @RequestMapping(value = "/getVisitDeptList", method = RequestMethod.POST)
    public Page<Map<String, String>> getVisitDeptList(String oid, String patientId, String idCardNo, String keyWord, int pageSize, int pageNo) {
//		String deptName = getParameter("keyWord");

        Page<Map<String, String>> page = axisViewService.getVisitDeptList(oid,patientId , idCardNo, keyWord, pageSize, pageNo);
        return page;
    }

    /**
     * @Description 就诊周数列表
     */
    @ApiOperation(value = "就诊周数列表", notes = "就诊周数列表", httpMethod = "POST")
    @RequestMapping(value = "/getVisitTimeList", method = RequestMethod.POST)
    public List<Map<String, String>> getVisitTimeList(String oid, String patientId, String visitId) {
        List<Map<String, String>> map = axisViewService.getVisitTimeList(oid, patientId, visitId);
        return map;
    }

    /**
     * @Description 获取部分生命体征
     */
    @ApiOperation(value = "获取部分生命体征", notes = "获取部分生命体征", httpMethod = "POST")
    @RequestMapping(value = "/getVisitTimeTpList", method = RequestMethod.POST)
    public List<Map<String, Object>> getVisitTimeTpList(String oid, String patientId, String visitId, String time, String week, String cols) {
//        String admissionTime = getParameter("time");
//        String weekDays = getParameter("week");
        int week1 = 0;
        if (week != null && !"all".equals(week))
            week1 = Integer.valueOf(week);
        int cols1 = Integer.valueOf(cols);
        List<Map<String, Object>> rs = axisViewService.getVisitTimeTpList(oid, patientId, visitId, time, week1, cols1);
        return rs;
    }

    /**
     * @Description 获取手术
     */
    @ApiOperation(value = "获取手术", notes = "获取手术", httpMethod = "POST")
    @RequestMapping(value = "/getVisitOperTimeList", method = RequestMethod.POST)
    public List<Map<String, Object>> getVisitOperTimeList(String oid, String patientId, String visitId, String time, String week, String cols) {
//        String admissionTime = getParameter("time");
//        String weekDays = getParameter("week");
        int week1 = 0;
        if (week != null && !"all".equals(week))
            week1 = Integer.valueOf(week);
        int cols1 = Integer.valueOf(cols);
        List<Map<String, Object>> rs = axisViewService.getVisitOperTimeList(oid, patientId, visitId, time, week1, cols1);
        return rs;
    }

    /**
     * @Description 体温和脉搏
     */
    @ApiOperation(value = "体温和脉搏", notes = "体温和脉搏", httpMethod = "POST")
    @RequestMapping(value = "/getVTBloodAndPulse", method = RequestMethod.POST)
    public Map<String, Object> getVTBloodAndPulse(String oid, String patientId, String visitId, String time, String week, String cols) {
//        String admissionTime = getParameter("time");
//        String weekDays = getParameter("week");
        int week1 = 0;
        if (week != null && !"all".equals(week))
            week1 = Integer.valueOf(week);
        int cols1 = Integer.valueOf(cols);
        Map<String, Object> rs = axisViewService.getVisitTimeBloodAndPulse(oid, patientId, visitId, time, week1, cols1);
        return rs;
    }

    /**
     * @Description 药品
     */
    @ApiOperation(value = "药品", notes = "药品", httpMethod = "POST")
    @RequestMapping(value = "/getVisitTimeDrugList", method = RequestMethod.POST)
    public Map<String, Object> getVisitTimeDrugList(String oid, String patientId, String visitId, String time, String week, String cols, String mainDiagCode, String deptCode, String showMainIndicator) {
//        String admissionTime = getParameter("time");
//        String weekDays = getParameter("week");
//        //末次诊断
//        String mainDiag = getParameter("mainDiagCode");
//        //末次科室
//        String deptCode = getParameter("deptCode");
//        //是否显示重点药品
//        String showMainIndicator = getParameter("showMainIndicator");
        int week1 = 0;
        if (week != null && !"all".equals(week))
            week1 = Integer.valueOf(week);
        int cols1 = Integer.valueOf(cols);
        Map<String, Object> map = axisViewService.getVisitTimeDrugList(oid, patientId, visitId, time, mainDiagCode, deptCode, showMainIndicator, week1, cols1);
        return map;
    }

    /**
     * @Description 检验
     */
    @ApiOperation(value = "检验", notes = "检验", httpMethod = "POST")
    @RequestMapping(value = "/getVisitTimeLabList", method = RequestMethod.POST)
    public Map<String, Object> getVisitTimeLabList(String oid, String patientId, String visitId, String time, String week, String cols, String mainDiagCode, String deptCode, String showMainIndicator) {
//        String admissionTime = getParameter("time");
//        String weekDays = getParameter("week");
        int week1 = 0;
        //末次诊断
//        String mainDiag = getParameter("mainDiagCode");
//        //末次科室
//        String deptCode = getParameter("deptCode");
//        //是否显示重点药品
//        String showMainIndicator = getParameter("showMainIndicator");
        if (null == showMainIndicator || "0".equals(showMainIndicator)) {
            mainDiagCode = "";
            deptCode = "";
        }
        if (week != null && !"all".equals(week))
            week1 = Integer.valueOf(week);
        int cols1 = Integer.valueOf(cols);
        Map<String, Object> map = axisViewService.getVisitTimeLabList(oid, patientId, visitId, time, mainDiagCode, deptCode, week1, cols1);
        return map;
    }

    /**
     * @Description 检查
     */
    @ApiOperation(value = "检查", notes = "检查", httpMethod = "POST")
    @RequestMapping(value = "/getVisitTimeExamList", method = RequestMethod.POST)
    public Map<String, Object> getVisitTimeExamList(String oid, String patientId, String visitId, String time, String week, String cols, String mainDiagCode, String deptCode, String showMainIndicator) {
//        String admissionTime = getParameter("time");
//        String weekDays = getParameter("week");
        int week1 = 0;
        //末次诊断
//        String mainDiag = getParameter("mainDiagCode");
//        //末次科室
//        String deptCode = getParameter("deptCode");
//        //是否显示重点药品
//        String showMainIndicator = getParameter("showMainIndicator");
        if (null == showMainIndicator || "0".equals(showMainIndicator)) {
            mainDiagCode = "";
            deptCode = "";
        }
        if (week!=null && !"all".equals(week))
            week1 = Integer.valueOf(week);
        int cols1 = Integer.valueOf(cols);
        Map<String, Object> map = axisViewService.getVisitTimeExamList(oid, patientId, visitId, time, mainDiagCode, deptCode, week1, cols1);
        return map;
    }

    /**
     * @Description 诊疗记录
     */
    @ApiOperation(value = "诊疗记录", notes = "诊疗记录", httpMethod = "POST")
    @RequestMapping(value = "/getVisitTimeClinicList", method = RequestMethod.POST)
    public Map<String, Object> getVisitTimeClinicList(String oid, String patientId, String visitId, String time, String week, String cols) {
//        String admissionTime = getParameter("time");
//        String weekDays = getParameter("week");
        int week1 = 0;
        if (week != null && !"all".equals(week))
            week1 = Integer.valueOf(week);
        int cols1 = Integer.valueOf(cols);
        Map<String, Object> map = axisViewService.getVisitTimeClinicList(oid, patientId, visitId, time, week1, cols1);
        return map;
    }

    /**
     * @Description 手术
     */
    @ApiOperation(value = "手术", notes = "手术", httpMethod = "POST")
    @RequestMapping(value = "/getVisitTimeOperList", method = RequestMethod.POST)
    public Map<String, Object> getVisitTimeOperList(String oid, String patientId, String visitId, String time, String week, String cols) {
//        String admissionTime = getParameter("time");
//        String weekDays = getParameter("week");
        int week1 = 0;
        if (week != null && !"all".equals(week))
            week1 = Integer.valueOf(week);
        int cols1 = Integer.valueOf(cols);
        Map<String, Object> map = axisViewService.getVisitTimeOperList(oid, patientId,visitId, time, week1, cols1);
        return map;
    }

    /**
     * @Description 治疗相关
     */
    @ApiOperation(value = "治疗相关", notes = "治疗相关", httpMethod = "POST")
    @RequestMapping(value = "/getVisitTimeCureList", method = RequestMethod.POST)
    public Map<String, Object> getVisitTimeCureList(String oid, String patientId, String visitId, String time, String week, String cols, String mainDiagCode, String deptCode, String showMainIndicator) {
//        String admissionTime = getParameter("time");
//        String weekDays = getParameter("week");
        int week1 = 0;

        //末次诊断
//        String mainDiag = getParameter("mainDiagCode");
//        //末次科室
//        String deptCode = getParameter("deptCode");
//        //是否显示重点药品
//        String showMainIndicator = getParameter("showMainIndicator");
        if (null == showMainIndicator || "0".equals(showMainIndicator)) {
            mainDiagCode = "";
            deptCode = "";
        }
//		mainDiag = "I10xx02";
//		deptCode = "1150101";
        if (week != null && !"all".equals(week))
            week1 = Integer.valueOf(week);
        int cols1 = Integer.valueOf(cols);
        Map<String, Object> map = axisViewService.getVisitTimeCureList(oid, patientId, visitId, time, mainDiagCode, deptCode, week1, cols1);
        return map;
    }

    /**
     * @Description 入出转
     */
    @ApiOperation(value = "入出转", notes = "入出转", httpMethod = "POST")
    @RequestMapping(value = "/getVisitTimePat_AdtList", method = RequestMethod.POST)
    public List<Map<String, Object>> getVisitTimePat_AdtList(String oid, String patientId, String visitId) {
        //String admissionTime = getParameter("time");
        //int week = Integer.valueOf(getParameter("week"));
        List<Map<String, Object>> map = axisViewService.getVisitTimePat_AdtList(oid, patientId, visitId);
        return map;
    }

    /**
     * @Description 新增配置
     */
    @ApiOperation(value = "新增配置", notes = "新增配置", httpMethod = "POST")
    @RequestMapping(value = "/insertConfig", method = RequestMethod.POST)
    public Map<String, String> insertConfig(String oid, String patientId, String visitType, String visitId, String itemCode, String itemName, String subItemCode, String subItemName) {
        Map<String, String> rs = new HashMap<String, String>();
        //usercode,patientid,visittype,visitid,itemcode,itemname,subitemcode,subitemname
        String usercode = SecurityCommonUtil.getLoginUserCode();
//        String itemcode = getParameter("itemCode");
//        String itemname = getParameter("itemName");
//        String subitemcode = getParameter("subItemCode");
//        String subitemname = getParameter("subItemName");
        TimeaxisConfig config = new TimeaxisConfig();
        config.setUsercode(usercode);
        config.setPatientid(patientId);
        config.setVisittype(visitType);
        config.setVisitid(visitId);
        config.setItemcode(itemCode);
        config.setItemname(itemName);
        config.setSubitemcode(subItemCode);
        config.setSubitemname(subItemName);

        rs = axisViewService.insertConfig(oid, config);
        return rs;
    }

    /**
     * @Description 获取检验新增配置
     */
    @ApiOperation(value = "获取检验新增配置", notes = "获取检验新增配置", httpMethod = "POST")
    @RequestMapping(value = "/getConfigs", method = RequestMethod.POST)
    public List<Map<String, String>> getConfigs(String oid, String patientId, String visitType, String visitId, String mainDiagCode, String deptCode, String showMainIndicator) {
        List<Map<String, String>> rs = new ArrayList<Map<String, String>>();
        String usercode = SecurityCommonUtil.getLoginUserCode();
        //末次诊断
//        String mainDiag = getParameter("mainDiagCode");
//        //末次科室
//        String deptCode = getParameter("deptCode");
//        //是否显示重点药品
//        String showMainIndicator = getParameter("showMainIndicator");
        if (null == showMainIndicator || "0".equals(showMainIndicator)) {
            rs = axisViewService.getConfigList(oid, usercode, patientId, visitType, visitId);
        } else {
            rs = axisViewService.getConfigConfigList(oid, mainDiagCode, deptCode);
        }
        return rs;
    }

    /**
     * @Description 删除配置
     */
    @ApiOperation(value = "删除配置", notes = "删除配置", httpMethod = "POST")
    @RequestMapping(value = "/deleteConfig", method = RequestMethod.POST)
    public Map<String, String> deleteConfig(String oid, String id) {
        Map<String, String> rs = new HashMap<String, String>();
//        String id = getParameter("id");

        boolean b = axisViewService.deleteConfig(oid, id);
        if (b) {
            rs.put("result", "1");
        } else {
            rs.put("result", "0");
        }
        return rs;
    }

    /**
     * @Description 删除配置
     */
    @ApiOperation(value = "删除配置", notes = "删除配置", httpMethod = "POST")
    @RequestMapping(value = "/getInspectItemLine", method = RequestMethod.POST)
    public Map<String, Object> getInspectItemLine(String oid, String patientId, String visitId, String subItemCode, String time, String week, String cols) {
        Map<String, Object> rs = new HashMap<String, Object>();
//        String subitemcode = getParameter("subItemCode");
//        String admissionTime = getParameter("time");
//        String weekDays = getParameter("week");
        int week1 = 0;
        if (week != null && !"all".equals(week))
            week1 = Integer.valueOf(week);
        int cols1 = Integer.valueOf(cols);
        rs = axisViewService.getInspectItemLine(oid, patientId, "02", visitId, subItemCode, time, week1, cols1);
        return rs;
    }

}
