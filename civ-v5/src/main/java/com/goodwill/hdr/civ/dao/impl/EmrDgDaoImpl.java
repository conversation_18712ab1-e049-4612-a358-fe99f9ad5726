package com.goodwill.hdr.civ.dao.impl;

import com.goodwill.hdr.civ.dao.EmrDgDao;
import com.goodwill.hdr.civ.mapper.HdrEmrDgHtmlMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Description 类描述：
 * @modify 修改记录：
 */
@Repository
public class EmrDgDaoImpl implements EmrDgDao {
    @Autowired
    HdrEmrDgHtmlMapper hdrEmrDgHtmlMapper;

    @Override
    public List<Map<String, String>> getEmrDgHtml(String mrClassCode) {

        List<Map<String, String>> emrDgHtml = hdrEmrDgHtmlMapper.getEmrDgHtml(mrClassCode);
        return emrDgHtml;
//        List<Map<String,String>> list = new ArrayList<Map<String, String>>();
//        try {
//            String sql = "SELECT  hedh.emr_class_code,hedh.emr_class_name,hedh.html_text,hed.dg_code,hed.dg_name FROM hdr_emr_dg_html hedh left join hdr_emr_dg hed on  hed.emr_class_code= hedh.emr_class_code " +
//                    " WHERE hedh.emr_class_code = ? AND hedh.is_enabled = 'Y' ";
//            Query query = createSqlQuery(sql,mrClassCode);
//            List<Object> listobj = query.list();
//            for (Object obj : listobj) {
//                Object[] objArr = (Object[]) obj;
//                String emr_class_code=objArr[0].toString();
//                String emr_class_name=objArr[1].toString();
//                String html_text=objArr[2].toString();
//                String dg_code=objArr[3].toString();
//                String dg_name=objArr[4].toString();
//                Map<String,String>  map = new HashMap<String, String>();
//                map.put("emr_class_code",emr_class_code);
//                map.put("emr_class_name",emr_class_name);
//                map.put("html_text",html_text);
//                map.put("dg_code",dg_code);
//                map.put("dg_name",dg_name);
//                list.add(map);
//            }
//        } catch (Exception e) {
//            e.printStackTrace();
//            return list;
//        }
//        return list;
    }
}
