package com.goodwill.hdr.civ.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;

import com.goodwill.hdr.civ.config.Config;
import com.goodwill.hdr.civ.dao.SprcialtyViewNewDao;
import com.goodwill.hdr.civ.entity.Sickness;
import com.goodwill.hdr.civ.entity.SpecialtyDept;
import com.goodwill.hdr.civ.enums.HdrTableEnum;
import com.goodwill.hdr.civ.mapper.SicknessMapper;
import com.goodwill.hdr.civ.mapper.SpecialtyDeptMapper;
import com.goodwill.hdr.civ.service.SpecialtyViewNewService;
import com.goodwill.hdr.civ.utils.ListPage;
import com.goodwill.hdr.civ.utils.Utils;
import com.goodwill.hdr.core.orm.MatchType;
import com.goodwill.hdr.core.orm.Page;
import com.goodwill.hdr.hbase.bo.PropertyFilter;
import com.goodwill.hdr.hbase.dto.responseVo.PageResultVo;
import com.goodwill.hdr.hbase.dto.responseVo.ResultVo;
import com.goodwill.hdr.hbaseQueryClient.builder.PageRequestBuilder;
import com.goodwill.hdr.hbaseQueryClient.service.HbaseQueryClient;
import com.goodwill.hdr.security.priority.entity.SecurityCommonDept;
import com.goodwill.hdr.security.priority.mapper.SecurityCommonDeptMapper;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * <AUTHOR>
 * @Description 类描述：
 * @modify 修改记录：
 */
//TODO  多院区版本新增 oid，由于专科视图删除，故此处先不改造，传空oid
@Service
public class SpecialtyViewNewServiceImpl implements SpecialtyViewNewService {

    @Autowired
    private SprcialtyViewNewDao sprcialtyViewNewDao;

    @Autowired
    private SicknessMapper sicknessMapper;

    @Autowired
    private SpecialtyDeptMapper specialtyDeptMapper;

    @Autowired
    private SecurityCommonDeptMapper securityCommonDeptMapper;
    private final HbaseQueryClient hbaseQueryClient;

    public SpecialtyViewNewServiceImpl(HbaseQueryClient hbaseQueryClient) {
        this.hbaseQueryClient = hbaseQueryClient;
    }

    /**
     * 获取疾病列表
     *
     * @param main_diag
     * @return
     */
    @Override
    public Page<Sickness> getSicknessList(String main_diag, int pageNo, int pageSize) {
        QueryWrapper<Sickness> wrapper = new QueryWrapper<>();
        wrapper.eq("is_inuse", "Y").isNotNull("sickness_code").isNotNull("sickness_name");
        if (StringUtils.isNotBlank(main_diag)) {
            wrapper.like("sickness_name", main_diag);
        }
        List<Sickness> listSickness = sicknessMapper.selectList(wrapper);
//        List<Sickness> listSickness = sprcialtyViewNewDao.getSicknessList(main_diag, 0, 0);
        List<Sickness> result = new ArrayList<Sickness>();
        Page<Sickness> page = new Page<Sickness>();
        //分页处理
        if (pageNo > 0 && pageSize > 0) {
            if (listSickness.size() > ((pageNo - 1) * pageSize)) {
                //左闭右开
                if (listSickness.size() < pageNo * pageSize) {
                    result = listSickness.subList((pageNo - 1) * pageSize, listSickness.size());
                } else if (listSickness.size() >= pageNo * pageSize) {
                    result = listSickness.subList((pageNo - 1) * pageSize, pageNo * pageSize);
                }
                page.setPageNo(pageNo);
                page.setPageSize(pageSize);
                page.setCountTotal(true);
                page.setTotalCount(listSickness.size());
                page.setResult(result);
            } else {
                pageNo = 1;
                //左闭右开
                if (listSickness.size() < pageNo * pageSize) {
                    result = listSickness.subList((pageNo - 1) * pageSize, listSickness.size());
                } else if (listSickness.size() >= pageNo * pageSize) {
                    result = listSickness.subList((pageNo - 1) * pageSize, pageNo * pageSize);
                }
                page.setPageNo(pageNo);
                page.setPageSize(pageSize);
                page.setCountTotal(true);
                page.setTotalCount(listSickness.size());
                page.setResult(result);
            }
        } else {
            page.setPageNo(0);
            page.setPageSize(10);
            page.setCountTotal(true);
            page.setTotalCount(listSickness.size());
            page.setResult(listSickness);
        }
        return page;
    }
//
//    /**
//     * 获取疾病各项指标
//     *
//     * @return
//     */
//    @Override
//    public List<Map<String, Object>> getSicknessIndicatorList(String patientId, String visitId, String main_diag) {
//        String userCode = SecurityUtils.getCurrentUserName(); //在线用户
//        //先查询当前登录用户是否已设置
//        List<SpecialtyIndicatorConfigEntity> list = sprcialtyViewNewDao.getSpecityIndicatorConfig(userCode, main_diag);
//        if (null == list || list.size() == 0) {
//            list = sprcialtyViewNewDao.getSpecityIndicatorConfig("admin", main_diag);
//        }
//        //获取疾病页面布局顺序
//        List<SpecialtyConfigEntity> listIndex = sprcialtyViewNewDao.getSpecityConfig(userCode, main_diag);
//        if (null == listIndex || listIndex.size() == 0) {
//            listIndex = sprcialtyViewNewDao.getSpecityConfig("admin", main_diag);
//        }
//        if (null == list || list.size() == 0) {
//            if (null == list || list.size() == 0) {
//                //todo 返回未初始化数据
//                return null;
//            }
//        }
//        //每个大项下面包含的小项
////        Map<String, List<String>> itemIndicatorMap = new HashMap<String, List<String>>();
////        for (SpecialtyIndicatorConfigEntity entity : list) {
////            List<String> listItemIndicator = itemIndicatorMap.get(entity.getItemCode());
////            if (null == listItemIndicator) {
////                listItemIndicator = new ArrayList<String>();
////            }
////            listItemIndicator.add(entity.getItemIndicatorCode());
////            itemIndicatorMap.put(entity.getItemCode(), listItemIndicator);
////        }
//        //查询HBASE:各大项的每一小项的信息
////        Map<String, Map<String, Map<String, String>>> datas = this.getItemDatas(patientId, visitId, itemIndicatorMap);
//        List<String> listCode = new ArrayList<String>();
//        List<Map<String, Object>> resultList = new ArrayList<Map<String, Object>>();
//        //处理
//        for (SpecialtyConfigEntity configEntity : listIndex) {
//            Map<String, Object> resultMap = new HashMap<String, Object>();
//            //指标大项（检查，检验）
//            String itemCode = configEntity.getItemCode();
//            String itemNmae = configEntity.getItemName();
//            List<Map<String, String>> mapList = new ArrayList<Map<String, String>>();
//            for (SpecialtyIndicatorConfigEntity indicatorConfigEntity : list) {
//                if (itemCode.equals(indicatorConfigEntity.getItemCode())) {
//                    Map<String, String> map = new HashMap<String, String>();
//
//                    if ("Exam".equals(itemCode) || "Lab".equals(itemCode)) {
//                        if (!listCode.contains(indicatorConfigEntity.getItemIndicatorCode())) {
//                            map.put("code", indicatorConfigEntity.getItemIndicatorCode());
//                            map.put("name", indicatorConfigEntity.getItemIndicatorName());
//                            listCode.add(indicatorConfigEntity.getItemIndicatorCode());
//                            map.put("classCode", indicatorConfigEntity.getItemIndicatorCode());
//                            map.put("chartType", "List");
//                            mapList.add(map);
//                        }
//                    } else if ("Health".equals(itemCode) || "LabDetail".equals(itemCode)) {
//                        map.put("chartType", "FoldLine");
//                        map.put("code", indicatorConfigEntity.getItemIndicatorCode());
//                        map.put("name", indicatorConfigEntity.getItemIndicatorName());
//                        map.put("classCode", indicatorConfigEntity.getItemClassCode());
//                        mapList.add(map);
//                    } else if ("Drag".equals(itemCode)) {
//                        map.put("chartType", "Table");
//                        map.put("code", indicatorConfigEntity.getItemIndicatorCode());
//                        map.put("name", indicatorConfigEntity.getItemIndicatorName());
//                        map.put("classCode", indicatorConfigEntity.getItemClassCode());
//                        mapList.add(map);
//                    } else if ("NurseAndEat".equals(itemCode)) {
//                        if (!listCode.contains(indicatorConfigEntity.getItemIndicatorCode())) {
//                            map.put("chartType", "Table");
//                            map.put("code", indicatorConfigEntity.getItemIndicatorCode());
//                            map.put("name", indicatorConfigEntity.getItemIndicatorName());
//                            map.put("classCode", indicatorConfigEntity.getItemIndicatorCode());
//                            listCode.add(indicatorConfigEntity.getItemIndicatorCode());
//                            mapList.add(map);
//                        }
//
//                    }
//
//                }
//            }
//            resultMap.put("name", itemNmae);
//            resultMap.put("dataType", configEntity.getDataType());
//            resultMap.put("list", mapList);
//            resultMap.put("code", itemCode);
//            resultList.add(resultMap);
//        }
//
//        return resultList;
//    }
//
//    /**
//     * 获取每大项指标下每一个小项指标的最后一次记录
//     *
//     * @param patientId
//     * @param visitId
//     * @param itemCode
//     * @param indicatorCode
//     * @return
//     */
//    @Override
//    public List<Map<String, String>> getLastIndicatorData(String patientId, String visitId, String itemCode, String indicatorCode) {
//        String tableName = "HDR_VITAL_MEASURE";
//        List<PropertyFilter> filters = new ArrayList<PropertyFilter>();
////        HbaseUtils.createPropertyFilter("MEASURING_TIME", date, MatchType.GE.getOperation(), filters);
//        HbaseUtils.createPropertyFilter("VITAL_TYPE_NAME", indicatorCode, MatchType.EQ.getOperation(), filters);
//        List<Map<String, String>> list = hbaseDao.findConditionByPatientVisitId(tableName,"", patientId, visitId, filters, "VITAL_TYPE_NAME", "MEASURING_TIME", "VITAL_SIGN_VALUE", "VITAL_SIGN2_VALUE", "VITAL_SIGN3_VALUE");
//        //时间 降序
//        Utils.sortListByDate(list, "MEASURING_TIME", "desc");
//
//        return null;
//    }
//
//
//    /**
//     * 获取各项指标最后一次数据
//     *
//     * @return
//     */
//    public Map<String, Map<String, Map<String, String>>> getItemDatas(String patientId, String visitId, Map<String, List<String>> itemIndicatorMap) {
//        Map<String, Map<String, Map<String, String>>> mapRes = new HashMap<String, Map<String, Map<String, String>>>();
//        for (Map.Entry entry : itemIndicatorMap.entrySet()) {
//            String key = (String) entry.getKey();
//            List<String> columns = (List<String>) entry.getValue();
//            Map<String, Map<String, String>> map = new HashMap<String, Map<String, String>>();
//            if ("smtz".equals(key)) {//生命体征
//                map = this.getSmtzLastData(patientId, visitId, this.changeListToStr(columns));
//                mapRes.put("smtz", map);
//            }
//            if ("jyjg".equals(key)) {//检验结果
//                map = this.getJyjgLastData(patientId, visitId, this.changeListToStr(columns));
//                mapRes.put("jyjg", map);
//            }
//            if ("hlys".equals(key)) {//护理饮食
//                map = this.getInOrderLastData(patientId, visitId, this.changeListToStr(columns));
//                mapRes.put("hlys", map);
//            }
//            if ("zdyy".equals(key)) {//重点用药
//                map = this.getInOrderLastData(patientId, visitId, this.changeListToStr(columns));
//                mapRes.put("zdyy", map);
//            }
//            if ("jcbg".equals(key)) {//检查报告
//                map = this.getJcbgLastData(patientId, visitId, this.changeListToStr(columns));
//                mapRes.put("jcbg", map);
//            }
//
//        }
//        return mapRes;
//    }
//
//    /**
//     * 查询每一个大项
//     */
//    public Map<String, Object> getDatasByItemCode(String itemCode, String indicatorCode, String beginTime, String endTime) {
//        Map<String, Object> datas = new HashMap<String, Object>();
//        List<PropertyFilter> filters = new ArrayList<PropertyFilter>();
//        String dateColumn = "";
//        String inditorCode = "";
//        String table = "";
//        String queryColumns = "";
//        if ("smtz".equals(itemCode)) { //生命体征
//            table = "HDR_VITAL_MEASURE";
//            dateColumn = "MEASURING_TIME";
//            inditorCode = "VITAL_TYPE_CODE";
//            queryColumns = "VITAL_TYPE_CODE,VITAL_TYPE_NAME,MEASURING_TIME,VITAL_SIGN_VALUE,VITAL_SIGN2_VALUE,VITAL_SIGN3_VALUE";
//        } else if ("jyjg".equals(itemCode)) {//检验结果
//            table = "HDR_LAB_REPORT_DETAIL";
//            dateColumn = "REPORT_TIME";
//            inditorCode = "LAB_SUB_ITEM_CODE";
//            queryColumns = "LAB_SUB_ITEM_CODE,LAB_SUB_ITEM_NAME,REPORT_TIME,RANGE,LAB_RESULT_VALUE,LAB_RESULT_UNIT";
//
//        } else if ("hlys".equals(itemCode)) {// 护理饮食
//            table = "HDR_LAB_REPORT_DETAIL";
//            dateColumn = "REPORT_TIME";
//            inditorCode = "VITAL_TYPE_CODE";
//            queryColumns = "ORDER_CLASS_NAME,ORDER_CLASS_CODE,ORDER_ITEM_CODE,ORDER_ITEM_NAME";
//        } else if ("zdyy".equals(itemCode)) {// 重点用药
//            table = "HDR_IN_ORDER";
//            dateColumn = "REPORT_TIME";
//            inditorCode = "ORDER_ITEM_NAME";
//            queryColumns = "ORDER_CLASS_NAME,ORDER_CLASS_CODE,ORDER_ITEM_CODE,ORDER_ITEM_NAME";
//        } else if ("jcbg".equals(itemCode)) { // 检查报告
//            table = "HDR_EXAM_REPORT";
//            dateColumn = "REPORT_TIME";
//            inditorCode = "ORDER_ITEM_NAME";
//            queryColumns = "EXAM_ITEM_CODE,EXAM_ITEM_NAME,EXAM_DIAG,ORDER_ITEM_NAME";
//        } else if ("zdjy".equals(itemCode)) {// 重点检验
//            table = "HDR_LAB_REPORT_DETAIL";
//            dateColumn = "REPORT_TIME";
//            inditorCode = "LAB_SUB_ITEM_CODE";
//            queryColumns = "LAB_SUB_ITEM_CODE,LAB_SUB_ITEM_NAME,REPORT_TIME,RANGE,LAB_RESULT_VALUE,LAB_RESULT_UNIT";
//        }
//        filters.add(new PropertyFilter(inditorCode, "STRING", MatchType.EQ.getOperation(), indicatorCode));
//        if (StringUtils.isNotBlank(beginTime) && StringUtils.isNotBlank(endTime)) {
//            filters.add(new PropertyFilter(dateColumn, "STRING", MatchType.GE.getOperation(), beginTime));
//            filters.add(new PropertyFilter(dateColumn, "STRING", MatchType.LE.getOperation(), endTime));
//        }
//        datas.put("table", table);
//        datas.put("filters", filters);
//        datas.put("date", dateColumn);
//        datas.put("inditorCode", inditorCode);
//        datas.put("queryColumns", queryColumns);
//        return datas;
//    }
//
//    /**
//     * 获取生命体征最后一次数据
//     *
//     * @param patientId
//     * @param visitId
//     * @return
//     */
//    public Map<String, Map<String, String>> getSmtzLastData(String patientId, String visitId, String itemIndicators) {
//        String tableName = "HDR_VITAL_MEASURE";
//        List<PropertyFilter> filters = new ArrayList<PropertyFilter>();
//        HbaseUtils.createPropertyFilter("VITAL_TYPE_NAME", itemIndicators, MatchType.IN.getOperation(), filters);
//        List<Map<String, String>> listquery = hbaseDao.findConditionByPatientVisitId(tableName,"", patientId, visitId, filters, "VITAL_TYPE_CODE", "VITAL_TYPE_NAME", "MEASURING_TIME", "VITAL_SIGN_VALUE", "VITAL_SIGN2_VALUE", "VITAL_SIGN3_VALUE");
//        //时间 降序
//        Utils.sortListByDate(listquery, "MEASURING_TIME", "desc");
//        Map<String, Map<String, String>> mapRes = new HashMap<String, Map<String, String>>();
//        if (null != listquery && listquery.size() > 0) {
//            //取最后一次数据
//            for (Map<String, String> map : listquery) {
//                map.put("time", map.get("MEASURING_TIME"));
//                map.put("data", map.get("VITAL_SIGN_VALUE"));
//                map.put("unit", "");
//                if (null != mapRes.get("VITAL_TYPE_CODE")) {
//                    Map<String, String> mapTemp = mapRes.get("VITAL_TYPE_CODE");
//                    String time1 = map.get("MEASURING_TIME");
//                    String time2 = mapTemp.get("MEASURING_TIME");
//                    int res = time1.compareTo(time2);
//                    if (res > 0) {
//                        mapRes.put(map.get("VITAL_TYPE_CODE"), map);
//                    }
//                } else {
//                    mapRes.put(map.get("VITAL_TYPE_CODE"), map);
//                }
//            }
//        }
//        return mapRes;
//    }
//
//    /**
//     * 获取检验结果最后一次数据
//     *
//     * @param patientId
//     * @param visitId
//     * @return
//     */
//    public Map<String, Map<String, String>> getJyjgLastData(String patientId, String visitId, String filterColumns) {
//        String tableName = "HDR_LAB_REPORT_DETAIL";
//        List<PropertyFilter> filters = new ArrayList<PropertyFilter>();
//        HbaseUtils.createPropertyFilter("LAB_SUB_ITEM_CODE", filterColumns, MatchType.IN.getOperation(), filters);
//        HbaseUtils.createPropertyFilter("VISIT_ID", visitId, MatchType.IN.getOperation(), filters);
//        List<Map<String, String>> listquery = hbaseDao.findConditionByPatient(tableName,"", patientId, filters, "LAB_SUB_ITEM_CODE", "LAB_SUB_ITEM_NAME", "REPORT_TIME", "RANGE", "LAB_RESULT_VALUE", "LAB_RESULT_UNIT");
//        //时间 降序
//        Utils.sortListByDate(listquery, "REPORT_TIME", "desc");
//        Map<String, Map<String, String>> mapRes = new HashMap<String, Map<String, String>>();
//        if (null != listquery && listquery.size() > 0) {
//            //取最后一次数据
//            for (Map<String, String> map : listquery) {
//                map.put("time", map.get("REPORT_TIME"));
//                map.put("data", map.get("LAB_RESULT_VALUE"));
//                map.put("unit", map.get("LAB_RESULT_UNIT"));
//                if (null != mapRes.get("LAB_SUB_ITEM_CODE")) {
//                    Map<String, String> mapTemp = mapRes.get("LAB_SUB_ITEM_CODE");
//                    String time1 = map.get("REPORT_TIME");
//                    String time2 = mapTemp.get("REPORT_TIME");
//                    int res = time1.compareTo(time2);
//                    if (res > 0) {
//                        mapRes.put(map.get("LAB_SUB_ITEM_CODE"), map);
//                    }
//                } else {
//                    mapRes.put(map.get("LAB_SUB_ITEM_CODE"), map);
//                }
//            }
//        }
//        return mapRes;
//    }
//
//
//    /**
//     * 查询医嘱表：获取护理等级，饮食情况，重点用药等
//     *
//     * @param patientId
//     * @param visitId
//     * @return
//     */
//    public Map<String, Map<String, String>> getInOrderLastData(String patientId, String visitId, String filterColumns) {
//        String tableName = "HDR_IN_ORDER";
//        List<PropertyFilter> filters = new ArrayList<PropertyFilter>();
//        HbaseUtils.createPropertyFilter("ORDER_CLASS_CODE", filterColumns, MatchType.IN.getOperation(), filters);
//        List<Map<String, String>> listquery = hbaseDao.findConditionByPatientVisitId(tableName,"", patientId, visitId, filters, "ORDER_CLASS_NAME", "ORDER_CLASS_CODE", "ORDER_ITEM_CODE", "ORDER_ITEM_NAME");
//        //时间 降序
//        Utils.sortListByDate(listquery, "REPORT_TIME", "desc");
//        Map<String, Map<String, String>> mapRes = new HashMap<String, Map<String, String>>();
//        if (null != listquery && listquery.size() > 0) {
//            //取最后一次数据
//            for (Map<String, String> map : listquery) {
//                map.put("time", map.get("REPORT_TIME"));
//                map.put("data", map.get("ORDER_ITEM_NAME"));
//                map.put("unit", "");
//                if (null != mapRes.get("ORDER_CLASS_CODE")) {
//                    Map<String, String> mapTemp = mapRes.get("ORDER_CLASS_CODE");
//                    String time1 = map.get("REPORT_TIME");
//                    String time2 = mapTemp.get("REPORT_TIME");
//                    int res = time1.compareTo(time2);
//                    if (res > 0) {
//                        mapRes.put(map.get("ORDER_CLASS_CODE"), map);
//                    }
//                } else {
//                    mapRes.put(map.get("ORDER_CLASS_CODE"), map);
//                }
//            }
//        }
//        return mapRes;
//    }
//
//
//    /**
//     * 检查报告
//     *
//     * @param patientId
//     * @param visitId
//     * @return
//     */
//    public Map<String, Map<String, String>> getJcbgLastData(String patientId, String visitId, String filterColumns) {
//        String tableName = "HDR_EXAM_REPORT";
//        List<PropertyFilter> filters = new ArrayList<PropertyFilter>();
//        HbaseUtils.createPropertyFilter("EXAM_ITEM_CODE", filterColumns, MatchType.IN.getOperation(), filters);
//        List<Map<String, String>> listquery = hbaseDao.findConditionByPatientVisitId(tableName,"", patientId, visitId, filters, "EXAM_ITEM_CODE", "EXAM_ITEM_NAME", "EXAM_DIAG", "ORDER_ITEM_NAME");
//        //时间 降序
//        Utils.sortListByDate(listquery, "REPORT_TIME", "desc");
//        Map<String, Map<String, String>> mapRes = new HashMap<String, Map<String, String>>();
//        if (null != listquery && listquery.size() > 0) {
//            //取最后一次数据
//            for (Map<String, String> map : listquery) {
//                map.put("time", map.get("REPORT_TIME"));
//                map.put("data", map.get("ORDER_ITEM_NAME"));
//                map.put("unit", "");
//                if (null != mapRes.get("EXAM_ITEM_CODE")) {
//                    Map<String, String> mapTemp = mapRes.get("EXAM_ITEM_CODE");
//                    String time1 = map.get("REPORT_TIME");
//                    String time2 = mapTemp.get("REPORT_TIME");
//                    int res = time1.compareTo(time2);
//                    if (res > 0) {
//                        mapRes.put(map.get("EXAM_ITEM_CODE"), map);
//                    }
//                } else {
//                    mapRes.put(map.get("EXAM_ITEM_CODE"), map);
//                }
//            }
//        }
//        return mapRes;
//    }
//
//    public String changeListToStr(List<String> list) {
//        String filterColumns = "";
//        if (null != list) {
//            for (int i = 0; i < list.size(); i++) {
//                if (i != list.size() - 1) {
//                    filterColumns += ",";
//                } else {
//                    filterColumns += "";
//                }
//            }
//        }
//        return filterColumns;
//    }
//
//    /**
//     * 折线图数据:重点生命体征，重点检验结果
//     *
//     * @param itemCode
//     * @param indicatorCode
//     * @param numIndex
//     * @param beginTime
//     * @param endTime
//     */
//    public void getFoldLineViewData(String patientId, String visitId, String itemCode, String indicatorCode, int numIndex, String beginTime, String endTime) {
//        Map<String, Object> mapData = getDatasByItemCode(itemCode, indicatorCode, beginTime, endTime);
//        List<PropertyFilter> filters = (List<PropertyFilter>) mapData.get("filters");
//        String table = (String) mapData.get("table");
//        String filterColumns = (String) mapData.get("inditorCode");
//        String date = (String) mapData.get("date");
//        List<Map<String, String>> listQuery = hbaseDao.findConditionByPatientVisitId(table,"", patientId, visitId, filters, (String) mapData.get("queryColumns"));
//        //时间 降序
//        Utils.sortListByDate(listQuery, date, "desc");
//        //近3次，近5次，需要分页
//        ListPage<Map<String, String>> listPage = new ListPage<Map<String, String>>(listQuery, 1, 3);
//        if (3 == numIndex) {
//            listPage = new ListPage<Map<String, String>>(listQuery, 1, 3);
//        } else if (5 == numIndex) {
//            listPage = new ListPage<Map<String, String>>(listQuery, 1, 5);
//        }
//        List<Map<String, String>> pagedList = listPage.getPagedList();
//        //当前页记录再排序，为统计时间坐标准备
//        Utils.sortListByDate(pagedList, date, "asc");
//        if ("jyjg".equals(itemCode)) { //检验结果
//            //小项名称
//            String subItemName = null;
//            //单位
//            String subItemUnit = null;
//            //参考值
//            String subItemRange = null;
//            //x坐标
//            List<String> categories = new ArrayList<String>();
//            List<Map<String, Object>> series = new ArrayList<Map<String, Object>>();
//            Map<String, Object> serie = new HashMap<String, Object>();
//            List<Map<String, Object>> valueList = new ArrayList<Map<String, Object>>();
//            //遍历检验结果
//            for (Map<String, String> map : pagedList) {
//                Map<String, Object> value = new HashMap<String, Object>();
//                if (StringUtils.isBlank(subItemName)) {
//                    subItemName = map.get("LAB_SUB_ITEM_NAME");
//                }
//                if (StringUtils.isBlank(subItemUnit)) {
//                    subItemUnit = map.get("LAB_RESULT_UNIT");
//                }
//                if (StringUtils.isBlank(subItemRange)) {
//                    subItemRange = map.get("RANGE");
//                }
//                //y轴  先取定量结果  若没有值 再取定性结果
//                String resultValue = map.get("LAB_RESULT_VALUE");
//                if (StringUtils.isNotBlank(resultValue)) {
//                    if (Utils.isNumber(resultValue)) {
//                        value.put("y", Double.parseDouble(resultValue));
//                    } else {
//                        resultValue = map.get("LAB_QUAL_RESULT");
//                        if (Utils.isNumber(resultValue)) {
//                            value.put("y", Double.parseDouble(resultValue));
//                        }
//                    }
//                } else {
//                    //取定性结果
//                    resultValue = map.get("LAB_QUAL_RESULT");
//                    if (Utils.isNumber(resultValue)) {
//                        value.put("y", Double.parseDouble(resultValue));
//                    }
//                }
//                value.put("name", map.get("LAB_ITEM_NAME"));
//                valueList.add(value);
//                categories.add(map.get("REPORT_TIME"));
//            }
//        } else if ("".equals(itemCode)) {
//
//        }
//    }
//
    /**
     * 折线图生命体征:
     * 呼吸 breath
     * 脉搏 pulse
     * 体温 temp
     * 血压  blood_pressure
     *
     * @param patientId
     * @param visitId
     * @param numIndex
     * @param beginTime
     * @param endTime
     */
    @Override
    public Map<String, Object> getFoldLineData(String oid, String patientId, String visitId, String itemCode, int numIndex, String beginTime, String endTime) {
        //封装数据
        Map<String, Object> result = new HashMap<String, Object>();
        List<PropertyFilter> filters = new ArrayList<PropertyFilter>();
        filters.add(new PropertyFilter("VITAL_TYPE_CODE", MatchType.IN.getOperation(), itemCode));
        if (StringUtils.isNotBlank(beginTime) && StringUtils.isNotBlank(endTime)) {
            filters.add(new PropertyFilter("MEASURING_TIME", MatchType.GE.getOperation(), beginTime));
            filters.add(new PropertyFilter("MEASURING_TIME", MatchType.LE.getOperation(), endTime));
        }
        String vitalConfig = Config.getConfigValue(oid,"CIV_VITAL_SIGN_CONFIG");
        if(StringUtils.isNotBlank(vitalConfig)){
            String[] config = vitalConfig.split("\\|");
            filters.add(new PropertyFilter(config[0],config[1] , config[2]));
        }
        List<Map<String, String>> listQuery = new ArrayList<>();
        ResultVo<PageResultVo<Map<String, String>>> resultVo = hbaseQueryClient.getPageByCondition(
                PageRequestBuilder.init()
                        .tableName(HdrTableEnum.HDR_VITAL_MEASURE.getCode())
                        .patientId(patientId)
                        .oid(oid)
                        .visitId(visitId)
                        .visitTypeCode("")
                        .filters(filters)
                        .pageNo(0)
                        .pageSize(0)
                        .orderBy("")
                        .desc()
                        .column("EXAM_ITEM_NAME", "REPORT_TIME", "EXAM_CLASS_NAME","VITAL_TYPE_NAME","VITAL_SIGN_VALUE","VITAL_SIGN2_VALUE","MEASURING_TIME")
                        .build());
        if(resultVo.isSuccess()){
            listQuery = resultVo.getContent().getResult();
        }
        //List<Map<String, String>> listQuery = hbaseDao.findConditionByPatientVisitId(HdrTableEnum.HDR_VITAL_MEASURE.getCode(),oid, patientId, visitId, filters, "VITAL_TYPE_CODE", "VITAL_TYPE_NAME", "MEASURING_TIME", "VITAL_SIGN_VALUE", "VITAL_SIGN2_VALUE", "VITAL_SIGN3_VALUE", "UNIT");
        if (null == listQuery || listQuery.size() == 0) {
            result.put("unit", "");
            result.put("series", new ArrayList<Map<String, Object>>());
            result.put("categories", new ArrayList<String>());
            result.put("name", "");
            return result;
        }
        //时间 降序
        Utils.sortListByDate(listQuery, "MEASURING_TIME", "desc");
        //近3次，近5次，需要分页
        int num = 0;
        if (listQuery.size() == 0) {
            num = 1;
        }else{
            num = listQuery.size();
        }
        ListPage<Map<String, String>> listPage = new ListPage<Map<String, String>>(listQuery, 1, num );

        if (3 == numIndex) {
            listPage = new ListPage<Map<String, String>>(listQuery, 1, 3);
        } else if (5 == numIndex) {
            listPage = new ListPage<Map<String, String>>(listQuery, 1, 5);
        }else if (10 == numIndex) {
            listPage = new ListPage<Map<String, String>>(listQuery, 1, 10);
        }else if (50 == numIndex) {
            listPage = new ListPage<Map<String, String>>(listQuery, 1, 50);
        }
        List<Map<String, String>> pagedList = listPage.getPagedList();
        //当前页记录再排序，为统计时间坐标准备
        Utils.sortListByDate(pagedList, "MEASURING_TIME", "asc");

        //小项名称
        String subItemName = null;
        String unit = "";
        //x坐标 时间
        List<String> categories = new ArrayList<String>();
        List<Map<String, Object>> series = new ArrayList<Map<String, Object>>();

        List<Object> valueList = new ArrayList<Object>();
        List<Object> valueList2 = new ArrayList<Object>();
        //添加上限和下限

        Set<Double> valueSetMax = new HashSet<>();
        Set<Double> valueSetMix = new HashSet<>();
        String vitalSubTypeName1 = "";
        String vitalSubTypeName2 = "";
        //遍历检验结果
        for (Map<String, String> map : pagedList) {
//            Map<String, Object> value = new HashMap<String, Object>();
            if (StringUtils.isBlank(subItemName)) {
                subItemName = map.get("VITAL_TYPE_NAME");
                unit = map.get("UNIT");
            }
            if (map.get("VITAL_TYPE_NAME").contains("血压")) {
                vitalSubTypeName1 = "收缩压";
                valueSetMax.add(getData(vitalSubTypeName1, "max"));
                valueSetMix.add(getData(vitalSubTypeName1, "min"));
                vitalSubTypeName2 = "舒张压";
                valueSetMax.add(getData(vitalSubTypeName2, "max"));
                valueSetMix.add(getData(vitalSubTypeName2, "min"));

            } else {
                valueSetMax.add(getData(map.get("VITAL_TYPE_NAME"), "max"));
                valueSetMix.add(getData(map.get("VITAL_TYPE_NAME"), "min"));
            }

            //y轴  先取定量结果  若没有值 再取定性结果
            String resultValue = map.get("VITAL_SIGN_VALUE");
            String resultValue2 = map.get("VITAL_SIGN2_VALUE");

            if (StringUtils.isNotBlank(resultValue)) {
                valueList.add(Double.valueOf(resultValue));
            }
            if (StringUtils.isNotBlank(resultValue2)) {
                valueList2.add(Double.valueOf(resultValue2));
            }

            categories.add(map.get("MEASURING_TIME"));
        }
        //检验细项名称
        //if (StringUtils.isNotBlank(subItemName)) {
            result.put("name", subItemName);
        /*} else {
            result.put("name", itemName);
        }*/

        if (subItemName.contains("血压")) {
            Map<String, Object> serie1 = new HashMap<>();
            serie1.put("name", vitalSubTypeName1);
            serie1.put("data", valueList);
            series.add(serie1);
            Map<String, Object> serie2 = new HashMap<>();
            serie2.put("name", vitalSubTypeName2);
            serie2.put("data", valueList2);
            series.add(serie2);
        } else {
            Map<String, Object> serie = new HashMap<>();
            serie.put("name", subItemName);
            serie.put("data", valueList);
            series.add(serie);
        }
        //y坐标
        Map<String, Object> serieMax = new HashMap<String, Object>();
        serieMax.put("name", "上限");
        serieMax.put("dashStyle", "ShortDot");
        Map<String, Boolean> map = new HashMap<String, Boolean>();
        map.put("enabled", false);
        serieMax.put("marker", map);
        serieMax.put("data", valueSetMax);

        result.put("maxValue", serieMax);

        Map<String, Object> serieMix = new HashMap<String, Object>();
        serieMix.put("name", "下限");
        serieMix.put("dashStyle", "ShortDot");
        Map<String, Boolean> map2 = new HashMap<String, Boolean>();
        map2.put("enabled", false);
        serieMix.put("marker", map2);
        serieMix.put("data", valueSetMix);
        result.put("minValue", serieMix);


        result.put("total", listQuery.size());
        result.put("unit", unit);
        result.put("series", series);
        result.put("categories", categories);
        return result;
    }

    private double getData(String vital, String flag) {
        if ("max".equals(flag)) {
            if ("收缩压".equals(vital)) {
                return 140;
            } else if ("舒张压".equals(vital)) {
                return 90;
            } else if ("呼吸".equals(vital)) {
                return 120;
            } else if ("脉搏".equals(vital)) {
                return 120;
            } else if ("体温".equals(vital)) {
                return 37.5;
            }
        } else if ("min".equals(flag)) {
            if ("收缩压".equals(vital)) {
                return 90;
            } else if ("舒张压".equals(vital)) {
                return 60;
            } else if ("呼吸".equals(vital)) {
                return 12;
            } else if ("脉搏".equals(vital)) {
                return 120;
            } else if ("体温".equals(vital)) {
                return 36.1;
            }
        }
        return 0;
    }

//    /**
//     * 折线图-重点检验结果
//     *
//     * @param patientId
//     * @param visitId
//     * @param numIndex
//     * @param beginTime
//     * @param endTime
//     * @return
//     */
//    @Override
//    public Map<String, Object> getFoldLineLabData(String patientId, String visitId, String labSubItemCode, int numIndex, String beginTime, String endTime) {
//        List<PropertyFilter> filters = new ArrayList<PropertyFilter>();
//        filters.add(new PropertyFilter("VISIT_ID", "STRING", MatchType.EQ.getOperation(), visitId));
//        filters.add(new PropertyFilter("LAB_SUB_ITEM_CODE", "STRING", MatchType.EQ.getOperation(), labSubItemCode));
//        if (StringUtils.isNotBlank(beginTime) && StringUtils.isNotBlank(endTime)) {
//            filters.add(new PropertyFilter("REPORT_TIME", "STRING", MatchType.GE.getOperation(), beginTime));
//            filters.add(new PropertyFilter("REPORT_TIME", "STRING", MatchType.LE.getOperation(), endTime));
//        }
//        List<Map<String, String>> listQuery = hbaseDao.findConditionByPatient(HdrTableEnum.HDR_LAB_REPORT_DETAIL.getCode(),"", patientId, filters, "LAB_SUB_ITEM_CODE", "LAB_SUB_ITEM_NAME", "REPORT_TIME", "RANGE", "LAB_RESULT_VALUE", "LAB_RESULT_UNIT", "REPORT_TIME", "LAB_RESULT_VALUE", "LAB_QUAL_RESULT");
//        //时间 降序
//        Utils.sortListByDate(listQuery, "REPORT_TIME", "desc");
//        //近3次，近5次，需要分页
//        ListPage<Map<String, String>> listPage = new ListPage<Map<String, String>>(listQuery, 1, 3);
//        if (3 == numIndex) {
//            listPage = new ListPage<Map<String, String>>(listQuery, 1, 3);
//        } else if (5 == numIndex) {
//            listPage = new ListPage<Map<String, String>>(listQuery, 1, 5);
//        }
//        List<Map<String, String>> pagedList = listPage.getPagedList();
//        //当前页记录再排序，为统计时间坐标准备
//        Utils.sortListByDate(pagedList, "REPORT_TIME", "asc");
//
//        //小项名称
//        String subItemName = null;
//        //单位
//        String subItemUnit = null;
//        //参考值
//        String subItemRange = null;
//        //x坐标
//        List<String> categories = new ArrayList<String>();
//        List<Map<String, Object>> series = new ArrayList<Map<String, Object>>();
//        Map<String, Object> serie = new HashMap<String, Object>();
//        List<Map<String, Object>> valueList = new ArrayList<Map<String, Object>>();
//        //遍历检验结果
//        for (Map<String, String> map : pagedList) {
//            Map<String, Object> value = new HashMap<String, Object>();
//            if (StringUtils.isBlank(subItemName)) {
//                subItemName = map.get("LAB_SUB_ITEM_NAME");
//            }
//            if (StringUtils.isBlank(subItemUnit)) {
//                subItemUnit = map.get("LAB_RESULT_UNIT");
//            }
//            if (StringUtils.isBlank(subItemRange)) {
//                subItemRange = map.get("RANGE");
//            }
//            //y轴  先取定量结果  若没有值 再取定性结果
//            String resultValue = map.get("LAB_RESULT_VALUE");
//            if (StringUtils.isNotBlank(resultValue)) {
//                if (Utils.isNumber(resultValue)) {
//                    value.put("y", Double.parseDouble(resultValue));
//                } else {
//                    resultValue = map.get("LAB_QUAL_RESULT");
//                    if (Utils.isNumber(resultValue)) {
//                        value.put("y", Double.parseDouble(resultValue));
//                    }
//                }
//            } else {
//                //取定性结果
//                resultValue = map.get("LAB_QUAL_RESULT");
//                if (Utils.isNumber(resultValue)) {
//                    value.put("y", Double.parseDouble(resultValue));
//                }
//            }
//            value.put("name", map.get("LAB_ITEM_NAME"));
//            valueList.add(value);
//            categories.add(map.get("REPORT_TIME"));
//        }
//        System.out.println("pppppp:" + subItemRange);
//        //封装数据
//        Map<String, Object> result = new HashMap<String, Object>();
//        //上限值 和 下限值
//        Map<String, Object> range = CivUtils.parseRange(subItemRange);
//        if (null != range.get("max") && null != range.get("min")) {
//            Double max = Double.valueOf((String) range.get("max"));
//            Double min = Double.valueOf((String) range.get("min"));
//
//            result.put("upper", max);
//            result.put("lower", min);
//        }
//        //检验细项名称
//        if (StringUtils.isNotBlank(subItemName)) {
//            result.put("name", subItemName);
//        } else {
//            result.put("name", subItemName);
//        }
//        //检验细项单位
//        result.put("unit", subItemUnit);
//        //y坐标
//        serie.put("name", subItemName);
//        serie.put("data", valueList);
//        series.add(serie);
//        result.put("series", series);
//        //x坐标
//        result.put("categories", categories);
//        return result;
//    }
//
//    /**
//     * 列表数据--护理和饮食
//     * 护理 3
//     * 饮食 a
//     *
//     * @param patientId
//     * @param visitId
//     * @param itemCodes
//     * @return
//     */
//    public Page<Map<String, String>> getHlysListData(String patientId, String visitId, String itemCodes, int pageNo, int pageSize) {
//        List<PropertyFilter> filters = new ArrayList<PropertyFilter>();
//        filters.add(new PropertyFilter("ORDER_CLASS_CODE", "STRING", MatchType.EQ.getOperation(), itemCodes));
////        filters.add(new PropertyFilter("ORDER_ITEM_CODE", "STRING", MatchType.EQ.getOperation(), itemCodes));
//        filters.add(new PropertyFilter("VISIT_ID", "STRING", MatchType.EQ.getOperation(), visitId));
//        List<Map<String, String>> listQuery = hbaseDao.findConditionByPatient(HdrTableEnum.HDR_IN_ORDER.getCode(),"", patientId, filters, "ORDER_CLASS_NAME", "ORDER_CLASS_CODE", "ORDER_ITEM_CODE", "REPORT_TIME", "ORDER_ITEM_NAME", "ORDER_BEGIN_TIME", "ORDER_END_TIME");
//        System.out.println("hdr_in_order:" + listQuery.size());
//        //时间 降序
//        Utils.sortListByDate(listQuery, "REPORT_TIME", "desc");
//        List<Map<String, String>> listRes = new ArrayList<Map<String, String>>();
//        for (Map<String, String> map : listQuery) {
//            Map<String, String> mapTemp = new HashMap<String, String>();
//            mapTemp.put("order_name", map.get("ORDER_ITEM_NAME"));//医嘱项
//            mapTemp.put("end_date", map.get("ORDER_END_TIME"));//结束时间
//            mapTemp.put("begin_date", map.get("ORDER_BEGIN_TIME"));//开始时间
//            listRes.add(mapTemp);
//        }
//        //分页
//        Page<Map<String, String>> listPage = new Page<Map<String, String>>(pageNo, pageSize);
//        listPage.setResult(listRes);
//        listPage.setTotalCount(listRes.size());
//        return listPage;
//    }
//
//    /**
//     * 重点用药列表数据
//     * 针剂药品 f
//     *
//     * @param patientId
//     * @param visitId
//     * @param itemCodes
//     * @param pageNo
//     * @param pageSize
//     * @return
//     */
//    @Override
//    public Page<Map<String, String>> getInorderListData(String patientId, String visitId, String itemCodes, int pageNo, int pageSize) {
//        List<PropertyFilter> filters = new ArrayList<PropertyFilter>();
////        filters.add(new PropertyFilter("ORDER_CLASS_CODE", "STRING", MatchType.EQ.getOperation(), itemCodes));
//        filters.add(new PropertyFilter("ORDER_ITEM_CODE", "STRING", MatchType.EQ.getOperation(), itemCodes));
//        filters.add(new PropertyFilter("VISIT_ID", "STRING", MatchType.EQ.getOperation(), visitId));
//        List<Map<String, String>> listQuery = hbaseDao.findConditionByPatient(HdrTableEnum.HDR_IN_ORDER.getCode(),"", patientId, filters);
//        //时间 降序
//        Utils.sortListByDate(listQuery, "REPORT_TIME", "desc");
//        List<Map<String, String>> listMap = new ArrayList<Map<String, String>>();
//        for (Map<String, String> map : listQuery) {
//            Map<String, String> mapTemp = new HashMap<String, String>();
//            mapTemp.put("name", map.get("ORDER_ITEM_NAME"));
//            mapTemp.put("dose", map.get("DOSAGE_VALUE"));
//            mapTemp.put("unit", map.get("DOSAGE_UNIT"));
//            mapTemp.put("use_type", map.get("ORDER_ITEM_CODE"));//用法
//            mapTemp.put("frequency", map.get("FREQUENCY_DESC"));//频率
//            mapTemp.put("properties", map.get("ORDER_PROPERTIES_NAME"));//医嘱类
//            mapTemp.put("order_doctor", map.get("ORDER_DOCTOR_NAME"));//
//            mapTemp.put("order_time", map.get("ORDER_CONFIRM_TIME"));
//            mapTemp.put("begin_time", map.get("ORDER_BEGIN_TIME"));
//            mapTemp.put("end_time", map.get("ORDER_END_TIME"));
//            mapTemp.put("order_status", map.get("ORDER_STATUS_NAME"));
////            mapTemp.put("view", map.get("ORDER_ITEM_CODE"));
//            listMap.add(mapTemp);
//        }
//        //分页
//        Page<Map<String, String>> listPage = new Page<Map<String, String>>(pageNo, pageSize);
//        listPage.setResult(listMap);
//        listPage.setTotalCount(listMap.size());
//        return listPage;
//    }
//
//    /**
//     * 重点检验卡片式左侧id列表
//     *
//     * @param patientId
//     * @param visitId
//     * @param itemCode
//     * @return
//     */
//    @Override
//    public List<Map<String, String>> getZdjyCardIdList(String patientId, String visitId, String itemCode, String pName) {
//        List<PropertyFilter> filters = new ArrayList<PropertyFilter>();
//        //filters.add(new PropertyFilter("LAB_ITEM_CODE", "STRING",MatchType.IN.getOperation(),itemCode));
//        filters.add(new PropertyFilter("LAB_ITEM_NAME", "STRING", MatchType.INLIKE.getOperation(), pName));
//        filters.add(new PropertyFilter("VISIT_ID", "STRING", MatchType.EQ.getOperation(), visitId));
//        List<Map<String, String>> listQuery = hbaseDao.findConditionByPatient(HdrTableEnum.HDR_LAB_REPORT.getCode(),"", patientId, filters, "LAB_ITEM_NAME", "REPORT_TIME", "REPORT_NO");
//        //时间 降序
//        Utils.sortListByDate(listQuery, "REPORT_TIME", "desc");
//        List<Map<String, String>> list = new ArrayList<Map<String, String>>();
//        for (Map<String, String> map : listQuery) {
//            Map<String, String> mapTemp = new HashMap<String, String>();
//            mapTemp.put("id", map.get("ROWKEY"));
//            mapTemp.put("name", map.get("LAB_ITEM_NAME"));
//            mapTemp.put("report_no", map.get("REPORT_NO"));
//            mapTemp.put("time", map.get("REPORT_TIME"));
//            list.add(mapTemp);
//        }
//        return list;
//    }
//
//    /**
//     * 重点检查卡片式左侧id列表
//     *
//     * @param patientId
//     * @param visitId
//     * @param itemCode
//     * @return
//     */
//    @Override
//    public List<Map<String, String>> getZdjcCardIdList(String patientId, String visitId, String itemCode) {
//        List<PropertyFilter> filters = new ArrayList<PropertyFilter>();
////        filters.add(new PropertyFilter("VISIT_ID", "STRING", MatchType.EQ.getOperation(), visitId));
////        filters.add(new PropertyFilter("VISIT_ID", "STRING", MatchType.EQ.getOperation(), itemCode));
////        List<Map<String, String>> listQuery = hbaseDao.findConditionByPatient(HdrTableEnum.HDR_LAB_REPORT_DETAIL.getCode(), patientId, filters, "rowKey", "LAB_SUB_ITEM_CODE", "LAB_SUB_ITEM_NAME", "REPORT_TIME", "RANGE", "LAB_RESULT_VALUE", "LAB_RESULT_UNIT");
//        filters.add(new PropertyFilter("EXAM_CLASS_CODE", "STRING", MatchType.EQ.getOperation(), itemCode));
//        filters.add(new PropertyFilter("VISIT_ID", "STRING", MatchType.EQ.getOperation(), visitId));
//        List<Map<String, String>> listQuery = hbaseDao.findConditionByPatient(HdrTableEnum.HDR_EXAM_REPORT.getCode(),"", patientId, filters);
//        //时间 降序
//        Utils.sortListByDate(listQuery, "REPORT_TIME", "desc");
//        List<Map<String, String>> resList = new ArrayList<Map<String, String>>();
//        for (Map<String, String> map : listQuery) {
//            Map<String, String> mapTemp = new HashMap<String, String>();
//            mapTemp.put("id", map.get("ROWKEY"));
//            mapTemp.put("name", map.get("EXAM_CLASS_NAME"));
//            mapTemp.put("time", map.get("REPORT_TIME"));
//            resList.add(mapTemp);
//        }
//        return resList;
//    }
//
//
//    /**
//     * 重点检验卡片式详细数据
//     *
//     * @param patientId
//     * @param visitId
//     * @param rowKey
//     * @return
//     */
//    public Map<String, Object> getZdjyCardListDetailData(String patientId, String visitId, String rowKey, String subItemCode) {
//        String table = "HDR_LAB_REPORT";
//        Map<String, String> mapQuery = hbaseDao.getByKey(table, rowKey);
//        Map<String, Object> resQuery = new HashMap<String, Object>();
//        //处理结果
//        resQuery.put("type", mapQuery.get("LAB_TYPE_NAME"));//检验类别
//        resQuery.put("item", mapQuery.get("LAB_ITEM_NAME"));//检验项目
//        resQuery.put("sample_no", mapQuery.get("SAMPLE_NO"));//样本编号
//        resQuery.put("sample_type", mapQuery.get("SPECIMAN_TYPE_NAME"));//采样类型
//        resQuery.put("sample_time", mapQuery.get("SAMPLE_TIME"));//采样时间
//        resQuery.put("lab_performed_time", mapQuery.get("LAB_PERFORMED_TIME"));//检验时间
//        resQuery.put("report_time", mapQuery.get("REPORT_TIME"));//报告时间
//        String reportNo = mapQuery.get("REPORT_NO");
//        List<PropertyFilter> filters = new ArrayList<PropertyFilter>();
////        filters.add(new PropertyFilter("LAB_SUB_ITEM_CODE", "STRING", MatchType.IN.getOperation(), subItemCode));
//        filters.add(new PropertyFilter("REPORT_NO", "STRING", MatchType.EQ.getOperation(), reportNo));
//        filters.add(new PropertyFilter("VISIT_ID", "STRING", MatchType.EQ.getOperation(), visitId));
//        List<Map<String, String>> listquery = hbaseDao.findConditionByPatient(HdrTableEnum.HDR_LAB_REPORT_DETAIL.getCode(),"", patientId, filters);
//        //时间 降序
//        Utils.sortListByDate(listquery, "REPORT_TIME", "desc");
//        List<Map<String, Object>> list = new ArrayList<Map<String, Object>>();
//        for (Map<String, String> map : listquery) {
//            Map<String, Object> mapTemp = new HashMap<String, Object>();
//            mapTemp.put("code", map.get("LAB_SUB_ITEM_CODE"));
//            mapTemp.put("name", map.get("LAB_SUB_ITEM_NAME"));
//            mapTemp.put("result", map.get("LAB_RESULT_VALUE"));
//            mapTemp.put("range", map.get("RANGE"));
//            mapTemp.put("unit", map.get("LAB_RESULT_UNIT"));
//            mapTemp.put("valuestatus", null == map.get("RESULT_STATUS_CODE") ? "N" : map.get("RESULT_STATUS_CODE"));
//            list.add(mapTemp);
//        }
//        resQuery.put("listData", list);
//        return resQuery;
//    }
//
//    /**
//     * 重点检查卡片式详细数据
//     *
//     * @param patientId
//     * @param visitId
//     * @param rowKey
//     * @return
//     */
//    public Map<String, Object> getZdjcCardListDetailData(String patientId, String visitId, String rowKey, String subItemCode) {
//        String table = "HDR_EXAM_REPORT";
//        Map<String, String> mapQuery = hbaseDao.getByKey(HdrTableEnum.HDR_EXAM_REPORT.getCode(), rowKey);
//        Map<String, Object> resQuery = new HashMap<String, Object>();
//        //处理结果
//        resQuery.put("type", mapQuery.get("EXAM_CLASS_NAME"));//检查类别
//        resQuery.put("item", mapQuery.get("EXAM_ITEM_NAME"));//检查项目
//        resQuery.put("order", mapQuery.get("ORDER_NO"));//医嘱
//        resQuery.put("part", mapQuery.get("EXAM_PART"));//检查部位
//        resQuery.put("time", mapQuery.get("REPORT_TIME"));//检查时间
//        resQuery.put("text", mapQuery.get("EXAM_FEATURE"));//检查所见
//        resQuery.put("diag", mapQuery.get("EXAM_DIAG"));//检查诊断
//        resQuery.put("report_time", mapQuery.get("REPORT_TIME"));//报告时间
//        resQuery.put("pacs_url", mapQuery.get("PACS_URL"));//url
//        resQuery.put("report_doctor", mapQuery.get("PERFORM_DOCTOR_NAME"));//报告人
//        String examClassName = mapQuery.get("EXAM_CLASS_NAME");
//        String examNo = mapQuery.get("REPORT_NO");
//        //需要查询检查表数据
//        List<PropertyFilter> filters = new ArrayList<PropertyFilter>();
////        filters.add(new PropertyFilter("EXAM_NO", "STRING", MatchType.EQ.getOperation(), examClassName));
//        filters.add(new PropertyFilter("REPORT_NO", "STRING", MatchType.EQ.getOperation(), examNo));
//        filters.add(new PropertyFilter("VISIT_ID", "STRING", MatchType.EQ.getOperation(), visitId));
//        return resQuery;
//    }
//
//    /**
//     * 生命体征末次数据
//     *
//     * @return
//     */
//    @Override
//    public Map<String, Object> getHeathLastData(String patientId, String visitId, String classCode, String subItemCode) {
//        //封装数据
//        Map<String, Object> result = new HashMap<String, Object>();
//        List<PropertyFilter> filters = new ArrayList<PropertyFilter>();
//        PropertyFilter filter = new PropertyFilter();
//        filter.setPropertyType("STRING");
//        filter.setPropertyValue(subItemCode);
//        filter.setPropertyName("VITAL_TYPE_NAME");
//        filter.setMatchType(MatchType.IN.getOperation());
//        filters.add(filter);
//        List<Map<String, String>> listQuery = hbaseDao.findConditionByPatientVisitId(HdrTableEnum.HDR_VITAL_MEASURE.getCode(),"", patientId, visitId, filters, "VITAL_TYPE_CODE", "VITAL_TYPE_NAME", "MEASURING_TIME", "VITAL_SIGN_VALUE", "VITAL_SIGN2_VALUE", "VITAL_SIGN3_VALUE", "RANGE");
//        //时间 降序
//        Utils.sortListByDate(listQuery, "MEASURING_TIME", "desc");
//        //近3次，近5次，需要分页
//        ListPage<Map<String, String>> listPage = new ListPage<Map<String, String>>(listQuery, 1, 1);
//        Map<String, String> map = new HashMap<String, String>();
//        Map<String, Object> mapRes = new HashMap<String, Object>();
//        if (null != listPage.getPagedList() && listPage.getPagedList().size() > 0) {
//            map = listPage.getPagedList().get(0);
//            String value1 = map.get("VITAL_SIGN_VALUE");
//            String value2 = map.get("VITAL_SIGN2_VALUE");
//            Double valueOne = 0.00;
//            Double valueTwo = 0.00;
//            if (null != value1) {
//                valueOne = Double.valueOf(value1);
//            }
//            if (null != value2) {
//                valueTwo = Double.valueOf(value2);
//            }
//            mapRes.put("time", map.get("MEASURING_TIME"));
//            String data = value1;
//            if (null != value2 && !"".equals(value2)) {
//                data = valueOne + "/" + valueTwo;
//            }
//
//            mapRes.put("data", data);
//            //上限值 和 下限值
//            Map<String, Object> range = CivUtils.parseRange(map.get("RANGE"));
//            mapRes.put("range", range);
//            Double max = 0.00;
//            Double min = 0.00;
//            if (null != range.get("min")) {
//                min = Double.valueOf((String) range.get("min"));
//            }
//            if (null != range.get("max")) {
//                max = Double.valueOf((String) range.get("max"));
//            }
//            if (valueOne > max && max != 0.00) {
//                mapRes.put("valueStatus", "H");
//            } else if (valueOne < min && min != 0.00) {
//                mapRes.put("valueStatus", "L");
//            } else {
//                mapRes.put("valueStatus", "N");
//            }
//        }
//
//        return mapRes;
//    }
//
//    /**
//     * 重点检验结果末次结果--小项
//     *
//     * @param patientId
//     * @param visitId
//     * @param subItemCode
//     * @return
//     */
//    @Override
//    public Map<String, Object> getImportmentLabResLastData(String patientId, String visitId, String classCode, String subItemCode) {
//        List<PropertyFilter> filters = new ArrayList<PropertyFilter>();
//        filters.add(new PropertyFilter("LAB_SUB_ITEM_CODE", "STRING", MatchType.IN.getOperation(), subItemCode));
//        filters.add(new PropertyFilter("VISIT_ID", "STRING", MatchType.EQ.getOperation(), visitId));
//        List<Map<String, String>> listquery = hbaseDao.findConditionByPatient(HdrTableEnum.HDR_LAB_REPORT_DETAIL.getCode(),"", patientId, filters, "LAB_SUB_ITEM_NAME", "REPORT_TIME", "RANGE", "LAB_RESULT_VALUE", "LAB_RESULT_UNIT");
//        //时间 降序
//        Utils.sortListByDate(listquery, "REPORT_TIME", "desc");
//        ListPage<Map<String, String>> listPage = new ListPage<Map<String, String>>(listquery, 1, 1);
//        Map<String, String> map = new HashMap<String, String>();
//        if (null != listPage.getPagedList() && listPage.getPagedList().size() > 0) {
//            map = listPage.getPagedList().get(0);
//        }
//        Map<String, Object> mapRes = new HashMap<String, Object>();
//        String value = map.get("LAB_RESULT_VALUE");
//        mapRes.put("data", value);
//        String unit = map.get("LAB_RESULT_UNIT");
//        mapRes.put("unit", unit);
//        mapRes.put("valueStatus", null == map.get("RESULT_STATUS_CODE") ? "N" : map.get("RESULT_STATUS_CODE"));
//        return mapRes;
//    }
//
//    /**
//     * 护理等级和饮食末次数据
//     *
//     * @param patientId
//     * @param visitId
//     * @param subItemCode
//     * @return
//     */
//    public Map<String, Object> getNurseAndEatLastData(String patientId, String visitId, String classCode, String subItemCode) {
//        List<PropertyFilter> filters = new ArrayList<PropertyFilter>();
//        filters.add(new PropertyFilter("ORDER_CLASS_CODE", "STRING", MatchType.IN.getOperation(), classCode));
////        filters.add(new PropertyFilter("ORDER_ITEM_CODE", "STRING", MatchType.IN.getOperation(), subItemCode));
//        filters.add(new PropertyFilter("VISIT_ID", "STRING", MatchType.IN.getOperation(), visitId));
//        List<Map<String, String>> listquery = hbaseDao.findConditionByPatient(HdrTableEnum.HDR_IN_ORDER.getCode(),"", patientId, filters, "ORDER_TIME", "ORDER_ITEM_NAME");
//        //时间 降序
//        Utils.sortListByDate(listquery, "ORDER_TIME", "desc");
//        ListPage<Map<String, String>> listPage = new ListPage<Map<String, String>>(listquery, 1, 1);
//        Map<String, String> map = new HashMap<String, String>();
//        if (null != listPage.getPagedList() && listPage.getPagedList().size() > 0) {
//            map = listPage.getPagedList().get(0);
//        }
//        Map<String, Object> mapRes = new HashMap<String, Object>();
//        mapRes.put("time", map.get("ORDER_TIME"));
//        mapRes.put("valueStatus", "N");
//        mapRes.put("data", map.get("ORDER_ITEM_NAME"));
//
//        return mapRes;
//    }
//
//    /**
//     * 重点检查报告末次数据
//     *
//     * @param patientId
//     * @param visitId
//     * @param subItemCode
//     * @return
//     */
//    public Map<String, Object> getExamLastData(String patientId, String visitId, String classCode, String subItemCode) {
//        List<PropertyFilter> filters = new ArrayList<PropertyFilter>();
////        HbaseUtils.createPropertyFilter("EXAM_ITEM_NAME", subItemCode, MatchType.EQ.getOperation(), filters);
//        filters.add(new PropertyFilter("EXAM_CLASS_CODE", "STRING", MatchType.IN.getOperation(), classCode));
////        filters.add(new PropertyFilter("EXAM_ITEM_CODE", "STRING", MatchType.IN.getOperation(),subItemCode));
//        filters.add(new PropertyFilter("VISIT_ID", "STRING", MatchType.EQ.getOperation(), visitId));
//        List<Map<String, String>> listQuery = hbaseDao.findConditionByPatient(HdrTableEnum.HDR_EXAM_REPORT.getCode(),"", patientId, filters, "EXAM_DIAG", "REPORT_TIME");
//        //时间 降序
//        Utils.sortListByDate(listQuery, "REPORT_TIME", "desc");
//        ListPage<Map<String, String>> listPage = new ListPage<Map<String, String>>(listQuery, 1, 1);
//        Map<String, String> map = new HashMap<String, String>();
//        Map<String, Object> mapRes = new HashMap<String, Object>();
//        if (null != listPage.getPagedList() && listPage.getPagedList().size() > 0) {
//            map = listPage.getPagedList().get(0);
//        }
//        mapRes.put("time", map.get("REPORT_TIME"));
//        mapRes.put("valueStatus", "N");
//        mapRes.put("data", map.get("EXAM_DIAG"));
//        return mapRes;
//    }
//
//    /**
//     * 重点用药末次数据
//     *
//     * @param patientId
//     * @param visitId
//     * @param subItemCode
//     * @return
//     */
//    public Map<String, Object> getDragLastData(String patientId, String visitId, String classCode, String subItemCode) {
//        List<PropertyFilter> filters = new ArrayList<PropertyFilter>();
//        filters.add(new PropertyFilter("ORDER_CLASS_CODE", "STRING", MatchType.EQ.getOperation(), classCode));
//        filters.add(new PropertyFilter("ORDER_ITEM_CODE", "STRING", MatchType.EQ.getOperation(), subItemCode));
//        filters.add(new PropertyFilter("VISIT_ID", "STRING", MatchType.EQ.getOperation(), visitId));
//        List<Map<String, String>> listQuery = hbaseDao.findConditionByPatient(HdrTableEnum.HDR_IN_ORDER.getCode(),"", patientId, filters,  "ORDER_TIME", "ORDER_ITEM_NAME");
//        //时间 降序
//        Utils.sortListByDate(listQuery, "ORDER_TIME", "desc");
//        ListPage<Map<String, String>> listPage = new ListPage<Map<String, String>>(listQuery, 1, 1);
//        Map<String, String> map = new HashMap<String, String>();
//        Map<String, Object> mapRes = new HashMap<String, Object>();
//        if (null != listPage.getPagedList() && listPage.getPagedList().size() > 0) {
//            map = listPage.getPagedList().get(0);
//            mapRes.put("time", map.get("ORDER_TIME"));
//            mapRes.put("data", map.get("ORDER_ITEM_NAME"));
//            mapRes.put("valueStatus", map.get("N"));
//        } else {
//            mapRes.put("time", "-");
//        }
//
//        return mapRes;
//    }
//
//    /**
//     * 重点检验末次数据--大项
//     *
//     * @param patientId
//     * @param visitId
//     * @param
//     * @return
//     */
//    public Map<String, Object> getLabLastData(String patientId, String visitId, String classCode, String className) {
//        List<PropertyFilter> filters = new ArrayList<PropertyFilter>();
////        PropertyFilter filter = new PropertyFilter("LAB_ITEM_CODE", "STRING", MatchType.IN.getOperation(), classCode);
//        PropertyFilter filter = new PropertyFilter("LAB_ITEM_NAME", "STRING", MatchType.INLIKE.getOperation(), className);
//        PropertyFilter filterVId = new PropertyFilter("VISIT_ID", "STRING", MatchType.EQ.getOperation(), visitId);
//        filters.add(filter);
//        filters.add(filterVId);
//        List<Map<String, String>> listQuery = hbaseDao.findConditionByPatient(HdrTableEnum.HDR_LAB_REPORT_DETAIL.getCode(),"", patientId, filters,  "LAB_ITEM_NAME", "REPORT_TIME", "ORDER_ITEM_NAME");
//        //时间 降序
//        Utils.sortListByDate(listQuery, "REPORT_TIME", "desc");
//        ListPage<Map<String, String>> listPage = new ListPage<Map<String, String>>(listQuery, 1, 1);
//        Map<String, String> map = new HashMap<String, String>();
//        Map<String, Object> mapRes = new HashMap<String, Object>();
//        if (null != listPage.getPagedList() && listPage.getPagedList().size() > 0) {
//            map = listPage.getPagedList().get(0);
//            mapRes.put("time", map.get("REPORT_TIME"));
//            mapRes.put("data", map.get("ORDER_ITEM_NAME"));
////            mapRes.put("data", map.get(" "));
//            mapRes.put("valueStatus", null == map.get("RESULT_STATUS_CODE") ? "N" : map.get("RESULT_STATUS_CODE"));
//        } else {
//            mapRes.put("time", "-");
//        }
//        return mapRes;
//    }
//
//
//    public void getDataByRowley() {
//        Map<String, String> map = hbaseDao.getByKey(HdrTableEnum.HDR_LAB_REPORT_DETAIL.getCode(), "0000|1.2.156.112636|001213000000||2016-05-17 00:00:00_106_339_1&1010101|1.2.156.112636.1.2.12",  "LAB_ITEM_NAME", "REPORT_TIME");
//        for (Map.Entry entry : map.entrySet()) {
//            System.out.println(entry.getKey());
//            System.out.println(entry.getValue());
//        }
//
//    }
//

    /**
     * 获取科室设置科室列表
     *
     * @param
     * @param pageNo
     * @param pageSize
     * @return
     */
    @Override
    public Page<SpecialtyDept> getDeptList(String dept, int pageNo, int pageSize) {
        //查询总量
        List<SpecialtyDept> listDeptAll = specialtyDeptMapper.getDeptList(dept);
//        List<SpecialtyDept> listDeptAll = sprcialtyViewNewDao.getDeptList(dept, 0, 0);
        //分页
        ListPage<SpecialtyDept> list = new ListPage<SpecialtyDept>(listDeptAll, pageNo, pageSize);
        //总数
        int total = listDeptAll.size();
        List<SpecialtyDept> listDept = list.getPagedList();
        Page<SpecialtyDept> page = new Page<SpecialtyDept>();
        page.setPageNo(pageNo);
        page.setPageSize(pageSize);
        page.setResult(listDept);
        page.setTotalCount(total);
        return page;
    }

    /**
     * 查询科室以供管理员添加配置
     *
     * @param keyWord
     * @param pageNo
     * @param pageSize
     * @return
     */
    @Override
    public Page<Map<String, String>> getDeptListToAdd(String keyWord, int pageNo, int pageSize) {
        //查询医院所有科室
        QueryWrapper<SecurityCommonDept> securityDeptwrapper = new QueryWrapper<>();
        securityDeptwrapper.eq("enabled", "1");
        if (StringUtils.isNotBlank(keyWord)) {
            securityDeptwrapper.like("deptname", keyWord).or().like("deptcode", keyWord);
        }
        if (pageNo > 0 && pageSize > 0) {
            String sql = "  limit " + (pageNo - 1) * pageSize + "," + pageSize;
            securityDeptwrapper.last(sql);
        }
        List<SecurityCommonDept> securityCommonDeptList = securityCommonDeptMapper.selectList(securityDeptwrapper);
        List<Map<String, String>> listDepts = new ArrayList<>();
        for (SecurityCommonDept securityCommonDept : securityCommonDeptList) {
            Map<String, String> map = new HashMap<String, String>();
            map.put("code", securityCommonDept.getDeptcode());
            map.put("name", securityCommonDept.getDeptname());
            listDepts.add(map);
        }
//        List<Map<String, String>> listDepts = sprcialtyViewNewDao.getDeptListToAdd(keyWord, pageNo, pageSize);
        //查询已经配置的科室
        List<SpecialtyDept> listConfDept = specialtyDeptMapper.selectList(new QueryWrapper<SpecialtyDept>().eq("is_inuse", "Y"));
//        List<SpecialtyDept> listConfDept = sprcialtyViewNewDao.getDeptList("", 0, 0);
        //所有科室打标记是否 配置
        for (Map<String, String> map : listDepts) {
            //未配置
            map.put("has_template", "未有模板");
            //未添加
            map.put("status", "0");
            String deptCode = map.get("code");
            for (SpecialtyDept entity : listConfDept) {
                if (StringUtils.isNotBlank(deptCode) && deptCode.equals(entity.getDeptCode())) {
                    //已配置
                    map.put("has_template", "已有模板");
                    map.put("status", "1");
                    break;
                }
            }
        }
        Page<Map<String, String>> page = new Page<Map<String, String>>();
        page.setPageNo(pageNo);
        page.setPageSize(pageSize);
        page.setResult(listDepts);
        return page;
    }

}
