package com.goodwill.hdr.civ.config;

import com.goodwill.hdr.civ.enums.DictType;
import com.goodwill.hdr.civ.utils.Utils;
import org.apache.commons.lang.StringUtils;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @Description 类描述：
 * @modify 修改记录：
 */
public class DictConfigs {

//	/**
//	 * 字典表名称
//	 */
//	private static final String DICTTABLENAME =  Utils.objToStr(ConfigCache.getCache(oid, "DICTTABLENAME"),"HDR_DICT_ITEM");
//	private static final String DTLABSUB_TEMP =  Utils.objToStr(ConfigCache.getCache(oid,"DTLABSUB_TEMP"),"TestItem");
//
//	// 字典查询条件
//	private static final String Dict_EXAMTYPE =  Utils.objToStr(ConfigCache.getCache(oid,"EXAM_TYPE"),"orderType:1,2,19");
//	private static final String Dict_MZ_EXAMTYPE =  Utils.objToStr(ConfigCache.getCache(oid,"MZ_EXAM_TYPE"),"deletedFlag:0");
//	private static final String Dict_LABTYPE =  Utils.objToStr(ConfigCache.getCache(oid,"LAB_TYPE"),"orderType:0,3,20");
//	private static final String Dict_MZ_LABTYPE = Utils.objToStr(ConfigCache.getCache(oid,"MZ_LAB_TYPE"),"deletedFlag:0");
//	private static final String Dict_LABSUBTYPE =  Utils.objToStr(ConfigCache.getCache(oid,"LABSUB_TYPE"),"");
//	private static final String Dict_DRUGTYPE =  Utils.objToStr(ConfigCache.getCache(oid,"DRUG_TYPE"),"serial:00");
//	private static final String Dict_OPERTYPE =  Utils.objToStr(ConfigCache.getCache(oid,"OPER_TYPE"),"orderType:17,9");
//	private static final String Dict_OPERICD9TYPE = Utils.objToStr(ConfigCache.getCache(oid,"OPERICD9_TYPE"),"");
//	private static final String Dict_DIAGTYPE =  Utils.objToStr(ConfigCache.getCache(oid,"DIAG_TYPE"),"");
//	private static final String Dict_NURSETYPE =  Utils.objToStr(ConfigCache.getCache(oid,"NURSE_TYPE"),"orderType:8");
//	private static final String Dict_EXAMCLASSTYPE =  Utils.objToStr(ConfigCache.getCache(oid,"EXAM_CLASS_TYPE"),"");
//	private static final String Dict_DEPTTYPE =  Utils.objToStr(ConfigCache.getCache(oid,"DEPT_TYPE"),"");
//
//	//字典类型(字典ROWKEY前缀)
//	private static final String Dict_DRUGCODE =  Utils.objToStr(ConfigCache.getCache(oid,"DRUG_CODE"),"1.2.156.112636.1.1.2.1.4.12");
//	private static final String Dict_EXAMCODE =  Utils.objToStr(ConfigCache.getCache(oid,"EXAM_CODE"),"1.2.156.112636.1.1.2.1.4.13");
//	private static final String Dict_MZ_EXAMCODE =  Utils.objToStr(ConfigCache.getCache(oid,"MZ_EXAM_CODE"),"1.2.156.112636.1.1.2.1.4.11");
//	private static final String Dict_LABCODE =  Utils.objToStr(ConfigCache.getCache(oid,"LAB_CODE"),"1.2.156.112636.1.1.2.1.4.13");
//	private static final String Dict_MZ_LABCODE = Utils.objToStr(ConfigCache.getCache(oid,"MZ_LAB_CODE"),"1.2.156.112636.1.1.2.1.4.9");
//	private static final String Dict_LABSUBCODE =  Utils.objToStr(ConfigCache.getCache(oid,"LABSUB_CODE"),"1.2.156.112636.1.2.12.1");
//	private static final String Dict_OPERCODE = Utils.objToStr(ConfigCache.getCache(oid,"OPER_CODE"),"1.2.156.112636.1.1.2.1.4.13");
//	private static final String Dict_OPERICD9CODE =  Utils.objToStr(ConfigCache.getCache(oid,"OPERICD9_CODE"),"1.2.156.112636.1.1.2.1.1.3");
//	private static final String Dict_DIAGCODE =  Utils.objToStr(ConfigCache.getCache(oid,"DIAG_CODE"),"1.2.156.112636.1.1.2.1.1.2");
//	private static final String Dict_NURSECODE =  Utils.objToStr(ConfigCache.getCache(oid,"NURSE_CODE"),"1.2.156.112636.1.1.2.1.4.13");
//	private static final String Dict_EXAMCLASSCODE =  Utils.objToStr(ConfigCache.getCache(oid,"EXAM_CLASS_CODE"),"examClassCode");
//	private static final String Dict_DEPTCODE =  Utils.objToStr(ConfigCache.getCache(oid,"DEPT_CODE"),"deptCode");

	/**
	 * 从配置文件中获取字典表名称
	 * @return
	 */
	public static String getDictTableNameString(String oid) {
		return Utils.objToStr(ConfigCache.getCache(oid, "DICTTABLENAME"),"HDR_DICT_ITEM");
	}

	/**
	 * 获取临时配置的检验明细项字典表名称
	 * @return
	 */
	public static String getDTLABSUB_TEMP(String oid) {
		return Utils.objToStr(ConfigCache.getCache(oid,"DTLABSUB_TEMP"),"TestItem");
	}

	/**
	 * 获取字典类型
	 * @param type DRUG,EXAM,LAB,OPER,OPERICD9,DIAG
	 * @return
	 */
	public static String getDictCode(String oid, DictType type) {
		String configString = "";

		switch (type) {
		case DRUG:
			configString = Utils.objToStr(ConfigCache.getCache(oid,"DRUG_CODE"),"1.2.156.112636.1.1.2.1.4.12");
			break;
		case EXAM:
			configString = Utils.objToStr(ConfigCache.getCache(oid,"EXAM_CODE"),"1.2.156.112636.1.1.2.1.4.13");
			break;
		case EXAM_MZ:
			configString = Utils.objToStr(ConfigCache.getCache(oid,"MZ_EXAM_CODE"),"1.2.156.112636.1.1.2.1.4.11");
			break;
		case LAB:
			configString = Utils.objToStr(ConfigCache.getCache(oid,"LAB_CODE"),"1.2.156.112636.1.1.2.1.4.13");
			break;
		case LAB_MZ:
			configString =  Utils.objToStr(ConfigCache.getCache(oid,"MZ_LAB_CODE"),"1.2.156.112636.1.1.2.1.4.9");
			break;
		case LABSUB:
			configString = Utils.objToStr(ConfigCache.getCache(oid,"LABSUB_CODE"),"1.2.156.112636.1.2.12.1");
			break;
		case OPER:
			configString = Utils.objToStr(ConfigCache.getCache(oid,"OPER_CODE"),"1.2.156.112636.1.1.2.1.4.13");
			break;
		case OPERICD9:
			configString = Utils.objToStr(ConfigCache.getCache(oid,"OPERICD9_CODE"),"1.2.156.112636.1.1.2.1.1.3");
			break;
		case DIAG:
			configString = Utils.objToStr(ConfigCache.getCache(oid,"DIAG_CODE"),"1.2.156.112636.1.1.2.1.1.2");
			break;
		case NURSE:
			configString = Utils.objToStr(ConfigCache.getCache(oid,"NURSE_CODE"),"1.2.156.112636.1.1.2.1.4.13");
			break;
		case EXAMCLASS:
			configString = Utils.objToStr(ConfigCache.getCache(oid,"EXAM_CLASS_CODE"),"examClassCode");
			break;
		case DEPT:
			configString =  Utils.objToStr(ConfigCache.getCache(oid,"DEPT_CODE"),"deptCode");
			break;
		default:
			break;
		}
		return configString;
	}

	/**
	 * 获取字典查询条件
	 * @param type DRUG,EXAM,LAB,OPER,OPERICD9,DIAG
	 * @return
	 */
	public static Map<String, String> getQueryType(String oid, DictType type) {
		Map<String, String> map = new HashMap<String, String>();
		String configString = "";
		switch (type) {
		case DRUG:
			configString = Utils.objToStr(ConfigCache.getCache(oid,"DRUG_TYPE"),"serial:00");
			break;
		case EXAM:
			configString = Utils.objToStr(ConfigCache.getCache(oid,"EXAM_TYPE"),"orderType:1,2,19");
			break;
		case EXAM_MZ:
			configString = Utils.objToStr(ConfigCache.getCache(oid,"MZ_EXAM_TYPE"),"deletedFlag:0");
			break;
		case LAB:
			configString = Utils.objToStr(ConfigCache.getCache(oid,"LAB_TYPE"),"orderType:0,3,20");
			break;
		case LAB_MZ:
			configString =  Utils.objToStr(ConfigCache.getCache(oid,"MZ_LAB_TYPE"),"deletedFlag:0");
			break;
		case LABSUB:
			configString = Utils.objToStr(ConfigCache.getCache(oid,"LABSUB_TYPE"),"");
			break;
		case OPER:
			configString = Utils.objToStr(ConfigCache.getCache(oid,"OPER_TYPE"),"orderType:17,9");
			break;
		case OPERICD9:
			configString = Utils.objToStr(ConfigCache.getCache(oid,"OPERICD9_TYPE"),"");
			break;
		case DIAG:
			configString = Utils.objToStr(ConfigCache.getCache(oid,"DIAG_TYPE"),"");
			break;
		case NURSE:
			configString = Utils.objToStr(ConfigCache.getCache(oid,"NURSE_TYPE"),"orderType:8");
			break;
		case EXAMCLASS:
			configString = Utils.objToStr(ConfigCache.getCache(oid,"EXAM_CLASS_TYPE"),"");
			break;
		case DEPT:
			configString = Utils.objToStr(ConfigCache.getCache(oid,"DEPT_TYPE"),"");
			break;
		default:
			break;
		}
		if (StringUtils.isBlank(configString)) {
			return map;
		}
		//分类以";"分隔，分类内字段名称和值以":"分隔，值之间以","分隔。
		//example:typ1:val1,val2;type2:val3;type3:val4
		String[] typesStrings = configString.split(";");
		for (String typeString : typesStrings) {
			String[] value = typeString.split(":");
			String keyString = value.length >= 1 ? value[0] : "";
			String valueString = value.length >= 2 ? value[1] : "";
			map.put(keyString, valueString);
		}
		return map;
	}

}
