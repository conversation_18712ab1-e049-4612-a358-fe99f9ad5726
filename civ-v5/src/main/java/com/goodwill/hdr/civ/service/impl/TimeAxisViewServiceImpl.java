package com.goodwill.hdr.civ.service.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;

import com.goodwill.hdr.civ.config.Config;
import com.goodwill.hdr.civ.entity.TimeaxisConfig;
import com.goodwill.hdr.civ.enums.HdrTableEnum;
import com.goodwill.hdr.civ.mapper.TimeaxisConfigMapper;
import com.goodwill.hdr.civ.service.SpecialtyViewPowerService;
import com.goodwill.hdr.civ.service.TimeAxisViewService;
import com.goodwill.hdr.civ.utils.CivUtils;
import com.goodwill.hdr.civ.utils.Utils;
import com.goodwill.hdr.civ.vo.SolrVo;
import com.goodwill.hdr.core.orm.MatchType;
import com.goodwill.hdr.core.orm.Page;
import com.goodwill.hdr.hbase.bo.PropertyFilter;
import com.goodwill.hdr.hbase.dto.responseVo.PageResultVo;
import com.goodwill.hdr.hbase.dto.responseVo.ResultVo;
import com.goodwill.hdr.hbaseQueryClient.builder.PageRequestBuilder;
import com.goodwill.hdr.hbaseQueryClient.service.HbaseQueryClient;
import com.goodwill.hdr.rest.client.enums.JhdcpServerCode;
import com.goodwill.hdr.rest.client.transmission.JhdcpHttpSender;
import com.goodwill.hdr.rest.client.wrapper.imp.JhdcpQueryWrapperImp;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.text.SimpleDateFormat;
import java.time.Duration;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;

@Service
public class TimeAxisViewServiceImpl implements TimeAxisViewService {

    private static final Logger log = LoggerFactory.getLogger(TimeAxisViewServiceImpl.class);


    @Autowired
    private TimeaxisConfigMapper timeaxisConfigMapper;

    @Autowired
    private SpecialtyViewPowerService specialtyViewPowerService;

    @Autowired
    private JhdcpHttpSender jhdcpHttpSender;
    private final HbaseQueryClient hbaseQueryClient;

    public TimeAxisViewServiceImpl(HbaseQueryClient hbaseQueryClient) {
        this.hbaseQueryClient = hbaseQueryClient;
    }

    @Override
    public Page<Map<String, String>> getVisitDeptList(String oid, String patientId, String idCardNo, String deptName, int pageSize, int pageNo) {
        List<Map<String, String>> rs = new ArrayList<Map<String, String>>();

        JhdcpQueryWrapperImp queryWrapperInVisit = new JhdcpQueryWrapperImp(JhdcpServerCode.ALL_VISIT.getCode(), pageNo, pageSize);

        if (StringUtils.isNotBlank(idCardNo)) {
            queryWrapperInVisit.eq("ID_CARD_NO", idCardNo);
        } else {
            queryWrapperInVisit.eq("HIS_PAT_ID",patientId);
        }
        queryWrapperInVisit.eq("SD_VISIT_TYPE_CODE","02");
        if (StringUtils.isNotBlank(deptName)) {
            queryWrapperInVisit.likeRight("SC_ADM_DEPT", deptName);
        }
        queryWrapperInVisit.descOrder("ADMISSION_TIME");
        String dataPageJson = jhdcpHttpSender.getDataPageJson(queryWrapperInVisit);
        SolrVo solrVo = JSON.parseObject(dataPageJson, SolrVo.class);
        List<Map<String, String>> list = solrVo.getData();

        List<Map<String, String>> patAdts = new ArrayList<Map<String, String>>();
        int currIdx = (pageNo > 1 ? (pageNo - 1) * pageSize : 0);
        for (int i = 0; i < pageSize && i < list.size() - currIdx; i++) {
            Map<String, String> map = list.get(currIdx + i);
            patAdts.add(map);
        }

        for (Map<String, String> map : patAdts) {
            Map<String, String> visit = new HashMap<String, String>();
            visit.put("deptName", map.get("ST_ADM_DEPT") == null ? "-" : map.get("ST_ADM_DEPT"));
            visit.put("deptCode", map.get("SC_ADM_DEPT") == null ? "-" : map.get("SC_ADM_DEPT"));
            visit.put("time", map.get("ADMISSION_TIME") == null ? "-" : map.get("ADMISSION_TIME"));
            visit.put("patientId", map.get("HIS_PAT_ID") == null ? "-" : map.get("HIS_PAT_ID"));
            visit.put("visitId", map.get("VISIT_NUM") == null ? "-" : map.get("VISIT_NUM"));
            visit.put("visitTypeCode", "02");
            visit.put("oid", map.get("ORG_CODE") == null ? "-" : map.get("ORG_CODE"));
            visit.put("orgName", map.get("ORG_NAME") == null ? "-" : map.get("ORG_NAME"));
            rs.add(visit);
        }

        Page<Map<String, String>> page = new Page<>(pageNo, pageSize);
        page.setResult(rs);
        page.setTotalCount(list.size());

        return page;
    }

    @Override
    public List<Map<String, String>> getVisitTimeList(String oid, String patientId,
                                                      String visitId) {
        List<Map<String, String>> rs = new ArrayList<Map<String, String>>();

        List<PropertyFilter> filters = new ArrayList<PropertyFilter>();
        //filters.add(new PropertyFilter("VISIT_ID", "STRING", MatchType.EQ.getOperation(), visitId)); //仅取入出院，不取转科
        filters.add(new PropertyFilter("TRANS_NO", MatchType.EQ.getOperation(), "0"));

        List<Map<String, String>> patAdts = new ArrayList<>();
        ResultVo<PageResultVo<Map<String, String>>> resultVo = hbaseQueryClient.getPageByCondition(
                PageRequestBuilder.init()
                        .tableName(HdrTableEnum.HDR_PAT_ADT.getCode())
                        .patientId(patientId)
                        .oid(oid)
                        .visitId(visitId)
                        .visitTypeCode("")
                        .filters(filters)
                        .pageNo(0)
                        .pageSize(0)
                        .orderBy("")
                        .desc()
                        .column("ADMISSION_TIME", "DISCHARGE_TIME", "VISIT_ID", "DEPT_ADMISSION_TO_NAME",
                                "DEPT_ADMISSION_TO_CODE")
                        .build());
        if(resultVo.isSuccess()){
            patAdts = resultVo.getContent().getResult();
        }
//        List<Map<String, String>> patAdts = hbaseDao.findConditionByPatient(HdrTableEnum.HDR_PAT_ADT.getCode(), oid,
//                patientId, filters, new String[]{"ADMISSION_TIME", "DISCHARGE_TIME", "VISIT_ID", "DEPT_ADMISSION_TO_NAME",
//                        "DEPT_ADMISSION_TO_CODE"});
        if (patAdts.size() == 0) {
            return rs;
        }
        int weeks = -1;
        DateTimeFormatter dateTimeFormatter=DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        LocalDateTime intime = LocalDateTime.parse(patAdts.get(0).get("ADMISSION_TIME"),dateTimeFormatter);

        LocalDateTime outtime = null;
        if (StringUtils.isNotBlank(patAdts.get(0).get("DISCHARGE_TIME"))) {
            outtime = LocalDateTime.parse(patAdts.get(0).get("DISCHARGE_TIME"),dateTimeFormatter);
        } else {
            outtime = LocalDateTime.now();
        }


        long allDay = Duration.between(intime,outtime).toDays();

        float num = (float) allDay / 7;
        weeks = (int) Math.ceil(num);
        List<String> list = Config.getWeekTypes(oid);

        for (int i = 0; i < weeks; i++) {
            if (i == 10) {
                return rs;
            }
            Map<String, String> week = new HashMap<String, String>();
            week.put("name", list.get(i));
            week.put("num", (i + 1) + "");
            week.put("time", intime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
            week.put("cols", "7");
            rs.add(week);
        }
        Map<String, String> all = new HashMap<String, String>();
        all.put("name", "全部");
        all.put("num", "all");
        all.put("time", intime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
        if (allDay < 7)
            all.put("cols", "7");
        else
            all.put("cols", allDay + "");
        rs.add(all);
        return rs;
    }

    @Override
    public List<Map<String, Object>> getVisitTimeTpList(String oid, String patientId,
                                                        String visitId, String admissionTime, int week, int cols) {
        // TODO Auto-generated method stub
        List<Map<String, Object>> rs = new ArrayList<Map<String, Object>>();

        String dischargetime = "";
        List<PropertyFilter> filters = new ArrayList<PropertyFilter>();
        filters.add(new PropertyFilter("TRANS_NO", MatchType.EQ.getOperation(), "0"));
        //filters.add(new PropertyFilter("VISIT_ID", "STRING", MatchType.EQ.getOperation(), visitId));
        List<Map<String, String>> list = new ArrayList<>();
        ResultVo<PageResultVo<Map<String, String>>> resultVo = hbaseQueryClient.getPageByCondition(
                PageRequestBuilder.init()
                        .tableName(HdrTableEnum.HDR_PAT_ADT.getCode())
                        .patientId(patientId)
                        .oid(oid)
                        .visitId(visitId)
                        .visitTypeCode("")
                        .filters(filters)
                        .pageNo(0)
                        .pageSize(0)
                        .orderBy("")
                        .desc()
                        .column("ADMISSION_TIME", "DISCHARGE_TIME", "VISIT_ID", "DEPT_ADMISSION_TO_NAME",
                                "DEPT_ADMISSION_TO_CODE")
                        .build());
        if(resultVo.isSuccess()){
            list = resultVo.getContent().getResult();
        }
//        List<Map<String, String>> list = hbaseDao.findConditionByPatient(HdrTableEnum.HDR_PAT_ADT.getCode(), oid,
//                patientId, filters, new String[]{"ADMISSION_TIME", "DISCHARGE_TIME", "VISIT_ID", "DEPT_ADMISSION_TO_NAME",
//                        "DEPT_ADMISSION_TO_CODE"});
        SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd");
        if (list.size() > 0) {
            if (StringUtils.isNotBlank(list.get(0).get("DISCHARGE_TIME")))
                dischargetime = df.format(Utils.strToDate(list.get(0).get("DISCHARGE_TIME"), "yyyy-MM-dd"));
            else
                dischargetime = df.format(new Date());
        }

        getTpDateAndInDayInfo(admissionTime, dischargetime, week, cols, rs);
        //手术后天数
        getTpDateOperDayInfo(oid, patientId, visitId, admissionTime, dischargetime, week, cols, rs);
        //优化使用原来的方法
//	    getVisitTimeOperList(patientId,admissionTime,week,cols,rs);
        //呼吸
        getTpBreathAndBloodInfo(oid, patientId, visitId, admissionTime, week, cols, rs);
        //dabian入量 niaoliang
        getTpOutSTInfo(oid, patientId, visitId, admissionTime, week, cols, rs);
        //排序
        Utils.sortListByDate(rs, "order", Page.Sort.ASC);

        return rs;
    }

    @Override
    public List<Map<String, Object>> getVisitOperTimeList(String oid, String patientId,
                                                          String visitId, String admissionTime, int week, int cols) {
        // TODO Auto-generated method stub
        List<Map<String, Object>> rs = new ArrayList<Map<String, Object>>();
        //手术后天数
//		getTpDateOperDayInfo(patientId,visitId,admissionTime,dischargetime,week,cols,rs);
        //优化使用原来的方法
        getVisitTimeOperList(oid, patientId,visitId, admissionTime, week, cols, rs);
        //排序
        Utils.sortListByDate(rs, "order", Page.Sort.ASC);
        return rs;
    }

    private void getTpDateOperDayInfo(String oid, String patientId, String visitId, String admissionTime, String dischargetime, int week, int cols,
                                      List<Map<String, Object>> rs) {
        // TODO Auto-generated method stub
        Map<String, Object> operAfter = new HashMap<String, Object>();
        operAfter.put("id", "operAfter");
        operAfter.put("name", "术后/分娩后天数");
        operAfter.put("order", "2");
        //获取住院时间
        List<PropertyFilter> filters = new ArrayList<PropertyFilter>();
        filters.add(new PropertyFilter("TRANS_NO", MatchType.EQ.getOperation(), "0")); //仅取入出院，不取转科
        //filters.add(new PropertyFilter("VISIT_ID", "STRING", MatchType.EQ.getOperation(), visitId)); //仅取入出院，不取转科
        List<Map<String, String>> patAdts = new ArrayList<>();
        ResultVo<PageResultVo<Map<String, String>>> resultVo = hbaseQueryClient.getPageByCondition(
                PageRequestBuilder.init()
                        .tableName(HdrTableEnum.HDR_PAT_ADT.getCode())
                        .patientId(patientId)
                        .oid(oid)
                        .visitId(visitId)
                        .visitTypeCode("")
                        .filters(filters)
                        .pageNo(0)
                        .pageSize(0)
                        .orderBy("")
                        .desc()
                        .column("ADMISSION_TIME", "DISCHARGE_TIME", "VISIT_ID", "DEPT_ADMISSION_TO_NAME",
                                "DEPT_ADMISSION_TO_CODE")
                        .build());
        if(resultVo.isSuccess()){
            patAdts = resultVo.getContent().getResult();
        }
//        List<Map<String, String>> patAdts = hbaseDao.findConditionByPatient(HdrTableEnum.HDR_PAT_ADT.getCode(), oid,
//                patientId, filters, new String[]{"ADMISSION_TIME", "DISCHARGE_TIME", "VISIT_ID", "DEPT_ADMISSION_TO_NAME",
//                        "DEPT_ADMISSION_TO_CODE"});
        if (patAdts.size() == 0) {
            rs.add(operAfter);
        }
        //获取手术日期
        List<String> operDate = new ArrayList<String>();
        filters = new ArrayList<PropertyFilter>();
        if (StringUtils.isNotBlank(patAdts.get(0).get("ADMISSION_TIME"))) {
            filters.add(new PropertyFilter("OPER_START_TIME", MatchType.GE.getOperation(), patAdts.get(0).get("ADMISSION_TIME")));
        }
        if (StringUtils.isNotBlank(patAdts.get(0).get("DISCHARGE_TIME"))) {
            filters.add(new PropertyFilter("OPER_START_TIME", MatchType.LE.getOperation(), patAdts.get(0).get("DISCHARGE_TIME")));
        }

        List<Map<String, String>> list = new ArrayList<>();
        ResultVo<PageResultVo<Map<String, String>>> resultVo1 = hbaseQueryClient.getPageByCondition(
                PageRequestBuilder.init()
                        .tableName(HdrTableEnum.HDR_OPER_ANAES.getCode())
                        .patientId(patientId)
                        .oid(oid)
                        .visitId(visitId)
                        .visitTypeCode("")
                        .filters(filters)
                        .pageNo(0)
                        .pageSize(0)
                        .orderBy("OPER_START_TIME")
                        .asc()
                        .column("OPER_START_TIME", "OPER_END_TIME")
                        .build());
        if(resultVo1.isSuccess()){
            list = resultVo1.getContent().getResult();
        }
//        List<Map<String, String>> list = hbaseDao.findConditionByPatient(HdrTableEnum.HDR_OPER_ANAES.getCode(), oid,
//                patientId, filters, new String[]{"OPER_START_TIME", "OPER_END_TIME"});
        Utils.sortListByDate(list, "OPER_START_TIME", Page.Sort.ASC);
        DateTimeFormatter dateTimeFormatter=DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        DateTimeFormatter dateFormatter=DateTimeFormatter.ofPattern("yyyy-MM-dd");
        for (int i = 0; i < list.size(); i++) {
//			Map<String, String> oper=new HashMap<String, String>();
            if (StringUtils.isNotBlank(list.get(i).get("OPER_START_TIME"))) {
                String START_TIME = list.get(i).get("OPER_START_TIME");
                LocalDateTime time = LocalDateTime.parse(START_TIME,dateTimeFormatter);

                operDate.add(time.format(dateFormatter));
            }
        }
        List<Map<String, String>> operAfterList = new ArrayList<Map<String, String>>();
        for (int i = 0; i < cols; i++) {
            if (week == 0)
                week = 1;
            String time = Utils.getDateAfter(Utils.strToDate(admissionTime, "yyyy-MM-dd"), (week - 1) * 7 + i);
            Map<String, String> operAfterMap = new HashMap<String, String>();
            String value = "";
            int num = Utils.compareStrDateTime(time + " 00:00:00", dischargetime + " 00:00:00");
            if (!(num > 0))
                value = getcompareStrDate(time, operDate);
            operAfterMap.put("value", value);
            operAfterList.add(operAfterMap);
        }
        operAfter.put("data", operAfterList);
        rs.add(operAfter);
    }

    private String getcompareStrDate(String time, List<String> operDate) {
        // TODO Auto-generated method stub
        int num = 0;
        int count = 0;
        for (int i = 0; i < operDate.size(); i++) {
            String date1 = operDate.get(i);
            Integer integer = Utils.compareStrDate(date1, time) + 1;
            if (num == 0) {
                if (integer > 0) {
                    num = integer;
                    count = i;
                }
            } else {
                if (integer > 0 && integer < num) {
                    num = integer;
                    count = i;
                }
            }
        }
        if (num > 0) {
            if (count == 0)
                return "Ⅰ-" + num;
            if (count == 1)
                return "Ⅱ-" + num;
            if (count == 2)
                return "Ⅲ-" + num;
            if (count == 3)
                return "Ⅳ-" + num;
            if (count == 4)
                return "Ⅴ-" + num;

        }

        return "";
    }

    private void getTpOutSTInfo(String oid, String patientId, String visitId, String admissionTime,
                                int week, int cols, List<Map<String, Object>> rs) {
        // TODO Auto-generated method stub
        Map<String, Object> shit = new HashMap<String, Object>();
        shit.put("id", "shit");
        shit.put("name", "大便（次/日）");
        shit.put("order", "4");
        Map<String, Object> incoming = new HashMap<String, Object>();
        incoming.put("id", "incoming");
        incoming.put("name", "入量（ml）");
        incoming.put("order", "5");
        Map<String, Object> urine = new HashMap<String, Object>();
        urine.put("id", "urine");
        urine.put("name", "尿量（ml）");
        urine.put("order", "6");

        List<PropertyFilter> filters = new ArrayList<PropertyFilter>();
        //filters.add(new PropertyFilter("VISIT_ID", "STRING", MatchType.EQ.getOperation(), visitId));
        filters.add(new PropertyFilter("IN_OUT_CATEGORY", MatchType.IN.getOperation(), "总入量,大便,尿"));

        List<Map<String, String>> list = new ArrayList<>();
        ResultVo<PageResultVo<Map<String, String>>> resultVo = hbaseQueryClient.getPageByCondition(
                PageRequestBuilder.init()
                        .tableName(HdrTableEnum.HDR_CARE_IN_OUTST.getCode())
                        .patientId(patientId)
                        .oid(oid)
                        .visitId(visitId)
                        .visitTypeCode("")
                        .filters(filters)
                        .pageNo(0)
                        .pageSize(0)
                        .orderBy("VITAL_SIGN_VALUE")
                        .asc()
                        .column("VISIT_ID", "IN_OUT_AMOUNT_VALUE", "IN_OUT_CATEGORY", "START_TIME")
                        .build());
        if(resultVo.isSuccess()){
            list = resultVo.getContent().getResult();
        }
//        List<Map<String, String>> list = hbaseDao.findConditionByPatient(HdrTableEnum.HDR_CARE_IN_OUTST.getCode(), oid,
//                patientId, filters, new String[]{"VISIT_ID", "IN_OUT_AMOUNT_VALUE", "IN_OUT_CATEGORY", "START_TIME"});
        Utils.sortListByDate(list, "VITAL_SIGN_VALUE", Page.Sort.ASC);
        Map<String, Object> shitDates = new HashMap<String, Object>();
        Map<String, Object> incomingDates = new HashMap<String, Object>();
        Map<String, Object> urineDates = new HashMap<String, Object>();
        DateTimeFormatter dateTimeFormatter=DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

        for (int i = 0; i < list.size(); i++) {
            if (StringUtils.isNotBlank(list.get(i).get("START_TIME"))) {
                String START_TIME = list.get(i).get("START_TIME");
                LocalDateTime time = LocalDateTime.parse(START_TIME,dateTimeFormatter);
                DateTimeFormatter df = DateTimeFormatter.ofPattern("yyyy-MM-dd");
                if ("大便".equals(list.get(i).get("IN_OUT_CATEGORY"))) {
                    shitDates.put(df.format(time), list.get(i));
                } else if ("总入量".equals(list.get(i).get("IN_OUT_CATEGORY"))) {
                    incomingDates.put(df.format(time), list.get(i));
                } else if ("尿".equals(list.get(i).get("IN_OUT_CATEGORY"))) {
                    urineDates.put(df.format(time), list.get(i));
                }
            }
        }
        List<Map<String, String>> shitList = new ArrayList<Map<String, String>>();
        List<Map<String, String>> incomingList = new ArrayList<Map<String, String>>();
        List<Map<String, String>> urineList = new ArrayList<Map<String, String>>();

        for (int i = 0; i < cols; i++) {
            if (week == 0)
                week = 1;
            String time = Utils.getDateAfter(Utils.strToDate(admissionTime, "yyyy-MM-dd"), (week - 1) * 7 + i);
            Map<String, String> shitMap = new HashMap<String, String>();
            if (shitDates.containsKey(time)) {
                Map<String, String> map = (Map<String, String>) shitDates.get(time);
                shitMap.put("value", map.get("IN_OUT_AMOUNT_VALUE"));
                boolean abnormal = true;
//                if (Double.valueOf(map.get("IN_OUT_AMOUNT_VALUE")) > 2) {
//                    abnormal = false;
//                }
                shitMap.put("abnormal",  "1" );
            } else {
                shitMap.put("value", "");
            }
            shitList.add(shitMap);

            Map<String, String> incomingMap = new HashMap<String, String>();
            if (incomingDates.containsKey(time)) {
                Map<String, String> map = (Map<String, String>) incomingDates.get(time);
                incomingMap.put("value", map.get("IN_OUT_AMOUNT_VALUE"));
                boolean abnormal = true;
//                if (Double.valueOf(map.get("IN_OUT_AMOUNT_VALUE")) > 2500 ||
//                        Double.valueOf(map.get("IN_OUT_AMOUNT_VALUE")) < 2000) {
//                    abnormal = false;
//                }
                incomingMap.put("abnormal",  "1" );
            } else {
                incomingMap.put("value", "");
            }
            incomingList.add(incomingMap);

            Map<String, String> urineMap = new HashMap<String, String>();
            if (urineDates.containsKey(time)) {
                Map<String, String> map = (Map<String, String>) urineDates.get(time);
                urineMap.put("value", map.get("IN_OUT_AMOUNT_VALUE"));
                boolean abnormal = true;
//                if (Double.valueOf(map.get("IN_OUT_AMOUNT_VALUE")) > 2500 ||
//                        Double.valueOf(map.get("IN_OUT_AMOUNT_VALUE")) < 2000) {
//                    abnormal = false;
//                }
                urineMap.put("abnormal",  "1" );
            } else {
                urineMap.put("value", "");
            }
            urineList.add(urineMap);
        }
        shit.put("data", shitList);
        incoming.put("data", incomingList);
        urine.put("data", urineList);
        rs.add(shit);
        rs.add(incoming);
        rs.add(urine);

    }

    private void getTpBreathAndBloodInfo(String oid, String patientId, String visitId, String admissionTime, int week, int cols,
                                         List<Map<String, Object>> rs) {
        // TODO Auto-generated method stub
        Map<String, Object> weight = new HashMap<String, Object>();
        weight.put("id", "weight");
        weight.put("name", "体重");
        weight.put("order", "7");
        Map<String, Object> height = new HashMap<String, Object>();
        height.put("id", "height");
        height.put("name", "身高");
        height.put("order", "8");
        Map<String, Object> blood = new HashMap<String, Object>();
        blood.put("id", "blood");
        blood.put("name", "血压（mmHg）");
        blood.put("order", "9");
        List<PropertyFilter> filters = new ArrayList<PropertyFilter>();
        //filters.add(new PropertyFilter("VISIT_ID", "STRING", MatchType.EQ.getOperation(), visitId));
        filters.add(new PropertyFilter("VITAL_TYPE_NAME", MatchType.IN.getOperation(), "呼吸,体重,身高,血压"));

        List<Map<String, String>> list = new ArrayList<>();
        ResultVo<PageResultVo<Map<String, String>>> resultVo = hbaseQueryClient.getPageByCondition(
                PageRequestBuilder.init()
                        .tableName(HdrTableEnum.HDR_VITAL_MEASURE.getCode())
                        .patientId(patientId)
                        .oid(oid)
                        .visitId(visitId)
                        .visitTypeCode("")
                        .filters(filters)
                        .pageNo(0)
                        .pageSize(0)
                        .orderBy("")
                        .desc()
                        .column("VISIT_ID", "VITAL_SIGN_VALUE", "VITAL_SIGN2_VALUE", "MEASURING_TIME", "VITAL_TYPE_NAME")
                        .build());
        if(resultVo.isSuccess()){
            list = resultVo.getContent().getResult();
        }
//        List<Map<String, String>> list = hbaseDao.findConditionByPatient(HdrTableEnum.HDR_VITAL_MEASURE.getCode(), oid,
//                patientId, filters, new String[]{"VISIT_ID", "VITAL_SIGN_VALUE", "VITAL_SIGN2_VALUE", "MEASURING_TIME", "VITAL_TYPE_NAME"});
        Utils.sortListByDate(list, "VITAL_SIGN_VALUE", Page.Sort.ASC);
        Map<String, Object> weightDates = new HashMap<String, Object>();
        Map<String, Object> heightDates = new HashMap<String, Object>();
        Map<String, List<Map<String, String>>> bloodDates = new HashMap<String, List<Map<String, String>>>();
        DateTimeFormatter dateTimeFormatter=DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");


        for (int i = 0; i < list.size(); i++) {
            if (StringUtils.isNotBlank(list.get(i).get("MEASURING_TIME"))) {
                String PRESC_TIME = list.get(i).get("MEASURING_TIME");
                LocalDateTime time = LocalDateTime.parse(PRESC_TIME, dateTimeFormatter);
                DateTimeFormatter df = DateTimeFormatter.ofPattern("yyyy-MM-dd");
                if ("体重".equals(list.get(i).get("VITAL_TYPE_NAME"))) {
                    weightDates.put(df.format(time), list.get(i));
                } else if ("身高".equals(list.get(i).get("VITAL_TYPE_NAME"))) {
                    heightDates.put(df.format(time), list.get(i));
                } else if ("血压".equals(list.get(i).get("VITAL_TYPE_NAME"))) {
                    if (bloodDates.containsKey(df.format(time))) {
                        bloodDates.get(df.format(time)).add(list.get(i));
                    } else {
                        List<Map<String, String>> list2 = new ArrayList<Map<String, String>>();
                        list2.add(list.get(i));
                        bloodDates.put(df.format(time), list2);
                    }
                }
            }
        }
        List<Map<String, String>> weightList = new ArrayList<Map<String, String>>();
        List<Map<String, String>> heightList = new ArrayList<Map<String, String>>();
        List<Map<String, String>> bloodList = new ArrayList<Map<String, String>>();

        for (int i = 0; i < cols; i++) {
            if (week == 0)
                week = 1;
            String time = Utils.getDateAfter(Utils.strToDate(admissionTime, "yyyy-MM-dd"), (week - 1) * 7 + i);
            Map<String, String> weightMap = new HashMap<String, String>();
            if (weightDates.containsKey(time)) {
                Map<String, String> map = (Map<String, String>) weightDates.get(time);
                weightMap.put("value", map.get("VITAL_SIGN_VALUE"));
                boolean abnormal = true;
                weightMap.put("abnormal", abnormal ? "1" : "0");
            } else {
                weightMap.put("value", "");
            }
            weightList.add(weightMap);

            Map<String, String> heightMap = new HashMap<String, String>();
            if (heightDates.containsKey(time)) {
                Map<String, String> map = (Map<String, String>) heightDates.get(time);
                heightMap.put("value", map.get("VITAL_SIGN_VALUE"));
                boolean abnormal = true;
                heightMap.put("abnormal", abnormal ? "1" : "0");
            } else {
                heightMap.put("value", "");
            }
            heightList.add(heightMap);

            Map<String, String> bloodMap = new HashMap<String, String>();
            if (bloodDates.containsKey(time)) {
                //一天中所有测量数据
                List<Map<String, String>> bloodLists = (List<Map<String, String>>) bloodDates.get(time);
                Map<String, Map<String, String>> map = groupByDateHour(bloodLists, 2, "MEASURING_TIME");
                String value = "";
                String abnormal = "";
                for (int j = 0; j < 2; j++) {
                    if (j == 0) {
                        if (map.containsKey(j + "")) {
                            double n = Double.valueOf(map.get(j + "").get("VITAL_SIGN_VALUE"));
                            double m=0.0;
                            if(StringUtils.isNotBlank(map.get(j + "").get("VITAL_SIGN2_VALUE"))) {
                                m = Double.valueOf(map.get(j + "").get("VITAL_SIGN2_VALUE"));
                            }
                            value = (int) n + "/" + ((int) m);
//							if(Double.valueOf(map.get(j+"").get("VITAL_SIGN_VALUE"))>140
//									||Double.valueOf(map.get(j+"").get("VITAL_SIGN2_VALUE"))<90){
//								abnormal="0";
//							}else{
                            abnormal = "1";
//							}
                        } else {
                            abnormal = "1";
                        }
                    } else {
                        if (map.containsKey(j + "")) {
                            double n = Double.valueOf(map.get(j + "").get("VITAL_SIGN_VALUE"));
                            double m=0.0;
                            if(StringUtils.isNotBlank(map.get(j + "").get("VITAL_SIGN2_VALUE"))) {
                                m = Double.valueOf(map.get(j + "").get("VITAL_SIGN2_VALUE"));
                            }
                            value = value + "," + (int) n + "/" + ((int) m);
//							if(Double.valueOf(map.get(j+"").get("VITAL_SIGN_VALUE"))>140
//									||Double.valueOf(map.get(j+"").get("VITAL_SIGN2_VALUE"))<90){
//								abnormal=abnormal+",0";
//							}else{
                            abnormal = abnormal + ",1";
//							}
                        } else {
                            abnormal = abnormal + "1";
                            value = value + ",";
                        }
                    }
                }
                bloodMap.put("value", value);
                bloodMap.put("abnormal", abnormal);
            } else {
                bloodMap.put("value", "");
                bloodMap.put("abnormal", "1");
            }
            bloodList.add(bloodMap);
        }
        weight.put("data", weightList);
        height.put("data", heightList);
        blood.put("data", bloodList);
        rs.add(weight);
        rs.add(height);
        rs.add(blood);
    }

    private Map<String, Map<String, String>> groupByDateHour(
            List<Map<String, String>> breathList, int i, String field) {
        int num = 24 / i;
        Map<String, Map<String, String>> rs = new HashMap<String, Map<String, String>>();
        DateTimeFormatter dateTimeFormatter=DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        for (Map<String, String> map : breathList) {
            LocalDateTime time = LocalDateTime.parse(map.get(field),dateTimeFormatter);
            DateTimeFormatter df = DateTimeFormatter.ofPattern("HH");
            int hour = Integer.parseInt(df.format(time));
            rs.put(hour / num + "", map);
        }

        return rs;
    }

    @Override
    public Map<String, Object> getVisitTimeBloodAndPulse(String oid, String patientId,
                                                         String visitId, String admissionTime, int week, int cols) {
        // TODO Auto-generated method stub
        Map<String, Object> rs = new HashMap<String, Object>();

        List<Object> pulse = new ArrayList<Object>();
        List<Object> temperature = new ArrayList<Object>();
        List<Object> breath = new ArrayList<Object>();
        List<Object> in = new ArrayList<Object>();
        List<Object> out = new ArrayList<Object>();
        List<PropertyFilter> filters = new ArrayList<PropertyFilter>();
        //filters.add(new PropertyFilter("VISIT_ID", "STRING", MatchType.EQ.getOperation(), visitId));
        filters.add(new PropertyFilter("VITAL_TYPE_NAME", MatchType.IN.getOperation(), "体温,脉搏,呼吸,入量,尿量"));
        List<Map<String, String>> list = new ArrayList<>();
        ResultVo<PageResultVo<Map<String, String>>> resultVo = hbaseQueryClient.getPageByCondition(
                PageRequestBuilder.init()
                        .tableName(HdrTableEnum.HDR_VITAL_MEASURE.getCode())
                        .patientId(patientId)
                        .oid(oid)
                        .visitId(visitId)
                        .visitTypeCode("")
                        .filters(filters)
                        .pageNo(0)
                        .pageSize(0)
                        .orderBy("")
                        .desc()
                        .column("VISIT_ID", "VITAL_SIGN_VALUE", "VITAL_SIGN2_VALUE", "MEASURING_TIME", "VITAL_TYPE_NAME")
                        .build());
        if(resultVo.isSuccess()){
            list = resultVo.getContent().getResult();
        }
//        List<Map<String, String>> list = hbaseDao.findConditionByPatient(HdrTableEnum.HDR_VITAL_MEASURE.getCode(), oid,
//                patientId, filters, new String[]{"VISIT_ID", "VITAL_SIGN_VALUE", "VITAL_SIGN2_VALUE", "MEASURING_TIME", "VITAL_TYPE_NAME"});
        Utils.sortListByDate(list, "VITAL_SIGN_VALUE", Page.Sort.ASC);

        Map<String, List<Map<String, String>>> pulseDates = new HashMap<String, List<Map<String, String>>>();
        Map<String, List<Map<String, String>>> temperatureDates = new HashMap<String, List<Map<String, String>>>();
        Map<String, List<Map<String, String>>> breathDates = new HashMap<String, List<Map<String, String>>>();
        Map<String, List<Map<String, String>>> rlDates = new HashMap<String, List<Map<String, String>>>();
        Map<String, List<Map<String, String>>> nlDates = new HashMap<String, List<Map<String, String>>>();
        DateTimeFormatter dateTimeFormatter=DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        for (int i = 0; i < list.size(); i++) {
            if (StringUtils.isNotBlank(list.get(i).get("MEASURING_TIME"))) {
                String PRESC_TIME = list.get(i).get("MEASURING_TIME");
                LocalDateTime time = LocalDateTime.parse(PRESC_TIME,dateTimeFormatter);

                DateTimeFormatter df = DateTimeFormatter.ofPattern("yyyy-MM-dd");
                if ("体温".equals(list.get(i).get("VITAL_TYPE_NAME"))) {
                    if (temperatureDates.containsKey(df.format(time))) {
                        temperatureDates.get(df.format(time)).add(list.get(i));
                    } else {
                        List<Map<String, String>> list2 = new ArrayList<Map<String, String>>();
                        list2.add(list.get(i));
                        temperatureDates.put(df.format(time), list2);
                    }
                } else if ("脉搏".equals(list.get(i).get("VITAL_TYPE_NAME"))) {
                    if (pulseDates.containsKey(df.format(time))) {
                        pulseDates.get(df.format(time)).add(list.get(i));
                    } else {
                        List<Map<String, String>> list2 = new ArrayList<Map<String, String>>();
                        list2.add(list.get(i));
                        pulseDates.put(df.format(time), list2);
                    }
                } else if ("呼吸".equals(list.get(i).get("VITAL_TYPE_NAME"))) {
                    if (breathDates.containsKey(df.format(time))) {
                        breathDates.get(df.format(time)).add(list.get(i));
                    } else {
                        List<Map<String, String>> list2 = new ArrayList<Map<String, String>>();
                        list2.add(list.get(i));
                        breathDates.put(df.format(time), list2);
                    }
                } else if ("入量".equals(list.get(i).get("VITAL_TYPE_NAME"))) {
                    if (rlDates.containsKey(df.format(time))) {
                        rlDates.get(df.format(time)).add(list.get(i));
                    } else {
                        List<Map<String, String>> list2 = new ArrayList<Map<String, String>>();
                        list2.add(list.get(i));
                        rlDates.put(df.format(time), list2);
                    }
                } else if ("尿量".equals(list.get(i).get("VITAL_TYPE_NAME"))) {
                    if (nlDates.containsKey(df.format(time))) {
                        nlDates.get(df.format(time)).add(list.get(i));
                    } else {
                        List<Map<String, String>> list2 = new ArrayList<Map<String, String>>();
                        list2.add(list.get(i));
                        nlDates.put(df.format(time), list2);
                    }
                }

            }
        }

        for (int i = 0; i < cols; i++) {
            if (week == 0)
                week = 1;
            String time = Utils.getDateAfter(Utils.strToDate(admissionTime, "yyyy-MM-dd"), (week - 1) * 7 + i);
            if (pulseDates.containsKey(time)) {
                //一天中所有测量数据
                List<Map<String, String>> pulseList = (List<Map<String, String>>) pulseDates.get(time);
                Map<String, Map<String, String>> map = groupByDateHour(pulseList, 6, "MEASURING_TIME");

                for (int j = 0; j < 6; j++) {
                    if (map.containsKey(j + "")) {
                        String value = map.get(j + "").get("VITAL_SIGN_VALUE") == null ? "" : map.get(j + "").get("VITAL_SIGN_VALUE");
                        pulse.add(value.equals("") ? "" : (Double.valueOf(value) > 0 ? Double.valueOf(value) : ""));
                    } else {
                        pulse.add("");
                    }
                }
            } else {
                for (int j = 0; j < 6; j++) {
                    pulse.add("");
                }
            }
            rs.put("pulse", pulse);

            if (temperatureDates.containsKey(time)) {
                //一天中所有测量数据
                List<Map<String, String>> temperatureList = (List<Map<String, String>>) temperatureDates.get(time);
                Map<String, Map<String, String>> map = groupByDateHour(temperatureList, 6, "MEASURING_TIME");
                for (int j = 0; j < 6; j++) {
                    if (map.containsKey(j + "")) {
                        String value = map.get(j + "").get("VITAL_SIGN_VALUE") == null ? "" : map.get(j + "").get("VITAL_SIGN_VALUE");
                        temperature.add(value.equals("") ? "" : (Double.valueOf(value) > 10 ? Double.valueOf(value) : ""));
                    } else {
                        temperature.add("");
                    }
                }
            } else {
                for (int j = 0; j < 6; j++) {
                    temperature.add("");
                }
            }
            rs.put("temperature", temperature);

            if (breathDates.containsKey(time)) {
                //一天中所有测量数据
                List<Map<String, String>> breathList = (List<Map<String, String>>) breathDates.get(time);
                Map<String, Map<String, String>> map = groupByDateHour(breathList, 6, "MEASURING_TIME");
                for (int j = 0; j < 6; j++) {
                    if (map.containsKey(j + "")) {
                        double n = Double.valueOf(map.get(j + "").get("VITAL_SIGN_VALUE"));
                        String value = (int) n + "";
                        breath.add(value.equals("") ? "" : (Double.valueOf(value) > 0 ? Double.valueOf(value) : ""));
                    } else {
                        breath.add("");
                    }
                }
            } else {
                for (int j = 0; j < 6; j++) {
                    breath.add("");
                }
            }
            rs.put("breath", breath);


            // 入量
            if (rlDates.containsKey(time)) {
                //一天中所有测量数据
                List<Map<String, String>> rlList = (List<Map<String, String>>) rlDates.get(time);
                Map<String, Map<String, String>> map = groupByDateHour(rlList, 6, "MEASURING_TIME");
                for (int j = 0; j < 6; j++) {
                    if (map.containsKey(j + "")) {
                        double n = Double.valueOf(map.get(j + "").get("VITAL_SIGN_VALUE"));
                        String value = (int) n + "";
                        in.add(value.equals("") ? "" : (Double.valueOf(value) > 0 ? Double.valueOf(value) : ""));
                    } else {
                        in.add("");
                    }
                }
            } else {
                for (int j = 0; j < 6; j++) {
                    in.add("");
                }
            }
            rs.put("intake", in);


            // 尿量
            if (nlDates.containsKey(time)) {
                //一天中所有测量数据
                List<Map<String, String>> nlList = (List<Map<String, String>>) nlDates.get(time);
                Map<String, Map<String, String>> map = groupByDateHour(nlList, 6, "MEASURING_TIME");
                for (int j = 0; j < 6; j++) {
                    if (map.containsKey(j + "")) {
                        double n = Double.valueOf(map.get(j + "").get("VITAL_SIGN_VALUE"));
                        String value = (int) n + "";
                        out.add(value.equals("") ? "" : (Double.valueOf(value) > 0 ? Double.valueOf(value) : ""));
                    } else {
                        out.add("");
                    }
                }
            } else {
                for (int j = 0; j < 6; j++) {
                    out.add("");
                }
            }
            rs.put("urinaryOutput", out);
        }

        return rs;
    }

    private void getTpDateAndInDayInfo(String admissionTime, String dischargetime, int week, int cols, List<Map<String, Object>> rs) {
        // TODO Auto-generated method stub
        Map<String, Object> date = new HashMap<String, Object>();
        List<Map<String, String>> datas = new ArrayList<Map<String, String>>();
        //"id":"date","name":"日期"
        date.put("id", "date");
        date.put("name", "日期");
        date.put("order", "0");
        Map<String, Object> inDays = new HashMap<String, Object>();
        List<Map<String, String>> inDayDatas = new ArrayList<Map<String, String>>();
        inDays.put("id", "indays");
        inDays.put("name", "住院天数");
        inDays.put("order", "1");
        for (int i = 0; i < cols; i++) {
            Map<String, String> dateMap = new HashMap<String, String>();
            Map<String, String> inMap = new HashMap<String, String>();
            if (week == 0)
                week = 1;
            String time = Utils.getDateAfter(Utils.strToDate(admissionTime, "yyyy-MM-dd"), (week - 1) * 7 + i);
            dateMap.put("value", time);
            datas.add(dateMap);
            int num = Utils.compareStrDateTime(time + " 00:00:00", dischargetime + " 00:00:00");
            if (num > 0) {
                inMap.put("value", "");
            } else {
                inMap.put("value", (week - 1) * 7 + i + 1 + "");
            }
            inDayDatas.add(inMap);

        }
        date.put("data", datas);
        inDays.put("data", inDayDatas);
        rs.add(date);
        rs.add(inDays);
    }

    @Override
    public Map<String, Object> getVisitTimeLabList(String oid, String patientId, String visitId, String admissionTime, String mainDiag, String deptCode, int week, int cols) {
        Map<String, Object> rs = new HashMap<String, Object>();
        int maxNum = 0;
        String begin = "";
        String end = "";
        if (week == 1 || week == 0) {
            begin = admissionTime;
        } else {
            begin = Utils.getDateAfter(Utils.strToDate(admissionTime, "yyyy-MM-dd"), 7 * (week - 1));
        }
        if (week == 0) {
            end = Utils.getDateAfter(Utils.strToDate(admissionTime, "yyyy-MM-dd"), cols);
        } else {
            end = Utils.getDateAfter(Utils.strToDate(admissionTime, "yyyy-MM-dd"), 7 * (week) - 1);
        }

        List<PropertyFilter> filters = new ArrayList<PropertyFilter>();
        //filters.add(new PropertyFilter("VISIT_ID", "STRING", MatchType.EQ.getOperation(), visitId));
//		filters.add(new PropertyFilter("ORDER_TIME", "STRING", MatchType.GE.getOperation(), begin));
//		filters.add(new PropertyFilter("ORDER_TIME", "STRING", MatchType.LE.getOperation(), end));
        List<String> orderClass = CivUtils.getOrderClass(oid, "OrderClose_LAB");
        if (orderClass != null && orderClass.size() > 0) {
            String orderClassString = "";
            for (String orderString : orderClass) {
                orderClassString += orderString + ",";
            }
            orderClassString = orderClassString.substring(0, orderClassString.length() - 1);
            filters.add(new PropertyFilter("ORDER_CLASS_CODE", MatchType.IN.getOperation(), orderClassString));
        }

        //是否只查询专科数据
        if (StringUtils.isNotBlank(mainDiag) || StringUtils.isNotBlank(deptCode)) {
            List<Map<String, String>> list = specialtyViewPowerService.getSpecialtyConfig(oid, mainDiag, "Lab", deptCode);
            String itemCode = " ";
            for (Map<String, String> map : list) {
                if (StringUtils.isNotBlank(map.get("subItemCode"))) {
                    itemCode += map.get("subItemCode") + ",";
                }
            }
            itemCode = itemCode.substring(0, itemCode.length() - 1).trim();
            filters.add(new PropertyFilter("ORDER_ITEM_CODE", MatchType.IN.getOperation(), itemCode));

        }
        List<Map<String, String>> list = new ArrayList<>();
        ResultVo<PageResultVo<Map<String, String>>> resultVo = hbaseQueryClient.getPageByCondition(
                PageRequestBuilder.init()
                        .tableName(HdrTableEnum.HDR_IN_ORDER.getCode())
                        .patientId(patientId)
                        .oid(oid)
                        .visitId(visitId)
                        .visitTypeCode("")
                        .filters(filters)
                        .pageNo(0)
                        .pageSize(0)
                        .orderBy("")
                        .desc()
                        .column("ORDER_ITEM_NAME", "ORDER_TIME", "ORDER_DOCTOR_NAME", "ORDER_NO")
                        .build());
        if(resultVo.isSuccess()){
            list = resultVo.getContent().getResult();
        }
//        List<Map<String, String>> list = hbaseDao.findConditionByPatient(HdrTableEnum.HDR_IN_ORDER.getCode(), oid,
//                patientId, filters, new String[]{"ORDER_ITEM_NAME", "ORDER_TIME", "ORDER_DOCTOR_NAME", "ORDER_NO"});

        //避免重复查询明细表，一次查询所有符合是金条件的明细数据按报告分组
        Map<String, Object> Labinfo = getInspectReportYC(oid, patientId, visitId, begin, end);
        //异常数据
        Map<String, List<Map<String, String>>> ycLabSubs = (Map<String, List<Map<String, String>>>) Labinfo.get("yc");
        //报告数据
        Map<String, Map<String, String>> reportinfo = (Map<String, Map<String, String>>) Labinfo.get("report");
        //查询结果分组
        Map<String, List<Map<String, String>>> labfz = new HashMap<String, List<Map<String, String>>>();
        CivUtils.groupByDayDate(labfz, list, "ORDER_TIME");
        //所有检验数据
        List<Map<String, Object>> labAll = new ArrayList<Map<String, Object>>();
        for (int i = 0; i < cols; i++) {
            Map<String, Object> labdata = new HashMap<String, Object>();
            labdata.put("date", (i + 1) + "");
            String key = Utils.getDateAfter(Utils.strToDate(begin, "yyyy-MM-dd"), i);
            //同一天的所有报告集合
            List<Map<String, Object>> labdays = new ArrayList<Map<String, Object>>();
            if (labfz.containsKey(key)) {
                //获取最大行数
                if (labfz.get(key).size() > maxNum) {
                    maxNum = labfz.get(key).size();
                }
                //循环同一天的报告
                for (Map<String, String> map : labfz.get(key)) {
                    Map<String, Object> lab = new HashMap<String, Object>();
                    lab.put("name", map.get("ORDER_ITEM_NAME") == null ? "-" : map.get("ORDER_ITEM_NAME"));
                    //获取报告信息
                    if (reportinfo.get(map.get("ORDER_NO")) != null) {
                        lab.put("reportTime", reportinfo.get(map.get("ORDER_NO")).get("REPORT_TIME") == null ?
                                "-" : reportinfo.get(map.get("ORDER_NO")).get("REPORT_TIME"));
                        lab.put("reportPerson", reportinfo.get(map.get("ORDER_NO")).get("REPORT_DOCTOR_NAME") == null ?
                                "-" : reportinfo.get(map.get("ORDER_NO")).get("REPORT_DOCTOR_NAME"));
                    } else {
                        lab.put("reportTime", "-");
                        lab.put("reportPerson", "-");
                    }
                    //获取报告的开立时间和申请时间
                    lab.put("time", map.get("ORDER_TIME") == null ? "-" : map.get("ORDER_TIME"));
                    lab.put("person", map.get("ORDER_DOCTOR_NAME") == null ? "-" : map.get("ORDER_DOCTOR_NAME"));
                    //获取异常数据
                    if (ycLabSubs.containsKey(map.get("ORDER_NO"))) {
                        lab.put("check", "1");
                        lab.put("exceptionItems", ycLabSubs.get(map.get("ORDER_NO")));
                    } else {
                        lab.put("check", "0");
                        lab.put("exceptionItems", new ArrayList<Map<String, String>>());
                    }
                    labdays.add(lab);
                }
            }
            labdata.put("date", (i + 1) + "");
            labdata.put("data", labdays);
            labAll.add(labdata);
        }

        rs.put("max", maxNum);
        rs.put("data", labAll);

        return rs;
    }

    private Map<String, Map<String, String>> getReportInfo(String oid, String patientId, String visitId) {
        // TODO Auto-generated method stub
        Map<String, Map<String, String>> rs = new HashMap<String, Map<String, String>>();
        List<PropertyFilter> filters = new ArrayList<PropertyFilter>();
        //filters.add(new PropertyFilter("VISIT_ID", "STRING", MatchType.EQ.getOperation(), visitId));
        List<Map<String, String>> list = new ArrayList<>();
        ResultVo<PageResultVo<Map<String, String>>> resultVo = hbaseQueryClient.getPageByCondition(
                PageRequestBuilder.init()
                        .tableName(HdrTableEnum.HDR_LAB_REPORT.getCode())
                        .patientId(patientId)
                        .oid(oid)
                        .visitId(visitId)
                        .visitTypeCode("")
                        .filters(filters)
                        .pageNo(0)
                        .pageSize(0)
                        .orderBy("")
                        .desc()
                        .column("REPORT_TIME", "REPORT_NO", "REPORT_DOCTOR_NAME")
                        .build());
        if(resultVo.isSuccess()){
            list = resultVo.getContent().getResult();
        }
//        List<Map<String, String>> list = hbaseDao.findConditionByPatient(HdrTableEnum.HDR_LAB_REPORT.getCode(), oid,
//                patientId, filters, new String[]{"REPORT_TIME", "REPORT_NO", "REPORT_DOCTOR_NAME"});
        for (Map<String, String> map : list) {
            rs.put(map.get("REPORT_NO"), map);
        }

        return rs;
    }

    public Map<String, Object> getInspectReportYC(String oid, String patientId, String visitId, String begin, String end) {
        Map<String, Object> rs = new HashMap<String, Object>();
        //异常项数据
        Map<String, List<Map<String, String>>> ycrs = new HashMap<String, List<Map<String, String>>>();
        //报告数据
        Map<String, Map<String, String>> report = new HashMap<String, Map<String, String>>();
        //查询报告主表，获取报告数据
        Map<String, Map<String, String>> reports = getReportInfo(oid, patientId, visitId);

        List<Map<String, String>> list = new ArrayList<Map<String, String>>();
        try {
            //查询当前检验报告下的  检验细项列表
            List<PropertyFilter> filters = new ArrayList<PropertyFilter>();
            //filters.add(new PropertyFilter("VISIT_ID", "STRING", MatchType.EQ.getOperation(), visitId));
            List<Map<String, String>> subLabs = new ArrayList<>();
            ResultVo<PageResultVo<Map<String, String>>> resultVo = hbaseQueryClient.getPageByCondition(
                    PageRequestBuilder.init()
                            .tableName(HdrTableEnum.HDR_LAB_REPORT_DETAIL.getCode())
                            .patientId(patientId)
                            .oid(oid)
                            .visitId(visitId)
                            .visitTypeCode("")
                            .filters(filters)
                            .pageNo(0)
                            .pageSize(0)
                            .orderBy("")
                            .desc()
                            .column("ORDER_NO", "REPORT_NO",
                                    "RESULT_STATUS_CODE", "LAB_SUB_ITEM_NAME", "LAB_RESULT_UNIT", "LAB_RESULT_VALUE", "RANGE", "LAB_SUB_ITEM_CODE")
                            .build());
            if(resultVo.isSuccess()){
                subLabs = resultVo.getContent().getResult();
            }
//            List<Map<String, String>> subLabs = hbaseDao.findConditionByPatient(
//                    HdrTableEnum.HDR_LAB_REPORT_DETAIL.getCode(), oid, patientId, filters, new String[]{"ORDER_NO", "REPORT_NO",
//                            "RESULT_STATUS_CODE", "LAB_SUB_ITEM_NAME", "LAB_RESULT_UNIT", "LAB_RESULT_VALUE", "RANGE", "LAB_SUB_ITEM_CODE"});
            //处理检验细项
            for (Map<String, String> map : subLabs) {
                report.put(map.get("ORDER_NO"), reports.get(map.get("REPORT_NO")));
                Map<String, String> sub = new HashMap<String, String>();
                //异常项判断
                String status = map.get("RESULT_STATUS_CODE");
                if (StringUtils.isNotBlank(status)) {
                    if ("H".equals(status) || "L".equals(status)) {
                        sub.put("reportNo", map.get("REPORT_NO"));
                        sub.put("orderNo", map.get("ORDER_NO"));
                        sub.put("name", map.get("LAB_SUB_ITEM_NAME"));
                        sub.put("code", map.get("LAB_SUB_ITEM_CODE"));
                        sub.put("value", map.get("LAB_RESULT_VALUE"));
                        sub.put("unit", map.get("LAB_RESULT_UNIT"));
                        sub.put("range", map.get("RANGE"));
                        list.add(sub);
                    }
                }
            }

        } catch (Exception e) {
            e.printStackTrace();
        }
        //将异常结果分组
        for (Map<String, String> map : list) {
            if (ycrs.containsKey(map.get("orderNo"))) {
                ycrs.get(map.get("orderNo")).add(map);
            } else {
                List<Map<String, String>> list2 = new ArrayList<Map<String, String>>();
                list2.add(map);
                ycrs.put(map.get("orderNo"), list2);
            }
        }
        rs.put("report", report);
        rs.put("yc", ycrs);

        return rs;
    }

    @Override
    public Map<String, Object> getVisitTimeExamList(String oid, String patientId,
                                                    String visitId, String admissionTime, String mainDiag, String deptCode, int week, int cols) {
        Map<String, Object> rs = new HashMap<String, Object>();
        int maxNum = 0;
        String begin = "";
        String end = "";
        if (week == 1 || week == 0) {
            begin = admissionTime;
        } else {
            begin = Utils.getDateAfter(Utils.strToDate(admissionTime, "yyyy-MM-dd"), 7 * (week - 1));
        }
        if (week == 0) {
            end = Utils.getDateAfter(Utils.strToDate(admissionTime, "yyyy-MM-dd"), cols);
        } else {
            end = Utils.getDateAfter(Utils.strToDate(admissionTime, "yyyy-MM-dd"), 7 * (week) - 1);
        }

        List<PropertyFilter> filters = new ArrayList<PropertyFilter>();
        //filters.add(new PropertyFilter("VISIT_ID", "STRING", MatchType.EQ.getOperation(), visitId));
//		filters.add(new PropertyFilter("ORDER_TIME", "STRING", MatchType.GE.getOperation(), begin));
//		filters.add(new PropertyFilter("ORDER_TIME", "STRING", MatchType.LE.getOperation(), end));
        List<String> orderClass = CivUtils.getOrderClass(oid, "OrderClose_EXAM");
        if (orderClass.size() > 0) {
            StringBuilder orderClassString = new StringBuilder();
            for (String orderString : orderClass) {
                orderClassString.append(orderString).append(",");
            }
            orderClassString = new StringBuilder(orderClassString.substring(0, orderClassString.length() - 1));
            filters.add(new PropertyFilter("ORDER_CLASS_CODE", MatchType.IN.getOperation(), orderClassString.toString()));
        }
        //是否只查询专科数据
        if (StringUtils.isNotBlank(mainDiag) || StringUtils.isNotBlank(deptCode)) {
            List<Map<String, String>> list = specialtyViewPowerService.getSpecialtyConfig(oid, mainDiag, "Exams", deptCode);
            StringBuilder itemCode = new StringBuilder();
            for (Map<String, String> map : list) {
                if (StringUtils.isNotBlank(map.get("subItemCode"))) {
                    itemCode.append(map.get("subItemCode")).append(",");
                }
            }
            itemCode = new StringBuilder(itemCode.substring(0, itemCode.length() - 1).trim());
            filters.add(new PropertyFilter("ORDER_ITEM_CODE", MatchType.IN.getOperation(), itemCode.toString()));
        }

        List<Map<String, String>> list = new ArrayList<>();
        ResultVo<PageResultVo<Map<String, String>>> resultVo = hbaseQueryClient.getPageByCondition(
                PageRequestBuilder.init()
                        .tableName(HdrTableEnum.HDR_IN_ORDER.getCode())
                        .patientId(patientId)
                        .oid(oid)
                        .visitId(visitId)
                        .visitTypeCode("")
                        .filters(filters)
                        .pageNo(0)
                        .pageSize(0)
                        .orderBy("")
                        .desc()
                        .column("ORDER_ITEM_NAME", "ORDER_TIME", "ORDER_DOCTOR_NAME", "ORDER_NO")
                        .build());
        if(resultVo.isSuccess()){
            list = resultVo.getContent().getResult();
        }
//        List<Map<String, String>> list = hbaseDao.findConditionByPatient(HdrTableEnum.HDR_IN_ORDER.getCode(), oid,
//                patientId, filters, "ORDER_ITEM_NAME", "ORDER_TIME", "ORDER_DOCTOR_NAME", "ORDER_NO");
//        //查询结果分组
        Map<String, List<Map<String, String>>> examfz = new HashMap<String, List<Map<String, String>>>();
        CivUtils.groupByDayDate(examfz, list, "ORDER_TIME");
        //获取检查报告数据
        Map<String, Map<String, String>> reportinfo = getExamReport(oid, patientId, visitId);
        //所有检查医嘱数据
        List<Map<String, Object>> examAll = new ArrayList<Map<String, Object>>();
        for (int i = 0; i < cols; i++) {
            Map<String, Object> examdata = new HashMap<String, Object>();
            examdata.put("date", String.valueOf(i + 1));
            String key = Utils.getDateAfter(Utils.strToDate(begin, "yyyy-MM-dd"), i);
            //同一天的所有报告集合
            List<Map<String, Object>> examdays = new ArrayList<Map<String, Object>>();
            if (examfz.containsKey(key)) {
                //获取最大行数
                if (examfz.get(key).size() > maxNum) {
                    maxNum = examfz.get(key).size();
                }
                //循环同一天的报告
                for (Map<String, String> map : examfz.get(key)) {
                    Map<String, Object> exam = new HashMap<String, Object>();
                    exam.put("name", map.get("ORDER_ITEM_NAME"));
                    if (reportinfo.get(map.get("ORDER_NO")) != null) {
                        exam.put("see", reportinfo.get(map.get("ORDER_NO")).get("EXAM_DIAG"));
                    } else {
                        exam.put("see", "报告未出");
                    }
                    examdays.add(exam);
                }
            }
            examdata.put("date", String.valueOf(i + 1));
            examdata.put("data", examdays);
            examAll.add(examdata);
        }
        rs.put("max", maxNum);
        rs.put("data", examAll);
        return rs;
    }

    private Map<String, Map<String, String>> getExamReport(String oid, String patientId,
                                                           String visitId) {
        Map<String, Map<String, String>> rs = new HashMap<String, Map<String, String>>();
        List<PropertyFilter> filters = new ArrayList<PropertyFilter>();
        //filters.add(new PropertyFilter("VISIT_ID", "STRING", MatchType.EQ.getOperation(), visitId));
        List<Map<String, String>> list = new ArrayList<>();
        ResultVo<PageResultVo<Map<String, String>>> resultVo = hbaseQueryClient.getPageByCondition(
                PageRequestBuilder.init()
                        .tableName(HdrTableEnum.HDR_EXAM_REPORT.getCode())
                        .patientId(patientId)
                        .oid(oid)
                        .visitId(visitId)
                        .visitTypeCode("")
                        .filters(filters)
                        .pageNo(0)
                        .pageSize(0)
                        .orderBy("")
                        .desc()
                        .column("EXAM_DIAG", "ORDER_NO", "EXAM_ITEM_NAME")
                        .build());
        if(resultVo.isSuccess()){
            list = resultVo.getContent().getResult();
        }
//        List<Map<String, String>> list = hbaseDao.findConditionByPatient(HdrTableEnum.HDR_EXAM_REPORT.getCode(), oid,
//                patientId, filters, "EXAM_DIAG", "ORDER_NO", "EXAM_ITEM_NAME");
        if (list.size() > 0) {
            for (Map<String, String> map : list) {
                rs.put(map.get("ORDER_NO"), map);
            }
        }

        return rs;
    }

    @Override
    public Map<String, Object> getVisitTimeClinicList(String oid, String patientId,
                                                      String visitId, String admissionTime, int week, int cols) {
        // TODO Auto-generated method stub
        Map<String, Object> rs = new HashMap<String, Object>();
        int maxNum = 0;
        String begin = "";
        String end = "";
        if (week == 1 || week == 0) {
            begin = admissionTime;
        } else {
            begin = Utils.getDateAfter(Utils.strToDate(admissionTime, "yyyy-MM-dd"), 7 * (week - 1));
        }
        if (week == 0) {
            end = Utils.getDateAfter(Utils.strToDate(admissionTime, "yyyy-MM-dd"), cols);
        } else {
            end = Utils.getDateAfter(Utils.strToDate(admissionTime, "yyyy-MM-dd"), 7 * (week) - 1);
        }

        List<PropertyFilter> filters = new ArrayList<PropertyFilter>();
        //filters.add(new PropertyFilter("VISIT_ID", "STRING", MatchType.EQ.getOperation(), visitId));
        filters.add(new PropertyFilter("CREATE_DATE_TIME", MatchType.GE.getOperation(), begin));
        filters.add(new PropertyFilter("CREATE_DATE_TIME", MatchType.LE.getOperation(), end));
        List<Map<String, String>> list = new ArrayList<>();
        ResultVo<PageResultVo<Map<String, String>>> resultVo = hbaseQueryClient.getPageByCondition(
                PageRequestBuilder.init()
                        .tableName(HdrTableEnum.HDR_EMR_CONTENT.getCode())
                        .patientId(patientId)
                        .oid(oid)
                        .visitId(visitId)
                        .visitTypeCode("")
                        .filters(filters)
                        .pageNo(0)
                        .pageSize(0)
                        .orderBy("")
                        .desc()
                        .column("CREATE_DATE_TIME", "CREATOR_NAME", "TOPIC")
                        .build());
        if(resultVo.isSuccess()){
            list = resultVo.getContent().getResult();
        }
//        List<Map<String, String>> list = hbaseDao.findConditionByPatient(HdrTableEnum.HDR_EMR_CONTENT.getCode(), oid,
//                patientId, filters, "CREATE_DATE_TIME", "CREATOR_NAME", "TOPIC");
        //查询结果分组
        Map<String, List<Map<String, String>>> mrfz = new HashMap<String, List<Map<String, String>>>();
        CivUtils.groupByDayDate(mrfz, list, "CREATE_DATE_TIME");
        //所有检验数据
        List<Map<String, Object>> mrAll = new ArrayList<Map<String, Object>>();
        for (int i = 0; i < cols; i++) {
            Map<String, Object> mrdata = new HashMap<String, Object>();
            mrdata.put("date", String.valueOf(i + 1));
            String key = Utils.getDateAfter(Utils.strToDate(begin, "yyyy-MM-dd"), i);
            //同一天的所有报告集合
            List<Map<String, Object>> mrdays = new ArrayList<Map<String, Object>>();
            if (mrfz.containsKey(key)) {
                //获取最大行数
                if (mrfz.get(key).size() > maxNum) {
                    maxNum = mrfz.get(key).size();
                }
                //循环同一天的报告
                for (Map<String, String> map : mrfz.get(key)) {
                    Map<String, Object> mr = new HashMap<String, Object>();
                    mr.put("name", map.get("TOPIC"));
                    mr.put("time", map.get("CREATE_DATE_TIME"));
                    mr.put("person", map.get("CREATOR_NAME"));
//					mr.put("rowkey", map.get("ROWKEY"));
                    mrdays.add(mr);
                }
            }
            mrdata.put("date", (i + 1) + "");
            mrdata.put("data", mrdays);
            mrAll.add(mrdata);
        }
        rs.put("max", maxNum);
        rs.put("data", mrAll);
        return rs;
    }

    /**
     * 新增时间轴获取手术信息
     *
     * @param patientId
     * @param admissionTime
     * @param week
     * @param cols
     * @param rs
     */
    @Override
    public void getVisitTimeOperList(String oid, String patientId,String visitId,String admissionTime, int week, int cols, List<Map<String, Object>> rs) {
        Map<String, Object> operAfter = new HashMap<String, Object>();
        operAfter.put("id", "operAfter");
        operAfter.put("name", "术后/分娩后天数");
        operAfter.put("order", "2");

        int maxNum = 0;
        String begin = "";
        String end = "";
        if (week == 1 || week == 0) {
            begin = admissionTime;
        } else {
            begin = Utils.getDateAfter(Utils.strToDate(admissionTime, "yyyy-MM-dd"), 7 * (week - 1));
        }
        if (week == 0) {
            end = Utils.getDateAfter(Utils.strToDate(admissionTime, "yyyy-MM-dd"), cols);
        } else {
            end = Utils.getDateAfter(Utils.strToDate(admissionTime, "yyyy-MM-dd"), 7 * (week) - 1);
        }

        List<PropertyFilter> filters = new ArrayList<PropertyFilter>();
        filters.add(new PropertyFilter("OPER_START_TIME", MatchType.GE.getOperation(), begin));
        filters.add(new PropertyFilter("OPER_START_TIME", MatchType.LE.getOperation(), end));
        List<Map<String, String>> list = new ArrayList<>();
        ResultVo<PageResultVo<Map<String, String>>> resultVo = hbaseQueryClient.getPageByCondition(
                PageRequestBuilder.init()
                        .tableName(HdrTableEnum.HDR_OPER_ANAES.getCode())
                        .patientId(patientId)
                        .oid(oid)
                        .visitId(visitId)
                        .visitTypeCode("")
                        .filters(filters)
                        .pageNo(0)
                        .pageSize(0)
                        .orderBy("")
                        .desc()
                        .column("OPERATION_NAME", "OPER_START_TIME", "OPER_END_TIME", "SURGEN_NAME")
                        .build());
        if(resultVo.isSuccess()){
            list = resultVo.getContent().getResult();
        }
//        List<Map<String, String>> list = hbaseDao.findConditionByPatient(HdrTableEnum.HDR_OPER_ANAES.getCode(), oid,
//                patientId, filters, new String[]{"OPERATION_NAME", "OPER_START_TIME", "OPER_END_TIME", "SURGEN_NAME"});
        //查询结果分组
        Map<String, List<Map<String, String>>> operfz = new HashMap<String, List<Map<String, String>>>();
        CivUtils.groupByDayDate(operfz, list, "OPER_START_TIME");
        //所有检验数据
        List<Map<String, Object>> operAll = new ArrayList<Map<String, Object>>();
        for (int i = 0; i < cols; i++) {
            Map<String, Object> operdata = new HashMap<String, Object>();
            operdata.put("date", (i + 1) + "");
            String key = Utils.getDateAfter(Utils.strToDate(begin, "yyyy-MM-dd"), i);
            //同一天的所有报告集合
            List<Map<String, Object>> operdays = new ArrayList<Map<String, Object>>();
            if (operfz.containsKey(key)) {
                //获取最大行数
                if (operfz.get(key).size() > maxNum) {
                    maxNum = operfz.get(key).size();
                }
                //循环同一天的报告
                for (Map<String, String> map : operfz.get(key)) {
                    Map<String, Object> oper = new HashMap<String, Object>();
                    oper.put("name", map.get("OPERATION_NAME"));
                    oper.put("operTime", map.get("OPER_START_TIME"));
                    oper.put("operPerson", map.get("SURGEN_NAME"));
                    oper.put("endTime", map.get("OPER_END_TIME"));
                    if (StringUtils.isNotBlank(map.get("ORDER_NO"))) {
                        Map<String, String> applyInfo = getOperApplyInfo(oid, patientId,visitId, map.get("ORDER_NO"));
                        if (applyInfo != null) {
                            oper.put("applyTime", applyInfo.get("APPLY_TIME"));
                            oper.put("applyPerson", applyInfo.get("APPLY_DOCTOR_NAME"));
                        }
                    } else {
                        oper.put("applyTime", "-");
                        oper.put("applyPerson", "-");
                    }
                    operdays.add(oper);
                }
            }
            operdata.put("data", operdays);
            operAll.add(operdata);
        }
        operAfter.put("data", operAll);
        rs.add(operAfter);
    }

    @Override
    public Map<String, Object> getVisitTimeOperList(String oid, String patientId, String visitId,String admissionTime, int week, int cols) {
        // TODO Auto-generated method stub
        Map<String, Object> rs = new HashMap<String, Object>();
        int maxNum = 0;
        String begin = "";
        String end = "";
        if (week == 1 || week == 0) {
            begin = admissionTime;
        } else {
            begin = Utils.getDateAfter(Utils.strToDate(admissionTime, "yyyy-MM-dd"), 7 * (week - 1));
        }
        if (week == 0) {
            end = Utils.getDateAfter(Utils.strToDate(admissionTime, "yyyy-MM-dd"), cols);
        } else {
            end = Utils.getDateAfter(Utils.strToDate(admissionTime, "yyyy-MM-dd"), 7 * (week) - 1);
        }

        List<PropertyFilter> filters = new ArrayList<PropertyFilter>();
        filters.add(new PropertyFilter("OPER_START_TIME", MatchType.GE.getOperation(), begin));
        filters.add(new PropertyFilter("OPER_START_TIME", MatchType.LE.getOperation(), end));
        List<Map<String, String>> list = new ArrayList<>();
        ResultVo<PageResultVo<Map<String, String>>> resultVo = hbaseQueryClient.getPageByCondition(
                PageRequestBuilder.init()
                        .tableName(HdrTableEnum.HDR_OPER_ANAES.getCode())
                        .patientId(patientId)
                        .oid(oid)
                        .visitId(visitId)
                        .visitTypeCode("")
                        .filters(filters)
                        .pageNo(0)
                        .pageSize(0)
                        .orderBy("")
                        .desc()
                        .column("OPERATION_NAME", "OPER_START_TIME", "OPER_END_TIME", "SURGEN_NAME")
                        .build());
        if(resultVo.isSuccess()){
            list = resultVo.getContent().getResult();
        }
//        List<Map<String, String>> HDR_OPER_ANAES = hbaseDao.findConditionByPatient(HdrTableEnum.HDR_OPER_ANAES.getCode(), oid,
//                patientId, filters, new String[]{"OPERATION_NAME", "OPER_START_TIME", "OPER_END_TIME", "SURGEN_NAME"});
        //查询结果分组
        Map<String, List<Map<String, String>>> operfz = new HashMap<String, List<Map<String, String>>>();
        CivUtils.groupByDayDate(operfz, list, "OPER_START_TIME");
        //所有检验数据
        List<Map<String, Object>> operAll = new ArrayList<Map<String, Object>>();
        for (int i = 0; i < cols; i++) {
            Map<String, Object> operdata = new HashMap<String, Object>();
            operdata.put("date", (i + 1) + "");
            String key = Utils.getDateAfter(Utils.strToDate(begin, "yyyy-MM-dd"), i);
            //同一天的所有报告集合
            List<Map<String, Object>> operdays = new ArrayList<Map<String, Object>>();
            if (operfz.containsKey(key)) {
                //获取最大行数
                if (operfz.get(key).size() > maxNum) {
                    maxNum = operfz.get(key).size();
                }
                //循环同一天的报告
                for (Map<String, String> map : operfz.get(key)) {
                    Map<String, Object> oper = new HashMap<String, Object>();
                    oper.put("name", map.get("OPERATION_NAME"));
                    oper.put("operTime", map.get("OPER_START_TIME"));
                    oper.put("operPerson", map.get("SURGEN_NAME"));
                    oper.put("endTime", map.get("OPER_END_TIME"));
                    if (StringUtils.isNotBlank(map.get("ORDER_NO"))) {
                        Map<String, String> applyInfo = getOperApplyInfo(oid, patientId,visitId, map.get("ORDER_NO"));
                        if (applyInfo != null) {
                            oper.put("applyTime", applyInfo.get("APPLY_TIME"));
                            oper.put("applyPerson", applyInfo.get("APPLY_DOCTOR_NAME"));
                        }
                    } else {
                        oper.put("applyTime", "-");
                        oper.put("applyPerson", "-");
                    }
                    operdays.add(oper);
                }
            }
            operdata.put("date", (i + 1) + "");
            operdata.put("data", operdays);
            operAll.add(operdata);
        }
        rs.put("max", maxNum);
        rs.put("data", operAll);
        return rs;
    }


    private Map<String, String> getOperApplyInfo(String oid, String patientId, String visitId,String orderNo) {
        // TODO Auto-generated method stub
        Map<String, String> map = null;
        List<PropertyFilter> filters = new ArrayList<PropertyFilter>();
        filters.add(new PropertyFilter("ORDER_NO", MatchType.GE.getOperation(), orderNo));
        List<Map<String, String>> list = new ArrayList<>();
        ResultVo<PageResultVo<Map<String, String>>> resultVo = hbaseQueryClient.getPageByCondition(
                PageRequestBuilder.init()
                        .tableName(HdrTableEnum.HDR_OPER_APPLY.getCode())
                        .patientId(patientId)
                        .oid(oid)
                        .visitId(visitId)
                        .visitTypeCode("")
                        .filters(filters)
                        .pageNo(0)
                        .pageSize(0)
                        .orderBy("")
                        .desc()
                        .column("APPLY_DOCTOR_NAME", "APPLY_TIME")
                        .build());
        if(resultVo.isSuccess()){
            list = resultVo.getContent().getResult();
        }
//        List<Map<String, String>> list = hbaseDao.findConditionByPatient(HdrTableEnum.HDR_OPER_APPLY.getCode(), oid,
//                patientId, filters, new String[]{"APPLY_DOCTOR_NAME", "APPLY_TIME"});
        if (list.size() > 0) {
            map = list.get(0);
        }

        return map;
    }

    @Override
    public Map<String, Object> getVisitTimeDrugList(String oid, String patientId, String visitId,
                                                    String admissionTime, String mainDiag, String deptCode, String showMainIndicator, int week, int cols) {
        // TODO Auto-generated method stub
        Map<String, Object> rs = new HashMap<String, Object>();
        if ("0".equals(showMainIndicator)) {//不显示重点指标
            mainDiag = "";
            deptCode = "";
        }
        //长期医嘱
        List<Map<String, Object>> cqdrug = getCQDrugList(oid, patientId, visitId, mainDiag, deptCode, admissionTime, week, cols);
        rs.put("long", cqdrug);
        //临时医嘱
        Map<String, Object> lsdrug = getLSDrugList(oid, patientId, visitId, mainDiag, deptCode, admissionTime, week, cols);
        rs.put("short", lsdrug);
        return rs;
    }

    private Map<String, Object> getLSDrugList(String oid, String patientId, String visitId, String mainDiag, String deptCode,
                                              String admissionTime, int week, int cols) {
        // TODO Auto-generated method stub
        Map<String, Object> rs = new HashMap<String, Object>();
        int maxNum = 0;
        String begin = "";
        String end = "";
        if (week == 1 || week == 0) {
            begin = admissionTime;
        } else {
            begin = Utils.getDateAfter(Utils.strToDate(admissionTime, "yyyy-MM-dd"), 7 * (week - 1));
        }
        if (week == 0) {
            end = Utils.getDateAfter(Utils.strToDate(admissionTime, "yyyy-MM-dd"), cols);
        } else {
            end = Utils.getDateAfter(Utils.strToDate(admissionTime, "yyyy-MM-dd"), 7 * (week) - 1);
        }
        List<PropertyFilter> filters = new ArrayList<PropertyFilter>();
        //filters.add(new PropertyFilter("VISIT_ID", "STRING", MatchType.EQ.getOperation(), visitId));
        filters.add(new PropertyFilter("ORDER_TIME", MatchType.GE.getOperation(), begin));
        filters.add(new PropertyFilter("ORDER_TIME", MatchType.LE.getOperation(), end));
        filters.add(new PropertyFilter("ORDER_PROPERTIES_CODE", MatchType.EQ.getOperation(), "0"));
        filters.add(new PropertyFilter("ORDER_STATUS_NAME", MatchType.NOTIN.getOperation(), "撤销"));
        //查询专科数据构造查询条件
        if (StringUtils.isNotBlank(mainDiag) || StringUtils.isNotBlank(deptCode)) {
            List<Map<String, String>> list = specialtyViewPowerService.getSpecialtyConfig(oid, mainDiag, "Drag", deptCode);
            String itemCode = " ";
            for (Map<String, String> map : list) {
                if (StringUtils.isNotBlank(map.get("subItemCode"))) {
                    itemCode += map.get("subItemCode") + ",";
                }
            }
            itemCode = itemCode.substring(0, itemCode.length() - 1).trim();
            filters.add(new PropertyFilter("ORDER_ITEM_CODE", MatchType.IN.getOperation(), itemCode));

        }
        //静脉口服
        List<String> orderClass = CivUtils.getOrderClass(oid, "OrderClose_DRUG_KF");
        orderClass.addAll(CivUtils.getOrderClass(oid, "OrderClose_DRUG_JMZS"));
        if (orderClass != null && orderClass.size() > 0) {
            String orderClassString = "";
            for (String orderString : orderClass) {
                orderClassString += orderString + ",";
            }
            orderClassString = orderClassString.substring(0, orderClassString.length() - 1);
            filters.add(new PropertyFilter("ORDER_CLASS_CODE", MatchType.IN.getOperation(), orderClassString));
        }
        List<Map<String, String>> list = new ArrayList<>();
        ResultVo<PageResultVo<Map<String, String>>> resultVo = hbaseQueryClient.getPageByCondition(
                PageRequestBuilder.init()
                        .tableName(HdrTableEnum.HDR_IN_ORDER.getCode())
                        .patientId(patientId)
                        .oid(oid)
                        .visitId(visitId)
                        .visitTypeCode("")
                        .filters(filters)
                        .pageNo(0)
                        .pageSize(0)
                        .orderBy("")
                        .desc()
                        .column("ORDER_ITEM_NAME", "ORDER_TIME", "ORDER_PROPERTIES_NAME",
                                "DOSAGE_VALUE", "DOSAGE_UNIT", "PHARMACY_WAY_NAME", "FREQUENCY_NAME", "ORDER_BEGIN_TIME", "ORDER_END_TIME", "PARENT_ORDER_NO")
                        .build());
        if(resultVo.isSuccess()){
            list = resultVo.getContent().getResult();
        }
//        List<Map<String, String>> list = hbaseDao.findConditionByPatient(HdrTableEnum.HDR_IN_ORDER.getCode(), oid,
//                patientId, filters, new String[]{"ORDER_ITEM_NAME", "ORDER_TIME", "ORDER_PROPERTIES_NAME",
//                        "DOSAGE_VALUE", "DOSAGE_UNIT", "PHARMACY_WAY_NAME", "FREQUENCY_NAME", "ORDER_BEGIN_TIME", "ORDER_END_TIME", "PARENT_ORDER_NO"});
        //查询结果分组
        Map<String, List<Map<String, String>>> drugfz = new HashMap<String, List<Map<String, String>>>();
        CivUtils.groupByDayDate(drugfz, list, "ORDER_TIME");
        //所有临时药品数据
        List<Map<String, Object>> drugAll = new ArrayList<Map<String, Object>>();
        for (int i = 0; i < cols; i++) {
            Map<String, Object> drugdata = new HashMap<String, Object>();
            drugdata.put("date", (i + 1) + "");
            String key = Utils.getDateAfter(Utils.strToDate(begin, "yyyy-MM-dd"), i);
            //同一天的所有报告集合
            List<Map<String, Object>> drugdays = new ArrayList<Map<String, Object>>();
            if (drugfz.containsKey(key)) {
                //同一天的按父医嘱号分组数据分组
                Map<String, List<Map<String, String>>> drugGroup = getDrugGroupByParent(drugfz.get(key));
                //获取最大行数
                int count = 0;
                for (String str : drugGroup.keySet()) {
                    count++;
                }
                if (count > maxNum) {
                    maxNum = count;
                }
                //循环同一天的报告
                for (String drugkey : drugGroup.keySet()) {
                    Map<String, Object> drug = new HashMap<String, Object>();
                    String name = "";
                    List<Map<String, String>> dataList = new ArrayList<Map<String, String>>();
                    for (int j = 0; j < drugGroup.get(drugkey).size(); j++) {
                        Map<String, String> map = drugGroup.get(drugkey).get(j);
                        Map<String, String> map2 = new HashMap<String, String>();
                        if (j == 0)
                            name = map.get("ORDER_ITEM_NAME");
                        else
                            name = name + "," + map.get("ORDER_ITEM_NAME");

                        map2.put("orderTime", map.get("ORDER_TIME"));
                        map2.put("orderPropertyName", map.get("ORDER_PROPERTIES_NAME"));
                        map2.put("orderName", map.get("ORDER_ITEM_NAME"));
                        map2.put("orderValue", map.get("DOSAGE_VALUE"));
                        map2.put("orderUnit", map.get("DOSAGE_UNIT"));
                        map2.put("pharmacyWayName", map.get("PHARMACY_WAY_NAME"));
                        map2.put("frequencyName", map.get("FREQUENCY_NAME"));
                        map2.put("prescTime", map.get("ORDER_BEGIN_TIME"));
                        map2.put("orderEndTime", map.get("ORDER_END_TIME"));
                        dataList.add(map2);
                    }
                    drug.put("name", name);
                    drug.put("dataList", dataList);

                    drugdays.add(drug);
                }
            }
            drugdata.put("date", (i + 1) + "");
            drugdata.put("data", drugdays);
            drugAll.add(drugdata);
        }
        rs.put("max", maxNum);
        rs.put("data", drugAll);
        return rs;
    }

    private Map<String, List<Map<String, String>>> getDrugGroupByParent(
            List<Map<String, String>> drugs) {
        // TODO Auto-generated method stub
        Map<String, List<Map<String, String>>> rs = new HashMap<String, List<Map<String, String>>>();
        for (Map<String, String> map : drugs) {
            String key = map.get("PARENT_ORDER_NO") == null ? map.get("ORDER_NO") : map.get("PARENT_ORDER_NO");
            if (rs.get(key) == null) {
                List<Map<String, String>> list = new ArrayList<Map<String, String>>();
                list.add(map);
                rs.put(key, list);
            } else {
                rs.get(key).add(map);
            }
        }

        return rs;
    }

    private List<Map<String, Object>> getCQDrugList(String oid, String patientId, String visitId, String mainDiag, String deptCode,
                                                    String admissionTime, int week, int cols) {
        List<Map<String, Object>> rs = new ArrayList<Map<String, Object>>();
        String begin = "";
        String end = "";
        if (week == 1 || week == 0) {
            begin = admissionTime;
        } else {
            begin = Utils.getDateAfter(Utils.strToDate(admissionTime, "yyyy-MM-dd"), 7 * (week - 1));
        }
        if (week == 0) {
            end = Utils.getDateAfter(Utils.strToDate(admissionTime, "yyyy-MM-dd"), cols);
        } else {
            end = Utils.getDateAfter(Utils.strToDate(admissionTime, "yyyy-MM-dd"), 7 * (week) - 1);
        }

        List<PropertyFilter> filters = new ArrayList<PropertyFilter>();
        //filters.add(new PropertyFilter("VISIT_ID", "STRING", MatchType.EQ.getOperation(), visitId));
        filters.add(new PropertyFilter("ORDER_END_TIME", MatchType.GE.getOperation(), begin));
        filters.add(new PropertyFilter("ORDER_BEGIN_TIME", MatchType.LE.getOperation(), end));
        filters.add(new PropertyFilter("ORDER_PROPERTIES_CODE", MatchType.EQ.getOperation(), "1"));
        filters.add(new PropertyFilter("ORDER_STATUS_NAME", MatchType.NOTIN.getOperation(), "撤销"));
        //查询专科数据构造查询条件
        if (StringUtils.isNotBlank(mainDiag) || StringUtils.isNotBlank(deptCode)) {
            List<Map<String, String>> list = specialtyViewPowerService.getSpecialtyConfig(oid, mainDiag, "Drag", deptCode);
            String itemCode = " ";
            for (Map<String, String> map : list) {
                if (StringUtils.isNotBlank(map.get("subItemCode"))) {
                    itemCode += map.get("subItemCode") + ",";
                }
            }
            itemCode = itemCode.substring(0, itemCode.length() - 1).trim();
            filters.add(new PropertyFilter("ORDER_ITEM_CODE", MatchType.IN.getOperation(), itemCode));

        }
        //静脉口服
        List<String> orderClass = CivUtils.getOrderClass(oid, "OrderClose_DRUG_KF");
        orderClass.addAll(CivUtils.getOrderClass(oid, "OrderClose_DRUG_JMZS"));
        if (orderClass != null && orderClass.size() > 0) {
            String orderClassString = "";
            for (String orderString : orderClass) {
                orderClassString += orderString + ",";
            }
            orderClassString = orderClassString.substring(0, orderClassString.length() - 1);
            filters.add(new PropertyFilter("ORDER_CLASS_CODE", MatchType.IN.getOperation(), orderClassString));
        }
        List<Map<String, String>> list = new ArrayList<>();
        ResultVo<PageResultVo<Map<String, String>>> resultVo = hbaseQueryClient.getPageByCondition(
                PageRequestBuilder.init()
                        .tableName(HdrTableEnum.HDR_IN_ORDER.getCode())
                        .patientId(patientId)
                        .oid(oid)
                        .visitId(visitId)
                        .visitTypeCode("")
                        .filters(filters)
                        .pageNo(0)
                        .pageSize(0)
                        .orderBy("")
                        .desc()
                        .column("ORDER_ITEM_NAME", "ORDER_TIME", "ORDER_PROPERTIES_NAME",
                                "DOSAGE_VALUE", "DOSAGE_UNIT", "PHARMACY_WAY_NAME", "ORDER_STATUS_CODE", "FREQUENCY_NAME", "ORDER_BEGIN_TIME", "ORDER_END_TIME",
                                "PARENT_ORDER_NO", "ORDER_NO")
                        .build());
        if(resultVo.isSuccess()){
            list = resultVo.getContent().getResult();
        }
//        List<Map<String, String>> list = hbaseDao.findConditionByPatient(HdrTableEnum.HDR_IN_ORDER.getCode(), oid,
//                patientId, filters, new String[]{"ORDER_ITEM_NAME", "ORDER_TIME", "ORDER_PROPERTIES_NAME",
//                        "DOSAGE_VALUE", "DOSAGE_UNIT", "PHARMACY_WAY_NAME", "ORDER_STATUS_CODE", "FREQUENCY_NAME", "ORDER_BEGIN_TIME", "ORDER_END_TIME",
//                        "PARENT_ORDER_NO", "ORDER_NO"});
        //父医嘱号分组数据分组
        Map<String, List<Map<String, String>>> drugGroup = getDrugGroupByParent(list);
        //所有长期药品医嘱数据

        for (String key : drugGroup.keySet()) {
            Map<String, Object> map = new HashMap<String, Object>();
            List<Map<String, String>> drugs = drugGroup.get(key);
            String orderPropertyCode = "1";
            String isend = "";
            String name = "";
            String exeDays = "";
            List<Map<String, String>> cellList = new ArrayList<Map<String, String>>();
            List<Map<String, String>> data = new ArrayList<Map<String, String>>();
            for (int i = 0; i < drugs.size(); i++) {
                Map<String, String> map2 = new HashMap<String, String>();

                Map<String, String> m= drugs.get(i);
                map2.put("orderTime", m.get("ORDER_TIME"));
                map2.put("orderPropertyName", m.get("ORDER_PROPERTIES_NAME"));
                map2.put("orderName", m.get("ORDER_ITEM_NAME"));
                map2.put("orderValue", m.get("DOSAGE_VALUE"));
                map2.put("orderUnit", m.get("DOSAGE_UNIT"));
                map2.put("pharmacyWayName", m.get("PHARMACY_WAY_NAME"));
                map2.put("frequencyName", m.get("FREQUENCY_NAME"));
                map2.put("prescTime", m.get("ORDER_BEGIN_TIME"));
                map2.put("orderEndTime", m.get("ORDER_END_TIME"));
                if (i == 0) {
                    String orderStatusCode = StringUtils.isNotBlank(m.get("ORDER_STATUS_CODE")) ? m.get("ORDER_STATUS_CODE").trim() : "";
                    isend = orderStatusCode.equals("5") ? "0" : "1";
                    name = m.get("ORDER_ITEM_NAME");
                } else {
                    name = name + "," + m.get("ORDER_ITEM_NAME");
                }
                data.add(map2);
            }
            //获取医嘱执行信息
            exeDays = getDrugOrderExeInfo(oid, cellList, patientId,visitId, begin, drugs.get(0), cols);

            map.put("orderPropertyCode", orderPropertyCode);
            map.put("end", isend);
            map.put("name", name);
            map.put("exeDays", exeDays);
            map.put("cellList", cellList);
            map.put("data", data);

            rs.add(map);
        }
        return rs;
    }

    private String getDrugOrderExeInfo(String oid, List<Map<String, String>> cellList,
                                       String patientId, String visitId,String begin, Map<String, String> map, int cols) {
        // TODO Auto-generated method stub
        int count = 0;
        Map<String, Object> dates = new HashMap<String, Object>();
        List<PropertyFilter> filters = new ArrayList<PropertyFilter>();
        filters.add(new PropertyFilter("ORDER_NO", MatchType.EQ.getOperation(), map.get("ORDER_NO")));
        List<Map<String, String>> list = new ArrayList<>();
        ResultVo<PageResultVo<Map<String, String>>> resultVo = hbaseQueryClient.getPageByCondition(
                PageRequestBuilder.init()
                        .tableName(HdrTableEnum.HDR_ORDER_EXE.getCode())
                        .patientId(patientId)
                        .oid(oid)
                        .visitId(visitId)
                        .visitTypeCode("")
                        .filters(filters)
                        .pageNo(0)
                        .pageSize(0)
                        .orderBy("")
                        .desc()
                        .column("PRESC_TIME", "ORDER_NO")
                        .build());
        if(resultVo.isSuccess()){
            list = resultVo.getContent().getResult();
        }
//        List<Map<String, String>> list = hbaseDao.findConditionByPatient(HdrTableEnum.HDR_ORDER_EXE.getCode(), oid,
//                patientId, filters, new String[]{"PRESC_TIME", "ORDER_NO"});
        DateTimeFormatter dateTimeFormatter=DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        for (int i = 0; i < list.size(); i++) {
            if (StringUtils.isNotBlank(list.get(i).get("PRESC_TIME"))) {
                String PRESC_TIME = list.get(i).get("PRESC_TIME");
                LocalDateTime time = LocalDateTime.parse(PRESC_TIME, dateTimeFormatter);
                DateTimeFormatter df = DateTimeFormatter.ofPattern("yyyy-MM-dd");
                dates.put(df.format(time), list.get(i));
            }
        }
        SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd");
        String orderbigentime = df.format(Utils.strToDate(map.get("ORDER_BEGIN_TIME"), "yyyy-MM-dd"));
        for (int i = 0; i < cols; i++) {
            Map<String, String> map2 = new HashMap<String, String>();
            String key = Utils.getDateAfter(Utils.strToDate(begin, "yyyy-MM-dd"), i);
            if (key.equals(orderbigentime) && dates.isEmpty()) {
                map2.put("day", i + 1 + "");
                map2.put("selected", "true");
                cellList.add(map2);
                continue;
            }
            if (dates.containsKey(key)) {
                map2.put("day", i + 1 + "");
                map2.put("selected", "true");
            } else {
                map2.put("day", i + 1 + "");
                map2.put("selected", "false");
            }
            cellList.add(map2);
        }
        for (String key : dates.keySet()) {
            count++;
        }
        return count + "";
    }

    @Override
    public Map<String, Object> getVisitTimeCureList(String oid, String patientId, String visitId, String admissionTime, String mainDiag, String deptCode, int week, int cols) {

        Map<String, Object> rs = new HashMap<String, Object>();
        //长期医嘱
        List<Map<String, Object>> cqdrug = getCQCureList(oid, patientId, visitId, admissionTime, mainDiag, deptCode, week, cols);
        rs.put("long", cqdrug);
        //临时医嘱
        Map<String, Object> lsdrug = getLSCureList(oid, patientId, visitId, admissionTime, mainDiag, deptCode, week, cols);
        rs.put("short", lsdrug);
        return rs;

    }

    public List<Map<String, Object>> getCQCureList(String oid, String patientId, String visitId, String admissionTime, String mainDiag, String deptCode, int week, int cols) {
        List<Map<String, Object>> rs = new ArrayList<Map<String, Object>>();
        String begin = "";
        String end = "";
        if (week == 1 || week == 0) {
            begin = admissionTime;
        } else {
            begin = Utils.getDateAfter(Utils.strToDate(admissionTime, "yyyy-MM-dd"), 7 * (week - 1));
        }
        if (week == 0) {
            end = Utils.getDateAfter(Utils.strToDate(admissionTime, "yyyy-MM-dd"), cols);
        } else {
            end = Utils.getDateAfter(Utils.strToDate(admissionTime, "yyyy-MM-dd"), 7 * (week) - 1);
        }
        List<PropertyFilter> filters = new ArrayList<PropertyFilter>();
        //filters.add(new PropertyFilter("VISIT_ID", "STRING", MatchType.EQ.getOperation(), visitId));
        List<String> orderClass = CivUtils.getOrderClass(oid, "OrderClose_CURE");
        if (orderClass != null && orderClass.size() > 0) {
            String orderClassString = "";
            for (String orderString : orderClass) {
                orderClassString += orderString + ",";
            }
            orderClassString = orderClassString.substring(0, orderClassString.length() - 1);
            filters.add(new PropertyFilter("ORDER_CLASS_CODE", MatchType.IN.getOperation(), orderClassString));
        }
        filters.add(new PropertyFilter("ORDER_PROPERTIES_NAME", MatchType.IN.getOperation(), "长期"));

        //是否只查询专科数据
        if (StringUtils.isNotBlank(mainDiag) || StringUtils.isNotBlank(deptCode)) {
            List<Map<String, String>> list = specialtyViewPowerService.getSpecialtyConfig(oid, mainDiag, "NurseAndEat", deptCode);
            String itemCode = " ";
            for (Map<String, String> map : list) {
                if (StringUtils.isNotBlank(map.get("subItemCode"))) {
                    itemCode += map.get("subItemCode") + ",";
                }
            }
//			list =  specialtyViewPowerService.getSpecialtyConfig(mainDiag, "Drag",deptCode);
//			for (Map<String, String> map : list) {
//				if (StringUtils.isNotBlank(map.get("subItemCode"))) {
//					itemCode += map.get("subItemCode") + ",";
//                }
//            }
            itemCode = itemCode.substring(0, itemCode.length() - 1).trim();
            filters.add(new PropertyFilter("ORDER_ITEM_CODE", MatchType.IN.getOperation(), itemCode));

        }
        List<Map<String, String>> list = new ArrayList<>();
        ResultVo<PageResultVo<Map<String, String>>> resultVo = hbaseQueryClient.getPageByCondition(
                PageRequestBuilder.init()
                        .tableName(HdrTableEnum.HDR_IN_ORDER.getCode())
                        .patientId(patientId)
                        .oid(oid)
                        .visitId(visitId)
                        .visitTypeCode("")
                        .filters(filters)
                        .pageNo(0)
                        .pageSize(0)
                        .orderBy("")
                        .desc()
                        .column()
                        .build());
        if(resultVo.isSuccess()){
            list = resultVo.getContent().getResult();
        }
//        List<Map<String, String>> list = hbaseDao.findConditionByPatient(HdrTableEnum.HDR_IN_ORDER.getCode(), oid, patientId,
//                filters);
        //父医嘱号分组数据分组
        Map<String, List<Map<String, String>>> drugGroup = getDrugGroupByParent(list);
        //所有长期药品医嘱数据
        List<Map<String, Object>> drugAll = new ArrayList<Map<String, Object>>();
        for (String key : drugGroup.keySet()) {
            Map<String, Object> map = new HashMap<String, Object>();
            List<Map<String, String>> drugs = drugGroup.get(key);
            String orderPropertyCode = "1";
            String isend = "";
            String name = "";
            String exeDays = "";
            List<Map<String, String>> cellList = new ArrayList<Map<String, String>>();
            List<Map<String, String>> data = new ArrayList<Map<String, String>>();
            for (int i = 0; i < drugs.size(); i++) {
                Map<String, String> map2 = new HashMap<String, String>();
                map2.put("order_time", drugs.get(i).get("ORDER_TIME"));//医嘱时间
                map2.put("order_item_name", drugs.get(i).get("ORDER_ITEM_NAME"));//医嘱项
                map2.put("order_properties_name", drugs.get(i).get("ORDER_PROPERTIES_NAME"));//医嘱类型
                map2.put("presc_dept_code", drugs.get(i).get("PRESC_DEPT_CODE"));//执行人
                map2.put("order_begin_time", drugs.get(i).get("ORDER_BEGIN_TIME"));//执行时间
                map2.put("order_end_time", drugs.get(i).get("ORDER_END_TIME"));//结束时间
                if (i == 0) {
                    isend = drugs.get(i).get("ORDER_STATUS_CODE").equals("5") ? "0" : "1";
                    name = drugs.get(i).get("ORDER_ITEM_NAME");
                } else {
                    name = name + "," + drugs.get(i).get("ORDER_ITEM_NAME");
                }
                data.add(map2);
            }
            //获取医嘱执行信息
            exeDays = getDrugOrderExeInfo(oid, cellList, patientId, visitId,begin, drugs.get(0), cols);

            map.put("orderPropertyCode", orderPropertyCode);
            map.put("end", isend);
            map.put("name", name);
            map.put("exeDays", exeDays);
            map.put("cellList", cellList);
            map.put("data", data);

            rs.add(map);
        }
        return rs;
    }

    public Map<String, Object> getLSCureList(String oid, String patientId, String visitId, String admissionTime, String mainDiag, String deptCode, int week, int cols) {
        Map<String, Object> rs = new HashMap<String, Object>();
        int maxNum = 0;
        String begin = "";
        String end = "";
        if (week == 1 || week == 0) {
            begin = admissionTime;
        } else {
            begin = Utils.getDateAfter(Utils.strToDate(admissionTime, "yyyy-MM-dd"), 7 * (week - 1));
        }
        if (week == 0) {
            end = Utils.getDateAfter(Utils.strToDate(admissionTime, "yyyy-MM-dd"), cols);
        } else {
            end = Utils.getDateAfter(Utils.strToDate(admissionTime, "yyyy-MM-dd"), 7 * (week) - 1);
        }

        List<PropertyFilter> filters = new ArrayList<PropertyFilter>();
        //filters.add(new PropertyFilter("VISIT_ID", "STRING", MatchType.EQ.getOperation(), visitId));
        filters.add(new PropertyFilter("ORDER_CLASS_NAME", MatchType.IN.getOperation(), "治疗类"));
        filters.add(new PropertyFilter("ORDER_PROPERTIES_NAME", MatchType.IN.getOperation(), "临时"));

        //是否只查询专科数据
        if (StringUtils.isNotBlank(mainDiag) || StringUtils.isNotBlank(deptCode)) {
            List<Map<String, String>> list = specialtyViewPowerService.getSpecialtyConfig(oid, mainDiag, "LabDetails", deptCode);
            String itemCode = " ";
            for (Map<String, String> map : list) {
                if (StringUtils.isNotBlank(map.get("subItemCode"))) {
                    itemCode += map.get("subItemCode") + ",";
                }
            }
            list = specialtyViewPowerService.getSpecialtyConfig(oid, mainDiag, "Drugs", deptCode);
            for (Map<String, String> map : list) {
                if (StringUtils.isNotBlank(map.get("subItemCode"))) {
                    itemCode += map.get("subItemCode") + ",";
                }
            }


            itemCode = itemCode.substring(0, itemCode.length() - 1).trim();
            filters.add(new PropertyFilter("ORDER_ITEM_CODE", MatchType.IN.getOperation(), itemCode));

        }
        List<Map<String, String>> list = new ArrayList<>();
        ResultVo<PageResultVo<Map<String, String>>> resultVo = hbaseQueryClient.getPageByCondition(
                PageRequestBuilder.init()
                        .tableName(HdrTableEnum.HDR_IN_ORDER.getCode())
                        .patientId(patientId)
                        .oid(oid)
                        .visitId(visitId)
                        .visitTypeCode("")
                        .filters(filters)
                        .pageNo(0)
                        .pageSize(0)
                        .orderBy("")
                        .desc()
                        .column()
                        .build());
        if(resultVo.isSuccess()){
            list = resultVo.getContent().getResult();
        }
//        List<Map<String, String>> list = hbaseDao.findConditionByPatient(HdrTableEnum.HDR_IN_ORDER.getCode(), oid, patientId,
//                filters);
        //查询结果分组
        Map<String, List<Map<String, String>>> drugfz = new HashMap<String, List<Map<String, String>>>();
        CivUtils.groupByDayDate(drugfz, list, "ORDER_TIME");
        //所有临时药品数据
        List<Map<String, Object>> drugAll = new ArrayList<Map<String, Object>>();
        for (int i = 0; i < cols; i++) {
            Map<String, Object> drugdata = new HashMap<String, Object>();
            drugdata.put("date", (i + 1) + "");
            String key = Utils.getDateAfter(Utils.strToDate(begin, "yyyy-MM-dd"), i);
            //同一天的所有报告集合
            List<Map<String, Object>> drugdays = new ArrayList<Map<String, Object>>();
            if (drugfz.containsKey(key)) {
                //同一天的按父医嘱号分组数据分组
                Map<String, List<Map<String, String>>> drugGroup = getDrugGroupByParent(drugfz.get(key));
                //获取最大行数
                if (drugfz.get(key).size() > maxNum) {
                    maxNum = drugfz.get(key).size();
                }
                //循环同一天的报告
                for (String drugkey : drugGroup.keySet()) {
                    Map<String, String> map = drugGroup.get(drugkey).get(0);
                    Map<String, Object> drug = new HashMap<String, Object>();
                    String name = "";
                    List<Map<String, String>> dataList = new ArrayList<Map<String, String>>();
                    for (int j = 0; j < drugGroup.get(drugkey).size(); j++) {
                        Map<String, String> map2 = new HashMap<String, String>();
                        if (j == 0)
                            name = drugGroup.get(drugkey).get(j).get("ORDER_ITEM_NAME");
                        else
                            name = name + "," + drugGroup.get(drugkey).get(j).get("ORDER_ITEM_NAME");

                        map2.put("order_time", map.get("ORDER_TIME"));//医嘱时间
                        map2.put("order_item_name", map.get("ORDER_ITEM_NAME"));//医嘱项
                        map2.put("order_properties_name", map.get("ORDER_PROPERTIES_NAME"));//医嘱类型
                        map2.put("presc_dept_code", map.get("PRESC_DEPT_CODE"));//执行人
                        map2.put("order_begin_time", map.get("ORDER_BEGIN_TIME"));//执行时间
                        map2.put("order_end_time", map.get("ORDER_END_TIME"));//结束时间

                        /*map2.put("orderTime", map.get("ORDER_TIME"));
						map2.put("orderPropertyName", map.get("ORDER_PROPERTIES_NAME"));
						map2.put("orderName", map.get("ORDER_ITEM_NAME"));
						map2.put("orderValue", map.get("DOSAGE_VALUE"));
						map2.put("orderUnit", map.get("DOSAGE_UNIT"));
						map2.put("pharmacyWayName", map.get("PHARMACY_WAY_NAME"));
						map2.put("frequencyName", map.get("FREQUENCY_NAME"));
						map2.put("prescTime", map.get("ORDER_BEGIN_TIME"));
						map2.put("orderEndTime", map.get("ORDER_END_TIME"));
						*/
                        dataList.add(map2);
                    }
                    drug.put("name", name);
                    drug.put("dataList", dataList);

                    drugdays.add(drug);
                }
            }
            drugdata.put("date", (i + 1) + "");
            drugdata.put("data", drugdays);
            drugAll.add(drugdata);
        }
        rs.put("max", maxNum);
        rs.put("data", drugAll);
        return rs;

    }

    @Override
    public List<Map<String, Object>> getVisitTimePat_AdtList(String oid, String patientId, String visitId) {
        Map<String, Object> rs = new HashMap<String, Object>();
        /*int maxNum = 0;
		String begin = "";
		String end = "";
		if (week == 1) {
			begin = admissionTime;
		} else {
			begin = Utils.getDateAfter(Utils.strToDate(admissionTime, "yyyy-MM-dd"), 7 * (week - 1));
		}
		end = Utils.getDateAfter(Utils.strToDate(admissionTime, "yyyy-MM-dd"), 7 * (week) - 1);*/

        List<PropertyFilter> filters = new ArrayList<PropertyFilter>();
        //filters.add(new PropertyFilter("VISIT_ID", "STRING", MatchType.EQ.getOperation(), visitId));
        filters.add(new PropertyFilter("TRANS_NO", MatchType.EQ.getOperation(), "1"));
        List<Map<String, String>> list = new ArrayList<>();
        ResultVo<PageResultVo<Map<String, String>>> resultVo = hbaseQueryClient.getPageByCondition(
                PageRequestBuilder.init()
                        .tableName(HdrTableEnum.HDR_PAT_ADT.getCode())
                        .patientId(patientId)
                        .oid(oid)
                        .visitId(visitId)
                        .visitTypeCode("")
                        .filters(filters)
                        .pageNo(0)
                        .pageSize(0)
                        .orderBy("")
                        .desc()
                        .column()
                        .build());
        if(resultVo.isSuccess()){
            list = resultVo.getContent().getResult();
        }
//        List<Map<String, String>> list = hbaseDao.findConditionByPatient(HdrTableEnum.HDR_PAT_ADT.getCode(), oid, patientId,
//                filters);
        if (list.isEmpty()) {
            filters.clear();
            //filters.add(new PropertyFilter("VISIT_ID", "STRING", MatchType.EQ.getOperation(), visitId));
            ResultVo<PageResultVo<Map<String, String>>> resultVo1 = hbaseQueryClient.getPageByCondition(
                    PageRequestBuilder.init()
                            .tableName(HdrTableEnum.HDR_PAT_ADT.getCode())
                            .patientId(patientId)
                            .oid(oid)
                            .visitId(visitId)
                            .visitTypeCode("")
                            .filters(filters)
                            .pageNo(0)
                            .pageSize(0)
                            .orderBy("")
                            .desc()
                            .column()
                            .build());
            if(resultVo1.isSuccess()){
                list = resultVo1.getContent().getResult();
            }
            // = hbaseDao.findConditionByPatient(HdrTableEnum.HDR_PAT_ADT.getCode(), oid, patientId, filters);
        }

        List<Map<String, Object>> mrdays = new ArrayList<Map<String, Object>>();
        for (Map<String, String> map : list) {
            //if (map.get("TRANS_NO").equals("0")) {//入院或出院
            if (map.get("ADMISSION_TIME") != null) {
                Map<String, Object> mr = new HashMap<String, Object>();
                mr.put("nodeName", "入院");
                mr.put("keshi", map.get("DEPT_ADMISSION_TO_NAME"));//入院科室
                mr.put("bingqu", map.get("DISTRICT_ADMISSION_TO_NAME"));//入院病区
                mr.put("showTime", map.get("ADMISSION_TIME"));
                mrdays.add(mr);
            }
            if (map.get("TRANS_NO").equals("1")) {//转科
                Map<String, Object> mr = new HashMap<String, Object>();
                mr.put("nodeName", "转科");
                mr.put("keshi", map.get("CURR_DEPT_NAME"));//转入科室
                mr.put("bingqu", map.get("CURR_DISTRICT_NAME"));//转入病区
                mr.put("showTime", map.get("TRANSFER_TIME"));
                mrdays.add(mr);
            }
            if (map.get("DISCHARGE_TIME") != null) {//出院时间大于入院时间证明是出院
                Map<String, Object> mr = new HashMap<String, Object>();
                mr.put("nodeName", "出院");
                mr.put("keshi", map.get("DEPT_DISCHARGE_FROM_NAME"));//出院科室
                mr.put("bingqu", map.get("DISTRICT_DISCHARGE_FROM_NAME"));//出院病区
                mr.put("showTime", map.get("DISCHARGE_TIME"));//出院时间
                mrdays.add(mr);
            }
        }
        DateTimeFormatter dateTimeFormatter=DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        //时间排序从小到大
        mrdays.sort((o1, o2) -> {
            String time1 = o1.get("showTime").toString();
            LocalDateTime date1 = LocalDateTime.parse(time1, dateTimeFormatter);
            String time2 = o2.get("showTime").toString();
            LocalDateTime date2 = LocalDateTime.parse(time2, dateTimeFormatter);
            return date1.compareTo(date2);
        });
        return mrdays;
    }

    @Override
    public List<Map<String, String>> getConfigList(String oid, String usercode,
                                                   String patientid, String visittype, String visitid) {
        // TODO Auto-generated method stub
        List<Map<String, String>> rs = new ArrayList<Map<String, String>>();
        QueryWrapper<TimeaxisConfig> wrapper = new QueryWrapper<>();
        wrapper.eq("usercode", usercode).eq("patientid", patientid).eq("visittype", visittype).eq("visitid", visitid);
        List<TimeaxisConfig> configs = timeaxisConfigMapper.selectList(wrapper);
//        List<TimeaxisConfig> configs = axisConfigDao.getConfigList(usercode, oid, patientid, visittype, visitid, "");
        for (TimeaxisConfig config : configs) {
            Map<String, String> map = new HashMap<String, String>();
            map.put("id", config.getId().toString());
            map.put("subItemCode", config.getSubitemcode());
            map.put("subItemName", config.getSubitemname());
            rs.add(map);
        }
        return rs;
    }

    /**
     * 获取专科视图配置的重点检验
     *
     * @param mainDiag
     * @param deptCode
     * @return
     */
    @Override
    public List<Map<String, String>> getConfigConfigList(String oid, String mainDiag, String deptCode) {
        List<Map<String, String>> rs = new ArrayList<Map<String, String>>();
        List<Map<String, String>> list = specialtyViewPowerService.getSpecialtyConfig(oid, mainDiag, "LabDetails", deptCode);
        for (Map<String, String> config : list) {
            Map<String, String> map = new HashMap<String, String>();
            map.put("subItemCode", config.get("subItemCode"));
            map.put("subItemName", config.get("subItemName"));
            rs.add(map);
        }
        return rs;
    }

    @Override
    public Map<String, String> insertConfig(String oid, TimeaxisConfig config) {
        // TODO Auto-generated method stub
        Map<String, String> rs = new HashMap<String, String>();
        QueryWrapper<TimeaxisConfig> wrapper = new QueryWrapper<>();
        wrapper.eq("usercode", config.getUsercode()).eq("patientid", config.getPatientid()).eq("visittype", config.getVisittype()).eq("visitid", config.getVisitid()).eq("subitemcode", config.getSubitemcode());
        List<TimeaxisConfig> list = timeaxisConfigMapper.selectList(wrapper);
//        List<TimeAxisConfig> list = axisConfigDao.getConfigList(config.getUsercode(), oid, config.getPatientid(),
//                config.getVisittype(), config.getVisitid(), config.getSubitemcode());
        if (list.size() > 0) {
            rs.put("result", "0");
            rs.put("msg", "[" + config.getSubitemname() + "]" + "检验项已添加。");
            return rs;
        }
        if (timeaxisConfigMapper.insert(config) == 1) {
            rs.put("result", "1");
            rs.put("msg", "[" + config.getSubitemname() + "]" + "检验项添加成功。");
        } else {
            rs.put("result", "0");
            rs.put("msg", "[" + config.getSubitemname() + "]" + "检验项添加失败。");
        }
        return rs;
    }

    @Override
    public boolean deleteConfig(String oid, String id) {
        // TODO Auto-generated method stub
        if (timeaxisConfigMapper.deleteById(id) == 1)
            return true;
        else return false;
    }

    @Override
    public Map<String, Object> getInspectItemLine(String oid, String patientid, String visittype,
                                                  String visitid, String subitemcode, String admissionTime, int week, int cols) {
        // TODO Auto-generated method stub
        Map<String, Object> rs = new HashMap<String, Object>();
        //过滤条件
        List<PropertyFilter> filters = new ArrayList<PropertyFilter>();
        //细项编码
        filters.add(new PropertyFilter("LAB_SUB_ITEM_CODE", MatchType.EQ.getOperation(), subitemcode));
        //filters.add(new PropertyFilter("VISIT_ID", "STRING", MatchType.EQ.getOperation(), visitid));
        //时间条件
        if (StringUtils.isNotBlank(visittype)) {
            filters.add(new PropertyFilter("VISIT_TYPE_CODE", MatchType.GE.getOperation(), visittype));
        }
        List<Map<String, String>> list = new ArrayList<>();
        ResultVo<PageResultVo<Map<String, String>>> resultVo = hbaseQueryClient.getPageByCondition(
                PageRequestBuilder.init()
                        .tableName(HdrTableEnum.HDR_LAB_REPORT_DETAIL.getCode())
                        .patientId(patientid)
                        .oid(oid)
                        .visitId(visitid)
                        .visitTypeCode(visittype)
                        .filters(filters)
                        .pageNo(0)
                        .pageSize(0)
                        .orderBy("")
                        .desc()
                        .column("REPORT_TIME", "LAB_QUAL_RESULT", "LAB_RESULT_UNIT", "LAB_RESULT_VALUE",
                                "LAB_SUB_ITEM_NAME", "LAB_SUB_ITEM_CODE", "RANGE")
                        .build());
        if(resultVo.isSuccess()){
            list = resultVo.getContent().getResult();
        }
//        List<Map<String, String>> list = hbaseDao.findConditionByPatient(HdrTableEnum.HDR_LAB_REPORT_DETAIL.getCode(), oid,
//                patientid, filters, "REPORT_TIME", "LAB_QUAL_RESULT", "LAB_RESULT_UNIT", "LAB_RESULT_VALUE",
//                "LAB_SUB_ITEM_NAME", "LAB_SUB_ITEM_CODE", "RANGE");

        Map<String, List<Map<String, String>>> labsubs = new HashMap<String, List<Map<String, String>>>();
        DateTimeFormatter dateTimeFormatter=DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        DateTimeFormatter df = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        for (int i = 0; i < list.size(); i++) {
            if (StringUtils.isNotBlank(list.get(i).get("REPORT_TIME"))) {
                String PRESC_TIME = list.get(i).get("REPORT_TIME");
                LocalDateTime time = LocalDateTime.parse(PRESC_TIME,dateTimeFormatter);
                if (labsubs.containsKey(df.format(time))) {
                    labsubs.get(df.format(time)).add(list.get(i));
                } else {
                    List<Map<String, String>> list2 = new ArrayList<Map<String, String>>();
                    list2.add(list.get(i));
                    labsubs.put(df.format(time), list2);
                }
            }
        }
        String unit = "";
        String name = "";
        List<Object> data = new ArrayList<Object>();
        for (int i = 0; i < cols; i++) {
            if (week == 0)
                week = 1;
            String time = Utils.getDateAfter(Utils.strToDate(admissionTime, "yyyy-MM-dd"), (week - 1) * 7 + i);
            if (labsubs.containsKey(time)) {
                //一天中所有测量数据
                List<Map<String, String>> pulseList = (List<Map<String, String>>) labsubs.get(time);
                Map<String, Map<String, String>> map = groupByDateHour(pulseList, 6, "REPORT_TIME");

                for (int j = 0; j < 6; j++) {
                    if (map.containsKey(j + "")) {
                        unit = map.get(j + "").get("LAB_RESULT_UNIT") == null ? "" : map.get(j + "").get("LAB_RESULT_UNIT");
                        name = map.get(j + "").get("LAB_SUB_ITEM_NAME") == null ? "" : map.get(j + "").get("LAB_SUB_ITEM_NAME");
                        String value = map.get(j + "").get("LAB_RESULT_VALUE") == null ? "" : map.get(j + "").get("LAB_RESULT_VALUE");
                        data.add("-".equals(value) || value.equals("") ? "" : (Double.valueOf(value) > 0 ? Double.valueOf(value) : ""));
                    } else {
                        data.add("");
                    }
                }
            } else {
                for (int j = 0; j < 6; j++) {
                    data.add("");
                }
            }
        }
        //单位
        rs.put("unit", unit);
        List<Map<String, Object>> series = new ArrayList<Map<String, Object>>();
        Map<String, Object> serie = new HashMap<String, Object>();
        serie.put("name", name);
        serie.put("data", data);
        series.add(serie);
        rs.put("series", series);

        return rs;
    }
}
