package com.goodwill.hdr.civ.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.goodwill.hdr.civ.entity.LogRecord;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-03-03
 */
@Mapper
public interface LogRecordMapper extends BaseMapper<LogRecord> {

    List<Map<String, Object>> selectMonitorLogAccordingDept(@Param("oid") String oid, @Param("beginDate") String beginDate, @Param("endDate") String endDate,
                                                            @Param("pageCode") String pageCode);

    List<Map<String, Object>> selectMonitorLogAccordingDoctor(@Param("oid") String oid, @Param("beginDate") String beginDate, @Param("endDate") String endDate,
                                                              @Param("pageCode") String pageCode, @Param("value") String value);

    List<Map<String, Object>> selectTimeMonitorLogAccordingDeptTimeNearOneYear(@Param("oid") String oid, @Param("deptCode") String deptCode, @Param("dateNow") String dateNow, @Param("pageCode") String pageCode);

    List<Map<String, Object>> selectTimeMonitorLogAccordingDeptTimeNearOneMonth(@Param("oid") String oid, @Param("deptCode") String deptCode, @Param("dateNow") String dateNow, @Param("pageCode") String pageCode);

    List<Map<String, Object>> selectTimeMonitorLogAccordingDoctorTimeNearOneYear(@Param("oid") String oid, @Param("deptCode") String deptCode, @Param("dateNow") String dateNow,
                                                                                 @Param("pageCode") String pageCode, @Param("userCode") String userCode);

    List<Map<String, Object>> selectTimeMonitorLogAccordingDoctorTimeNearOneMonth(@Param("oid") String oid, @Param("deptCode") String deptCode, @Param("dateNow") String dateNow,
                                                                                  @Param("pageCode") String pageCode, @Param("userCode") String userCode);
}

