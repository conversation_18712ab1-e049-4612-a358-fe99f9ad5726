package com.goodwill.hdr.civ.service;



import com.goodwill.hdr.civ.entity.SysConfig;
import com.goodwill.hdr.civ.vo.NameAndCodeVo;
import com.goodwill.hdr.core.orm.Page;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Description 类描述：系统设置
 * @Date 2018年5月2日
 * @modify 修改记录：
 */
public interface PowerService {

    //判断是不是管理员用户

    /**
     * @return
     * @Description 获取全局设置
     */
    public Map<String, String> getCheckAdmin(String oid, String userCode);

    //全局设置s

    /**
     * 获取全局设置
     *
     * @return
     */
    public List<SysConfig> getSysConfig(String oid);

    /**
     * 获取脱敏设置
     *
     * @return
     */
    List<SysConfig> getSysConfigForHide(String oid);

    /**
     * 获取全局设置
     *
     * @return
     */
    public Map<String, String> getSysConfigByType(String oid, String configCode);

    /**
     * 修改全局设置
     *
     * @return
     */
    public boolean updateSysConfig(String oid, String configCode, String configValue);
    //用户权限设置

    /**
     * 获取用户当前视图权限
     *
     * @return
     */
    public Map<String, String> getPowerConfigByPage(String oid, String userCode);

    /**
     * 获取用户就诊视图权限
     *
     * @return
     */
    public List<Map<String, Object>> getPowerConfigByVisit(String oid, String userCode);

    /**
     * 获取用户分类视图权限
     *
     * @return
     */
    List<Map<String, Object>> getPowerConfigByCategory(String oid, String userCode);

    /**
     * 获取用户查看病历类型权限
     *
     * @return
     */
    public Map<String, Object> getPowerConfigByEMR(String oid, String userCode);

    /**
     * @return
     * @Description 方法描述: 获取用户查看检查报告类型权限
     */
    public Map<String, Object> getPowerConfigByExam(String oid, String userCode);

    /**
     * @param userCode
     * @return
     * @Description 方法描述: 获取用户所有权限
     */
    public Map<String, Object> getPowerConfigByUser(String oid, String userCode);
    public List<Map<String, String>> getVipConfigByUser(String oid, String userCode);

    /**
     * @return
     * @Description 方法描述: 获取科室所有权限
     */
    public Map<String, Object> getPowerConfigByDept(String oid, String deptCode);
    public  List<Map<String, String>> getVipConfigByDept(String oid, String deptCode);

    /**
     * @Description 方法描述: 修改用户权限
     */
    public Map<String, String> updatePowerConfigByUser(String oid, String userCodes, String Current, String Specialty,
                                                       String TimeAxis, String Visit, String Category, String Mr, String Exam, String Pathology,
                                                       String specialtyTimeAxis, String medicalView,String userOid);

    /**
     * @param Current
     * @param Visit
     * @param Pathology
     * @return
     * @Description 方法描述: 修改用户权限
     */
    public Map<String, String> updatePowerConfigByDept(String oid, String deptCodes, String Current, String Specialty,
                                                       String TimeAxis, String Visit, String Category, String Mr, String Exam, String Pathology,
                                                       String SpecialtyTimeAxis, String medicalView);

    /**
     * @param userName
     * @param deptCode
     * @param pageNo
     * @param pageSize
     * @return
     * @Description 方法描述: 查询用户列表
     */
    public Map<String, Object> getUserList(String oid, String userName, String deptCode, int pageNo, int pageSize);

    /**
     * @param pageNo
     * @param pageSize
     * @return
     * @Description 方法描述: 查询部门列表
     */
    public Page<Map<String, Object>> getDeptCodeList(String oid, String deptName, int pageNo, int pageSize);

    /**
     * @return
     * @Description 方法描述: 初始化用户所有权限
     */
    public Map<String, String> initDeptPower(String oid);

    /**
     * 获取脱敏配置字段
     *
     * @return
     */
    List<Map<String, Object>> getInfoHiddenField(String oid, String code);

    Page<Map<String, String>> getInfoHiddenDict(String oid, String keyWord,int pageNo,int pageSize);

    /**
     * 更新脱敏配置字段
     *
     * @return
     */
    boolean updateSysHideConfig(String oid, String code, String value, String enabled, String fields, String maskRuleCode, String maskRuleName);



    List<Map<String, String>> getInfoHidden(List<Map<String, String>> info);

    /**
     * @return
     * @Description 方法描述: 获取用户查看病理报告类型权限
     */
    public Map<String, Object> getPowerConfigByPathology(String oid, String userCode);


    /**
     * 获取通用配置
     *
     * @return
     */
    Map<String, Object> getCommonConfig();


    /**
     * 获取通知配置
     *
     * @return
     */
    Map<String, Object> getNoticeConfig(String oid);

    /**
     * 获取患者列表页配置
     *
     * @return
     */
    Map<String, Object> getPatListConfig();

    /**
     * 获取当前视图配置
     *
     * @return
     */
    Map<String, Object> getCurrentConfig(String oid);

    /**
     * 获取就诊试图配置
     *
     * @param userCode
     * @return
     */
    Map<String, Object> getVisitConfig(String oid, String userCode);


    /**
     * 获取就诊试图配置
     *
     * @param userCode
     * @return
     */
    Map<String, Object> getCatagoryConfig( String userCode, String patientId, String visitId);

    String getUserName(String oid, String userCode);

    /**
     * 获取当前医院集团的oid集合
     *
     * @return
     */

    boolean deleteSysHideConfig(String configCode,String fields);

    boolean insertSysHideConfig( String configName, String configValue, String fields, String maskRuleCode, String maskRuleName);

    List<Map<String, String>> getOidConfigByUserCode(String userCode);

    List<Map<String, String>> getOidConfigByDeptCode(String deptCode);

    List<NameAndCodeVo> getVisitStatusDict();

    String getSingleloginUser(String oid, String jhTicket);
    Map<String, String> updateVipPatientDataRight(String oid, String userCodes,String configValue,String deptCode);

    /**
     * 获取脱敏规则
     */
    Map<String, List<Map<String, Object>>> getMaskRules(String oid);

    /**
     * List<Map>获取脱敏数据
     *
     * @return
     */
    List<Map<String, String>> getInfoHiddenByMaskRule(List<Map<String, String>> info);

    /**
     * 获取用户配置的脱敏字段
     * @param userCode
     * @return
     */
    Map<String, Object> getSysHideConfigByUser(String oid,String userCode);

    /**
     * @param userList eg:[{"oid":"42661889.5","userCode":"test_operator/emr"}]
     * @param dataMasking
     * @return
     */
    boolean insertFiledUser(String userList, String dataMasking);

    /**
     * 保存用户与脱敏字段的映射关系
     * @param oid
     * @param userCode
     * @param userCode
     * @return
     */
    boolean insertFiledUser( String oid,String userCode,String dataMasking);

    /**
     * 获取当前用户脱敏字段配置信息
     */
    Map<String, Map<String, Object>> getMaskConfigForCurrentUser();

    /**
     * 根据配置 进行某字段值脱敏
     */
    String maskFiledValue(String fieldValue,Map<String, Object> ruleMap);

    /**
     * 20250116 第三方单点登录对接调用接口
     *
     * @param url
     * @return
     */
    String getSingleloginUserByOther(String oid, String param);

}
