package com.goodwill.hdr.civ.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.goodwill.hdr.security.priority.entity.DeptUserEntity;
import com.goodwill.hdr.security.priority.vo.QueryParamVO;
import com.goodwill.hdr.security.priority.vo.SecurityDeptUserVO;
import com.goodwill.hdr.web.core.mapper.HdrBaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;
import java.util.Map;


/**
 * <AUTHOR>
 * @Date 2022/03/22
 * @Description security_dept_user表mapper
 */
@Mapper
@DS("security")
public interface SecurityDeptUserMapper extends HdrBaseMapper<DeptUserEntity> {

    @Select("select distinct pk_dept from security_dept_user where pk_user =#{pkUser}")
    List<String> selectDeptByuser(String pkUser);

    @Select({"<script>select dept.deptcode,dept.deptname,dept.parent_dept_code as parentDeptcode,dept.parent_dept_name as         parentDeptname,org.code as orgCode,org.name as orgName,sdu.pk_dept_user as pkDeptUser,sdu.startdate,sdu.enddate         from security_dept dept left join org_information org on dept.org_code=org.code         left join security_dept_user sdu on sdu.pk_dept = dept.pk_dept         left join security_user su on su.pk_user = sdu.pk_user         <where>             <if test=\"queryParamVO.userCode != null and queryParamVO.userCode != ''\">                 su.usercode = #{queryParamVO.userCode}             </if>             <if test=\"queryParamVO.deptName != null and queryParamVO.deptName != ''\">                 and dept.deptname like CONCAT('%',#{queryParamVO.deptName},'%')             </if>             <if test=\"queryParamVO.orgCode != null and queryParamVO.orgCode != ''\">                 and dept.org_code = #{queryParamVO.orgCode}             </if>         </where>      </script>"})
    List<SecurityDeptUserVO> queryAuthronizedDeptList(@Param("queryParamVO") QueryParamVO queryParamVO);

    @Select("select distinct sdu.pk_dept from (select pk_user from security_user where usercode =#{userCode}) su LEFT JOIN security_dept_user  sdu ON su.pk_user = sdu.pk_user ")
    List<String> selectpkDeptByUserCode(String userCode);

    @Select({"<script>select distinct dept.deptcode as deptCode,dept.deptname as deptName " +
            "from (select code from org_information  " +
            "<where><if test=\"oid != null and oid != '' and oid != 'ALL'\"> CODE=#{oid} </if></where> ) org " +
            "LEFT JOIN security_dept dept ON dept.org_code = org.CODE " +
            "where dept.pk_dept in (${pkDeptList}) </script>"})
    List<Map<String, String>> selectDeptByOid(String oid, String pkDeptList);

}
