package com.goodwill.hdr.civ.controller;

import com.goodwill.hdr.civ.config.CommonConfig;
import com.goodwill.hdr.civ.config.Config;
import com.goodwill.hdr.civ.service.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Description 类描述：过敏信息Action
 * @Date 2018年9月28日
 * @modify 修改记录：
 */
@RequestMapping("/category")
@RestController
@Api(tags = "分类视图")
public class CategoryAction {

    private static final long serialVersionUID = 1L;

    @Autowired
    private PowerService powerService;
    @Autowired
    private PathologyReportService pathology;
    @Autowired
    private InspectReportService exam;
    @Autowired
    private CheckReportService check;
    @Autowired
    private OperService oper;
    @Autowired
    private DiagnoseService diag;
    @Autowired
    private MedicalRecordService medical;
    @Autowired
    private OrderService order;
    @Autowired
    private VisitService visitService;
    @Autowired
    private CurrentViewService cvService;
    @Autowired
    private NursingService nursingService;
    @Autowired
    private CurerecordService curerecordService;

    /**
     * @Description 获得分类视图数量
     */
    @ApiOperation(value = "获得分类视图数量", notes = "获得分类视图数量", httpMethod = "POST")
    @RequestMapping(value = "/getModernNum", method = RequestMethod.POST)
    public List<Map<String, Object>> getModernNum(String userCode, String outPatientId, String visitType, String oid, String patientId) {
//        String userCode = getParameter("userCode");
//        String outPatientId = getParameter("outPatientId");
//        String visitType = getParameter("visitType");

        List<Map<String, Object>> list = powerService.getPowerConfigByCategory(oid, userCode);
        for (Map<String, Object> map : list) {
            if (StringUtils.isNotBlank(map.get("id").toString())) {
                String code = map.get("id").toString();
                if ("exam_module".equals(code)) {
                    exam.getAllReportsCount(map, outPatientId);
                } else if ("pathology_module".equals(code)) {
                    pathology.getPathologyReportsCount(map, outPatientId);
                } else if ("check_module".equals(code)) {
                    check.getAllReportsCount(map, outPatientId);
                } else if ("oper_module".equals(code)) {
                    oper.getOpersNum(map, outPatientId);
                } else if ("main_diag_module".equals(code)) {
                    diag.getDiagsListNum(outPatientId, map);
                } else if ("record_module".equals(code)) {
                    medical.getAllMRCount(map, outPatientId);
                } else if ("hd_module".equals(code)) {
                    curerecordService.getHDReportNum(map, outPatientId);
                } else if ("nurse_module".equals(code)) {
                    //暂时在这里判断是否配置了url
                    boolean isDistinguish = Config.getCIV_NURSE_URL_OUT_OR_IN(oid);
                    boolean trueOrFalse = StringUtils.isNotBlank(CommonConfig.getURL(oid, "NURSE")) ||
                            (isDistinguish && StringUtils.isNotBlank(CommonConfig.getURL(oid, "IN_NURSE"))) ||
                            (isDistinguish && StringUtils.isNotBlank(CommonConfig.getURL(oid, "OUT_NURSE")));
                    if (trueOrFalse) {
                        map.put("map", "");
                    } else {
                        map.put("num",
                                nursingService.getCategoryNurseNum(outPatientId));
                    }
                } else if ("durg_orally_module".equals(code)) {
                    order.getOrdersNum(map, "KF", outPatientId);
                } else if ("durg_vein_module".equals(code)) {
                    order.getOrdersNum(map, "JM", outPatientId);
                } else if ("durg_qt_module".equals(code)) {
                    order.getOrdersNum(map, "QT", outPatientId);
                } else if ("dialysis_module".equals(code)) {
                    map.put("num", 0);
                }else if ("order_module".equals(code)) {
                    order.getOrdersNum(map, "ALL", outPatientId);
                }
            }
        }
        return list;
    }

    /**
     * @Description 获取最后一次就诊的现病史
     */
    @ApiOperation(value = "获取最后一次就诊的现病史", notes = "获取最后一次就诊的现病史", httpMethod = "POST")
    @RequestMapping(value = "/getHistory_Module", method = RequestMethod.POST)
    public Map<String, String> getHistory_Module( String oid, String patientId) {
//        String patientId = getParameter("patientId");
        String visitId = visitService.getFinalInVisit(oid, patientId, "");
        Map<String, String> result = cvService.getCVNowHis(oid, patientId, visitId);
        return result;

    }
}
