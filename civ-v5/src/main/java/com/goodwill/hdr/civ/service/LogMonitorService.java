package com.goodwill.hdr.civ.service;


import com.goodwill.hdr.civ.entity.LogRecord;
import com.goodwill.hdr.core.orm.Page;

import java.util.List;
import java.util.Map;

public interface LogMonitorService {
    /**
     * @Description 日志监控记录保存
     */
    int insertMonitorLog(LogRecord logRecord);

    /**
     * @param oid
     * @param userName
     * @param deptCode
     * @param pageNo
     * @param pageSize
     * @return
     * @Description 通过科室获得日志监控页面医生列表
     */
    public Page<Map<String, String>> getDoctorPage(String oid, String userName, String deptCode, int pageNo, int pageSize);

    /**
     * @param oid
     * @param deptCode
     * @param userCode
     * @param visitPageCode
     * @param pageNo
     * @param pageSize
     * @return
     * @Description 通过查询条件获取监控日志数据
     */
    public Page<Map<String, String>> getMonitorLogPage(String oid, String deptCode, String userCode, String visitPageCode, String beginDate, String endDate, int pageNo, int pageSize);

    /**
     * @return
     * @Description 获取所有监控日志
     */
    public Page<Map<String, Object>> getAllMonitorLogPage();

    /**
     * 处理有权限的视图
     *
     * @param map
     */
    public Map<String, String> handleView(String oid, Map<String, String> map);


    /**
     * 根据不同条件进行分类获取监控日志数据
     */
    public Map<String, Object> getClassifyMonitorLog(String oid, String deptCode, String beginDate, String endDate, String flagTemp);

    /**
     * 根据不同条件进行分类获取监控日志数据
     */
    public Map<String, Object> getSortedByTimeMonitorLog(String oid, String deptCode, String beginDate, String endDate, String sortField);

    /**
     * 返回空数据
     *
     * @param days
     * @return
     */
    public Map<String, Object> getEmptyData(String oid, String days);

    /**
     * 扇形图
     *
     * @return
     */
    public List<Map<String, Object>> getSectorData();

    /**
     * top 5
     *
     * @return
     */
    public List<Map<String, Object>> getTopFiveLogData(String flag, String value, String userCode);
}
