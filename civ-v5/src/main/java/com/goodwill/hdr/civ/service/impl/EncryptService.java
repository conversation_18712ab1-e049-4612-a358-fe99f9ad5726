package com.goodwill.hdr.civ.service.impl;

import cn.hutool.core.util.CharsetUtil;
import cn.hutool.core.util.HexUtil;
import cn.hutool.crypto.symmetric.SM4;
import cn.hutool.crypto.symmetric.SymmetricCrypto;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;

import com.goodwill.hdr.civ.config.ConfigCache;
import com.goodwill.hdr.civ.enums.commonModule.LianZhongDmrEnum;
import com.goodwill.hdr.civ.utils.Utils;

import com.goodwill.hdr.civ.utils.WSUtils;
import com.goodwill.hdr.core.utils.ApplicationException;
import com.goodwill.hdr.hbase.bo.PropertyFilter;
import com.goodwill.hdr.hbase.dto.responseVo.PageResultVo;
import com.goodwill.hdr.hbase.dto.responseVo.ResultVo;
import com.goodwill.hdr.hbaseQueryClient.builder.PageRequestBuilder;
import com.goodwill.hdr.hbaseQueryClient.service.HbaseQueryClient;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.DigestUtils;
import sun.misc.BASE64Encoder;

import javax.crypto.Cipher;
import javax.crypto.SecretKey;
import javax.crypto.SecretKeyFactory;
import javax.crypto.spec.DESKeySpec;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * 加密业务类
 */
@Service
public class EncryptService {
    protected static Logger logger = LoggerFactory.getLogger(EncryptService.class);

    @Autowired
    private CommonModuleService commonModuleService;
    @Autowired
    private ObjectMapper objectMapper;
    private final HbaseQueryClient hbaseQueryClient;

    public EncryptService(HbaseQueryClient hbaseQueryClient) {
        this.hbaseQueryClient = hbaseQueryClient;
    }

    public String DesEncrypt(String plaintext) throws Exception {
        Cipher cipher = Cipher.getInstance("DES/CBC/PKCS5Padding");
        String key = "Core_H_N";
        DESKeySpec desKeySpec = new DESKeySpec(key.getBytes("UTF-8"));

        SecretKeyFactory keyFactory = SecretKeyFactory.getInstance("DES");
        SecretKey secretKey = keyFactory.generateSecret(desKeySpec);
        IvParameterSpec iv = new IvParameterSpec("CryDesIv".getBytes("UTF-8"));
        cipher.init(Cipher.ENCRYPT_MODE, secretKey, iv);

        byte[] bt = cipher.doFinal(plaintext.getBytes(StandardCharsets.UTF_8));

        String strs = Base64.getEncoder().encodeToString(bt);
        return URLEncoder.encode(strs, "UTF-8");
    }

    /**
     * 获取lz加密接口返回的字符串
     *
     * @return 加密字符串
     */
    public String getLzEncryptedString(String oid, String id, String variable, String patientId, String visitId) {

//        List<PropertyFilter> filters = new ArrayList<>();
//        filters.add(new PropertyFilter("VISIT_ID", "STRING", MatchType.EQ.getOperation(), visitId));
        String jobNumber = commonModuleService.getLzJobNumber(oid, id);
        String emrScanWebserviceUrl = commonModuleService.getLzEncryptedUrl(oid, id);
        String dischargeTime = getLzDischargeTime(oid, id, patientId, visitId);
        return WSUtils.getWSData2(jobNumber, emrScanWebserviceUrl, variable, dischargeTime);
    }

    private String getLzDischargeTime(String oid, String sysCode, String patientId, String visitId) {
        String dischargeTimeConfig = ConfigCache.getCache(oid, LianZhongDmrEnum.DISCHARGE_TIME.getCode(sysCode));
        if (StringUtils.isNotBlank(dischargeTimeConfig)) {
            JsonNode jsonNode = null;

            try {
                jsonNode = objectMapper.readTree(dischargeTimeConfig);
            } catch (IOException e) {
                e.printStackTrace();
            }
            assert jsonNode != null;
            String field = jsonNode.path("field").textValue();
            String table = jsonNode.path("table").textValue();
            List<Map<String, String>> dischargeTimeList = new ArrayList<>();
            List<PropertyFilter> filters = new ArrayList<>();
            ResultVo<PageResultVo<Map<String, String>>> resultVo = hbaseQueryClient.getPageByCondition(
                    PageRequestBuilder.init()
                            .tableName(table)
                            .patientId(patientId)
                            .oid(oid)
                            .visitId(visitId)
                            .visitTypeCode("")
                            .filters(filters)
                            .pageNo(0)
                            .pageSize(0)
                            .orderBy("")
                            .desc()
                            .column(field)
                            .build());
            if(resultVo.isSuccess()){
                dischargeTimeList = resultVo.getContent().getResult();
            }
            //List<Map<String, String>> dischargeTimeList = hbaseDao.findConditionByPatient(table, oid, patientId, filters, field);
            String dischargeTime = dischargeTimeList.get(0).get(field);
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
            Date date = null;
            try {
                date = sdf.parse(dischargeTime);
            } catch (ParseException e) {
                throw new ApplicationException("时间格式解析错误");
            }
            String onlyDate = sdf.format(date);
            return Utils.objToStr(onlyDate, "");
        }
        return "";
    }

    public String UrlEncodeBase64(String paraStr) {
        String value = "";
        if (StringUtils.isNotBlank(paraStr)) {
            value = new BASE64Encoder().encode(paraStr.getBytes()).replace("+", "%2B");
        }
        return value;
    }

    public String AesEncrypt(String sSrc, String sKey) throws Exception {
        if (sKey == null) {
            System.out.print("Key为空null");
            return null;
        }
        // 判断Key是否为16位
        if (sKey.length() != 16) {
            System.out.print("Key长度不是16位");
            return null;
        }
        byte[] raw = sKey.getBytes("utf-8");
        SecretKeySpec skeySpec = new SecretKeySpec(raw, "AES");
        Cipher cipher = Cipher.getInstance("AES/ECB/PKCS5Padding");//"算法/模式/补码方式"
        cipher.init(Cipher.ENCRYPT_MODE, skeySpec);
        byte[] encrypted = cipher.doFinal(sSrc.getBytes("utf-8"));
        return encrypted.toString();
    }

    public String EhrBase64Encode(String str) {
        Base64.Encoder encoder = Base64.getEncoder();
        return encoder.encodeToString(str.getBytes(StandardCharsets.UTF_8));
    }

    public String EhrChineseUriEncode(String str) {
        String value = "";
        try {
            value = URLEncoder.encode(str, "UTF-8");
        } catch (UnsupportedEncodingException e) {
            throw new ApplicationException("中文URI转码失败", e);
        }
        return value;
    }

    public String gpMd5Encrypt(String s) {
        return DigestUtils.md5DigestAsHex(s.getBytes(StandardCharsets.UTF_8));
    }

    /**
     * SM4-加密
     * @param key 秘钥长度要求16位即64it
     * @param content
     * @return
     */
    public static String SM4EncryptBy16Key(String key, String content) throws Exception{
        logger.info("key:"+key+";;;content="+content);
        SymmetricCrypto sm4 = new SymmetricCrypto("SM4/ECB/PKCS5Padding", key.getBytes());
        return sm4.encryptHex(content);
    }

    /**
     * SM4-解密
     * @param key 秘钥长度要求16位即64it
     * @param content
     * @return
     */
    public static String SM4DecryptBy16Key(String key, String content) {
        SymmetricCrypto sm4 = new SymmetricCrypto("SM4/ECB/PKCS5Padding", key.getBytes());
        return sm4.decryptStr(content);
    }

    /**
     * SM4-加密
     * @param key 秘钥长度要求32位
     * @param content
     * @return
     */
    public static String SM4EncryptBy32Key(String key, String content) {
        SM4 sm4 = new SM4(HexUtil.decodeHex(key));
        return sm4.encryptHex(content);
    }

    /**
     * SM4-解密
     * @param key 秘钥长度要求32位
     * @param content
     * @return
     */
    public static String SM4DecryptBy32Key(String key, String content) {
        SM4 sm4 = new SM4(HexUtil.decodeHex(key));
        return sm4.decryptStr(content, CharsetUtil.CHARSET_UTF_8);
    }

}
