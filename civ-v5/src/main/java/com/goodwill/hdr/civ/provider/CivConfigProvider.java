package com.goodwill.hdr.civ.provider;

import com.baomidou.dynamic.datasource.toolkit.StringUtils;

public class CivConfigProvider {

    public String getConfigBycode(String oid, String configCode) {
//        StringBuffer sql = new StringBuffer("select id,config_code,ifnull(config_name,\"\") as config_name,ifnull(config_value,\"\") as config_value,ifnull(config_desc,\"\") as config_desc,config_type,config_level,config_index,config_scope_page,group_id,group_name,group_index,create_time,last_update_time,is_inuse,oid  from civ_config where   is_inuse = 'Y' and  config_code = #{configCode} ");
        StringBuffer sql = new StringBuffer("select * from civ_config where   is_inuse = 'Y' and  config_code = #{configCode} ");
        if (StringUtils.isNotBlank(oid)) {
            sql.append(" and oid=#{oid}");
        }
        sql.append(" order by id,group_index");
        return sql.toString();
    }

}
