package com.goodwill.hdr.civ.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.io.Serializable;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2022-03-03
 */
@TableName("civ_log_record_data")
public class LogRecordData implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    private Integer code;

    private Integer codeYear;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public Integer getCodeYear() {
        return codeYear;
    }

    public void setCodeYear(Integer codeYear) {
        this.codeYear = codeYear;
    }

    @Override
    public String toString() {
        return "LogRecordData{" +
                "id=" + id +
                ", code=" + code +
                ", codeYear=" + codeYear +
                "}";
    }
}
