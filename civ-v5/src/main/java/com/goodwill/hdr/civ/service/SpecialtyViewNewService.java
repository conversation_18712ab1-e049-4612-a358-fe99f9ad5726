package com.goodwill.hdr.civ.service;



import com.goodwill.hdr.civ.entity.Sickness;
import com.goodwill.hdr.civ.entity.SpecialtyDept;
import com.goodwill.hdr.core.orm.Page;

import java.util.Map;

public interface SpecialtyViewNewService {
    /**
     * 获取诊断列表
     * @param main_diag
     * @param pageNo
     * @param pageSize
     * @return
     */
    Page<Sickness> getSicknessList(String main_diag, int pageNo, int pageSize);

    /**
     * 获取已配置的科室列表

     * @param pageNo
     * @param pageSize
     * @return
     */
    Page<SpecialtyDept> getDeptList(String dept, int pageNo, int pageSize);

    /**
     * 获取科室以供 管理员 添加设置

     * @param pageNo
     * @param pageSize
     * @return
     */
    Page<Map<String, String>> getDeptListToAdd(String keyWord, int pageNo, int pageSize);

//    List<Map<String, Object>> getSicknessIndicatorList(String patientId, String visitId, String main_diag);
//
//    List<Map<String, String>> getLastIndicatorData(String patientId, String visitId, String itemCode, String indicatorCode);
//
//    void getFoldLineViewData(String patientId, String visitId, String itemCode, String indicatorCode, int numIndex, String beginTime, String endTime);
//
    Map<String, Object> getFoldLineData(String oid, String patientId, String visitId, String subItemCode, int numIndex, String beginTime, String endTime);
//
//    Map<String, Object> getFoldLineLabData(String patientId, String visitId, String itemCodes, int numIndex, String beginTime, String endTime);
//
//    Page<Map<String, String>>  getHlysListData(String patientId, String visitId, String itemCodes, int pageNo, int pageSize);
//
//    Page<Map<String, String>>  getInorderListData(String patientId, String visitId, String itemCodes, int pageNo, int pageSize);
//
//    List<Map<String, String>> getZdjyCardIdList(String patientId, String visitId, String itemCode, String pName);
//
//    List<Map<String, String>> getZdjcCardIdList(String patientId, String visitId, String itemCode);
//
//    Map<String, Object> getZdjyCardListDetailData(String patientId, String visitId, String rowKey, String itemCode);
//
//    Map<String, Object> getZdjcCardListDetailData(String patientId, String visitId, String rowKey, String subItemCode);
//
//    Map<String, Object> getHeathLastData(String patientId, String visitId, String classCode, String subItemCode);
//
//    Map<String, Object> getImportmentLabResLastData(String patientId, String visitId, String classCode, String subItemCode);
//
//    Map<String, Object> getNurseAndEatLastData(String patientId, String visitId, String classCode, String subItemCode);
//
//    Map<String, Object> getExamLastData(String patientId, String visitId, String classCode, String subItemCode);
//
//    Map<String, Object> getDragLastData(String patientId, String visitId, String classCode, String subItemCode);
//
//    Map<String, Object> getLabLastData(String patientId, String visitId, String classCode, String className);


}
