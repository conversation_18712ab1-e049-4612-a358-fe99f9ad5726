package com.goodwill.hdr.civ.enums.commonModule;


/**
 * 封装嵌入联众电子病案的相关配置信息
 *
 * <AUTHOR>
 * @since 2021/9/7
 */
public enum LianZhongDmrEnum {
    /**
     * 获取加密串的地址
     */

    ENCRYPTED_URL {
        @Override
        public String getCode(String sysCode) {
            return "SD_" + sysCode + "_WEBSERVICE_URL";
        }


    },

    /**
     * 联众电子病历加密串组成部分的 工号
     */
    LZ_DMR_JOB_NUMBER {
        @Override
        public String getCode(String id) {
            return "SD_" + id + "_LZ_DMR_JOB_NUMBER";
        }

    },
    /**
     * 联众电子病历加密串组成部分的 出院时间
     */
    DISCHARGE_TIME {
        @Override
        public String getCode(String sysCode) {
            return "SD_" + sysCode + "_DISCHARGE_TIME";
        }

    };

    public abstract String getCode(String id);


}
