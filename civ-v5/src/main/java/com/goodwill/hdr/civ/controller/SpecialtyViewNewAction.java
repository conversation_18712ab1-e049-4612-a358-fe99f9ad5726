package com.goodwill.hdr.civ.controller;

import com.goodwill.hdr.civ.service.SpecialtyViewNewService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;
import java.util.Map;


/**
 * <AUTHOR>
 * @Description 类描述：新版专科视图action
 * @modify 修改记录：
 */
@RequestMapping("/svn")
@RestController
@Api(tags = "新版专科视图")
public class SpecialtyViewNewAction {

    private static final long serialVersionUID = 1L;

    @Autowired
    private SpecialtyViewNewService specialtyViewNewService;



    /**
     * 折线图--重点生命体征
     */
    @ApiOperation(value = "折线图--重点生命体征", notes = "折线图--重点生命体征", httpMethod = "POST")
    @RequestMapping(value = "/getChartData_FoldLine_Health", method = RequestMethod.POST)
    public Map<String, Object> getChartData_FoldLine_Health(String oid, String patientId, String visitId, String code, String dateType, String startDate, String endDate) {

        Map<String, Object> map = specialtyViewNewService.getFoldLineData(oid, patientId, visitId, code, Integer.parseInt(dateType), startDate, endDate);
        return map;
    }


}
