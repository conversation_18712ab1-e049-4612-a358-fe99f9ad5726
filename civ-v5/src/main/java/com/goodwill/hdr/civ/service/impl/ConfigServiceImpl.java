package com.goodwill.hdr.civ.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;

import com.goodwill.hdr.civ.config.ConfigCache;
import com.goodwill.hdr.civ.dao.OrganizationDao;
import com.goodwill.hdr.civ.dao.PowerDao;
import com.goodwill.hdr.civ.entity.Config;
import com.goodwill.hdr.civ.entity.FeedbackSuggestions;
import com.goodwill.hdr.civ.entity.SysFiledUser;
import com.goodwill.hdr.civ.mapper.*;
import com.goodwill.hdr.civ.service.ConfigService;
import com.goodwill.hdr.civ.utils.ListPage;
import com.goodwill.hdr.civ.vo.DeptListWithinUser;
import com.goodwill.hdr.civ.vo.NameAndCodeVo;
import com.goodwill.hdr.civ.vo.TreeNodeVo;
import com.goodwill.hdr.civ.vo.UserVo;
import com.goodwill.hdr.core.orm.Page;
import com.goodwill.hdr.security.priority.entity.SecurityCommonDept;
import com.goodwill.hdr.web.core.modal.HdrSimplePage;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Description 类描述：
 * @modify 修改记录：
 */
@Service("configService")
public class ConfigServiceImpl implements ConfigService {
    protected Logger logger = LoggerFactory.getLogger(getClass());


    @Autowired
    private ConfigMapper configMapper;
    @Autowired
    private OrganizationDao organizationDao;

    @Autowired
    private FeedbackSuggestionsMapper feedbackSuggestionsMapper;
    @Autowired
    private ObjectMapper objectMapper;
    @Autowired
    private SecurityDeptMapper securityDeptMapper;
    @Autowired
    private PowerDao powerDao;
    @Autowired
    private SysFiledUserMapper sysFiledUserMapper;


    /**
     * 通过分类或者关键字查询配置
     *
     * @param keyWord
     * @param configScopePage
     * @return
     */

    public Page<Map<String, Object>> queryConfigs(String oid, String keyWord, String configScopePage, String code, int pageNo, int pageSize) {
        if (pageNo == 0) {
            pageNo = 1;
        }
        if (pageSize == 0) {
            pageSize = 10;
        }
        List<Config> list = configMapper.queryConfigList(code, keyWord, configScopePage);
        //如果通过关键字查询到了

        Map<String, List<Config>> map = new HashMap<String, List<Config>>();
        //先按groupid或者getConfigCode来统计配置
        for (Config entity : list) {
            List<Config> configList = new ArrayList<Config>();
            if (StringUtils.isBlank(entity.getGroupId())) {//单行配置
                configList.add(entity);
                map.put(entity.getConfigCode(), configList);
            } else {//多行配置
                configList = map.get(entity.getGroupId());
                if (null == configList) {
                    configList = new ArrayList<Config>();
                }
                configList.add(entity);
                map.put(entity.getGroupId(), configList);
            }
        }
        //构造前端需要json数据
        List<Map<String, Object>> listMap = new ArrayList<Map<String, Object>>();
        for (String key : map.keySet()) {
            Map<String, Object> mapData = new HashMap<String, Object>();
            List<Config> listentity = map.get(key);
            if (listentity.size() == 1) {//只包含单行配置
                Config entity = listentity.get(0);
                mapData.put("name", entity.getConfigName());
                List<Map<String, String>> listChild = new ArrayList<Map<String, String>>();
                Map<String, String> childMap = new HashMap<String, String>();
                childMap.put("name", entity.getConfigName());
                childMap.put("code", entity.getConfigCode());
                childMap.put("value", entity.getConfigValue());
                childMap.put("type", entity.getConfigType());
                childMap.put("desc", entity.getConfigDesc());
                childMap.put("id", String.valueOf(entity.getId()));
                listChild.add(childMap);
                mapData.put("child", listChild);
            } else {//一项配置 包含 多行配置
                List<Map<String, String>> listChild = new ArrayList<Map<String, String>>();
                for (Config entity : listentity) {
                    mapData.put("name", entity.getGroupName());
                    Map<String, String> childMap = new HashMap<String, String>();
                    childMap.put("name", entity.getConfigName());
                    childMap.put("code", entity.getConfigCode());
                    childMap.put("value", entity.getConfigValue());
                    childMap.put("type", entity.getConfigType());
                    childMap.put("desc", entity.getConfigDesc());
                    childMap.put("id", String.valueOf(entity.getId()));
                    listChild.add(childMap);
                }
                mapData.put("child", listChild);
            }
            listMap.add(mapData);
        }
        //分页  todo
        Page<Map<String, Object>> page = new Page<Map<String, Object>>();
        page.setResult(listMap);
        //借住此类分页
        ListPage<Map<String, Object>> listPage = new ListPage<Map<String, Object>>(listMap, pageNo, pageSize);
        listMap = listPage.getPagedList();
        page.setResult(listMap);
        page.setPageNo(pageNo);
        page.setPageSize(pageSize);
        page.setTotalCount(listMap.size());
        return page;
    }

    /**
     * 保存配置
     *
     * @param id
     * @param value
     */
    @Transactional
    public Map<String, String> updateOrSaveConfig(String oid, String id, String value) {
        Config config = new Config();
        config.setConfigValue(value);
        QueryWrapper<Config> wrapper = new QueryWrapper();
        wrapper.eq("id", id);
        int nums = configMapper.update(config, wrapper);
//        int nums = configMapper.saveOrUpdateConfig(oid, id, value);
        Map<String, String> map = new HashMap<String, String>();
        map.put("nums", nums + "");
        if (nums > 0) {
            //更新成功需要实时更新缓存里的数据
//            Config entity = configMapper.getConfigById(id);
//            ConfigCache.setCache(oid, entity.getConfigCode(), value);
            map.put("status", "1");
            map.put("msg", "更新成功");
        } else {
            map.put("status", "0");
            map.put("msg", "出现未知错误");
        }
        return map;
    }


    /**
     * 获取所有医院oid name
     *
     * @return
     */

    public List<TreeNodeVo> getHospitalOid() {

        String parentOids = ConfigCache.getCache("ALL", "PARENT_OID");

        List<NameAndCodeVo> list;
        try {
            list = objectMapper.readValue(parentOids, new TypeReference<List<NameAndCodeVo>>() {
            });
        } catch (JsonProcessingException e) {
            throw new RuntimeException("PARENT_OID应使用json列表格式");
        }
        return organizationDao.getOrgInfoMap(list);

    }


    public List<Config> getConfigBycode(String oid, String configCode) {

        return configMapper.getConfigBycode(oid, configCode);
    }


    @Transactional
    public Map<String, String> insertConfig(String code, String oid, String configCode, String configName, String configValue, String configDesc, String configType, String groupId, String groupName, String groupScopePage) {
        Map<String, String> map = new HashMap<String, String>();
        Config config = new Config();
        config.setOid(code);
        config.setConfigCode(configCode);
        config.setConfigName(configName);
        config.setConfigValue(configValue);
        config.setConfigDesc(configDesc);
        config.setConfigType(configType);
        config.setGroupId(groupId);
        config.setGroupName(groupName);
        config.setConfigScopePage(groupScopePage);
        config.setOid(oid);
        int i = configMapper.insert(config);
        if (i > 0) {
            map.put("status", "1");
            map.put("msg", "添加成功");
            return map;
        } else {
            map.put("status", "0");
            map.put("msg", "添加失败");
            return map;
        }
    }


    public Map<String, String> isOnlyConfigCode(String code, String oid, String configCode) {
        QueryWrapper<Config> wrapper = new QueryWrapper<>();
        wrapper.eq("oid", code).eq("config_code", configCode);
        List<Config> configEntityList = configMapper.selectList(wrapper);
        Map<String, String> map = new HashMap<String, String>();
        if (configEntityList.size() > 0) {
            map.put("status", "0");
            map.put("msg", "已存在该配置");
            return map;
        } else {
            map.put("status", "1");
            map.put("msg", "未有该配置,可以添加");
            return map;
        }
    }

    @Transactional
    public Map<String, String> saveSuggestion(String oid, String dateId, String relevantUser, String relevantEmail, String phone, String type, String description, byte[] bytes, String imagPath, String submitTime, String fileFileName) {
        int num = 0;
        QueryWrapper<FeedbackSuggestions> wrapper = new QueryWrapper<>();
        FeedbackSuggestions feedbackSuggestions = new FeedbackSuggestions();
        if (StringUtils.isNotBlank(dateId)) {
            wrapper.eq("id", dateId);
            feedbackSuggestions.setRelevantUser(relevantUser);
            feedbackSuggestions.setRelevantEmail(relevantEmail);
            feedbackSuggestions.setPhone(phone);
            feedbackSuggestions.setType(type);
            feedbackSuggestions.setDescription(description);
            feedbackSuggestions.setSubmitTime(submitTime);
            num = feedbackSuggestionsMapper.update(feedbackSuggestions, wrapper);
//            num = configDao.updateSuggestion(oid, dateId, relevantUser, relevantEmail, phone, type, description, bytes, imagPath, submitTime, fileFileName);
        } else {
            Date date = new Date();
            dateId = date.getTime() + ""; //主键
            feedbackSuggestions.setId(dateId);
            feedbackSuggestions.setOid(oid);
            feedbackSuggestions.setRelevantUser(relevantUser);
            feedbackSuggestions.setDescription(description);
            feedbackSuggestions.setRelevantEmail(relevantEmail);
            feedbackSuggestions.setSubmitTime(submitTime);
            feedbackSuggestions.setPhone(phone);
            feedbackSuggestions.setType(type);
            num = feedbackSuggestionsMapper.insert(feedbackSuggestions);
//            num = configDao.insertSuggestion(oid, dateId, relevantUser, relevantEmail, phone, type, description, bytes, imagPath, submitTime, fileFileName);
        }
        Map<String, String> map = new HashMap<String, String>();
        if (num > 0) {
            map.put("status", "1");
            map.put("msg", "添加成功");
            return map;
        } else {
            map.put("status", "0");
            map.put("msg", "添加失败");
            return map;
        }
    }

    @Transactional
    public Map<String, String> saveSuggestionImage(String oid, String dateId, String imagePath, byte[] bytes, String fileFileName) {
        FeedbackSuggestions feedbackSuggestions = new FeedbackSuggestions();
        feedbackSuggestions.setId(dateId);
        feedbackSuggestions.setOid(oid);
        feedbackSuggestions.setRelevantImage(bytes.toString());
        feedbackSuggestions.setRelevantImageUrl(imagePath);
        feedbackSuggestions.setFileName(fileFileName);
        int num = feedbackSuggestionsMapper.insert(feedbackSuggestions);
//        int num = configDao.insertSuggestionImage(oid, dateId, imagePath, bytes, fileFileName);
        Map<String, String> map = new HashMap<String, String>();
        if (num > 0) {
            map.put("status", "1");
            map.put("msg", "添加成功");
            map.put("dateId", dateId);
            map.put("fileName", fileFileName);
            map.put("imagePath", imagePath);
            return map;
        } else {
            map.put("status", "0");
            map.put("msg", "添加失败");
            return map;
        }
    }


    public FeedbackSuggestions querySuggestionImageById(String id) {
        QueryWrapper<FeedbackSuggestions> wrapper = new QueryWrapper<>();
        wrapper.eq("id", id);
        return feedbackSuggestionsMapper.selectOne(wrapper);
    }


    public Page<Map<String, String>> querySuggestionByPageNo(String oid, int pageNo, int pageSize) {
        Page<Map<String, String>> page = new Page<>();
        int count = feedbackSuggestionsMapper.selectCount(new QueryWrapper<>());
//        int count = configDao.querySuggestionNums(oid);
        List<FeedbackSuggestions> suggestionEntityList = feedbackSuggestionsMapper.selectPage(new com.baomidou.mybatisplus.extension.plugins.pagination.Page<>(pageNo, pageSize), new QueryWrapper<>()).getRecords();
//        List<SuggestionEntity> suggestionEntityList = configDao.querySuggestionByPageNo(oid,pageNo, pageSize);
        List<Map<String, String>> list = new LinkedList<>();
        for (FeedbackSuggestions suggestionEntity : suggestionEntityList) {
            Map<String, String> map = new HashMap();
            map.put("user", suggestionEntity.getRelevantUser());
            map.put("email", suggestionEntity.getRelevantEmail());
            map.put("phone", suggestionEntity.getPhone());
            map.put("type", suggestionEntity.getType());
            map.put("description", suggestionEntity.getDescription());
            map.put("submitTime", suggestionEntity.getSubmitTime());
            map.put("relevantImageUrl", suggestionEntity.getRelevantImageUrl());
            list.add(map);
        }
        page.setResult(list);
        page.setTotalCount(count);
        return page;
    }

    @Transactional

    public Map<String, String> deleteSuggestionImage(String oid, String dateId) {
        Map<String, String> map = new HashMap<>();
        List<FeedbackSuggestions> suggestionEntityList = feedbackSuggestionsMapper.selectList(new QueryWrapper<FeedbackSuggestions>().eq("id", dateId));
//        List<SuggestionEntity> suggestionEntityList = configDao.querySuggestionImageById(dateId);
        if (StringUtils.isNotBlank(suggestionEntityList.get(0).getDescription())) {
            map.put("status", "0");
            map.put("msg", "已存在该问题,无法删除");
            return map;
        }
        int i = feedbackSuggestionsMapper.delete(new QueryWrapper<FeedbackSuggestions>().eq("id", dateId));
//        int i = configDao.deleteSuggestionImage(dateId);
        if (i > 0) {
            map.put("status", "1");
            map.put("msg", "删除成功");
            return map;
        }
        map.put("status", "0");
        map.put("msg", "无删除数据");
        return map;
    }

    @Override
    public Page<Map<String, String>> getDeptPage(String oid, String keyWord, int pageNo, int pageSize) {
        QueryWrapper<SecurityCommonDept> queryWrapper = new QueryWrapper<>();
        queryWrapper.isNotNull("parent_dept_code");
        queryWrapper.eq(StringUtils.isNotBlank(oid), "org_code", oid);
        if (StringUtils.isNotBlank(keyWord)) {
            queryWrapper.like("deptname", keyWord).or().eq("deptcode", keyWord);
        }


        HdrSimplePage<SecurityCommonDept> simplePage = securityDeptMapper.selectPage(new HdrSimplePage<>(pageNo, pageSize), queryWrapper);
        Page<Map<String, String>> page = new Page<>(pageNo, pageSize);
        List<Map<String, String>> resultList = new ArrayList<>();
        for (SecurityCommonDept record : simplePage.getRecords()) {
            Map<String, String> map = new HashMap<>();
            map.put("deptname", record.getDeptname());
            map.put("deptcode", record.getDeptcode());
            resultList.add(map);
        }
        page.setResult(resultList);
        page.setTotalCount(simplePage.getTotal());
        return page;
    }

    @Override
    public Page<Map<String, Object>> queryUser(String oid, String deptCode, String keyWord, int pageNo, int pageSize, List<TreeNodeVo> oids) {
        Page<Map<String, Object>> page = new Page();
       /*List<DeptListWithinUser> deptListWithinUserList = powerDao.getUserList(oid,keyWord,deptCode);
       List<Map<String, Object>> resutlList = new ArrayList<>();
        for (DeptListWithinUser dept : deptListWithinUserList) {
            Map<String, Object> map = new HashMap<>();
            List<Map<String, String>> userList = new ArrayList<>();
            for (UserVo userVo : dept.getUser()) {
                Map<String, String> usermap = new HashMap<>();
                usermap.put("name", userVo.getUserName());
                usermap.put("code", userVo.getUserCode());
                usermap.put("power", String.valueOf(userVo.getPower()));
                usermap.put("type", "USER");
                userList.add(usermap);
            }
            map.put("children", userList);
            map.put("name", dept.getDeptName());
            map.put("code", dept.getDeptCode());
            map.put("type", "DEPT");
            resutlList.add(map);
        }
        page.setResult(resutlList);
        return page;
        }
        */
        for (TreeNodeVo treeNodeVo : oids)
        {
            List<Map<String, Object>> orgList = new ArrayList();
            Map<String, Object> orgmap1 = new HashMap();
            List<NameAndCodeVo> children = treeNodeVo.getChildren();
            String orgName = treeNodeVo.getName();
            for (NameAndCodeVo nameAndCodeVos : children)
            {
                Map<String, Object> orgmap = new HashMap();
                orgmap.put("name", nameAndCodeVos.getName());
                orgmap.put("code", nameAndCodeVos.getCode());
                orgmap.put("type", "DEPT");
                if (StringUtils.isBlank(oid))
                {
                    oid = nameAndCodeVos.getCode();
                    List<DeptListWithinUser> deptListWithinUserList = this.powerDao.getUserList(oid, keyWord, deptCode);
                    List<Map<String, Object>> resutlList = new ArrayList();
                    for (DeptListWithinUser dept : deptListWithinUserList)
                    {
                        Map<String, Object> map = new HashMap();
                        List<Map<String, String>> userList = new ArrayList();
                        for (UserVo userVo : dept.getUser())
                        {
                            Map<String, String> usermap = new HashMap();
                            usermap.put("name", userVo.getUserName());
                            usermap.put("code", userVo.getUserCode());
                            usermap.put("power", String.valueOf(userVo.getPower()));
                            usermap.put("type", "USER");
                            userList.add(usermap);
                        }
                        map.put("children", userList);
                        map.put("name", dept.getDeptName());
                        map.put("code", dept.getDeptCode());
                        map.put("type", "DEPT");
                        resutlList.add(map);
                    }
                    orgmap.put("children", resutlList);
                    orgmap1.put("children", orgmap);
                }
                else if (oid.equals(nameAndCodeVos.getCode()))
                {
                    orgmap.put("name", nameAndCodeVos.getName());
                    orgmap.put("code", nameAndCodeVos.getCode());
                    orgmap.put("type", "ORG");
                    List<DeptListWithinUser> deptListWithinUserList = this.powerDao.getUserList(oid, keyWord, deptCode);
                    List<Map<String, Object>> resutlList = new ArrayList();
                    for (DeptListWithinUser dept : deptListWithinUserList)
                    {
                        Map<String, Object> map = new HashMap();
                        List<Map<String, String>> userList = new ArrayList();
                        for (UserVo userVo : dept.getUser())
                        {
                            Map<String, String> usermap = new HashMap();
                            usermap.put("name", userVo.getUserName());
                            usermap.put("code", userVo.getUserCode());
                            usermap.put("power", String.valueOf(userVo.getPower()));
                            usermap.put("type", "USER");
                            userList.add(usermap);
                        }
                        map.put("children", userList);
                        map.put("name", dept.getDeptName());
                        map.put("code", dept.getDeptCode());
                        map.put("type", "DEPT");
                        resutlList.add(map);
                    }
                    orgmap.put("children", resutlList);
                    orgmap1.put("children", orgmap);
                }
            }
            orgList.add(orgmap1);
            page.setResult(orgList);
        }
        return page;
    }

    @Override
    public Page<Map<String, Object>> queryUserOrRole(String oid, String deptCode, String keyWord, int pageNo, int pageSize, List<TreeNodeVo> oids) {
        Page<Map<String, Object>> page = new Page();
        int totalCount = 0;

        List<Map<String, Object>> orgList = new ArrayList();
        for (TreeNodeVo treeNodeVo : oids) {
            List<NameAndCodeVo> children = treeNodeVo.getChildren();
            for (NameAndCodeVo nameAndCodeVos : children) {
                String currentOid=nameAndCodeVos.getCode();
                oid = StringUtils.isBlank(oid) ? currentOid : oid;

                Map<String, Object> orgmap = new HashMap();
                orgmap.put("name", nameAndCodeVos.getName());
                orgmap.put("code", currentOid);
                orgmap.put("type", "ORG");

                //获取oid院区的全部用户
                List<Map<String, String>> userList = this.powerDao.getUserOrRoleList(oid, keyWord,deptCode);
                //获取该oid院区下已配置脱敏信息的用户 暂时不分院区 oid设置为ALL
                QueryWrapper<SysFiledUser> wrapper = new QueryWrapper<>();
                wrapper.eq("oid", "ALL");
                List<SysFiledUser> userConfigedList = sysFiledUserMapper.selectList(wrapper);
                List<String> userCodes = userConfigedList.stream().map(SysFiledUser::getUserCode).distinct().collect(Collectors.toList());

                List<Map<String, String>> tempUserList=new ArrayList<>();
                for (Map<String, String> map : userList) {
                    if(map.get("oid").equals(currentOid)){
                        Map<String,String> tempUserMap=new HashMap<>(map);
                        boolean exists = userCodes.contains(map.get("usercode"));
                        if (exists) {
                            tempUserMap.put("power", "1");
                        } else {
                            tempUserMap.put("power", "0");
                        }
                        tempUserList.add(tempUserMap);
                    }
                }
                orgmap.put("children", tempUserList);
                orgList.add(orgmap);
                totalCount += userList.size();
            }
        }
        page.setResult(orgList);
        page.setTotalCount(totalCount);
        return page;
    }

}
