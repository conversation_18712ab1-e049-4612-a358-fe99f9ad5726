package com.goodwill.hdr.civ.service;



import com.goodwill.hdr.core.orm.Page;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Description 类描述：护理记录
 * @Date 2019年8月12日
 * @modify 修改记录：
 */
public interface NursingService {





    List<Map<String, String>> getAllINVisits(String outPatientId);





    /**
     * 获取护理单表头
     *

     * @return
     */
    List<Map<String, String>> getNurseTableHead(String oid);

    /**
     * 获取护理单详情
     */
    Page<Map<String, String>> getNurseDetail(String oid, String patientId, String visitId, String visitType, String topic, String date , int pageNo, int pageSize);

    /**
     * 统计护理数量
     *
     * @param outPatientId
     * @return
     */
    Integer getCategoryNurseNum(String outPatientId);


    /**
     * 统计护理数量
     *
     * @param patientId
     * @param visitId
     * @param visitType
     * @return
     */
    Integer getVisitNurseNum( String oid, String patientId, String visitId, String visitType);


    List<Map<String, String>> getNurseTypesByVisit(String oid, String patientId, String visitId, String visitType);

    List<Map<String, String>> getNurseTypesByCaregory(String outPatientId);

    Page<Map<String, String>> getNurseListByVisit(String oid, String patientId, String visitId, String visitType,  String nurseType, int pageNo, int pageSize);

    Page<Map<String, String>> getNurseListByCategory(String outPatientId, String nurseType, int pageNo, int pageSize);
}
