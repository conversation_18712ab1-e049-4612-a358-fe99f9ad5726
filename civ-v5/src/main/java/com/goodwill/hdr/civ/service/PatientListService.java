package com.goodwill.hdr.civ.service;


import com.goodwill.hdr.civ.vo.NameAndCodeVo;
import com.goodwill.hdr.core.orm.Page;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Description 类描述：获取患者列表接口
 * @Date 2018年5月2日
 * @modify 修改记录：
 */
public interface PatientListService {



    /**
     * @param patientId      患者ID
     * @param patientName    患者姓名
     * @param card_Id        身份证号
     * @param visit_Type     就诊类型
     * @param visit_Dept     就诊科室
     * @param visitBTime     开始时间
     * @param visitETime     结束时间
     * @param dischargeBTime 出院开始时间
     * @param dischargeETime 出院结束时间
     * @param visitStatus
     * @param pageNo         页码
     * @param pageSize       分页单位
     * @return 分页对象
     * @Description 方法描述: 查询患者列表（列表形式）查询数据中台
     */
    public Page<Map<String, String>> getPatientList_ListByJHDCP(String oid, String patientId, String visitNo, String patientName, String card_Id, String visit_Type,
                                                                String visit_Dept, String visitBTime, String visitETime, String dischargeBTime, String dischargeETime,
                                                                String birthdayBTime, String birthdayETime, String district, String visitStatus, int pageNo, int pageSize,
                                                                String inpvDays, String inpvDaysType, String hospital_second, String diagName,String isCollect,String eid);



    /**
     * @param keyWord
     * @param typeCode
     * @param pageNo
     * @param pageSize
     * @return
     * @Description 方法描述:统计solr里的所有科室
     */
    public Page<Map<String, String>> getAllFacetDataByJHDCP(String oid, String keyWord, String typeCode, String factCode, String factName, int pageNo, int pageSize);
//	/**
//	 * @Description
//	 * 方法描述: 根据登录用户获取科室
//	 * @return 返回类型： List<Map<String,String>>
//	 * @param keyWord 搜索字段
//	 * @param pageNo 页码
//	 * @param pageSize 分页单位
//	 * @return 分页对象
//	 */
//	public Page<Map<String, String>> getUserDeptAuth(String keyWord, int pageNo, int pageSize);
//


    /**
     * 获取当前用户查看患者详情的权限
     *
     * @return
     */
    Map<String, String> getUserAuthority(String oid, String deptCode);

    List<Map<String,String>> getAuthorisedOid(List<String> oidList);

    Page<Map<String,String>> getAuthorisedDept(String keyWord, String oid, int pageNo, int pageSize);

    List<NameAndCodeVo> getVisitStatus();

    /**
     * 收藏患者
     * @param patientId
     * @param visitTypeCode
     * @param oid
     * @param isCollect
     * @return
     */
    String collectPatient(String patientId, String visitId, String visitTypeCode, String oid, String isCollect);
}
