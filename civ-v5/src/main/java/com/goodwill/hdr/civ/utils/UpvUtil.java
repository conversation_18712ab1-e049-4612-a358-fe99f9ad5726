package com.goodwill.hdr.civ.utils;

import net.sf.json.JSONObject;
import org.apache.commons.lang3.StringUtils;

import java.text.SimpleDateFormat;
import java.util.*;

/**
* 通用工具类
* <AUTHOR>
*/
public class UpvUtil {
	public static Map<String, Object> parserToMap(String s) {
		Map<String, Object> map = new HashMap<String, Object>();
		JSONObject json = JSONObject.fromObject(s);
		@SuppressWarnings("unchecked")
        Iterator<String> keys = json.keys();
		while (keys.hasNext()) {
			String key = (String) keys.next();
			String value = json.get(key).toString();
			if (value.startsWith("{") && value.endsWith("}")) {
				map.put(key, parserToMap(value));
			} else {
				map.put(key, value);
			}

		}
		return map;
	}

	/**
	 * @Description
	 * 方法描述:将list Map<String,Object>中键值全部转换为小写 xiehongwei 2017-3-14 看代码逻辑，此处的list是  List<Map<String,Object>>
	 * @return 返回类型： List<Map<String,Object>>
	 * @param list
	 * @return
	 */
	public static List<Map<String, Object>> convertListMapObject2Low(List<Map<String, Object>> list) {
		if (list == null) {
			return null;
		}
		List<Map<String, Object>> dataList = new ArrayList<Map<String, Object>>();
		for (int i = 0; i < list.size(); i++) {
			Map<String, Object> map = list.get(i);
			Map<String, Object> map1 = new HashMap<String, Object>();
			for (Map.Entry<String, Object> entry : map.entrySet()) {
				map1.put(entry.getKey().toLowerCase(), entry.getValue());
			}
			dataList.add(map1);
		}
		return dataList;
	}

	/**
	 * @Description
	 * 方法描述:将list Map<String,String>中键值全部转换为小写 xiehongwei 2017-3-14 看代码逻辑，此处的list是  List<Map<String,Object>>
	 * @return 返回类型： List<Map<String,Object>>
	 * @param list
	 * @return
	 */
	public static List<Map<String, String>> convertListMapString2Low(List<Map<String, String>> list) {
		if (list == null) {
			return null;
		}
		List<Map<String, String>> dataList = new ArrayList<Map<String, String>>();
		for (int i = 0; i < list.size(); i++) {
			Map<String, String> map = list.get(i);
			Map<String, String> map1 = new HashMap<String, String>();
			for (Map.Entry<String, String> entry : map.entrySet()) {
				map1.put(entry.getKey().toLowerCase(), entry.getValue());
			}
			dataList.add(map1);
		}
		return dataList;
	}

	/**
	 * @Description
	 * 方法描述:对一系列Map按照START_TIME进行排序
	 * @return 返回类型： List<Map<String,String>>
	 * @param list
	 * @return
	 */
	public static List<Map<String, String>> sortMapList(List<Map<String, String>> list, final String sortBy) {
		List<Map<String, String>> result = new LinkedList<Map<String, String>>(list);
		if (list.size() > 1) {
			Collections.sort(result, new Comparator<Map<String, String>>() {
				@Override
				public int compare(Map<String, String> o1, Map<String, String> o2) {
					String s1 = o2.get(sortBy);
					String s2 = o1.get(sortBy);
					if (StringUtils.isBlank(s1)) {
						s1 = "1900-01-01";
					}
					if (StringUtils.isBlank(s2)) {
						s2 = "1900-01-01";
					}
					return (s1).compareTo(s2);
				}
			});
		}
		return result;
	}

	/**
	 * CheckAndPut2Map(map, "", "", "-",false); 
	 * 注意，如果value值和putIfNull有重复的不能使用此方法，因为不能判断是值还是系统赋的占位符
	 * @return 返回类型： void
	 * @param target
	 * @param key
	 * @param value
	 * @param putIfNull  如果为NULL，就是不赋值value为空的情况
	 */
	public static void CheckAndPut2Map(Map<String, String> target, String key, String value, String putIfNull,
                                       boolean update) {
		boolean put = false;
		if (target.containsKey(key)) { //如果原来已经有Key，判断情况
			if (putIfNull != null && putIfNull.equals(target.get(key))) {
				put = true;//判断是替代字符,则直接更新
			} else {
				if (update) {//判断不是替代字符，update的情况下更新
					put = true;
				}
			}
		} else { //原来没有Key，直接进行PUT操作
			put = true;
		}
		if (!put) {
			return;
		}
		//如果值为空，更新为替代字符串
		if (StringUtils.isBlank(value)) {
			if (putIfNull != null) {
				target.put(key, putIfNull);
			}
		} else {
			target.put(key, value);
		}
	}

	/**
	 * @Description
	 * 方法描述:获取传入日期的long型的时间戳
	 * @return 返回类型： long
	 * @param dateTime
	 * @param sdf
	 * @return
	 */
	public static long getDateLongTimes(String dateTime, SimpleDateFormat sdf) {
		long result = -1;
		try {
			result = sdf.parse(dateTime).getTime();
		} catch (Exception e) {
			e.printStackTrace();
		}
		return result;
	}
}
