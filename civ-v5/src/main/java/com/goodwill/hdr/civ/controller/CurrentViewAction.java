package com.goodwill.hdr.civ.controller;


import com.goodwill.hdr.civ.config.Config;
import com.goodwill.hdr.civ.service.CurrentViewService;
import com.goodwill.hdr.core.orm.Page;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Description 类描述：当前视图Action
 * @Date 2018年8月29日
 * @modify 修改记录：
 */
@RequestMapping("/cv")
@RestController
@Api(tags = "当前视图")
public class CurrentViewAction {

    @Autowired
    private CurrentViewService cvService;

    /**
     * @Description 末次住院病历主诉
     */
    @ApiOperation(value = "获取末次住院病历主诉", notes = "获取末次住院病历主诉", httpMethod = "POST")
    @RequestMapping(value = "/getCVConfig", method = RequestMethod.POST)
    public String getCVConfig(String oid) {
        String result = Config.getCIV_CURRENT_CONFIG(oid);
        return result;
    }


    /**
     * @Description 末次住院病历主诉
     */
    @ApiOperation(value = "获取末次住院病历主诉", notes = "获取末次住院病历主诉", httpMethod = "POST")
    @RequestMapping(value = "/getCVMain", method = RequestMethod.POST)
    public Map<String, String> getCVMain( String oid, String patientId, String visitId) {
        Map<String, String> result = cvService.getCVMain(oid, patientId, visitId);
        return result;
    }

    /**
     * @Description 末次住院病历现病史
     */
    @ApiOperation(value = "获取末次住院病历现病史", notes = "获取末次住院病历现病史", httpMethod = "POST")
    @RequestMapping(value = "/getCVNowHis", method = RequestMethod.POST)
    public Map<String, String> getCVNowHis( String oid, String patientId, String visitId) {
        Map<String, String> result = new HashMap<String, String>();

        result = cvService.getCVNowHis(oid, patientId, visitId);
        return result;
    }

    /**
     * @Description 末次住院诊断
     */
    @ApiOperation(value = "获取末次住院诊断", notes = "获取末次住院诊断", httpMethod = "POST")
    @RequestMapping(value = "/getCVDiag", method = RequestMethod.POST)
    public Map<String, Object> getCVDiag( String oid, String patientId, String visitId) {
        Map<String, Object> result = cvService.getCVDiag(oid, patientId, visitId);
        return result;
    }

    /**
     * @Description 末次住院体征数据，仅取近两天
     */
    @ApiOperation(value = "获取末次住院体征数据", notes = "获取末次住院体征数据", httpMethod = "POST")
    @RequestMapping(value = "/getCVVitalSign", method = RequestMethod.POST)
    public List<Map<String, String>> getCVVitalSign(String oid, String patientId, String visitId, String mainDiag, String dept_code) {
        List<Map<String, String>> result = new ArrayList<Map<String, String>>();
        if (StringUtils.isNotBlank(patientId)) {
            result = cvService.getPatVitalSign(oid, patientId, visitId, mainDiag, dept_code);
        }
        return result;
    }

    /**
     * @Description 获取医嘱状态名称列表
     */
    @ApiOperation(value = "获取医嘱状态名称列表", notes = "获取医嘱状态名称列表", httpMethod = "POST")
    @RequestMapping(value = "/getCVOrderStatusNameList", method = RequestMethod.POST)
    public List<String> getCVOrderStatusNameList(String oid) {
        List<String> result = Config.getCIV_ORDERSTATUS(oid);
        return result;
    }
    @ApiOperation(value = "获取医嘱类型名称列表", notes = "获取医嘱类型名称列表", httpMethod = "POST")
    @RequestMapping(value = "/getCVOrderDrugTypeNameList", method = RequestMethod.POST)
    public List<String> getCVOrderDrugTypeNameList(String oid) {
        List<String> result = Config.getCIV_ORDERDRUGTYPE(oid);
        return result;
    }
    @ApiOperation(value = "获取医嘱类型名称列表", notes = "获取医嘱类型名称列表", httpMethod = "POST")
    @RequestMapping(value = "/getCVOrderDrugPropertyNameList", method = RequestMethod.POST)
    public List<String> getCVOrderDrugPropertyNameList(String oid) {
        List<String> result = Config.getCIV_ORDERDRUGPROPERTY(oid);
        return result;
    }

    /**
     * @Description 获取患者护理任务数据
     */
    @ApiOperation(value = "获取患者护理任务数据", notes = "获取患者护理任务数据", httpMethod = "POST")
    @RequestMapping(value = "/getCVPatNurseTarsk", method = RequestMethod.POST)
    public List<Map<String, String>> getCVPatNurseTarsk(String oid, String patientId, String visitId) {
        List<Map<String, String>> result = new ArrayList<Map<String, String>>();
        if (StringUtils.isNotBlank(patientId)) {
            result = cvService.getPatNurseTask(oid, patientId, visitId);
        }
        return result;
    }

    /**
     * @Description 获取患者风险评估数据
     */
    @ApiOperation(value = "获取患者风险评估数据", notes = "获取患者风险评估数据", httpMethod = "POST")
    @RequestMapping(value = "/getCVPatRiskAssess", method = RequestMethod.POST)
    public Page<Map<String, String>> getCVPatRiskAssess(String oid, String patientId, String visitId, int pageNo, int pageSize) {
        Page<Map<String, String>> result = new Page<Map<String, String>>();
        if (StringUtils.isNotBlank(patientId)) {
            result = cvService.getPatRiskAssess(oid, patientId, visitId, pageNo, pageSize);
        }
        return result;
    }

    /**
     * @Description 获取患者诊疗计划，个性化方法，之后再扩展修改
     */
    @ApiOperation(value = "获取患者诊疗计划", notes = "获取患者诊疗计划", httpMethod = "POST")
    @RequestMapping(value = "/getCVPatAssessPlan", method = RequestMethod.POST)
    public Map<String, String> getCVPatAssessPlan(String oid, String patientId, String visitId) {
        Map<String, String> result = new HashMap<String, String>();
        result.put("result", cvService.getPatAssessPlan(oid, patientId, visitId));
        return result;
    }

    /**
     * @Description 院感
     */
    @ApiOperation(value = "获取院感", notes = "获取院感", httpMethod = "POST")
    @RequestMapping(value = "/getPatInfectionWarn", method = RequestMethod.POST)
    public Page<Map<String, String>> getPatInfectionWarn(String oid, String patientId, String visitId) {
        Page<Map<String, String>> result = new Page<Map<String, String>>();
        //院感数据很少，暂时通过100查询所有的院感
        int pageNo = 1;
        int pageSize = 100;
        if (StringUtils.isNotBlank(patientId)) {
            result = cvService.getPatInfectionWarn(oid, patientId, visitId, pageNo, pageSize);
        }
        return result;
    }

    /**
     * @Description 获取患者危急值数据
     */
    @ApiOperation(value = "获取患者危急值数据", notes = "获取患者危急值数据", httpMethod = "POST")
    @RequestMapping(value = "/getPatCriticalValues", method = RequestMethod.POST)
    public Page<Map<String, String>> getPatCriticalValues(String oid, String patientId, String visitId) {
        Page<Map<String, String>> result = new Page<Map<String, String>>();
        int pageNo = 1;
        int pageSize = 100;
        if (StringUtils.isNotBlank(patientId)) {
            result = cvService.getPatCriticalValues(oid, patientId, visitId, pageNo, pageSize);
        }
        return result;
    }

}
