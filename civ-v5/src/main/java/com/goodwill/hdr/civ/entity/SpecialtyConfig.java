package com.goodwill.hdr.civ.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.io.Serializable;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2022-03-03
 */
@TableName("civ_specialty_config")
public class SpecialtyConfig implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    private String doctorCode;

    private String doctorName;

    private String sicknessCode;

    private String sicknessName;

    private String itemCode;

    private String itemName;

    private String dataType;

    private String isInuse;

    private Integer arrayIndex;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getDoctorCode() {
        return doctorCode;
    }

    public void setDoctorCode(String doctorCode) {
        this.doctorCode = doctorCode;
    }

    public String getDoctorName() {
        return doctorName;
    }

    public void setDoctorName(String doctorName) {
        this.doctorName = doctorName;
    }

    public String getSicknessCode() {
        return sicknessCode;
    }

    public void setSicknessCode(String sicknessCode) {
        this.sicknessCode = sicknessCode;
    }

    public String getSicknessName() {
        return sicknessName;
    }

    public void setSicknessName(String sicknessName) {
        this.sicknessName = sicknessName;
    }

    public String getItemCode() {
        return itemCode;
    }

    public void setItemCode(String itemCode) {
        this.itemCode = itemCode;
    }

    public String getItemName() {
        return itemName;
    }

    public void setItemName(String itemName) {
        this.itemName = itemName;
    }

    public String getDataType() {
        return dataType;
    }

    public void setDataType(String dataType) {
        this.dataType = dataType;
    }

    public String getIsInuse() {
        return isInuse;
    }

    public void setIsInuse(String isInuse) {
        this.isInuse = isInuse;
    }

    public Integer getArrayIndex() {
        return arrayIndex;
    }

    public void setArrayIndex(Integer arrayIndex) {
        this.arrayIndex = arrayIndex;
    }

    @Override
    public String toString() {
        return "SpecialtyConfig{" +
                "id=" + id +
                ", doctorCode=" + doctorCode +
                ", doctorName=" + doctorName +
                ", sicknessCode=" + sicknessCode +
                ", sicknessName=" + sicknessName +
                ", itemCode=" + itemCode +
                ", itemName=" + itemName +
                ", dataType=" + dataType +
                ", isInuse=" + isInuse +
                ", arrayIndex=" + arrayIndex +
                "}";
    }
}
