package com.goodwill.hdr.civ.service;

import com.goodwill.hdr.civ.vo.InspectVoForMedicalTechologies;
import com.goodwill.hdr.civ.vo.InspectionReportTabVo;
import com.goodwill.hdr.core.orm.Page;
import com.goodwill.hdr.web.common.vo.ResultVO;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Description 类描述：检验报告服务接口
 * @Date 2018年5月2日
 * @modify 修改记录：
 */
public interface InspectReportService {

    /**
     * @param patientId 患者编号
     * @param visitId   就诊次数
     * @param visitType 就诊类型
     * @param orderBy   排序字段
     * @param orderDir  排序规则
     * @param pageNo    页码
     * @param pageSize  分页单位
     * @return 分页对象
     * @Description 方法描述: 查询患者某次就诊的检验报告
     */
    public Page<Map<String, String>> getInspectReportList(String oid, String patientId, String visitId, String visitType,
                                                          String orderBy, String orderDir, int pageNo, int pageSize,String tabCode);

    /**
     * @param patientId 患者编号
     * @param reportNo  报告号
     * @param pageNo    页码
     * @param pageSize  分页单位
     * @param show      显示标记   1:显示异常结果
     * @return 分页对象
     * @Description 方法描述:获取检验报告的详细信息
     */
    public Map<String, Object> getInspectReportDetails(String oid, String patientId,String visitId, String reportNo, int pageNo, int pageSize,
                                                       String show,String tabCode,String wyhrReportNo);

    /**
     * @param patientId 患者编号
     * @param reportNo  报告号
     * @param pageNo    页码
     * @param pageSize  分页单位
     * @return 分页对象
     * @Description 方法描述:获取检验报告的详细信息
     */
    public List<Map<String, Object>> getInspectReportDetailSubItems(String oid, String patientId, String visitId,String reportNo, int pageNo, int pageSize,
                                                                    List<String> reportTime,String labItemCode);

    /**
     * @param patientId 患者编号
     * @param reportNo  就诊次数
     * @param pageNo    页码
     * @param pageSize  分页单位
     * @param show      是否仅获取异常项
     * @return
     * @Description 方法描述: 查询检验报告下的检验明细列表
     */
    Page<Map<String, String>> getInspectSubItems(String oid, String patientId,String visitId, String reportNo, int pageNo, int pageSize,
                                                 String show);

    /**
     * @param patientId   患者编号
     * @param dateType    时间类型  近3次，近5次，自定义时间段
     * @param startDate   开始日期
     * @param endDate     结束日期
     * @param subItemCode 检验细项编码
     * @return
     * @Description 方法描述: 获取各个检验细项的趋势图
     */
    public Map<String, Object> getInspectReportDetailsLine(String oid, String patientId, String visitType, String outpatientId, String dateType, String startDate,
                                                           String endDate, String subItemCode, String specimanTypeCode);

    /**
     * @param patientId 患者编号
     * @param visitId   就诊次数
     * @param visitType 就诊类型
     * @return
     * @Description 方法描述: 某次就诊的检验报告数量
     */
    public long getInspectCount(String oid, String patientId, String visitId, String visitType,String tabCode);

    /**
     * @param pageNo   页码
     * @param pageSize 分页单位
     * @param orderby  排序字段
     * @param orderdir 排序规则
     * @return 分页对象
     * @Description 方法描述:查询患者所有的检验报告（分类视图）
     */
    public List<Map<String, Object>> getInspectReports(String this_oid, String oid, String inpatientId, String visitType, int pageNo, int pageSize, String orderby,
                                                       String orderdir, String outPatientId, String year,String typeCode);

    /**
     * @return labItems 检验项  "血常规，尿常规，便常规"
     * @Description 方法描述:查询患者所有的检验报告类型
     */
    public List<Map<String, String>> getReportsTypes(String this_oid, String oid, String inpatientId, String outPatientId, String visitType);

    /**
     * 获取报告类型或报告总数
     *
     * @param resultMap
     */
    public void getAllReportsCount(  Map<String, Object> resultMap, String outPatientId);

    /**
     * @param patientId 患者编号
     * @param visitId   就诊次数
     * @param visitType 就诊类型 OUTPV|INPV
     * @return
     * @Description 方法描述: 获取某次就诊的所有异常检验结果
     */
    public List<Map<String, String>> getExLabResult( String oid, String patientId, String visitId, String visitType, String mainDiag, String deptCode);

    public List<Map<String, String>> getLabReportDetail(String oid, String patientId, String visitId, String orderNo, String orderItemCode);

    public List<Map<String, String>> getExamReportDetail(String oid, String patientId, String visitId, String orderNo, String orderItemCode);

    ResultVO<Map<String, InspectVoForMedicalTechologies>> getInspect( String oid, String oidLast, String patientId, String visitId, String visitTypeCode, String outPatientId, String date);

    ResultVO<List<Map<String, String>>> getInspectList( String outPatientId, String itemName,String date, boolean currentVist);

    /**
     * @return labItems 检验项  "血常规，尿常规，便常规"
     * @Description 方法描述:查询患者所有的检验报告类型
     */
    public List<Map<String, String>> getInspectionReportTab(String oid, String patientId, String visitId, String visitType, String outPatientId,List<InspectionReportTabVo> list);

    /**
     * 获得检验报告类型
     * @param oid
     * @return
     */
    List<InspectionReportTabVo> getReportType(String oid);
}
