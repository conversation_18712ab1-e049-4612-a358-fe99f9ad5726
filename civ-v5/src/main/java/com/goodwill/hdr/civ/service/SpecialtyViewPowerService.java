package com.goodwill.hdr.civ.service;



import com.goodwill.hdr.civ.entity.DeptSpecialtyConfig;
import com.goodwill.hdr.civ.entity.Sickness;
import com.goodwill.hdr.civ.entity.SpecialtyConfig;
import com.goodwill.hdr.civ.entity.SpecialtyIndicatorConfigEntity;
import com.goodwill.hdr.civ.enums.DictType;
import com.goodwill.hdr.core.orm.Page;

import java.util.List;
import java.util.Map;

public interface SpecialtyViewPowerService {
    /**
     * 查询诊断设置的大项明细
     *
     * @param mainDiag
     * @return
     */
    List<SpecialtyConfig> getItemList(String oid, String mainDiag);
    /**
     * 查询科室设置的大项明细
     *
     * @param dept
     * @return
     */
    List<DeptSpecialtyConfig> getDeptItemList(String oid, String dept);

    /**
     * 获取自定义疾病卡片
     *
     * @return
     */
    List<Map<String, String>> getSicknessList(String oid);

    /**
     * 删除疾病
     *
     * @param sciknessCode
     */
    int delSickness(String oid,String sciknessCode);

    /**
     * 删除科室
     *
     * @param sciknessCode
     */
    int delDeptConfig(String oid,String sciknessCode);

    /**
     * 查询字典数据并过滤已配置的诊断细项
     *
     * @param pageNo
     * @param pageSize
     * @param dictType
     * @return
     */
    Page<Map<String, String>> getDictData(String oid,String visitId,String sicknessCode, String type, String keyWord, int pageNo, int pageSize, DictType dictType);

    /**
     *查询字典数据并过滤已配置的科室细项

     * @param type
     * @param keyWord
     * @param pageNo
     * @param pageSize
     * @param dictType
     * @return
     */
    Page<Map<String, String>> getDeptDictData(String oid, String visitId, String deptCode, String type, String keyWord, int pageNo, int pageSize, DictType dictType);

//    Page<Map<String, String>> getExamDictData(String oid,String deptCode, String type, String keyWord, int pageNo, int pageSize, DictType dictType);
//    /**
//     * 添加诊断到mysql数据库
//     *
//     * @param sicknessCode
//     * @param sicknessname
//     */
//    int addSickness(String oid,String sicknessCode, String sicknessname);
//
//    /**
//     * 添加专科科室到mysql数据库
//     *
//     * @param sicknessCode
//     * @param sicknessname
//     */
//    int addSecurityDept(String oid,String deptCode, String deptName);

    /**
     * 获取疾病各类大项下的明细项
     *
     * @param sicknessCode
     * @param type
     */
    Page<SpecialtyIndicatorConfigEntity> getDetailData(String oid, String sicknessCode, String type, int pageNo, int pageSize);

    /**
     * 获取科室各类大项下的明细项
     *
     * @param deptCode
     * @param type
     */
    Page<Map<String, Object>> getDeptDetailData(String oid, String deptCode, String type, int pageNo, int pageSize);


//    /**
//     * 删除疾病明细
//     *
//     * @param id
//     */
//    int delInditorConfig(String oid,String id);
//
//    /**
//     * 删除科室明细
//     *
//     * @param id
//     */
    int delDeptInditorConfig(String oid,String id);
//
//    /**
//     * 新增疾病细项
//     */
    int addInditorConfig(String oid,Map<String, String> map);
//
//    /**
//     * 新增科室细项
//     */
    int addDeptInditorConfig(String oid,Map<String, String> map);
//
//
    /**
     * 去重
     *
     * @param page
     * @param pageDiag
     * @return
     */
    Page<Map<String, String>> executeDuplicates(String oid,Page<Map<String, String>> page, Page<Sickness> pageDiag);

    Page<Map<String, String>> executeDuplicatesList(String oid, Page<Map<String, String>> page, List<Map<String, String>> list);


    Page<Map<String, String>> getDictDiagData(String oid,String visitId,String keyWord, int pageNo, int pageSize, DictType dictType);

    /**
     * 查询生命体征
     *
     * @return
     */
    Page<Map<String, String>> getHealthData(String oid,String diag, String keyWord, int pageNo, int pageSize);

    /**
     * 查询科室生命体征

     * @param keyWord
     * @param pageNo
     * @param pageSize
     * @return
     */
    Page<Map<String, String>> getDeptHealthData(String oid,String deptCode, String keyWord, int pageNo, int pageSize);

    /**
     * 去掉未配置模板数据
     *

     * @return
     */
    Page<Map<String, String>> removeNoConfigData(String oid, Page<Sickness> pageDiag, List<Map<String, String>> list);

//    /**
//     * 通过科室查询专科配置
//     * @param dept
//     * @param intemName
//     * @return
//     */
//    List<SpecialtyDeptIndicatorConfig> getDeptConfig(String oid,String itemCode, String subItemCode,String deptCode);
//

    /**
     * 获取专科设置：先查找 医生自定义设置 》 admin的设置 》 医生所在科室的设置
     *
     * @param mainDiag
     * @return
     */
    List<Map<String, String>> getSpecialtyConfig(String oid, String mainDiag, String itemCode, String deptCode);
    int addSickness(String oid,String sicknessCode, String sicknessname);
    int delInditorConfig(String oid,String id);
    int addSecurityDept(String oid,String deptCode, String deptName);
}
