package com.goodwill.hdr.civ.service;


import com.goodwill.hdr.core.orm.Page;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Description 类描述：病理interface
 * @Date 2020年8月7日
 */
public interface PathologyReportService {
    /**
     * @param patId     患者编号
     * @param visitId   就诊次数
     * @param visitType 就诊类型
     * @param orderBy   排序字段
     * @param orderDir  排序规则
     * @param pageNo    页码
     * @param pageSize  分页单位
     * @return 分页对象
     * @Description 方法描述: 查询患者某次就诊的病理报告
     */
    public Page<Map<String, String>> getPathologyReportList(String oid, String patId, String visitId, String visitType,
                                                            String orderBy, String orderDir, String mainDiag, String deptCode, int pageNo, int pageSize);

    /**
     * 通过病理报告反向关联病理医嘱
     *
     * @param pid
     * @param vid
     * @param order
     * @return
     */
    List<Map<String, String>> getPathologyOrderByPid(String oid, String pid, String vid, String order);

    /**
     * @return
     * @Description 方法描述: 查询某份病理报告的详情
     */
    public Map<String, String> getPathologyReportDetails(String oid, String pid, String vid, String reportNo);

    /**
     * @param patientId 患者编号
     * @param visitId   就诊次数
     * @param visitType 就诊类型
     * @return
     * @Description 方法描述: 某次就诊的病理报告数量
     */
    public long getPathologyCount(String oid, String patientId, String visitId, String visitType);

    /**
     * @param patientId    患者编号
     * @param pageNo       页码
     * @param pageSize     分页单位
     * @param orderby      排序字段
     * @param orderdir     排序规则
     * @param outPatientId 患者ID
     * @param year         年份
     * @return 分页对象
     * @Description 方法描述:查询患者所有的病理报告
     */
    public List<Map<String, Object>> getPathologyReports( String oid, String patientId, int pageNo, int pageSize, String orderby,
                                                         String orderdir, String outPatientId, String visitType, String year, String key, String type,
                                                         String click_Type);

    /**
     * 获取病理报告总数
     *
     * @param resultMap
     */
    public void getPathologyReportsCount(  Map<String, Object> resultMap, String outPatientId);

    /**
     * 获取病理报告类型
     *
     * @param patientId
     */
    public List<Map<String, String>> getPathologyReportTypes( String oid, String patientId, String outPatientId, String visitType);

}
