package com.goodwill.hdr.civ.controller;

import com.goodwill.hdr.civ.entity.FeedbackSuggestions;
import com.goodwill.hdr.civ.service.ConfigService;
import com.goodwill.hdr.civ.vo.TreeNodeVo;
import com.goodwill.hdr.core.orm.Page;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.ServletInputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Description 类描述：
 * @modify 修改记录：
 */
@RequestMapping("/config")
@RestController
@Api(tags = "配置管理")
public class ConfigAction {
    @Autowired
    private ConfigService configService;


    /**
     * 获取配置项
     */
    @ApiOperation(value = "获取配置项", notes = "获取配置项", httpMethod = "POST")
    @RequestMapping(value = "/getConfigs", method = RequestMethod.POST)
    public Page<Map<String, Object>> getConfigs(String keyWord, String configScopePage, String oid, String code) {
        int pageNo = 1;
        int pageSize = 1000;
        Page<Map<String, Object>> page = configService.queryConfigs(oid, keyWord, configScopePage, code, pageNo, pageSize);
        return page;
    }

    /**
     * 保存修改的配置
     */
    @ApiOperation(value = "保存修改的配置", notes = "保存修改的配置", httpMethod = "POST")
    @RequestMapping(value = "/saveConfig", method = RequestMethod.POST)
    public Map<String, String> saveConfig(String oid, String id, String value) {
        Map<String, String> map = configService.updateOrSaveConfig(oid, id, value);
        return map;
    }


    /**
     * 获取医院分类
     */
    @ApiOperation(value = "获取医院分类", notes = "获取医院分类", httpMethod = "POST")
    @RequestMapping(value = "/getConfigType", method = RequestMethod.POST)
    public List<TreeNodeVo> getConfigType() {
        return configService.getHospitalOid();

    }

    /**
     * 插入配置项
     */
    @ApiOperation(value = "插入配置项", notes = "插入配置项", httpMethod = "POST")
    @RequestMapping(value = "/insertConfig", method = RequestMethod.POST)
    public Map<String, String> insertConfig(String code, String oid, String configCode, String configName, String configValue, String configDesc, String configType, String groupId, String groupName, String groupScopePage) {
        Map<String, String> map = configService.insertConfig(code, oid, configCode, configName, configValue, configDesc, configType, groupId, groupName, groupScopePage);
        return map;
    }

    /**
     * 判断配置项是否已存在
     */
    @ApiOperation(value = "判断配置项是否已存在", notes = "判断配置项是否已存在", httpMethod = "POST")
    @RequestMapping(value = "/isOnlyConfigCode", method = RequestMethod.POST)
    public Map<String, String> isOnlyConfigCode(String code, String oid, String configCode) {
        Map<String, String> map = configService.isOnlyConfigCode(code, oid, configCode);
        return map;
    }

    /**
     * 反馈功能和建议
     */
    @RequestMapping(value = "/saveSuggestion", method = RequestMethod.POST)
    public Map<String, String> saveSuggestion(String oid, String user, String email, String phone, String type, String description, String dateId, HttpServletRequest request) {
        DateTimeFormatter dateTimeFormatter=DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        Map<String, String> map = new HashMap<>();
        try {

            byte[] bytes = null;

            String basePath = request.getScheme() + "://" + request.getServerName() + ":" + request.getServerPort()
                    + request.getContextPath() + "/";
            //图片路径
            String imagPath = basePath + "civ/config_showSuggestionImage?id=" + dateId;
            map = configService.saveSuggestion(oid, dateId, user, email, phone, type, description, bytes, imagPath, LocalDateTime.now().format(dateTimeFormatter), "");
        } catch (Exception e) {
            e.printStackTrace();
        }
        return map;
    }


    /**
     * 上传图片
     *
     * @param request
     * @param response
     * @return
     * @throws IOException
     */
    @RequestMapping(value = "/uploadImage", method = RequestMethod.POST)
    public Map<String, String> uploadImage(HttpServletRequest request, HttpServletResponse response) throws IOException {
        ServletInputStream inputStream = request.getInputStream();
        ByteArrayOutputStream swapStream = new ByteArrayOutputStream();
        //buff用于存放循环读取的临时数据
        byte[] buff = new byte[100];
        int rc = 0;
        while ((rc = inputStream.read(buff, 0, 100)) > 0) {
            swapStream.write(buff, 0, rc);
        }
        //bytes为转换之后的结果
        byte[] bytes = swapStream.toByteArray();

        String oid = request.getParameter("oid");
        Map<String, String> map = new HashMap<>();
        try {
            Date date = new Date();
            //主键
            String dateId = date.getTime() + "";
            String basePath = request.getScheme() + "://" + request.getServerName() + ":" + request.getServerPort()
                    + request.getContextPath() + "/";
            //图片路径
            String imagPath = basePath + "config/showSuggestionImage?id=" + dateId;
            map = configService.saveSuggestionImage(oid, dateId, imagPath, bytes, "");
        } catch (Exception e) {
            e.printStackTrace();
        }
        response.setHeader("X-Frame-Options", "SAMEORIGIN");
        return map;
    }

    /**
     * 删除反馈建议图片
     */
    @RequestMapping(value = "/deleteImage", method = RequestMethod.POST)
    public Map<String, String> deleteImage(String oid, String dateId) {
        Map<String, String> map = new HashMap<>();
        map = configService.deleteSuggestionImage(oid, dateId);
        return map;
    }

    /**
     * 分页查询反馈建议
     */
    @RequestMapping(value = "/querySuggestionByPageNo", method = RequestMethod.POST)
    public Page<Map<String, String>> querySuggestionByPageNo(String oid, String pageNo, String pageSize) {
        int pageNoInt = Integer.parseInt(StringUtils.isNotBlank(pageNo) ? pageNo : "1");
        int pageSizeInt = Integer.parseInt(StringUtils.isNotBlank(pageSize) ? pageSize : "10");
        Page<Map<String, String>> page = configService.querySuggestionByPageNo(oid, pageNoInt, pageSizeInt);
        return page;
    }

    /**
     * 查看反馈建议图片
     */
    @RequestMapping(value = "/showSuggestionImage", method = RequestMethod.GET)
    public void showSuggestionImage(String id, HttpServletResponse response) {
        try {
            FeedbackSuggestions suggestionEntity = configService.querySuggestionImageById(id);
            byte[] bytes = suggestionEntity.getRelevantImage().getBytes();
            // 取得文件名
            String fileName = suggestionEntity.getFileName();
            response.setContentType("image/png");
            response.addHeader("Content-Length", "" + bytes.length);
            OutputStream output = response.getOutputStream();
            output.write(bytes);
            output.flush();
            output.close();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @PostMapping("/getDeptPage")
    public Page<Map<String, String>> getDeptPage(String oid, String keyWord, int pageNo, int pageSize) {
        return configService.getDeptPage(oid, keyWord, pageNo, pageSize);

    }

    @PostMapping("/queryUser")
    public Page<Map<String, Object>> queryUser(@RequestBody Map<String, String> params) {
        List<TreeNodeVo> oids = configService.getHospitalOid();
        String oid = params.get("orgCode");
        if(StringUtils.isBlank(oid) || "".equals(oid)){
            oid = oids.get(0).getChildren().get(0).getCode();
        }
        int pageNo = Integer.valueOf(params.get("pageNo"));
        int pageSize = Integer.valueOf(params.get("pageSize"));
        String deptCode = params.get("deptCode");
        String keyword = params.get("queryValue");
        return configService.queryUser(oid, deptCode, keyword, pageNo, pageSize,oids);
    }

    /**
     * 获取院区用户或角色 信息脱敏处使用
     * 2024-09-27
     * @param params
     * @return
     */
    @PostMapping("/queryUserOrRole")
    public Page<Map<String, Object>> queryUserOrRole(@RequestBody Map<String, String> params) {
        List<TreeNodeVo> oids = configService.getHospitalOid();
        String oid = params.get("orgCode");
        if(StringUtils.isBlank(oid) || "".equals(oid)){
            oid = oids.get(0).getChildren().get(0).getCode();
        }
        int pageNo = Integer.valueOf(params.get("pageNo"));
        int pageSize = Integer.valueOf(params.get("pageSize"));
        String keyword = params.get("queryValue");
        String type=params.get("type");
        String deptCode = params.get("deptCode");

        Page<Map<String, Object>> page=new Page<>();
        if("user".equalsIgnoreCase(type)){
            page= configService.queryUserOrRole(oid,deptCode, keyword, pageNo, pageSize,oids);
        }else if("role".equalsIgnoreCase(type)){
            //暂未开发
        }
        return page;
    }

}
