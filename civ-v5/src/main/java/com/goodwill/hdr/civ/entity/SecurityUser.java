package com.goodwill.hdr.civ.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.io.Serializable;

/**
 * 用户信息
 *
 * @TableName security_user
 */
@TableName(value = "security_user")
public class SecurityUser implements Serializable {
    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    @TableId(value = "pk_user")
    private String pkUser;
    /**
     * 用户工号
     */
    @TableField(value = "usercode")
    private String usercode;
    /**
     *
     */
    @TableField(value = "username")
    private String username;
    /**
     *
     */
    @TableField(value = "password")
    private String password;
    /**
     *
     */
    @TableField(value = "name")
    private String name;
    /**
     *
     */
    @TableField(value = "telephone")
    private String telephone;
    /**
     *
     */
    @TableField(value = "mobile")
    private String mobile;
    /**
     *
     */
    @TableField(value = "email")
    private String email;
    /**
     *
     */
    @TableField(value = "address")
    private String address;
    /**
     * 证件类型
     */
    @TableField(value = "id_type")
    private String idType;
    /**
     * 证件号码
     */
    @TableField(value = "id_no")
    private String idNo;
    /**
     *
     */
    @TableField(value = "enabled")
    private String enabled;
    /**
     *
     */
    @TableField(value = "locked")
    private String locked;
    /**
     *
     */
    @TableField(value = "credentials_non_expired")
    private String credentialsNonExpired;
    /**
     *
     */
    @TableField(value = "if_admin")
    private String ifAdmin;
    /**
     *
     */
    @TableField(value = "if_all_dept")
    private String ifAllDept;
    /**
     *
     */
    @TableField(value = "if_all_user")
    private String ifAllUser;
    /**
     *
     */
    @TableField(value = "valid_date")
    private String validDate;
    /**
     *
     */
    @TableField(value = "expiry_date")
    private String expiryDate;
    /**
     *
     */
    @TableField(value = "note")
    private String note;
    /**
     *
     */
    @TableField(value = "hos_oid")
    private String hosOid;
    /**
     *
     */
    @TableField(value = "wxuserid")
    private String wxuserid;
    /**
     *
     */
    @TableField(value = "title")
    private String title;
    /**
     * 2018-2-27增加系统OID用于系统用户展示访问量
     */
    @TableField(value = "sys_oid")
    private String sysOid;
    /**
     *
     */
    @TableField(value = "vip_search")
    private String vipSearch;
    /**
     *
     */
    @TableField(value = "first_login_fail_time")
    private String firstLoginFailTime;
    /**
     *
     */
    @TableField(value = "login_fail_times")
    private Integer loginFailTimes;
    /**
     *
     */
    @TableField(value = "token_out_time")
    private Integer tokenOutTime;
    /**
     * 用户职位，例如医生，护士等
     */
    @TableField(value = "userpositioncode")
    private String userpositioncode;
    /**
     * 用户职位
     */
    @TableField(value = "userpositionname")
    private String userpositionname;
    /**
     * 用户类型编码。system：系统用户， ordinary：普通用户
     */
    @TableField(value = "user_type_code")
    private String userTypeCode;
    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    private String createTime;
    /**
     * 创建人
     */
    @TableField(value = "create_user")
    private String createUser;
    /**
     * 修改时间
     */
    @TableField(value = "modify_time")
    private String modifyTime;
    /**
     * 修改人
     */
    @TableField(value = "modify_user")
    private String modifyUser;
    /**
     * 业务系统编码
     */
    @TableField(value = "domain_system_code")
    private String domainSystemCode;
    /**
     * 科室表主键
     */
    @TableField(value = "pk_dept")
    private String pkDept;
    /**
     * 科室编码
     */
    @TableField(value = "deptcode")
    private String deptcode;
    /**
     * 科室名称
     */
    @TableField(value = "deptname")
    private String deptname;

    /**
     * 主键
     */
    public String getPkUser() {
        return pkUser;
    }

    /**
     * 主键
     */
    public void setPkUser(String pkUser) {
        this.pkUser = pkUser;
    }

    /**
     * 用户工号
     */
    public String getUsercode() {
        return usercode;
    }

    /**
     * 用户工号
     */
    public void setUsercode(String usercode) {
        this.usercode = usercode;
    }

    /**
     *
     */
    public String getUsername() {
        return username;
    }

    /**
     *
     */
    public void setUsername(String username) {
        this.username = username;
    }

    /**
     *
     */
    public String getPassword() {
        return password;
    }

    /**
     *
     */
    public void setPassword(String password) {
        this.password = password;
    }

    /**
     *
     */
    public String getName() {
        return name;
    }

    /**
     *
     */
    public void setName(String name) {
        this.name = name;
    }

    /**
     *
     */
    public String getTelephone() {
        return telephone;
    }

    /**
     *
     */
    public void setTelephone(String telephone) {
        this.telephone = telephone;
    }

    /**
     *
     */
    public String getMobile() {
        return mobile;
    }

    /**
     *
     */
    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    /**
     *
     */
    public String getEmail() {
        return email;
    }

    /**
     *
     */
    public void setEmail(String email) {
        this.email = email;
    }

    /**
     *
     */
    public String getAddress() {
        return address;
    }

    /**
     *
     */
    public void setAddress(String address) {
        this.address = address;
    }

    /**
     * 证件类型
     */
    public String getIdType() {
        return idType;
    }

    /**
     * 证件类型
     */
    public void setIdType(String idType) {
        this.idType = idType;
    }

    /**
     * 证件号码
     */
    public String getIdNo() {
        return idNo;
    }

    /**
     * 证件号码
     */
    public void setIdNo(String idNo) {
        this.idNo = idNo;
    }

    /**
     *
     */
    public String getEnabled() {
        return enabled;
    }

    /**
     *
     */
    public void setEnabled(String enabled) {
        this.enabled = enabled;
    }

    /**
     *
     */
    public String getLocked() {
        return locked;
    }

    /**
     *
     */
    public void setLocked(String locked) {
        this.locked = locked;
    }

    /**
     *
     */
    public String getCredentialsNonExpired() {
        return credentialsNonExpired;
    }

    /**
     *
     */
    public void setCredentialsNonExpired(String credentialsNonExpired) {
        this.credentialsNonExpired = credentialsNonExpired;
    }

    /**
     *
     */
    public String getIfAdmin() {
        return ifAdmin;
    }

    /**
     *
     */
    public void setIfAdmin(String ifAdmin) {
        this.ifAdmin = ifAdmin;
    }

    /**
     *
     */
    public String getIfAllDept() {
        return ifAllDept;
    }

    /**
     *
     */
    public void setIfAllDept(String ifAllDept) {
        this.ifAllDept = ifAllDept;
    }

    /**
     *
     */
    public String getIfAllUser() {
        return ifAllUser;
    }

    /**
     *
     */
    public void setIfAllUser(String ifAllUser) {
        this.ifAllUser = ifAllUser;
    }

    /**
     *
     */
    public String getValidDate() {
        return validDate;
    }

    /**
     *
     */
    public void setValidDate(String validDate) {
        this.validDate = validDate;
    }

    /**
     *
     */
    public String getExpiryDate() {
        return expiryDate;
    }

    /**
     *
     */
    public void setExpiryDate(String expiryDate) {
        this.expiryDate = expiryDate;
    }

    /**
     *
     */
    public String getNote() {
        return note;
    }

    /**
     *
     */
    public void setNote(String note) {
        this.note = note;
    }

    /**
     *
     */
    public String getHosOid() {
        return hosOid;
    }

    /**
     *
     */
    public void setHosOid(String hosOid) {
        this.hosOid = hosOid;
    }

    /**
     *
     */
    public String getWxuserid() {
        return wxuserid;
    }

    /**
     *
     */
    public void setWxuserid(String wxuserid) {
        this.wxuserid = wxuserid;
    }

    /**
     *
     */
    public String getTitle() {
        return title;
    }

    /**
     *
     */
    public void setTitle(String title) {
        this.title = title;
    }

    /**
     * 2018-2-27增加系统OID用于系统用户展示访问量
     */
    public String getSysOid() {
        return sysOid;
    }

    /**
     * 2018-2-27增加系统OID用于系统用户展示访问量
     */
    public void setSysOid(String sysOid) {
        this.sysOid = sysOid;
    }

    /**
     *
     */
    public String getVipSearch() {
        return vipSearch;
    }

    /**
     *
     */
    public void setVipSearch(String vipSearch) {
        this.vipSearch = vipSearch;
    }

    /**
     *
     */
    public String getFirstLoginFailTime() {
        return firstLoginFailTime;
    }

    /**
     *
     */
    public void setFirstLoginFailTime(String firstLoginFailTime) {
        this.firstLoginFailTime = firstLoginFailTime;
    }

    /**
     *
     */
    public Integer getLoginFailTimes() {
        return loginFailTimes;
    }

    /**
     *
     */
    public void setLoginFailTimes(Integer loginFailTimes) {
        this.loginFailTimes = loginFailTimes;
    }

    /**
     *
     */
    public Integer getTokenOutTime() {
        return tokenOutTime;
    }

    /**
     *
     */
    public void setTokenOutTime(Integer tokenOutTime) {
        this.tokenOutTime = tokenOutTime;
    }

    /**
     * 用户职位，例如医生，护士等
     */
    public String getUserpositioncode() {
        return userpositioncode;
    }

    /**
     * 用户职位，例如医生，护士等
     */
    public void setUserpositioncode(String userpositioncode) {
        this.userpositioncode = userpositioncode;
    }

    /**
     * 用户职位
     */
    public String getUserpositionname() {
        return userpositionname;
    }

    /**
     * 用户职位
     */
    public void setUserpositionname(String userpositionname) {
        this.userpositionname = userpositionname;
    }

    /**
     * 用户类型编码。system：系统用户， ordinary：普通用户
     */
    public String getUserTypeCode() {
        return userTypeCode;
    }

    /**
     * 用户类型编码。system：系统用户， ordinary：普通用户
     */
    public void setUserTypeCode(String userTypeCode) {
        this.userTypeCode = userTypeCode;
    }

    /**
     * 创建时间
     */
    public String getCreateTime() {
        return createTime;
    }

    /**
     * 创建时间
     */
    public void setCreateTime(String createTime) {
        this.createTime = createTime;
    }

    /**
     * 创建人
     */
    public String getCreateUser() {
        return createUser;
    }

    /**
     * 创建人
     */
    public void setCreateUser(String createUser) {
        this.createUser = createUser;
    }

    /**
     * 修改时间
     */
    public String getModifyTime() {
        return modifyTime;
    }

    /**
     * 修改时间
     */
    public void setModifyTime(String modifyTime) {
        this.modifyTime = modifyTime;
    }

    /**
     * 修改人
     */
    public String getModifyUser() {
        return modifyUser;
    }

    /**
     * 修改人
     */
    public void setModifyUser(String modifyUser) {
        this.modifyUser = modifyUser;
    }

    /**
     * 业务系统编码
     */
    public String getDomainSystemCode() {
        return domainSystemCode;
    }

    /**
     * 业务系统编码
     */
    public void setDomainSystemCode(String domainSystemCode) {
        this.domainSystemCode = domainSystemCode;
    }

    /**
     * 科室表主键
     */
    public String getPkDept() {
        return pkDept;
    }

    /**
     * 科室表主键
     */
    public void setPkDept(String pkDept) {
        this.pkDept = pkDept;
    }

    /**
     * 科室编码
     */
    public String getDeptcode() {
        return deptcode;
    }

    /**
     * 科室编码
     */
    public void setDeptcode(String deptcode) {
        this.deptcode = deptcode;
    }

    /**
     * 科室名称
     */
    public String getDeptname() {
        return deptname;
    }

    /**
     * 科室名称
     */
    public void setDeptname(String deptname) {
        this.deptname = deptname;
    }

    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        SecurityUser other = (SecurityUser) that;
        return (this.getPkUser() == null ? other.getPkUser() == null : this.getPkUser().equals(other.getPkUser()))
                && (this.getUsercode() == null ? other.getUsercode() == null : this.getUsercode().equals(other.getUsercode()))
                && (this.getUsername() == null ? other.getUsername() == null : this.getUsername().equals(other.getUsername()))
                && (this.getPassword() == null ? other.getPassword() == null : this.getPassword().equals(other.getPassword()))
                && (this.getName() == null ? other.getName() == null : this.getName().equals(other.getName()))
                && (this.getTelephone() == null ? other.getTelephone() == null : this.getTelephone().equals(other.getTelephone()))
                && (this.getMobile() == null ? other.getMobile() == null : this.getMobile().equals(other.getMobile()))
                && (this.getEmail() == null ? other.getEmail() == null : this.getEmail().equals(other.getEmail()))
                && (this.getAddress() == null ? other.getAddress() == null : this.getAddress().equals(other.getAddress()))
                && (this.getIdType() == null ? other.getIdType() == null : this.getIdType().equals(other.getIdType()))
                && (this.getIdNo() == null ? other.getIdNo() == null : this.getIdNo().equals(other.getIdNo()))
                && (this.getEnabled() == null ? other.getEnabled() == null : this.getEnabled().equals(other.getEnabled()))
                && (this.getLocked() == null ? other.getLocked() == null : this.getLocked().equals(other.getLocked()))
                && (this.getCredentialsNonExpired() == null ? other.getCredentialsNonExpired() == null : this.getCredentialsNonExpired().equals(other.getCredentialsNonExpired()))
                && (this.getIfAdmin() == null ? other.getIfAdmin() == null : this.getIfAdmin().equals(other.getIfAdmin()))
                && (this.getIfAllDept() == null ? other.getIfAllDept() == null : this.getIfAllDept().equals(other.getIfAllDept()))
                && (this.getIfAllUser() == null ? other.getIfAllUser() == null : this.getIfAllUser().equals(other.getIfAllUser()))
                && (this.getValidDate() == null ? other.getValidDate() == null : this.getValidDate().equals(other.getValidDate()))
                && (this.getExpiryDate() == null ? other.getExpiryDate() == null : this.getExpiryDate().equals(other.getExpiryDate()))
                && (this.getNote() == null ? other.getNote() == null : this.getNote().equals(other.getNote()))
                && (this.getHosOid() == null ? other.getHosOid() == null : this.getHosOid().equals(other.getHosOid()))
                && (this.getWxuserid() == null ? other.getWxuserid() == null : this.getWxuserid().equals(other.getWxuserid()))
                && (this.getTitle() == null ? other.getTitle() == null : this.getTitle().equals(other.getTitle()))
                && (this.getSysOid() == null ? other.getSysOid() == null : this.getSysOid().equals(other.getSysOid()))
                && (this.getVipSearch() == null ? other.getVipSearch() == null : this.getVipSearch().equals(other.getVipSearch()))
                && (this.getFirstLoginFailTime() == null ? other.getFirstLoginFailTime() == null : this.getFirstLoginFailTime().equals(other.getFirstLoginFailTime()))
                && (this.getLoginFailTimes() == null ? other.getLoginFailTimes() == null : this.getLoginFailTimes().equals(other.getLoginFailTimes()))
                && (this.getTokenOutTime() == null ? other.getTokenOutTime() == null : this.getTokenOutTime().equals(other.getTokenOutTime()))
                && (this.getUserpositioncode() == null ? other.getUserpositioncode() == null : this.getUserpositioncode().equals(other.getUserpositioncode()))
                && (this.getUserpositionname() == null ? other.getUserpositionname() == null : this.getUserpositionname().equals(other.getUserpositionname()))
                && (this.getUserTypeCode() == null ? other.getUserTypeCode() == null : this.getUserTypeCode().equals(other.getUserTypeCode()))
                && (this.getCreateTime() == null ? other.getCreateTime() == null : this.getCreateTime().equals(other.getCreateTime()))
                && (this.getCreateUser() == null ? other.getCreateUser() == null : this.getCreateUser().equals(other.getCreateUser()))
                && (this.getModifyTime() == null ? other.getModifyTime() == null : this.getModifyTime().equals(other.getModifyTime()))
                && (this.getModifyUser() == null ? other.getModifyUser() == null : this.getModifyUser().equals(other.getModifyUser()))
                && (this.getDomainSystemCode() == null ? other.getDomainSystemCode() == null : this.getDomainSystemCode().equals(other.getDomainSystemCode()))
                && (this.getPkDept() == null ? other.getPkDept() == null : this.getPkDept().equals(other.getPkDept()))
                && (this.getDeptcode() == null ? other.getDeptcode() == null : this.getDeptcode().equals(other.getDeptcode()))
                && (this.getDeptname() == null ? other.getDeptname() == null : this.getDeptname().equals(other.getDeptname()));
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getPkUser() == null) ? 0 : getPkUser().hashCode());
        result = prime * result + ((getUsercode() == null) ? 0 : getUsercode().hashCode());
        result = prime * result + ((getUsername() == null) ? 0 : getUsername().hashCode());
        result = prime * result + ((getPassword() == null) ? 0 : getPassword().hashCode());
        result = prime * result + ((getName() == null) ? 0 : getName().hashCode());
        result = prime * result + ((getTelephone() == null) ? 0 : getTelephone().hashCode());
        result = prime * result + ((getMobile() == null) ? 0 : getMobile().hashCode());
        result = prime * result + ((getEmail() == null) ? 0 : getEmail().hashCode());
        result = prime * result + ((getAddress() == null) ? 0 : getAddress().hashCode());
        result = prime * result + ((getIdType() == null) ? 0 : getIdType().hashCode());
        result = prime * result + ((getIdNo() == null) ? 0 : getIdNo().hashCode());
        result = prime * result + ((getEnabled() == null) ? 0 : getEnabled().hashCode());
        result = prime * result + ((getLocked() == null) ? 0 : getLocked().hashCode());
        result = prime * result + ((getCredentialsNonExpired() == null) ? 0 : getCredentialsNonExpired().hashCode());
        result = prime * result + ((getIfAdmin() == null) ? 0 : getIfAdmin().hashCode());
        result = prime * result + ((getIfAllDept() == null) ? 0 : getIfAllDept().hashCode());
        result = prime * result + ((getIfAllUser() == null) ? 0 : getIfAllUser().hashCode());
        result = prime * result + ((getValidDate() == null) ? 0 : getValidDate().hashCode());
        result = prime * result + ((getExpiryDate() == null) ? 0 : getExpiryDate().hashCode());
        result = prime * result + ((getNote() == null) ? 0 : getNote().hashCode());
        result = prime * result + ((getHosOid() == null) ? 0 : getHosOid().hashCode());
        result = prime * result + ((getWxuserid() == null) ? 0 : getWxuserid().hashCode());
        result = prime * result + ((getTitle() == null) ? 0 : getTitle().hashCode());
        result = prime * result + ((getSysOid() == null) ? 0 : getSysOid().hashCode());
        result = prime * result + ((getVipSearch() == null) ? 0 : getVipSearch().hashCode());
        result = prime * result + ((getFirstLoginFailTime() == null) ? 0 : getFirstLoginFailTime().hashCode());
        result = prime * result + ((getLoginFailTimes() == null) ? 0 : getLoginFailTimes().hashCode());
        result = prime * result + ((getTokenOutTime() == null) ? 0 : getTokenOutTime().hashCode());
        result = prime * result + ((getUserpositioncode() == null) ? 0 : getUserpositioncode().hashCode());
        result = prime * result + ((getUserpositionname() == null) ? 0 : getUserpositionname().hashCode());
        result = prime * result + ((getUserTypeCode() == null) ? 0 : getUserTypeCode().hashCode());
        result = prime * result + ((getCreateTime() == null) ? 0 : getCreateTime().hashCode());
        result = prime * result + ((getCreateUser() == null) ? 0 : getCreateUser().hashCode());
        result = prime * result + ((getModifyTime() == null) ? 0 : getModifyTime().hashCode());
        result = prime * result + ((getModifyUser() == null) ? 0 : getModifyUser().hashCode());
        result = prime * result + ((getDomainSystemCode() == null) ? 0 : getDomainSystemCode().hashCode());
        result = prime * result + ((getPkDept() == null) ? 0 : getPkDept().hashCode());
        result = prime * result + ((getDeptcode() == null) ? 0 : getDeptcode().hashCode());
        result = prime * result + ((getDeptname() == null) ? 0 : getDeptname().hashCode());
        return result;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", pkUser=").append(pkUser);
        sb.append(", usercode=").append(usercode);
        sb.append(", username=").append(username);
        sb.append(", password=").append(password);
        sb.append(", name=").append(name);
        sb.append(", telephone=").append(telephone);
        sb.append(", mobile=").append(mobile);
        sb.append(", email=").append(email);
        sb.append(", address=").append(address);
        sb.append(", idType=").append(idType);
        sb.append(", idNo=").append(idNo);
        sb.append(", enabled=").append(enabled);
        sb.append(", locked=").append(locked);
        sb.append(", credentialsNonExpired=").append(credentialsNonExpired);
        sb.append(", ifAdmin=").append(ifAdmin);
        sb.append(", ifAllDept=").append(ifAllDept);
        sb.append(", ifAllUser=").append(ifAllUser);
        sb.append(", validDate=").append(validDate);
        sb.append(", expiryDate=").append(expiryDate);
        sb.append(", note=").append(note);
        sb.append(", hosOid=").append(hosOid);
        sb.append(", wxuserid=").append(wxuserid);
        sb.append(", title=").append(title);
        sb.append(", sysOid=").append(sysOid);
        sb.append(", vipSearch=").append(vipSearch);
        sb.append(", firstLoginFailTime=").append(firstLoginFailTime);
        sb.append(", loginFailTimes=").append(loginFailTimes);
        sb.append(", tokenOutTime=").append(tokenOutTime);
        sb.append(", userpositioncode=").append(userpositioncode);
        sb.append(", userpositionname=").append(userpositionname);
        sb.append(", userTypeCode=").append(userTypeCode);
        sb.append(", createTime=").append(createTime);
        sb.append(", createUser=").append(createUser);
        sb.append(", modifyTime=").append(modifyTime);
        sb.append(", modifyUser=").append(modifyUser);
        sb.append(", domainSystemCode=").append(domainSystemCode);
        sb.append(", pkDept=").append(pkDept);
        sb.append(", deptcode=").append(deptcode);
        sb.append(", deptname=").append(deptname);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}