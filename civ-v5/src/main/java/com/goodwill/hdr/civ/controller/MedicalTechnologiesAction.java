package com.goodwill.hdr.civ.controller;

import com.goodwill.hdr.civ.service.*;
import com.goodwill.hdr.civ.vo.ExamVoForMedicalTechnologies;
import com.goodwill.hdr.civ.vo.InspectVoForMedicalTechologies;
import com.goodwill.hdr.web.common.vo.ResultVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang.StringUtils;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 * @Description 类描述：医技视图Action
 * @Date 2022年8月9日
 * @modify 修改记录：
 */

@RequestMapping("/technologies")
@RestController
@Api(tags = "医技视图")
public class MedicalTechnologiesAction {

    @Autowired
    private DiagnoseService diagnoseService;
    @Autowired
    private OrderService orderService;
    @Autowired
    private InspectReportService inspectReportService;

    private final MedicalRecordService medicalRecordService;

    private final CheckReportService checkService;

    private final Map<String, String> visitTypeConstantMap;


    public MedicalTechnologiesAction(MedicalRecordService medicalRecordService, CheckReportService checkService, @Qualifier("visitTypeConstantMap") Map<String, String> visitTypeConstantMap) {
        this.medicalRecordService = medicalRecordService;
        this.checkService = checkService;
        this.visitTypeConstantMap = visitTypeConstantMap;
    }

    /**
     * 获取主诉、现病史、既往史、个人史、家族史、体格检查
     */
    @PostMapping("/medicalRecordInfo")
    public ResultVO<Set<Map.Entry<String, String>>> getMedicalRecordInfo(String this_oid, String oid, String patientId, String visitId, String visitType) {
        return medicalRecordService.getMedicalRecordInfo(oid, patientId, visitId, visitTypeConstantMap.get(visitType));
    }


    @PostMapping("/medicalRecordList")
    public ResultVO<List<Map<String, Object>>> getMedicalRecordList(String this_oid, String oid, String oidLast, String patientId, String visitId, String visitType, String outPatientId) {
        String visitTypeCode = visitTypeConstantMap.get(visitType);
        return medicalRecordService.getTechnologiesMedicalRecordList(oid, oidLast, patientId, visitId, visitTypeCode, outPatientId);
    }

    @PostMapping("/checkItem")
    public ResultVO<Map<String, ExamVoForMedicalTechnologies>> getCheckListForMedicalTechnologies(String this_oid, String oid, String oidLast, String patientId, String visitId, String visitType, String outPatientId, String date) {
        String visitTypeCode = visitTypeConstantMap.get(visitType);
        return checkService.getCheckListForMedicalTechnologies(this_oid, oid, oidLast, patientId, visitId, visitTypeCode, outPatientId, date);
    }

    @PostMapping("/checkItemList")
    public ResultVO<List<Map<String, String>>> getCheckItemList(String this_oid, String outPatientId, String itemName, String date, boolean currentVisit) {
        return checkService.getCheckItemList(this_oid, outPatientId, itemName, date, currentVisit);
    }

    @ApiOperation(value = "获取实验室报告", notes = "获取实验室报告", httpMethod = "POST")
    @RequestMapping(value = "/getInspect", method = RequestMethod.POST)
    public ResultVO<Map<String, InspectVoForMedicalTechologies>> getInspect(String this_oid, String oid, String oidLast, String patientId, String visitId, String visitTypeCode, String outPatientId, String date) {
        if (StringUtils.isNotBlank(visitTypeCode) && visitTypeCode.equals("INPV")) {
            visitTypeCode = "02";
        } else {
            visitTypeCode = "01";
        }
        return inspectReportService.getInspect(oid, oidLast, patientId, visitId, visitTypeCode, outPatientId, date);

    }

    @ApiOperation(value = "获取实验室报告详情", notes = "获取实验室报告详情", httpMethod = "POST")
    @RequestMapping(value = "/getInspectList", method = RequestMethod.POST)
    public ResultVO<List<Map<String, String>>> getInspectList(String this_oid, String outPatientId, String itemName, String date, boolean currentVisit) {
        return inspectReportService.getInspectList(outPatientId, itemName, date, currentVisit);

    }


    @ApiOperation(value = "获取诊断信息", notes = "获取诊断信息", httpMethod = "POST")
    @RequestMapping(value = "/getDiags", method = RequestMethod.POST)
    public ResultVO<Map<String, Object>> getDiags(String this_oid, String oid, String patientId, String visitId, String visitType) {
        if (StringUtils.isNotBlank(visitType) && visitType.equals("INPV")) {
            visitType = "02";
        } else {
            visitType = "01";
        }
        ResultVO<Map<String, Object>> resultVO = diagnoseService.getDiags(this_oid, oid, patientId, visitId, visitType);
        return resultVO;
    }

    @ApiOperation(value = "获取历次诊断信息", notes = "获取历次诊断信息", httpMethod = "POST")
    @RequestMapping(value = "/getDiagLists", method = RequestMethod.POST)
    public ResultVO<List<Map<String, Object>>> getDiagLists(String this_oid, String oid, String outPatientId) {
        ResultVO<List<Map<String, Object>>> resultVO = diagnoseService.getDiagLists(this_oid, oid, outPatientId);
        return resultVO;
    }

    @ApiOperation(value = "获取手术医嘱", notes = "获取手术医嘱", httpMethod = "POST")
    @RequestMapping(value = "/getOperList", method = RequestMethod.POST)
    public ResultVO<List<Map<String, String>>> getOperList(String this_oid, String oid, String patientId, String visitId, String visitType) {
        if (StringUtils.isNotBlank(visitType) && visitType.equals("INPV")) {
            visitType = "02";
        } else {
            visitType = "01";
        }
        ResultVO<List<Map<String, String>>> resultVO = orderService.getOperApplyList(this_oid, oid, patientId, visitId, visitType);
        return resultVO;
    }
}
