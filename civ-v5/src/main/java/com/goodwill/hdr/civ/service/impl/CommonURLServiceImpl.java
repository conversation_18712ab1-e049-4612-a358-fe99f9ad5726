package com.goodwill.hdr.civ.service.impl;


import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;


import com.goodwill.hdr.civ.config.CommonConfig;
import com.goodwill.hdr.civ.config.Config;
import com.goodwill.hdr.civ.config.ConfigCache;
import com.goodwill.hdr.civ.enums.HdrConstantEnum;
import com.goodwill.hdr.civ.enums.HdrTableEnum;
import com.goodwill.hdr.civ.enums.commonModule.CommonUrlEnum;
import com.goodwill.hdr.civ.enums.commonModule.SysCodeEnum;
import com.goodwill.hdr.civ.service.CommonURLService;
import com.goodwill.hdr.civ.service.PowerService;
import com.goodwill.hdr.civ.utils.*;
import com.goodwill.hdr.civ.vo.*;
import com.goodwill.hdr.core.orm.Page;
import com.goodwill.hdr.core.orm.PropertyType;
import com.goodwill.hdr.core.utils.ApplicationException;
import com.goodwill.hdr.hbase.bo.PropertyFilter;
import com.goodwill.hdr.hbase.dto.requestVo.PageRequest;
import com.goodwill.hdr.hbase.dto.responseVo.PageResultVo;
import com.goodwill.hdr.hbase.dto.responseVo.ResultVo;
import com.goodwill.hdr.hbaseQueryClient.builder.PageRequestBuilder;
import com.goodwill.hdr.hbaseQueryClient.service.HbaseQueryClient;
import com.goodwill.hdr.security.utils.SecurityCommonUtil;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URL;
import java.net.URLEncoder;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import com.goodwill.hdr.core.orm.MatchType;

/**
 * <AUTHOR>
 * @Description 类描述：
 * @Date 2019-12-09 15:04
 * @modify 修改记录：
 */
@Service
public class CommonURLServiceImpl implements CommonURLService {
    private static final Logger logger = LoggerFactory.getLogger(CommonURLServiceImpl.class);
    List<String> encryptSysCodeList;
    private ObjectMapper objectMapper;
    private CommonModuleService commonModuleService;
    private EncryptService encryptService;
    @Autowired
    private PowerService powerService;
    private final HbaseQueryClient hbaseQueryClient;

    @Autowired
    public CommonURLServiceImpl(ObjectMapper objectMapper, CommonModuleService commonModuleService, EncryptService encryptService, HbaseQueryClient hbaseQueryClient) {
        this.objectMapper = objectMapper;
        this.commonModuleService = commonModuleService;
        this.encryptService = encryptService;
        this.hbaseQueryClient = hbaseQueryClient;
        this.encryptSysCodeList = new ArrayList<>();
        encryptSysCodeList.add(SysCodeEnum.BASE64.getCode());
        encryptSysCodeList.add(SysCodeEnum.DES.getCode());
        encryptSysCodeList.add(SysCodeEnum.LZ.getCode());
        encryptSysCodeList.add(SysCodeEnum.AES.getCode());
        encryptSysCodeList.add(SysCodeEnum.CEMSMD5.getCode());
        encryptSysCodeList.add(SysCodeEnum.RSA.getCode());
        encryptSysCodeList.add(SysCodeEnum.SM4.getCode());
    }

    private static void sortListThenTransferToPage(int pageNo, int pageSize, String sort, String sortField, Page<Map<String, String>> page, List<Map<String, String>> totalList) {
        //筛选出orderby不为空的数据
        List<Map<String, String>> orderByList1=totalList.stream().filter(map-> StringUtils.isNotBlank(map.get(sortField))).collect(Collectors.toList());
        //筛选出orderby为空的数据
        List<Map<String, String>> orderByList2=totalList.stream().filter(map-> StringUtils.isBlank(map.get(sortField))).collect(Collectors.toList());
        if (sort.equals("desc")) {
            totalList = orderByList1.stream().sorted((map1, map2) -> map2.get(sortField).compareToIgnoreCase(map1.get(sortField))).collect(Collectors.toList());
        } else {
            totalList = orderByList1.stream().sorted((map1, map2) -> map1.get(sortField).compareToIgnoreCase(map2.get(sortField))).collect(Collectors.toList());
        }
        totalList.addAll(orderByList2);
        ListPage<Map<String, String>> listPage = new ListPage<Map<String, String>>(totalList, pageNo, pageSize);
        page.setResult(listPage.getPagedList());
        page.setPageNo(pageNo);
        page.setPageSize(pageSize);
        page.setTotalCount(totalList.size());
    }

    /**
     * 20241107 应西南医科大学附属口腔医院调阅第三方接口要求
     *   第三方url：http://xxx/getBrowseHealthFileV?hosCode=xxx&operator=xxx&ipAddress=xxx&applicateScene=xxx&&message={"cardId":"","name":""}
     *   要求：hosCode、operator、ipAddress、applicateScene值进行base64加密处理，
     *        message值的json字符串(下文以组合参数称)进行sm4加密处理
     * 作如下修改：
     * 1）添加该方法getGroupParamMapWithSM4判断配置中是否有组合参数进行sm4加密
     * 2）添加代码判断是否全部参数值需要进行加密
     *
     * @param oid
     * @param patientId
     * @param visitId
     * @param visitType
     * @param id
     * @param sysCode
     * @return
     */
    @Override
    public Map<String, String> getCommonUrl(String oid, String patientId, String visitId, String visitType, String id, String sysCode) {
        logger.info("进入getCommonUrl:" + sysCode);
        // TODO Auto-generated method stub
        Map<String, String> rs = new HashMap<String, String>();


        String url = commonModuleService.getURL(oid, id);//获取系统URL
        logger.info("读取到url:" + url);
        if (StringUtils.isBlank(url)) {
            rs.put("status", "0");
            rs.put("msg", "请联系管理员，请配置调用地址。");
            return rs;
        }

        List<String> params = commonModuleService.getParams(oid, id);
        logger.info("获取到参数:" + params);
        Map<String, String> param_configs = commonModuleService.getParam_configs(oid, id);
        logger.info("获取到参数配置：" + param_configs);
        Map<String, String> param_value = getParamValue(oid, patientId, visitId, visitType, param_configs);
        logger.info("获取到参数和值：" + param_value);
        if (encryptSysCodeList.contains(sysCode)) {
            //获取不需要加密的字段配置
            String unencryptField=ConfigCache.getCache(oid, CommonUrlEnum.UNENCRYPT_FIELD.getCode(id));
            logger.info("不需要加密的字段：" + unencryptField);
            //剔出不需要加密的字段
            String[] keysToRemove = unencryptField.split(",");
            Map<String, String> encryptFieldMap = param_value.entrySet().stream()
                    .filter(entry -> !Arrays.asList(keysToRemove).contains(entry.getKey()))
                    .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));
            logger.info("需要加密的参数：" + encryptFieldMap);

            getParamMapWithEncrypt(oid, patientId, visitId, id, sysCode, encryptFieldMap);
            param_value.putAll(encryptFieldMap);
            logger.info("加密后的参数：" + param_value);
        }

        for (String field : params) {
            logger.info("进入赋值阶段，此处获取到的field：" + field);
            String value = param_value.get(field);
            logger.info("进入赋值阶段，此处获取到的value:" + value);
            if (StringUtils.isNotBlank(value)) {
                url = url.replace("#{" + field + "}", value);
            } else {
                rs.put("status", "0");
                rs.put("msg", "未查询到" + field + "参数！");
                return rs;
            }
        }
        logger.info("url:"+url);

        //20241107添加
        url = getGroupParamMapWithSM4(oid, id, params, param_value, url);

       /* //获取token
        String token="";
        String tokenUrl = commonModuleService.getTokenUrl(oid, id);
        if(StringUtils.isNotBlank(tokenUrl)){
            if(HdrConstantEnum.HOSPITAL_BJTR.getCode().equals(oid)){
                Map<String, String> paramConfigs =new HashMap<>();
                paramConfigs.put("examNo","EXAM_NO,HDR_EXAM_REPORT");
                Map<String, String> paramValue = getParamValue(oid, patientId, visitId, visitType, paramConfigs);
                System.out.printf("paramValue: "+paramValue);
                token = WsUtil.getToken(tokenUrl,paramValue.get("examNo"));
            }
            url = url + "?token=" + token;
            logger.info("添加token后的url："+url);
        }*/

        rs.put("status", "1");
        rs.put("linkType", commonModuleService.getLinkType(oid, id));
        rs.put("url", url);
        return rs;
    }

    /**
     * 组合参数SM4加密
     * eg：http://xxx.?message={"cardId":"#{cardId}","name":"#{name}"}中的message值
     *
     * @param oid
     * @param id
     * @param params
     * @param param_value
     * @return
     */
    public String getGroupParamMapWithSM4(String oid, String id, List<String> params, Map<String, String> param_value, String url) {
        String sm4Field = ConfigCache.getCache(oid, CommonUrlEnum.SM4_FIELD.getCode(id));
        if (StringUtils.isBlank(sm4Field)) {
            return url;
        }
        for (String field : params) {
            String value = param_value.get(field);
            if (StringUtils.isNotBlank(value)) {
                sm4Field = sm4Field.replace("#{" + field + "}", value);
            }
        }
        logger.info("需要SM4加密的值：" + sm4Field);

        //获取秘钥
        String sm4Key = ConfigCache.getCache(oid, CommonUrlEnum.SM4_KEY.getCode(id));
        if (StringUtils.isBlank(sm4Key)) {
            logger.info("秘钥SD_" + id + "_SM4_KEY值为空");
            return url;
        }
        logger.info("秘钥为：" + sm4Key);

        String sm4KeyLen=ConfigCache.getCache(oid, CommonUrlEnum.SM4_KEY_LEN.getCode(id));
        logger.info("秘钥长度为："+sm4KeyLen);
        if(StringUtils.isBlank(sm4KeyLen)){
            logger.info("秘钥长度SD_"+ id + "_SM4_KEY_LEN为空");
            return url;
        }

        String encryptedValue = "";
        try {
            if(Integer.parseInt(sm4KeyLen)==16){
                encryptedValue = encryptService.SM4EncryptBy16Key(sm4Key, sm4Field);
            }else{
                encryptedValue = encryptService.SM4EncryptBy32Key(sm4Key, sm4Field);
            }
        } catch (Exception e) {
            throw new ApplicationException("SM4加密失败");
        }

        String sm4IsUpper=ConfigCache.getCache(oid, CommonUrlEnum.SM4_ISUPPER.getCode(id));
        logger.info("SM4加密后值是否大写："+sm4IsUpper);
        if(Boolean.parseBoolean(sm4IsUpper)){
            encryptedValue=encryptedValue.toUpperCase();
        }else{
            encryptedValue=encryptedValue.toLowerCase();
        }
        logger.info("SM4加密后值：" + encryptedValue);

        url = url.replace(sm4Field, encryptedValue);
        logger.info("组合参数加密后的url:" + url);

        return url;
    }

    @Override
    public Map<String, String> getEhrUrl(String oid, String idCardNo, String id, String sysCode) {
        Map<String, String> rs = new HashMap<String, String>();
        String url = commonModuleService.getURL(oid, id);//获取系统URL
        if (StringUtils.isBlank(url)) {
            rs.put("status", "0");
            rs.put("msg", "请联系管理员，请配置调用地址。");
            return rs;
        }
        String paramMapJson = ConfigCache.getCache(oid, "EHR_PARAM_MAP");
        Map<String, String> paramMap = new HashMap<>();
        try {
            paramMap = objectMapper.readValue(paramMapJson, new TypeReference<Map<String, String>>() {
            });
        } catch (IOException e) {
            throw new ApplicationException("EHR_PARAM_MAP格式有误", e);
        }

        String user = encryptService.EhrBase64Encode(paramMap.get("user"));
        paramMap.put("user", user);
        String password = encryptService.EhrBase64Encode(paramMap.get("password"));
        paramMap.put("password", password);
        String idCard = encryptService.EhrBase64Encode(idCardNo);
        paramMap.put("idCard", idCard);
        String docname = encryptService.EhrChineseUriEncode(paramMap.get("docname"));
        paramMap.put("docname", docname);
        String datasource = encryptService.EhrChineseUriEncode(paramMap.get("datasource"));
        paramMap.put("datasource", datasource);

        for (String key : paramMap.keySet()) {
            url = url.replace("#{" + key + "}", paramMap.get(key));
        }
        rs.put("status", "1");
        rs.put("linkType", commonModuleService.getLinkType(oid, sysCode));
        rs.put("url", url);
        return rs;
    }

    @Override
    public Map<String, String> getSysencUrl(String oid, String patientId, String visitId, String visitType, String id, String sysCode) {
        logger.info("进入getSysencUrl:" + sysCode);
        // TODO Auto-generated method stub
        Map<String, String> rs = new HashMap<String, String>();


        String url = commonModuleService.getURL(oid, id);//获取系统URL
        logger.info("读取到url:" + url);
        if (StringUtils.isBlank(url)) {
            rs.put("status", "0");
            rs.put("msg", "请联系管理员，请配置调用地址。");
            return rs;
        }

        List<String> params = commonModuleService.getParams(oid, id);
        logger.info("获取到参数:" + params);
        Map<String, String> param_configs = commonModuleService.getParam_configs(oid, id);
        logger.info("获取到参数配置：" + param_configs);
        Map<String, String> param_value = getParamValue(oid, patientId, visitId, visitType, param_configs);
        logger.info("获取到参数和值：" + param_value);
        if (encryptSysCodeList.contains(sysCode)) {
            getParamMapWithEncrypt(oid, patientId, visitId, id, sysCode, param_value);
            logger.info("加密后的参数：" + param_value);
        }

        for (String field : params) {
            logger.info("进入赋值阶段，此处获取到的field：" + field);
            String value = param_value.get(field);
            logger.info("进入赋值阶段，此处获取到的value:" + value);
            if (StringUtils.isNotBlank(value)) {
                url = url.replace("#{" + field + "}", value);
            } else {
                rs.put("status", "0");
                rs.put("msg", "未查询到" + field + "参数！");
                return rs;
            }
        }

        //系统全局加密
        Map<String, String> sysenc = powerService.getSysConfigByType("all", "StartUse_HidePatKeyM");
        if (sysenc != null) {
            logger.info("系统全局加密参数：" + sysenc.get("result"));
            url = url + "&secret=" + sysenc.get("result");
        } else {
            logger.info("系统全局加密参数为空，返回未加密url");
            url = url + "&secret=0";
        }


        rs.put("status", "1");
        rs.put("linkType", commonModuleService.getLinkType(oid, id));
        rs.put("url", url);
        return rs;
    }

    @Override
    public Page<Map<String, String>> getTableCommonModule(String oid, String patientId, String visitId, String visitType, String id, List<KeyValueDto> paramList, int pageNo, int pageSize) {

        TableTemplateConfig tableTemplateConfig = getTableTemplateConfig(oid, id);

        Page<Map<String, String>> page = new Page<>();
        List<Map<String, String>> totalList=new ArrayList<>();
        String sourceType=tableTemplateConfig.getSourceType();
        if(sourceType.equalsIgnoreCase("table")){
            //查询表
            totalList = queryThenConvertResult(oid, patientId, visitId, visitType, tableTemplateConfig.getCivJsonTemplateList(), paramList);
        } else if (sourceType.equalsIgnoreCase("ws")){
            //调用第三方接口
            totalList = getURLByOtherInterface(oid, id, patientId, visitId, visitType, tableTemplateConfig);
        }
        sortListThenTransferToPage(pageNo, pageSize, tableTemplateConfig.getSort(), tableTemplateConfig.getSortField(), page, totalList);

        return page;
    }

    private List<Map<String, String>> queryThenConvertResult(String oid, String patientId, String visitId, String visitType, List<CivJsonTemplate> civJsonTemplateList, List<KeyValueDto> paramList) {
        List<Map<String, String>> totalList = new ArrayList<>();
        for (CivJsonTemplate civJsonTemplate : civJsonTemplateList) {
            List<PropertyFilter> filters = new ArrayList<>();
            String[] columns = new String[civJsonTemplate.getColumnConfigList().size()];
            List<CivCondition> civConditions = civJsonTemplate.getConditionList();
            for (CivCondition civCondition : civConditions) {
                String reg = "#\\{\\S*}";
                Pattern p = Pattern.compile(reg);
                Matcher m = p.matcher(civCondition.getValue());
                if (m.matches()) {
                    String group = m.group();
                    for (KeyValueDto keyValueDto : paramList) {
                        String a = "#{" + keyValueDto.getKey() + "}";
                        if (a.equals(group)) {
                            String s = m.replaceAll(keyValueDto.getValue());
                            civCondition.setValue(s);
                        }
                    }

                }
                filters.add(new PropertyFilter(civCondition.getField(), civCondition.getOp(), civCondition.getValue()));
            }
            civJsonTemplate.setConditionList(civConditions);

            int i = 0;
            for (ColumnConfig columnConfig : civJsonTemplate.getColumnConfigList()) {
                columns[i++] = columnConfig.getField();
            }

            String tableName = civJsonTemplate.getTableName();
            List<Map<String, String>> mapList = new ArrayList<>();
            ResultVo<PageResultVo<Map<String, String>>> resultVo = hbaseQueryClient.getPageByCondition(
                    PageRequestBuilder.init()
                            .tableName(tableName)
                            .patientId(patientId)
                            .oid(oid)
                            .visitId(visitId)
                            .visitTypeCode(visitType)
                            .filters(filters)
                            .pageNo(0)
                            .pageSize(0)
                            .orderBy("")
                            .desc()
                            .column(columns)
                            .build());


            if (resultVo.isSuccess()) {
                mapList = resultVo.getContent().getResult();
            }
            //List<Map<String, String>> mapList = hbaseDao.findConditionByPatient(tableName, oid, patientId, filters, columns);
            List<ColumnConfig> columnConfigList = civJsonTemplate.getColumnConfigList();
            List<Map<String, String>> convertMapList = new ArrayList<>();
            for (Map<String, String> map : mapList) {
                Map<String, String> order = new HashMap<String, String>();
                for (ColumnConfig config : columnConfigList) {
                    String name = config.getName();
                    String column = config.getField();
                    Utils.checkAndPutToMap(order, name, map.get(column), "-", false);
                }
                convertMapList.add(order);
            }
            totalList.addAll(convertMapList);
        }
        return totalList;
    }

    public TableTemplateConfig getTableTemplateConfig(String oid, String id) {
        String tableTemplateConfigJson = commonModuleService.getTableTemplateConfig(oid, id);


        ObjectMapper objectMapper = new ObjectMapper();
        TableTemplateConfig tableTemplateConfig;
        try {
            tableTemplateConfig = objectMapper.readValue(tableTemplateConfigJson, new TypeReference<TableTemplateConfig>() {
            });
        } catch (IOException e) {
            throw new RuntimeException(CommonUrlEnum.TABLE_TEMPLATE_CONFIG.getCode(id) + "格式有误", e);
        }
        if(StringUtils.isBlank(tableTemplateConfig.getSourceType())){
            //默认查询表数据
            tableTemplateConfig.setSourceType("table");
        }
        return tableTemplateConfig;
    }

    @Override
    public List<ColumnConfig> getCommonTableConfig(String oid, String id) {
        TableTemplateConfig tableTemplateConfig = getTableTemplateConfig(oid, id);
        List<CivJsonTemplate> civJsonTemplateList = tableTemplateConfig.getCivJsonTemplateList();
        List<ColumnConfig> resultList = new ArrayList<>();
        civJsonTemplateList.forEach(
                c -> resultList.addAll(c.getColumnConfigList())
        );
        return resultList;
    }

    @Override
    public CommonListConfigResponse getCommonListConfig(String oid, String id) {
        String configJson = ConfigCache.getCache(oid, id + "_COMMON_LIST_CONFIG");
        CommonListConfigResponse commonListConfigResponse = new CommonListConfigResponse();
        CommonListConfig commonListConfig;
        try {
            commonListConfig = objectMapper.readValue(configJson, new TypeReference<CommonListConfig>() {
            });
        } catch (IOException e) {
            return commonListConfigResponse;
        }
        commonListConfigResponse.setParam(commonListConfig.getParamList());
        commonListConfigResponse.setColumnConfigList(commonListConfig.getColumnList());
        return commonListConfigResponse;

    }

    @Override
    public Map<String, String> getCommonWebUrl(String oid, String patientId, String visitId, String visit_type_code, String id, String sysCode, Map<String, String> paramMap) {
        Map<String, String> rs = new HashMap<>();
        String project = CommonConfig.getProject_site(oid);
        if (StringUtils.isBlank(project)) {
            rs.put("status", "0");
            rs.put("msg", "请联系管理员，请配置项目PROJECT_SITE值。");
            return rs;
        }
        String url = CommonConfig.getURL(oid, sysCode);
        if (StringUtils.isBlank(url)) {
            rs.put("status", "0");
            rs.put("msg", "请联系管理员，请配置调用地址。");
            return rs;
        }
        try {
            List<String> params = CommonConfig.getParams(oid, sysCode);
            Map<String, String> config = CommonConfig.getParam_configs(oid, sysCode);
            Map<String, String> values = getParamValue(oid, patientId, visitId, visit_type_code , paramMap, config);
            for (int i = 0; i < params.size(); i++) {
                String field = params.get(i);
                String value = values.get(field);
                if ("WEBEMR".equals(sysCode) && HdrConstantEnum.HOSPITAL_QFS.getCode().equals(ConfigCache.getCache(oid, "org_oid"))) {
                    value = Utils.UrlEncodeBase64(value);
                }
                if ("EMR_SCAN".equals(sysCode)) {
                    if (HdrConstantEnum.HOSPITAL_ZDFB.getCode().equals(ConfigCache.getCache(oid, "org_oid"))) {
                        value = WSUtils.getWSData(SecurityCommonUtil.getCurrentLoginUser().getUsercode(), Config.getConfigValue(oid, "SD_EMR_SCAN_WEBSERVICE_URL"), value, paramMap.get("end_time"));
                    } else {
                        value = WSUtils.getWSData2("jhdy", Config.getConfigValue(oid, "SD_EMR_SCAN_WEBSERVICE_URL"), paramMap.get("id_card_no"), "");
                    }
                }
                if ("FCQY_YLZX".equals(sysCode) && HdrConstantEnum.HOSPITAL_ZDFB.getCode().equals(ConfigCache.getCache(oid, "org_oid")) && "id_card_no".equals(field)) {
                    value = CivUtils.Encrypt(value, "nmrmczdfb1234567");
                }
                if (StringUtils.isNotBlank(value)) {
                    url = url.replace("#{" + field + "}", value);
                } else {
                    rs.put("status", "0");
                    rs.put("msg", "未查询到+" + field + "参数！");
                    return rs;
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        rs.put("status", "1");
        rs.put("linkType", CommonConfig.getLinkType(oid, sysCode));
        rs.put("url", url);
        return rs;
    }


    @Override
    public Page<Map<String, String>> getCategoryTableTemplate(String outPid, String id, List<KeyValueDto> paramList, int pageNo, int pageSize) {
        TableTemplateConfig tableTemplateConfig = getTableTemplateConfig("ALL", id);
        List<Map<String, String>> totalList = new ArrayList<>();
        Page<Map<String, String>> page = new Page<>();
        if(tableTemplateConfig.getSourceType().equalsIgnoreCase("table")) {
            //查询表
            for (String s : outPid.split(",")) {
                String[] split = s.split("\\|");
                List<Map<String, String>> mapList = queryThenConvertResult(split[2], split[1], split[3], split[0], tableTemplateConfig.getCivJsonTemplateList(), paramList);
                totalList.addAll(mapList);
            }
        }else if(tableTemplateConfig.getSourceType().equalsIgnoreCase("ws") || tableTemplateConfig.getSourceType().equalsIgnoreCase("post")){
            String oid = "";
            String pid = "";
            for (String s : outPid.split(",")) {
                String[] split = s.split("\\|");
                oid = split[2];
                pid = split[1];
                if (StringUtils.isNotBlank(oid) && StringUtils.isNotBlank(pid)) {
                    break;
                }
                List<Map<String, String>> mapList = getURLByOtherInterface(oid, id, split[1], split[3], split[0], tableTemplateConfig);
                totalList.addAll(mapList);
            }

        }
        sortListThenTransferToPage(pageNo, pageSize, tableTemplateConfig.getSort(), tableTemplateConfig.getSortField(), page, totalList);
        return page;
    }

    @Override
    public List<Map<String, String>> getCommonListForVisit(String oid, String patientId, String visitId, String visitType, String id) {
        String configJson = ConfigCache.getCache(oid, id + "_COMMON_LIST_CONFIG");

        if (StringUtils.isBlank(configJson)) {
            return new ArrayList<>();
        }

        CommonListConfig commonListConfig;
        try {
            commonListConfig = objectMapper.readValue(configJson, new TypeReference<CommonListConfig>() {
            });
        } catch (IOException e) {
            return new ArrayList<>();
        }

        PageRequestBuilder.OrderBy orderBy = PageRequestBuilder.init()
                .tableName(commonListConfig.getTableName())
                .patientId(patientId)
                .oid(oid)
                .visitId(visitId)
                .visitTypeCode(visitType)
                .filters(commonListConfig.getConditionList())
                .pageNo(0)
                .pageSize(0)
                .orderBy(commonListConfig.getSortColumn());
        PageRequestBuilder.PageOrderDir orderDir = null;
        if ("asc".equalsIgnoreCase(commonListConfig.getSortType())) {
            orderDir = orderBy.asc();
        } else {
            orderDir = orderBy.desc();
        }

        List<CommonListConfig.ColumnConfig> columnConfigs = commonListConfig.getColumnList();
        List<String> columnList = new ArrayList<>();
        columnConfigs.forEach(columnConfig -> columnList.add(columnConfig.getColumn()));
        PageRequest pageRequest = orderDir.column(columnList.toArray(new String[0]))
                .build();
        ResultVo<PageResultVo<Map<String, String>>> pageRvo = hbaseQueryClient.getPageByCondition(pageRequest);
        if (!pageRvo.isSuccess()) {
            return new ArrayList<>();
        }

        return pageRvo.getContent().getResult();
    }


    @Override
    public List<Map<String, String>> getCommonListForCategory(String outPatientId, String id) {

        String configJson = ConfigCache.getCache("ALL", id + "_COMMON_LIST_CONFIG");
        if (StringUtils.isNotBlank(configJson)) {
            return new ArrayList<>();
        }

        CommonListConfig commonListConfig;
        try {
            commonListConfig = objectMapper.readValue(configJson, new TypeReference<CommonListConfig>() {
            });
        } catch (IOException e) {
            throw new RuntimeException(id + "_COMMON_LIST_CONFIG格式有误", e);
        }


        String[] split = outPatientId.split(",");
        List<Map<String, String>> totalList = new ArrayList<>();
        for (String s : split) {
            String[] split1 = s.split("\\|");
            String patientId = split1[1];
            String visitId = split1[3];
            String visitType = split1[0];
            String oid = split1[2];

            PageRequestBuilder.OrderBy orderBy = PageRequestBuilder.init()
                    .tableName(commonListConfig.getTableName())
                    .patientId(patientId)
                    .oid(oid)
                    .visitId(visitId)
                    .visitTypeCode(visitType)
                    .filters(commonListConfig.getConditionList())
                    .pageNo(0)
                    .pageSize(0)
                    .orderBy(commonListConfig.getSortColumn());
            PageRequestBuilder.PageOrderDir orderDir = null;
            if ("asc".equalsIgnoreCase(commonListConfig.getSortType())) {
                orderDir = orderBy.asc();
            } else {
                orderDir = orderBy.desc();
            }
            List<CommonListConfig.ColumnConfig> columnConfigs = commonListConfig.getColumnList();
            List<String> columnList = new ArrayList<>();
            columnConfigs.forEach(columnConfig -> columnList.add(columnConfig.getColumn()));
            PageRequest pageRequest = orderDir.column(columnList.toArray(new String[0]))
                    .build();
            ResultVo<PageResultVo<Map<String, String>>> pageRvo = hbaseQueryClient.getPageByCondition(pageRequest);
            if (!pageRvo.isSuccess()) {
                continue;
            }

            List<Map<String, String>> mapList = pageRvo.getContent().getResult();

            totalList.addAll(mapList);
        }
        return totalList;

    }

    @Override
    public List<NameAndCodeVo> getCommonTab(String oid, String id) {
        String configJson = ConfigCache.getCache(oid, id + "_COMMON_TAB_CONFIG");
        List<NameAndCodeVo> commonTabConfig = new ArrayList<>();
        if (StringUtils.isNotBlank(configJson)) {
            try {
                commonTabConfig = objectMapper.readValue(configJson, new TypeReference<List<NameAndCodeVo>>() {
                });

            } catch (IOException e) {
                return commonTabConfig;
            }
        }
        return commonTabConfig;
    }

    /**
     * 创建Filter
     *
     * @param columnName
     * @param keyword
     * @param filters
     */
    public static void createPropertyFilter(String columnName, String keyword, String MatchType,
                                            List<PropertyFilter> filters) {
        if (org.apache.commons.lang3.StringUtils.isNotBlank(keyword)) {
            PropertyFilter filter1 = new PropertyFilter();
            filter1.setMatchType(MatchType);
            filter1.setPropertyName(columnName);
            filter1.setPropertyValue(keyword);
            //filter1.setPropertyType("STRING");
            filters.add(filter1);
        }
    }

    private Map<String, String> getParamValue(String oid, String patientId, String visitId, String visitType, Map<String, String> configs) {
        Map<String, String> rs = new HashMap<String, String>();
        for (String key : configs.keySet()) {

            if (StringUtils.isNotBlank(patientId) && "patientId".equalsIgnoreCase(key)) {
                rs.put(key, patientId);
                continue;
            }
            if (StringUtils.isNotBlank(visitId) && "visitId".equalsIgnoreCase(key)) {
                rs.put(key, visitId);
                continue;
            }
            //此处是获取登录用户的信息
            if ("usercode".equalsIgnoreCase(key)) {
                rs.put(key, SecurityCommonUtil.getCurrentLoginUser().getUsercode());
                continue;
            }
            if ("username".equalsIgnoreCase(key)) {
                rs.put(key, SecurityCommonUtil.getCurrentLoginUser().getUsername());
                continue;
            }
            if ("userNameCn".equalsIgnoreCase(key)) {
                rs.put(key, SecurityCommonUtil.getCurrentLoginUser().getName());
                continue;
            }
            if ("password".equalsIgnoreCase(key)) {
                String deptname = SecurityCommonUtil.getCurrentLoginUser().getPassword();
                rs.put(key, deptname);
                continue;
            }
            if ("userDeptName".equalsIgnoreCase(key)) {
                String deptname = SecurityCommonUtil.getCurrentLoginUser().getDeptname();
                rs.put(key, deptname);
                continue;
            }
            if ("requestTime".equalsIgnoreCase(key)) {
                rs.put(key, LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
                continue;
            }
            if ("uuid".equalsIgnoreCase(key)) {
                rs.put(key, UUID.randomUUID().toString());
                continue;
            }
            if (StringUtils.isNotBlank(configs.get(key))) {
                String mapValue=configs.get(key);
                //需要查询
                String[] config = mapValue.split(",");
                if(config.length==1){
                    rs.put(key, config[0]);
                    continue;
                }

                if(config[0].contains(".")){
                    //以此判断反射调用本地方法
                    String value = Utils.reflect(config[0],config[1]).toString();
                    rs.put(key, value);
                    continue;
                }

                List<PropertyFilter> filters = new ArrayList<PropertyFilter>();
                List<Map<String, String>> list = new ArrayList<Map<String, String>>();

//                if (StringUtils.isNotBlank(visitId) && !config[1].equals(HdrTableEnum.HDR_PATIENT.getCode())) {
//                    filters.add(new PropertyFilter("VISIT_ID", "STRING", MatchType.EQ.getOperation(), visitId));
//                }
                //filters.add(new PropertyFilter("VISIT_TYPE_CODE", "STRING", MatchType.EQ.getOperation(), visitType));
                createPropertyFilter("VISIT_TYPE_CODE", visitType, MatchType.EQ.getOperation(), filters);
                String visitIdTemp=visitId;
                if (config[1].equals(HdrTableEnum.HDR_PATIENT.getCode())) {
                    visitIdTemp="";
                }

                String tableName=config[1];
                String columnName=config[0];
                if(mapValue.contains("|")){
                    //应用于查询表和字段，分门诊、住院情况事
                    String[] tableArr=mapValue.split("\\|");
                    if("01".equals(visitType)){
                        config=tableArr[0].split(",");
                        tableName=config[1];
                        columnName=config[0];
                    }else if("02".equals(visitType)){
                        config=tableArr[1].split(",");
                        tableName=config[1];
                        columnName=config[0];
                    }
                }

                ResultVo<PageResultVo<Map<String, String>>> resultVo = hbaseQueryClient.getPageByCondition(
                        PageRequestBuilder.init()
                                .tableName(tableName)
                                .patientId(patientId)
                                .oid(oid)
                                .visitId(visitIdTemp)
                                .visitTypeCode(visitType)
                                .filters(filters)
                                .pageNo(0)
                                .pageSize(0)
                                .orderBy("")
                                .desc()
                                .column(columnName)
                                .build());
                //list = hbaseDao.findConditionByPatient(config[1], oid, patientId, filters, config[0]);
                if (resultVo.isSuccess()) {
                    list = resultVo.getContent().getResult();
                }
                if (list.size() > 0) {
                    String value = list.get(0).get(config[0]);
                    if (StringUtils.isBlank(value)) {
                        rs.put(key, "-");
                    } else {
                        rs.put(key, value);
                    }

                }

            } else {
                rs.put(key, "");
            }

        }
        return rs;
    }

//    @Override
//    public Map<String, String> getCommonNurseUrl(String patientId, String visitId, String visitType,
//                                                 Map<String, String> paramMap) {
//        Map<String, String> rs = new HashMap<String, String>();
//        boolean isDistinguish = Config.getCIV_NURSE_URL_OUT_OR_IN();
//        if ("INPV".equals(visitType) && isDistinguish) {
//            rs = getCommonUrl(patientId, visitId,visitType , "IN_NURSE", );
//        } else if ("OUTPV".equals(visitType) && isDistinguish) {
//            rs = getCommonUrl(patientId, visitId, visitType, "OUT_NURSE", );
//        } else {
//            rs = getCommonUrl(patientId, visitId, visitType, "NURSE", );
//        }
//        return rs;
//    }

    private Map<String, String> getParamValue(String oid, String patientId, String visitId, String visit_type_code, Map<String, String> mapParam, Map<String, String> configs) {
        Map<String, String> rs = new HashMap<>();
        for (String key : configs.keySet()) {
            if (mapParam.keySet().contains(key)) {
                rs.put(key, mapParam.get(key));
                continue;
            }
            if (StringUtils.isNotBlank(patientId) && "patientId".equalsIgnoreCase(key)) {
                rs.put(key, patientId);
                continue;
            }
            if (StringUtils.isNotBlank(visitId) && "visitId".equalsIgnoreCase(key)) {
                rs.put(key, visitId);
                continue;
            }
            if (StringUtils.isNotBlank(key) && (key
                    .equalsIgnoreCase("username") || key.equalsIgnoreCase("user_name"))) {
                rs.put(key, SecurityCommonUtil.getCurrentLoginUser().getUsercode());
                continue;
            }
            String[] config = ((String)configs.get(key)).split(",");
            List<PropertyFilter> filters = new ArrayList<>();
            List<Map<String, String>> list = new ArrayList<>();
            if (StringUtils.isBlank(mapParam.get("ROWKEY")) && !HdrTableEnum.HDR_PATIENT.getCode().equals(config[1])) {
                boolean isNeeded = true;
                if (config.length > 2)
                    try {
                        isNeeded = Boolean.valueOf(config[2]).booleanValue();
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                if (StringUtils.isNotBlank(visitId) && isNeeded){
                    createPropertyFilter("VISIT_ID", visitId, MatchType.EQ.getOperation(), filters);
                }
            }
            if (StringUtils.isBlank(mapParam.get("ROWKEY"))) {
                ResultVo<PageResultVo<Map<String, String>>> resultVo = hbaseQueryClient.getPageByCondition(
                        PageRequestBuilder.init()
                                .tableName(config[1])
                                .patientId(patientId)
                                .oid(oid)
                                .visitId(visitId)
                                .visitTypeCode(visit_type_code)
                                .filters(filters)
                                .pageNo(0)
                                .pageSize(0)
                                .orderBy("")
                                .desc()
                                .column(new String[] { config[0] })
                                .build());
                if (resultVo.isSuccess()) {
                    list = resultVo.getContent().getResult();
                }
            }
            if (list.size() > 0) {
                rs.put(key, (String)((Map)list.get(0)).get(config[0]));
                continue;
            }
            if ("INP_NO".equals(config[0]) && "HDR_PAT_ADT".equals(config[1])) {
                Map<String, String> resultMap = getPatientInFinalVisit(oid, patientId, "02");
                if (resultMap != null && resultMap.size() > 0) {
                    rs.put(key, (String)((Map)list.get(0)).get(config[0]));
                }
            }
        }
        return rs;
    }

    public Map<String, String> getPatientInFinalVisit(String oid, String patientId, String visitType) {
        Map<String, String> rs = new HashMap<>();
        List<PropertyFilter> filters = new ArrayList<>();
        List<Map<String, String>> inVisits = new ArrayList<Map<String, String>>();
        createPropertyFilter("VISIT_TYPE_CODE", visitType, MatchType.EQ.getOperation(), filters);
        ResultVo<PageResultVo<Map<String, String>>> resultVo = hbaseQueryClient.getPageByCondition(
                PageRequestBuilder.init()
                        .tableName(HdrTableEnum.HDR_PAT_ADT.getCode())
                        .patientId(patientId)
                        .oid(oid)
                        .visitId("")
                        .visitTypeCode(visitType)
                        .filters(filters)
                        .pageNo(0)
                        .pageSize(0)
                        .orderBy("")
                        .desc()
                        .column("VISIT_ID", "ADMISSION_TIME", "PERSON_NAME", "SEX_NAME", "DATE_OF_BIRTH", "VISIT_TYPE_CODE", "CURR_BED_LABEL", "CURR_DEPT_NAME", "CURR_DEPT_CODE", "ADMISSION_TIME", "INP_NO")
                        .build());
        if (resultVo.isSuccess()) {
            inVisits = resultVo.getContent().getResult();
        }
        String visitTimeString = "";
        String patString = "";
        String result = "";
        String resultInpNo = "";
        if (inVisits != null && inVisits.size() > 0)
            for (Map<String, String> map : inVisits) {
                patString = map.get("PATIENT_ID");
                String visitId = (((String)map.get("VISIT_ID")).trim() == null) ? "" : ((String)map.get("VISIT_ID")).trim();
                String adtString = (map.get("ADMISSION_TIME") == null) ? "" : map.get("ADMISSION_TIME");
                String inp_no = (map.get("INP_NO") == null) ? "" : map.get("INP_NO");
                if (adtString.compareTo(visitTimeString) > 0) {
                    visitTimeString = adtString;
                    result = visitId;
                    resultInpNo = inp_no;
                }
            }
        if (StringUtils.isNotBlank(patString) && StringUtils.isNotBlank(result)) {
            rs.put("patientId", patString);
            rs.put("visitId", result);
            rs.put("inpNo", resultInpNo);
        }
        return rs;
    }

    @Override
    public Page<Map<String, String>> getUrlListFromHbaseForVisit(String oid, String patientId, String visitId, String visitType, String sysCode, int pageNo, int pageSize) {
        Map<String, Object> pdfModleInfoMap = getPdfModleInfoMap(oid, sysCode);
        String tableName = (String) pdfModleInfoMap.get("tableName");
        String linkType = (String) pdfModleInfoMap.get("linkType");
        Map<String, String> columnMap = (Map<String, String>) pdfModleInfoMap.get("column");
        List<String> columnList = new ArrayList<>();
        for (Map.Entry<String, String> entry : columnMap.entrySet()) {
            columnList.add(entry.getValue());
        }
        List<PropertyFilter> filters = new ArrayList<PropertyFilter>();
        //filters.add(new PropertyFilter("VISIT_ID", "STRING", MatchType.EQ.getOperation(), visitId));
        //filters.add(new PropertyFilter("VISIT_TYPE_CODE", "STRING", MatchType.EQ.getOperation(), visitType));

        Page<Map<String, String>> resultPage = new Page<>();
        resultPage.setPageNo(pageNo);
        resultPage.setOrderBy(columnMap.get("createDateTime"));
        resultPage.setPageSize(pageSize);
        ResultVo<PageResultVo<Map<String, String>>> resultVo = hbaseQueryClient.getPageByCondition(
                PageRequestBuilder.init()
                        .tableName(tableName)
                        .patientId(patientId)
                        .oid(oid)
                        .visitId(visitId)
                        .visitTypeCode(visitType)
                        .filters(filters)
                        .pageNo(0)
                        .pageSize(0)
                        .orderBy("")
                        .desc()
                        .column(columnList.toArray(new String[0]))
                        .build());
        if (resultVo.isSuccess()) {
            resultPage.setResult(resultVo.getContent().getResult());
            resultPage.setTotalCount(resultVo.getContent().getTotal());
        }
        //resultPage = hbaseDao.getPageByCondition(tableName, oid, patientId, filters, resultPage, columnList.toArray(new String[0]));
        List<Map<String, String>> tmpResult = resultPage.getResult();
        List<Map<String, String>> result = new ArrayList<>();
        for (Map<String, String> v : tmpResult) {
            Map<String, String> map = new HashMap<>();
            for (String key : columnMap.keySet()) {
                String column = columnMap.get(key);
                /*if (linkType.equals("pdf")) {
                    map.put(key, CivUtils.getPdfData(v.get(column), oid, patientId, visitId, LocalDateTime.now().toString()));
                } else {
                    map.put(key, v.get(column));
                }*/

                map.put(key, v.get(column));
                if (linkType.equals("pdf") && "url".equalsIgnoreCase(key)) {
                    map.put(key, CivUtils.getPdfData(v.get(column), oid, patientId, visitId, LocalDateTime.now().toString()));
                }
            }
            map.put("linkType", linkType);
            result.add(map);
        }
        SortByDateDescForPdfModule comparator = new SortByDateDescForPdfModule();
        Collections.sort(result, comparator);

        resultPage.setResult(result);
        return resultPage;
    }

    private Map<String, Object> getPdfModleInfoMap(String oid, String moduleId) {
        String pdfModuleJson = ConfigCache.getCache(oid, moduleId + "_LIST_MODULE_JSON");
        ObjectMapper objectMapper = new ObjectMapper();
        Map<String, Object> result = new HashMap<>();
        String tableName = "";
        String linkType = "";
        Map<String, String> columnMap = new HashMap<>();
        try {
            JsonNode jsonNode = objectMapper.readTree(pdfModuleJson);
            tableName = jsonNode.get("tableName").asText();
            linkType = jsonNode.get("linkType").asText();
            JsonNode columnNode = jsonNode.get("column");
            columnMap.put("name", columnNode.get("name").asText());
            columnMap.put("creator", columnNode.get("creator").asText());
            if (linkType.equals("html")) {
                columnMap.put("htmlValue", columnNode.get("value").asText());
            } else if ("iframe".equals(linkType) || "open_page".equals(linkType) || "pdf".equals(linkType)) {
                columnMap.put("url", columnNode.get("value").asText());
            }
            columnMap.put("createDateTime", columnNode.get("createDateTime").asText());

        } catch (IOException e) {
            e.printStackTrace();
        }

        result.put("tableName", tableName);
        result.put("linkType", linkType);
        result.put("column", columnMap);
        return result;
    }

    private void getParamMapWithEncrypt(String oid, String patientId, String visitId, String id, String sysCode, Map<String, String> param_value) {
        logger.info("进入加密方法");
        if (sysCode.equals(SysCodeEnum.DES.getCode())) {
            for (String k : param_value.keySet()) {
                String value = param_value.get(k);
                try {
                    String encryptedValue = encryptService.DesEncrypt(value);
                    param_value.put(k, encryptedValue);
                } catch (Exception e) {
                    throw new ApplicationException(value + "加密失败");
                }
            }
        } else if (sysCode.equals(SysCodeEnum.BASE64.getCode())) {
            for (String k : param_value.keySet()) {
                String value = param_value.get(k);
                String encryptedValue = encryptService.UrlEncodeBase64(value);
                try {
                    encryptedValue=URLEncoder.encode(encryptedValue, "UTF-8");
                } catch (UnsupportedEncodingException e) {
                    e.printStackTrace();
                }
                param_value.put(k, encryptedValue);
            }
        } else if (sysCode.equals(SysCodeEnum.LZ.getCode())) {
            if (HdrConstantEnum.HOSPITAL_KMFY.getCode().equals(oid)) {
                try {
                    String emrScanWebserviceUrl = commonModuleService.getLzEncryptedUrl(oid, id);
                    //获取调用第三方接口的参数编码以及值
                    String inParam = ConfigCache.getCache(oid, CommonUrlEnum.REQUEST_PARAM_VALUE.getCode(id));
                    String resultKey = "";
                    for (Map.Entry<String, String> entry : param_value.entrySet()) {
                        String key = entry.getKey();
                        String value = entry.getValue();
                        if (StringUtils.isBlank(value)) {
                            resultKey = key;
                        } else {
                            inParam = inParam.replace("#{" + key + "}", value);
                        }
                    }

                    String encryptedValue = WSUtils.wsConnectionData(new URL(emrScanWebserviceUrl), inParam);
                    param_value.put(resultKey, encryptedValue);
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }else{
                for (String k : param_value.keySet()) {
                    String value = param_value.get(k);
                    String encryptedValue = encryptService.getLzEncryptedString(oid, id, value, patientId, visitId);
                    param_value.put(k, encryptedValue);
                }
            }
        } else if (sysCode.equals(SysCodeEnum.AES.getCode())) {
            for (String k : param_value.keySet()) {
                String value = param_value.get(k);
                if ("id_card_no".equals(k)) {
                    try {
                        String encryptedValue = encryptService.AesEncrypt(value, "nmrmczdfb1234567");
                        param_value.put(k, encryptedValue);
                    } catch (Exception e) {
                        throw new ApplicationException("Aes加密失败");
                    }
                }
            }

        } else if (sysCode.equals(SysCodeEnum.CEMSMD5.getCode())) {

            for (String k : param_value.keySet()) {
                logger.info("进行CEMSMD5加密，当前加密的参数为：" + k);
                if ("sign".equals(k)) {
//                  获取inp_no
                    List<PropertyFilter> filters = new ArrayList<PropertyFilter>();

                    //filters.add(new PropertyFilter("VISIT_ID", "STRING", MatchType.EQ.getOperation(), visitId));
                    List<Map<String, String>> inVisits = new ArrayList<>();
                    ResultVo<PageResultVo<Map<String, String>>> resultVo = hbaseQueryClient.getPageByCondition(
                            PageRequestBuilder.init()
                                    .tableName(HdrTableEnum.HDR_PAT_ADT.getCode())
                                    .patientId(patientId)
                                    .oid(oid)
                                    .visitId(visitId)
                                    .visitTypeCode("")
                                    .filters(filters)
                                    .pageNo(0)
                                    .pageSize(0)
                                    .orderBy("")
                                    .desc()
                                    .column("INP_NO")
                                    .build());


                    if (resultVo.isSuccess()) {
                        inVisits = resultVo.getContent().getResult();
                    }
                    //List<Map<String, String>> inVisits = hbaseDao.findConditionByKey(HdrTableEnum.HDR_PAT_ADT.getCode(), oid, patientId, filters, "INP_NO");

                    String inpNo = inVisits.get(0).get("INP_NO");
                    logger.info("查询到的INP_NO为" + inpNo);
                    String s = inpNo + "&ZXYYXXK";
                    String md5Encrypt = encryptService.gpMd5Encrypt(s);
                    logger.info("加密后的sign:" + md5Encrypt);
                    param_value.put(k, md5Encrypt);
                }
            }
        } else if (sysCode.equals(SysCodeEnum.SM4.getCode())) {
            /**
             * 此处加密只是但对针对单个的变量，而非组合变量
             * 例如：适用eg2给#{cardId}加密，不适用eg1,不会给{"cardId":"#{cardId}","name":"#{name}"}加密
             * eg1：http://xxx.?message={"cardId":"#{cardId}","name":"#{name}"}
             * eg2：http://xxx.?message=#{cardId}
             */

            String sm4Field = ConfigCache.getCache(oid, CommonUrlEnum.SM4_FIELD.getCode(id));
            if(StringUtils.isNotBlank(sm4Field)){
                return;
            }
            //获取秘钥
            String sm4Key = ConfigCache.getCache(oid, CommonUrlEnum.SM4_KEY.getCode(id));
            if(StringUtils.isBlank(sm4Key)){
                logger.info("秘钥SD_"+ id + "_SM4_KEY值为空");
                return;
            }
            logger.info("秘钥为："+sm4Key);
            String sm4KeyLen=ConfigCache.getCache(oid, CommonUrlEnum.SM4_KEY_LEN.getCode(id));
            logger.info("秘钥长度为："+sm4KeyLen);
            if(StringUtils.isBlank(sm4KeyLen)){
                logger.info("秘钥长度SD_"+ id + "_SM4_KEY_LEN为空");
                return;
            }
            for (String k : param_value.keySet()) {
                String value = param_value.get(k);
                try {
                    String encryptedValue="";
                    if(Integer.parseInt(sm4KeyLen)==16){
                        encryptedValue = encryptService.SM4EncryptBy16Key(sm4Key, value);
                    }else{
                        encryptedValue = encryptService.SM4EncryptBy32Key(sm4Key, value);
                    }
                    param_value.put(k, encryptedValue);
                } catch (Exception e) {
                    throw new ApplicationException(value + "加密失败");
                }
            }
        }
    }

    /**
     * 调用第三方接口获取xml数据并解析
     * 20250305 因重庆市潼南中医院接入 体检、检验模块需求开发
     * @param patientId
     * @param id
     * @return
     * */
    public List<Map<String, String>> getURLByOtherInterface(String oid, String id, String patientId, String visitId, String visitType, TableTemplateConfig tableTemplateConfig) {
        List<Map<String, String>> resultList = new ArrayList<>();

        //获取系统URL
        String url = commonModuleService.getURL(oid, id);
        logger.info("读取到url:" + url);
        if (StringUtils.isBlank(url)) {
            Map<String, String> map = new HashMap<>();
            map.put("status", "0");
            map.put("msg", "请联系管理员，请配置调用地址【SD_" + id + "_URL】");
            resultList.add(map);
            return resultList;
        }

        //获取变量配置
        List<String> params = commonModuleService.getParams(oid, id);
        logger.info("获取到参数:" + params);
        Map<String, String> param_configs = commonModuleService.getParam_configs(oid, id);
        logger.info("获取到参数配置：" + param_configs);
        Map<String, String> param_value = getParamValue(oid, patientId, visitId, visitType, param_configs);
        logger.info("获取到参数和值：" + param_value);

        //获取调用第三方接口的参数编码以及值
        String inParam=ConfigCache.getCache(oid, CommonUrlEnum.REQUEST_PARAM_VALUE.getCode(id));
        for (String field : params) {
            String value = param_value.get(field);
            if (StringUtils.isNotBlank(value)) {
                inParam = inParam.replace("#{" + field + "}", value);
            }else {
                Map<String, String> map = new HashMap<>();
                map.put("status", "0");
                map.put("msg", "未查询到" + field + "参数！");
                resultList.add(map);
                return resultList;
            }
        }

        String resultStr = "";
        if (tableTemplateConfig.getSourceType().equalsIgnoreCase("WS")) {
            logger.info("调用webservice接口");
            resultStr= WSUtils.wsConnectionCommon(url,inParam);

            String beginTag = ConfigCache.getCache(oid, CommonUrlEnum.REQUEST_RESULT_START.getCode(id));
            String endTag = ConfigCache.getCache(oid, CommonUrlEnum.REQUEST_PARAM_END.getCode(id));
            int beginIndex = resultStr.indexOf(beginTag);
            int endIndex = resultStr.indexOf(endTag);
            resultStr = resultStr.substring(beginIndex + beginTag.length(), endIndex);
        }
        logger.info("resultStr：" + resultStr);

        //解析结果
        String postResultFormat = ConfigCache.getCache(oid, CommonUrlEnum.REQUEST_RESULT_FORMAT.getCode(id));
        if (StringUtils.isNotBlank(postResultFormat) && postResultFormat.equalsIgnoreCase("XML")) {
            //获取节点配置
            String nodeConfig = ConfigCache.getCache(oid, CommonUrlEnum.REQUEST_RESULT_NODE.getCode(id));
            String[] nodeArr = nodeConfig.split("\\;");
            String parentNode = nodeArr[0];
            String[] childNodes = nodeArr[1].split(",");

            Map<String, String> keyMap = new HashMap();
            for (CivJsonTemplate templet : tableTemplateConfig.getCivJsonTemplateList()) {
                List<ColumnConfig> configList = templet.getColumnConfigList();
                for (ColumnConfig column : configList) {
                    keyMap.put(column.getField(), column.getName());
                }
            }
            resultList = Utils.xmlToList(resultStr, keyMap, parentNode, childNodes);
        }
        return resultList;
    }

}