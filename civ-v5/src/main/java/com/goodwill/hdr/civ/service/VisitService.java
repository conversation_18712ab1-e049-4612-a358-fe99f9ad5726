package com.goodwill.hdr.civ.service;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Description 类描述：门诊就诊服务接口
 * @Date 2018年4月19日
 * @modify 修改记录：
 */
public interface VisitService {

    /**
     * @param patientId 患者编号
     * @param visitType 就诊类型
     * @return
     * @Description 方法描述: 患者表中查询患者信息
     */
    public Map<String, String> getInfoFromPatient(String oid, String patientId, String visitType);

    /**
     * @param initOid
     * @param patientId 患者编号
     * @param visitType 就诊类型
     * @return {status:false，ids:'xxx，xxx'}
     * @Description 方法描述: 判断门诊患者标识和住院患者标识是否一致
     */
    public Map<String, Object> getOutOrInPatientId(String oid,String initOid, String patientId, String visitType,String visitDate, String visitDept, String timeStart, String timeEnd, String deptType);


    /**
     * @return
     * @Description 方法描述: 查询患者末次就诊信息
     */
    public Map<String, String> getCurrentPatientInfo(String oid,String ids);

    /**
     * 通过pid,vid，visitType获取换阵就诊信息
     *
     * @param patientId
     * @param visitId
     * @param visitType
     * @return
     */
    Map<String, String> getPatientInfo(String oid, String patientId, String visitId, String visitType);

    /**
     * @param patientId    患者编号
     * @param outPatientId 关联的患者编号
     * @param visitType    就诊类型
     * @return
     * @Description 方法描述: 查询患者末次住院信息
     */
    public Map<String, String> getPatientInFinalVisit(String this_oid, String oid, String patientId, String outPatientId, String visitType);

    /**
     * @param patientId    患者编号
     * @param outPatientId 关联的患者编号
     * @return
     * @Description 方法描述: 查询患者末次住院VISIT_ID
     */
    public String getFinalInVisit(String oid, String patientId, String outPatientId);



    /**
     * 或者患者某次诊断的主诊断，读取数据方法由配置控制
     *
     * @param patId
     * @param visitId
     * @return
     */
    public List<Map<String, String>> getPatMainDiagInpbyConfig(String oid, String patId, String visitId);

    /**
     * @param patId   患者编号
     * @param visitId 就诊次数
     * @param isMain  是否为主诊断  1主诊断 2非主诊断 3全部诊断
     * @return
     * @Description 方法描述: 查询患者某次门诊的诊断
     */
    public List<Map<String, String>> getPatDiagOutp(String oid, String patId, String visitId, String isMain);


    /**
     * @param patId   患者编号
     * @param visitId 就诊次数
     * @param isMain  是否为主诊断  1主诊断 2非主诊断 3全部诊断
     * @return
     * @Description 方法描述: 查询患者急诊的诊断
     */
    public List<Map<String, String>> getPatDiagEmp(String oid, String patId, String visitId, String isMain);

    /**
     * 根据身份证号+患者姓名关联免登陆跳转地址
     *
     * @param id_crad_no
     * @return
     */
    public Map<String, String> getPatientinfo(String oid, String id_crad_no);


    Map<String, String> getRpcUrlByInpNo(String oid, String inpNo);

    Map<String, String> getRpcUrlByVisitNo(String oid, String visitNo);

    Map<String, Object> getPatientVisitsList(String oid, String outPatientId, String visitDept, String deptType, String visitStatus, String timeStart, String timeEnd, String visitDate);

    Map<String, Object> getPatientVisitDeptList(String oid, String outPatientId, String visitStatus, String visitDept, String timeStart, String timeEnd, String visitDate,String patientId,String visitType);
}
