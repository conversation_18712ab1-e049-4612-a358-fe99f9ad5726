package com.goodwill.hdr.civ.service.impl;


import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;

import com.goodwill.hdr.civ.config.CommonConfig;
import com.goodwill.hdr.civ.config.Config;
import com.goodwill.hdr.civ.config.ConfigCache;
import com.goodwill.hdr.civ.dao.EmrDgDao;
import com.goodwill.hdr.civ.dao.OrganizationDao;
import com.goodwill.hdr.civ.enums.HdrConstantEnum;
import com.goodwill.hdr.civ.enums.HdrTableEnum;
import com.goodwill.hdr.civ.enums.VisitTypeEnum;
import com.goodwill.hdr.civ.service.CommonURLService;
import com.goodwill.hdr.civ.service.MedicalRecordService;
import com.goodwill.hdr.civ.service.PowerService;
import com.goodwill.hdr.civ.utils.*;
import com.goodwill.hdr.core.orm.MatchType;
import com.goodwill.hdr.core.orm.Page;
import com.goodwill.hdr.hbase.bo.PropertyFilter;
import com.goodwill.hdr.hbase.dto.responseVo.PageResultVo;
import com.goodwill.hdr.hbase.dto.responseVo.ResultVo;
import com.goodwill.hdr.hbaseQueryClient.builder.PageRequestBuilder;
import com.goodwill.hdr.hbaseQueryClient.service.HbaseQueryClient;
import com.goodwill.hdr.security.utils.SecurityCommonUtil;
import com.goodwill.hdr.web.common.vo.ResultVO;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.converter.StringHttpMessageConverter;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;
import sun.misc.BASE64Decoder;

import java.io.File;
import java.nio.charset.StandardCharsets;
import java.util.*;

@Service
public class MedicalRecordServiceImpl implements MedicalRecordService {

    protected Logger logger = LoggerFactory.getLogger(getClass());

    @Autowired
    private PowerService powerService;

    @Autowired
    private CommonURLService commonURLService;

    @Autowired
    private EmrDgDao emrDgDao;
    @Autowired
    private ObjectMapper objectMapper;
    @Autowired
    private RestTemplate restTemplate;
    @Autowired
    private OrganizationDao organizationDao;
    private final HbaseQueryClient hbaseQueryClient;

    public MedicalRecordServiceImpl(HbaseQueryClient hbaseQueryClient) {
        this.hbaseQueryClient = hbaseQueryClient;
    }

    @Override
    public Page<Map<String, String>> getTechnologiesMedicalRecordList(String oid, String patientId, String visitId, String visitType,
                                                                      String orderBy, String orderDir, int pageNo, int pageSize, String emrTypeCode) {
        List<Map<String, String>> mrs = new ArrayList<Map<String, String>>();
        //默认查询住院的病历文书
        String vtype = "";
        if ("OUTPV".equals(visitType)) {
            vtype = "01";
        } else if ("INPV".equals(visitType)) {
            vtype = "02";
        }
        //分页  排序
        Page<Map<String, String>> page = new Page<Map<String, String>>();
        //分页
        boolean pageable = true;
        if (pageNo == 0 || pageSize == 0) {
            pageable = false;
        } else {
            page.setPageNo(pageNo);
            page.setPageSize(pageSize);
        }
        //排序
        if (StringUtils.isBlank(orderBy) || StringUtils.isBlank(orderDir)) {
            page.setOrderBy("CREATE_DATE_TIME");
            page.setOrderDir("desc");
        } else {
            page.setOrderBy(orderBy);
            page.setOrderDir(orderDir);
        }

        //条件过滤
        List<PropertyFilter> filters = new ArrayList<PropertyFilter>();
        //病历权限筛选
        String userName = SecurityCommonUtil.getLoginUserCode(); //在线用户账号
        //String userName = "admin";
        Map<String, Object> power = powerService.getPowerConfigByEMR(oid, userName);
        if ("false".equals(power.get("isAll"))) {
            List<String> classList = (List<String>) power.get("power");
            String filterStr = StringUtils.join(classList.toArray(), ",");
            filters.add(new PropertyFilter("MR_CLASS_CODE", MatchType.IN.getOperation(), filterStr));
        }
        //filters.add(new PropertyFilter("VISIT_ID", "STRING", MatchType.EQ.getOperation(), visitId));
        filters.add(new PropertyFilter("VISIT_TYPE_CODE",  MatchType.EQ.getOperation(), vtype));
        //新增病历过滤配置 civ配置文件 EMR_VIEW_CONFIG
        Config.setEmrViewVonfig(oid, filters);
        //新增病历类型过滤条件
        if (StringUtils.isNotBlank(emrTypeCode)) {
            //获取过滤条件值对应的列字段名
            String emrTypeFieldName = Config.getEmrTypeFieldName(oid);
            filters.add(new PropertyFilter(emrTypeFieldName, MatchType.EQ.getOperation(), emrTypeCode));
        }
        //分页判断
        if (pageable) {
            Page<Map<String, String>> page2 = new Page<Map<String, String>>();
            ResultVo<PageResultVo<Map<String, String>>> resultVo = hbaseQueryClient.getPageByCondition(
                    PageRequestBuilder.init()
                            .tableName(HdrTableEnum.HDR_EMR_CONTENT.getCode())
                            .patientId(patientId)
                            .oid(oid)
                            .visitId(visitId)
                            .visitTypeCode(vtype)
                            .filters(filters)
                            .pageNo(pageNo)
                            .pageSize(pageSize)
                            .orderBy("CREATE_DATE_TIME")
                            .desc()
                            .column("FIRST_MR_SIGN_DATE_TIME", "CREATOR_NAME", "TOPIC", "VISIT_TYPE_CODE",
                                    "FILE_FLAG", "MR_CLASS_CODE", "MR_CLASS_NAME", "CREATE_DATE_TIME", "IN_PATIENT_ID", "OUT_PATIENT_ID", "VISIT_ID", "FILE_NO", "FILE_UNIQUE_ID", "FFILE_CLASS", "OID")
                            .build());
            List<Map<String, String>> result = new ArrayList<>();
            if(resultVo.isSuccess()){
                result = resultVo.getContent().getResult();
            }
//            page2 = hbaseDao.findPageConditionByPatient(HdrTableEnum.HDR_EMR_CONTENT.getCode(), oid, patientId, page,
//                    filters, "FIRST_MR_SIGN_DATE_TIME", "CREATOR_NAME", "TOPIC", "VISIT_TYPE_CODE",
//                    "FILE_FLAG", "MR_CLASS_CODE", "MR_CLASS_NAME", "CREATE_DATE_TIME", "IN_PATIENT_ID", "OUT_PATIENT_ID", "VISIT_ID", "FILE_NO", "FILE_UNIQUE_ID", "FFILE_CLASS", "OID");
//            List<Map<String, String>> result = page2.getResult();
//            Utils.sortListByDate(result, page.getOrderBy(), page.getOrderDir());
            page.setResult(result);
            page.setTotalCount(result.size());
        } else {
            ResultVo<PageResultVo<Map<String, String>>> resultVo = hbaseQueryClient.getPageByCondition(
                    PageRequestBuilder.init()
                            .tableName(HdrTableEnum.HDR_EMR_CONTENT.getCode())
                            .patientId(patientId)
                            .oid(oid)
                            .visitId(visitId)
                            .visitTypeCode(vtype)
                            .filters(filters)
                            .pageNo(0)
                            .pageSize(0)
                            .orderBy("CREATE_DATE_TIME")
                            .desc()
                            .column( "FIRST_MR_SIGN_DATE_TIME", "CREATOR_NAME", "TOPIC", "VISIT_TYPE_CODE", "FILE_FLAG",
                                    "MR_CLASS_CODE", "MR_CLASS_NAME", "CREATE_DATE_TIME", "IN_PATIENT_ID", "OUT_PATIENT_ID", "VISIT_ID", "FILE_NO", "FILE_UNIQUE_ID", "FFILE_CLASS", "OID")
                            .build());
            List<Map<String, String>> result = new ArrayList<>();
            if(resultVo.isSuccess()){
                mrs = resultVo.getContent().getResult();
            }
//            mrs = hbaseDao.findConditionByPatient(HdrTableEnum.HDR_EMR_CONTENT.getCode(), oid, patientId, filters,
//                    "FIRST_MR_SIGN_DATE_TIME", "CREATOR_NAME", "TOPIC", "VISIT_TYPE_CODE", "FILE_FLAG",
//                    "MR_CLASS_CODE", "MR_CLASS_NAME", "CREATE_DATE_TIME", "IN_PATIENT_ID", "OUT_PATIENT_ID", "VISIT_ID", "FILE_NO", "FILE_UNIQUE_ID", "FFILE_CLASS", "OID");
//            Utils.sortListByDate(mrs, page.getOrderBy(), page.getOrderDir());
            page.setResult(mrs);
            page.setTotalCount(mrs.size());
        }
        //未查到数据，终止执行
        if (page.getTotalCount() <= 0) {
            return page;
        }
        //字段映射 数据处理
        List<Map<String, String>> emrs = new ArrayList<Map<String, String>>();
        for (Map<String, String> map : page) {
            //获取病历标记  添加状态字段
            String flag = map.get("FILE_FLAG");
            String status = "";
            if (HdrConstantEnum.HOSPITAL_BYSY.getCode().equals(ConfigCache.getCache(oid, "org_oid"))) {
                if ("02".equals(map.get("VISIT_TYPE_CODE"))) {
                    if ("T".equals(flag)) {
                        status = "未提交";
                    } else if ("1".equals(flag)) {
                        status = "住院医";
                    } else if ("2".equals(flag)) {
                        status = "主治审签";
                    } else if ("3".equals(flag)) {
                        status = "主任已签";
                    } else if ("4".equals(flag)) {
                        status = "上报卡打回";
                    } else if ("5".equals(flag)) {
                        status = "上报卡作废";
                    } else {
                        status = "未知状态";
                    }
                } else {
                    if ("T".equals(flag)) {
                        status = "草稿";
                    } else if ("0".equals(flag)) {
                        status = "未提交";
                    } else if ("1".equals(flag)) {
                        status = "已提交";
                    } else {
                        status = "未知状态";
                    }
                }
            } else {
                status = map.get("FILE_FLAG");
            }
            //保存状态字段
            map.put("STATUS", status);
            //首次签名时间不存在特殊处理
            if (StringUtils.isBlank(map.get("FIRST_MR_SIGN_DATE_TIME"))) {
                //替换为创建时间
                map.put("FIRST_MR_SIGN_DATE_TIME", map.get("CREATE_DATE_TIME"));
            }
            //处理下in_patient_id和out_patient_id,主要解决未按规范抽数导致的住院只有in_patient_id,门诊只有out_patient_id
            String pid = StringUtils.isNotBlank(map.get("IN_PATIENT_ID")) ? map.get("IN_PATIENT_ID") : map.get("OUT_PATIENT_ID");
            map.put("IN_PATIENT_ID", pid);
            map.put("OUT_PATIENT_ID", pid);
            //字段映射
            Map<String, String> emr = new HashMap<String, String>();
            ColumnUtil.convertMapping(emr, map, new String[]{"FIRST_MR_SIGN_DATE_TIME", "CREATOR_NAME",
                    "STATUS", "TOPIC", "MR_CLASS_CODE", "MR_CLASS_NAME", "CREATE_DATE_TIME", "IN_PATIENT_ID", "OUT_PATIENT_ID", "VISIT_ID", "FILE_NO", "FILE_UNIQUE_ID", "OID", "VISIT_TYPE_CODE"});
            //珠海妇幼特殊处理
            String ffileClass = map.get("FFILE_CLASS");
            if (StringUtils.isNotBlank(ffileClass)) {
                if ("PDF".equals(ffileClass)) {
                    //如果是PDF需要将加密后的base64转码
                    emr.put("linkType", "pdf");
                }
                if ("HTML".equals(ffileClass) || "NULL".equals(ffileClass)) {
                    emr.put("linkType", "html");
                }

            }
            emrs.add(emr);
        }
        String linkType = CommonConfig.getLinkType(oid, "RM");
        String url = CommonConfig.getURL(oid, "RM");
        if (StringUtils.isBlank(url)) {
            linkType = "html";
        }
        for (Map<String, String> map : emrs) {
            map.put("linkType", linkType);
            map.put("patient_id", map.get("inPatientId"));
            map.put("visit_id", map.get("visitId"));
        }

        emrs.sort((m1,m2)->m2.get("createDateTime").compareTo(m1.get("createDateTime")));

        //重置分页
        page.setResult(emrs);
        return page;
    }

    @Override
    public List<Map<String, String>> getCVMrTypes(String oid, String patientId, String visitId) {
        //类型
        List<Map<String, String>> list = new ArrayList<Map<String, String>>();
        Map<String, String> map = new HashMap<String, String>();
        Page<Map<String, String>> page = getTechnologiesMedicalRecordList(oid, patientId, visitId, "INPV", "FIRST_MR_SIGN_DATE_TIME",
                "desc", 0, 0, "");
        //类型统计 去重
        for (Map<String, String> m : page) {
            String mrCode = m.get("mrClassCode");
            String mrName = m.get("mrClassName");
            if ("-".equals(mrCode)) {
                map.put("other", "其它");
            } else {
                map.put(mrCode, "-".equals(mrName) ? "未知类型" : mrName);
            }
        }
        //处理类型
        Set<String> set = new HashSet<>();
        for (Map.Entry<String, String> kv : map.entrySet()) {
            if (set.contains(kv.getKey())) {
                continue;
            }
            Map<String, String> cn = new HashMap<String, String>();
            cn.put("code", kv.getKey());
            cn.put("name", kv.getValue());
            list.add(cn);
            set.add(kv.getKey());
        }
        return list;
    }

    @Override
    public Page<Map<String, String>> getCVMrList(String oid, String patientId, String visitId, String type, int pageNo, int pageSize) {
        Page<Map<String, String>> page = new Page<Map<String, String>>();
        //条件筛选
        List<Map<String, String>> list = new ArrayList<Map<String, String>>();
        if (StringUtils.isBlank(type) || "all".equals(type)) {
            page = getTechnologiesMedicalRecordList(oid, patientId, visitId, "INPV", "FIRST_MR_SIGN_DATE_TIME", "desc", 0, 0, "");
            ListPage<Map<String, String>> listPage = new ListPage<Map<String, String>>(page.getResult(), pageNo, pageSize);
            page.setPageNo(pageNo);
            page.setPageSize(pageSize);
            page.setResult(listPage.getPagedList());
            return page;
        }
        //查询本次所有病历
        page = getTechnologiesMedicalRecordList(oid, patientId, visitId, "INPV", "FIRST_MR_SIGN_DATE_TIME", "desc", 0, 0, "");
        for (Map<String, String> map : page) {
            //其它
            if ("other".equals(type)) {
                if ("-".equals(map.get("mrClassCode"))) {
                    list.add(map);
                }
            } else {
                if (type.equals(map.get("mrClassCode"))) {
                    list.add(map);
                }
            }
        }
        //重置分页
        ListPage<Map<String, String>> listPage = new ListPage<Map<String, String>>(list, pageNo, pageSize);
        page.setTotalCount(list.size());
        page.setPageNo(pageNo);
        page.setPageSize(pageSize);
        page.setResult(listPage.getPagedList());
        return page;
    }

    @Override
    public long getMRCount(String oid, String patientId, String visitId, String visitType) {
        //查询病历文书
        Page<Map<String, String>> page = getTechnologiesMedicalRecordList(oid, patientId, visitId, visitType, "", "", 0, 0, "");
        long mrCount = page.getTotalCount();
        if (mrCount < 0) {
            mrCount = 0;
        }
        return mrCount;
    }

    @Override
    public List<Map<String, Object>> getMedicalRecords(String oid, String patientId, String visitType, String outPatientId, String year, String key,
                                                       String type, String click_Type) {
        List<Map<String, Object>> listResult = new ArrayList<Map<String, Object>>();
        List<Map<String, String>> types = new ArrayList<Map<String, String>>();//getMedicalTypes(patientId, outPatientId,visitType);
        List<Map<String, String>> List = new ArrayList<Map<String, String>>();

        //分组结果结果集
        Map<String, Object> resultList = new HashMap<String, Object>();

        /*if (StringUtils.isBlank(year)) {
            Calendar date = Calendar.getInstance();
            year = String.valueOf((date.get(Calendar.YEAR) + 1));
        }*/
        int num = 0;
        boolean is = true;
        while (List.size() == 0) {
            /*year = (Integer.valueOf(year) - 1) + "";*/
//            if (StringUtils.isNotBlank(patientId) && StringUtils.isNotBlank(this_oid) && this_oid.equals(oid) || this_oid.equals("ALL")) {
//                is = getMedicalRecordsByPat(oid, patientId, visitType, types, List, year, key, type, click_Type);
//            }
            if (!is) {
                resultList.put("status", "0");
                resultList.put("msg", "查询超时。");
                listResult.add(resultList);
                return listResult;
            }
            if (StringUtils.isNotBlank(outPatientId)) {
                String[] pats = outPatientId.split(",");
                for (int i = 0; i < pats.length; i++) {
                    if (StringUtils.isNotBlank(pats[i])) {
                        String[] pat = pats[i].split("\\|");
                        is = getMedicalRecordsByPat(pat[2], pat[1], pat[0], pat[3], types, List, year, key, type, click_Type);
                    }
                    if (!is) {
                        resultList.put("status", "0");
                        resultList.put("msg", "查询超时。");
                        listResult.add(resultList);
                        return listResult;
                    }
                }
            }
            num++;
            if (num == 20) {
                break;
            }
        }

        //未找到病历文书  中断运行
        if (null == List || List.size() == 0) {
            return listResult;
        }
        //按照月份分组
        CivUtils.groupByDate(resultList, List, "MediRecordTime");

        //将结果按分组时间  降序排序
        Map<String, Object> result = Utils.sortMapByKey(resultList, Page.Sort.DESC);

        for (String time : result.keySet()) {
            Map<String, Object> rs = new HashMap<String, Object>();
            rs.put("time", time);
            rs.put("order", Integer.valueOf(CivUtils.changeFormatDate(time)));
            rs.put("data", resultList.get(time));
            listResult.add(rs);
        }
        Utils.sortListByDate(listResult, "order", Page.Sort.DESC);

        return listResult;
    }

    public boolean getMedicalRecordsByPat(String oid, String patientId, String visitType, String visitId, List<Map<String, String>> types,
                                          List<Map<String, String>> List, String year, String key, String field, String click_Type) {
        //分页
        Page<Map<String, String>> page = new Page<Map<String, String>>();
        page.setPageNo(1);
        page.setPageSize(1000);
        page.setOrderBy("CREATE_DATE_TIME");
        page.setOrderDir(Page.Sort.DESC);
        //过滤条件  文书类型
        List<PropertyFilter> filters = new ArrayList<PropertyFilter>();
        filters.add(new PropertyFilter("VISIT_TYPE_CODE",  MatchType.EQ.getOperation(), visitType));
        //filters.add(new PropertyFilter("VISIT_ID", "STRING", MatchType.EQ.getOperation(), visitId));
        //新增病历过滤配置 civ配置文件 EMR_VIEW_CONFIG
        Config.setEmrViewVonfig(oid, filters);
        //病历权限筛选
        String userName = SecurityContextHolder.getContext().getAuthentication().getName(); //在线用户账号
        Map<String, Object> power = powerService.getPowerConfigByEMR(oid, userName);
        if ("false".equals(power.get("isAll"))) {
            List<String> classList = (List<String>) power.get("power");
            String filterStr = StringUtils.join(classList.toArray(), ",");
            filters.add(new PropertyFilter("MR_CLASS_CODE", MatchType.IN.getOperation(), filterStr));
        }

        if (StringUtils.isNotBlank(year)) {
            PropertyFilter filter1 = new PropertyFilter("CREATE_DATE_TIME", MatchType.GE.getOperation(),
                    year + "-01-01");
            filters.add(filter1);
            PropertyFilter filter2 = new PropertyFilter("CREATE_DATE_TIME",
                    MatchType.LE.getOperation(), year + "-12-31 23:59:59");
            filters.add(filter2);

        }
        if (StringUtils.isNotBlank(key)) {
            if ("1".equals(field)) {
                PropertyFilter keyFilter = new PropertyFilter("TOPIC", MatchType.LIKE.getOperation(), key);
                filters.add(keyFilter);
            } else if ("2".equals(field)) {
                PropertyFilter keyFilter = new PropertyFilter("MR_CONTENT_HTML",
                        MatchType.LIKE.getOperation(), key);
                filters.add(keyFilter);
            }
        }

        Page<Map<String, String>> resultPage = new Page<Map<String, String>>();
        try {
            ResultVo<PageResultVo<Map<String, String>>> resultVo = hbaseQueryClient.getPageByCondition(
                    PageRequestBuilder.init()
                            .tableName(HdrTableEnum.HDR_EMR_CONTENT.getCode())
                            .patientId(patientId)
                            .oid(oid)
                            .visitId(visitId)
                            .visitTypeCode(visitType)
                            .filters(filters)
                            .pageNo(0)
                            .pageSize(0)
                            .orderBy("")
                            .desc()
                            .column("CREATE_DATE_TIME","LAST_MODIFY_DATE_TIME", "CREATOR_NAME", "STATUS", "TOPIC",
                                    "MR_CLASS_NAME", "FILE_NO", "FILE_UNIQUE_ID", "FILE_NAME", "VISIT_ID", "IN_PATIENT_ID", "OUT_PATIENT_ID", "FILE_CLASS", "MR_CLASS_CODE", "OID", "ORG_NO", "ORG_NAME", "VISIT_TYPE_CODE")
                            .build());
            if(resultVo.isSuccess()){
                resultPage.setResult(resultVo.getContent().getResult());
            }
//            resultPage = hbaseDao.findPageConditionByPatient(HdrTableEnum.HDR_EMR_CONTENT.getCode(), oid, patientId, page,
//                    filters, "LAST_MODIFY_DATE_TIME", "CREATOR_NAME", "STATUS", "TOPIC",
//                    "MR_CLASS_NAME", "FILE_NO", "FILE_UNIQUE_ID", "FILE_NAME", "VISIT_ID", "IN_PATIENT_ID", "OUT_PATIENT_ID", "FILE_CLASS", "MR_CLASS_CODE", "OID", "ORG_NO", "ORG_NAME", "VISIT_TYPE_CODE");
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
        List<Map<String, String>> list = resultPage.getResult();

        //数据处理  字段映射
        for (Map<String, String> map : list) {
            //获取病历标记  添加状态字段
            String flag = map.get("FILE_FLAG");
            String status = "";
            //北医三院特殊处理
            if (HdrConstantEnum.HOSPITAL_BYSY.getCode().equals(ConfigCache.getCache(oid, "org_oid"))) {
                if ("02".equals(map.get("VISIT_TYPE_CODE"))) {
                    if ("T".equals(flag)) {
                        status = "未提交";
                    } else if ("1".equals(flag)) {
                        status = "住院医";
                    } else if ("2".equals(flag)) {
                        status = "主治审签";
                    } else if ("3".equals(flag)) {
                        status = "主任已签";
                    } else if ("4".equals(flag)) {
                        status = "上报卡打回";
                    } else if ("5".equals(flag)) {
                        status = "上报卡作废";
                    } else {
                        status = "未知状态";
                    }
                } else {
                    if ("T".equals(flag)) {
                        status = "草稿";
                    } else if ("0".equals(flag)) {
                        status = "未提交";
                    } else if ("1".equals(flag)) {
                        status = "已提交";
                    } else {
                        status = "未知状态";
                    }
                }
            } else {
                status = map.get("FILE_FLAG");
            }
            //保存状态字段
            map.put("STATUS", status);
            //字段映射
            Map<String, String> emr = new HashMap<String, String>();
//			Utils.checkAndPutToMap(emr, "rowkey", map.get("ROWKEY"), "-", false); //rowkey
            Utils.checkAndPutToMap(emr, "MediRecordTime", map.get("CREATE_DATE_TIME"), "-", false); //时间
            Utils.checkAndPutToMap(emr, "creatorName", map.get("CREATOR_NAME"), "-", false); //创建人
            Utils.checkAndPutToMap(emr, "status", map.get("STATUS"), "-", false); //状态
            Utils.checkAndPutToMap(emr, "topic", map.get("TOPIC"), "-", false); //名称
            Utils.checkAndPutToMap(emr, "patient_id", StringUtils.isNotBlank(map.get("IN_PATIENT_ID")) ? map.get("IN_PATIENT_ID") : map.get("OUT_PATIENT_ID"), "-", false); //pid
            Utils.checkAndPutToMap(emr, "visit_id", map.get("VISIT_ID"), "-", false); //vid
            Utils.checkAndPutToMap(emr, "file_no", map.get("FILE_NO"), "-", false);
            Utils.checkAndPutToMap(emr, "file_name", map.get("FILE_NAME"), "-", false);
            Utils.checkAndPutToMap(emr, "file_unique_id", map.get("FILE_UNIQUE_ID"), "-", false);
            Utils.checkAndPutToMap(emr, "mrClassCode", map.get("MR_CLASS_CODE"), "-", false);
            Utils.checkAndPutToMap(emr, "oid", map.get("OID"), "-", false);
            Utils.checkAndPutToMap(emr, "orgNo", map.get("ORG_NO"), "-", false);
            Utils.checkAndPutToMap(emr, "orgName", map.get("ORG_NAME"), "-", false);
            Utils.checkAndPutToMap(emr, "visitTypeCode", map.get("VISIT_TYPE_CODE"), "-", false);
            String mrClassName = map.get("MR_CLASS_NAME");
            if (mrClassName != null) {
                mrClassName = map.get("MR_CLASS_NAME").replace("(", "").replace(")", "").replace("（", "")
                        .replace("）", "");
            }
            for (Map<String, String> type : types) {
                if (type.get("name").equals(mrClassName)) {
                    emr.put("mrClassCode", type.get("id"));
                    emr.put("mrClassName", type.get("name"));
                    break;
                }
                if (mrClassName == null && type.get("name").equals("其他")) {
                    emr.put("mrClassCode", type.get("id"));
                    emr.put("mrClassName", type.get("name"));
                    break;
                }
            }
            //珠海妇幼特殊处理
            String fileClass = map.get("FILE_CLASS");
            if (StringUtils.isNotBlank(fileClass)) {
                if ("PDF".equals(fileClass)) {
                    //如果是PDF需要将加密后的base64转码
                    emr.put("linkType", "pdf");
                }
                if ("HTML".equals(fileClass) || "NULL".equals(fileClass)) {
                    emr.put("linkType", "html");
                }

            }
            List.add(emr);
        }
        return true;
    }

    @Override
    public void getAllMRCount(Map<String, Object> resultMap, String outPatientId) {

        int num = 0;
        List<PropertyFilter> filters = new ArrayList<PropertyFilter>();
        String userName = SecurityCommonUtil.getLoginUserCode(); //在线用户账号
        Map<String, String> loginOrgInfo = organizationDao.getLoginOrgInfo();
        String oid = loginOrgInfo.get("code");
        Map<String, Object> power = powerService.getPowerConfigByEMR(oid, userName);



        if (StringUtils.isNotBlank(outPatientId)) {
            String[] pats = outPatientId.split(",");
            for (int i = 0; i < pats.length; i++) {
                if (StringUtils.isNotBlank(pats[i])) {
                    String[] pat = pats[i].split("\\|");
                    filters = new ArrayList<PropertyFilter>();
                    if ("false".equals(power.get("isAll"))) {
                        List<String> classList = (List<String>) power.get("power");
                        String filterStr = StringUtils.join(classList.toArray(), ",");
                        filters.add(new PropertyFilter("MR_CLASS_CODE", MatchType.IN.getOperation(), filterStr));
                    }
                    filters.add(new PropertyFilter("VISIT_TYPE_CODE", MatchType.EQ.getOperation(), pat[0]));
                    //新增病历过滤配置 civ配置文件 EMR_VIEW_CONFIG
                    Config.setEmrViewVonfig(pat[2], filters);
                    num = num + getAllMRCountByPat(pat[2], pat[1], pat[3], filters);
                }
            }
        }
        resultMap.put("num", num);
    }

    /**
     * @Description 类描述：患者病例文书数量
     */
    public int getAllMRCountByPat(String oid, String patientId, String visitId, List<PropertyFilter> filters) {
        //filters.add(new PropertyFilter("VISIT_ID", "STRING", MatchType.EQ.getOperation(), visitId));
        List<Map<String, String>> page = new ArrayList<>();
        ResultVo<PageResultVo<Map<String, String>>> resultVo = hbaseQueryClient.getPageByCondition(
                PageRequestBuilder.init()
                        .tableName(HdrTableEnum.HDR_EMR_CONTENT.getCode())
                        .patientId(patientId)
                        .oid(oid)
                        .visitId(visitId)
                        .visitTypeCode("")
                        .filters(filters)
                        .pageNo(0)
                        .pageSize(0)
                        .orderBy("")
                        .desc()
                        .column("LAST_MODIFY_DATE_TIME", "CREATOR_NAME", "STATUS",
                                "TOPIC", "MR_CLASS_NAME")
                        .build());
        if(resultVo.isSuccess()){
            page = resultVo.getContent().getResult();
        }
//        List<Map<String, String>> page = hbaseDao.findConditionByPatient(HdrTableEnum.HDR_EMR_CONTENT.getCode(), oid,
//                patientId, filters, new String[]{"LAST_MODIFY_DATE_TIME", "CREATOR_NAME", "STATUS",
//                        "TOPIC", "MR_CLASS_NAME"});
        return page.size();
    }

    /**
     * @Description 类描述：患者病例文书类型
     */
    public void getAllMRCountByPat(String oid, String patientId, String visitId, List<PropertyFilter> filters, Map<String, String> items) {
        //filters.add(new PropertyFilter("VISIT_ID", "STRING", MatchType.EQ.getOperation(), visitId));
        List<Map<String, String>> page = new ArrayList<>();
        ResultVo<PageResultVo<Map<String, String>>> resultVo = hbaseQueryClient.getPageByCondition(
                PageRequestBuilder.init()
                        .tableName(HdrTableEnum.HDR_EMR_CONTENT.getCode())
                        .patientId(patientId)
                        .oid(oid)
                        .visitId(visitId)
                        .visitTypeCode("")
                        .filters(filters)
                        .pageNo(0)
                        .pageSize(0)
                        .orderBy("")
                        .desc()
                        .column("LAST_MODIFY_DATE_TIME", "CREATOR_NAME", "STATUS",
                                "TOPIC", "MR_CLASS_NAME", "MR_CLASS_CODE")
                        .build());
        if(resultVo.isSuccess()){
            page = resultVo.getContent().getResult();
        }
//        List<Map<String, String>> page = hbaseDao.findConditionByPatient(HdrTableEnum.HDR_EMR_CONTENT.getCode(), oid,
//                patientId, filters, new String[]{"LAST_MODIFY_DATE_TIME", "CREATOR_NAME", "STATUS",
//                        "TOPIC", "MR_CLASS_NAME", "MR_CLASS_CODE"});
        for (Map<String, String> map : page) {

            if (map.get("MR_CLASS_NAME") == null) {
                items.put("其他", "其他");
                continue;
            } else {
                items.put(map.get("MR_CLASS_NAME"), map.get("MR_CLASS_CODE"));
            }
            String mrClassName = map.get("MR_CLASS_NAME").replace("(", "").replace(")", "").replace("（", "")
                    .replace("）", "");
            if (null == items.get(mrClassName)) {
                items.put(mrClassName, mrClassName);
            }
        }
        //移除上一个查询的VISIT_ID
        Iterator<PropertyFilter> it = filters.iterator();
        while (it.hasNext()) {
            PropertyFilter filter = it.next();
            if(filter.getPropertyName().equals("VISIT_ID")){
                it.remove();
            }
        }
    }


    /**
     * 将base64数据解析成PDF数据，并返回路径
     *
     * @param imgStr
     * @param patienId
     * @param visitId
     * @param fileNo
     * @param result
     * @return
     */
    @Override
    public String getPdfData(String imgStr, String oid, String patienId, String visitId, String fileNo, Map<String, String> result, String path) {

        //这里返回给前端相对路径，定时清理通过绝对路径处理
        //String path = servletContext.getRealPath("");
        File file = new File(path + File.separator + "pdf_datas");
        file.mkdirs();
        path = file.getPath() + File.separator;
        String relativePath = File.separator + "pdf_datas";
        result.put("linkType", "PDF");
        return relativePath + File.separator + CivUtils.Base64ToImage(imgStr, oid, patienId, visitId, fileNo, path);

    }
//
//    /**
//     * 获取文书类型配置列表
//     *
//     * @return
//     */
//    @Override
//    public List<Map<String, String>> getEmrTypes(String oid) {
//        List<Map<String, String>> list = new ArrayList<Map<String, String>>();
//        String allergyTypeString = Config.getEmrTypeList(oid);
//        String[] types = allergyTypeString.split(";");
//        for (String type : types) {
//            Map<String, String> map = new HashMap<String, String>();
//            String[] array = type.split("\\|");
//            map.put("code", array[0]);
//            map.put("name", array[1]);
//            list.add(map);
//        }
//        return list;
//    }
//

    @Override
    public Map<String, String> getMedicalRecordDetails(String fileNo, String oid, String pid, String vid, String visitTypeCode, String mrClassCode, String sysCode) {
        Map<String, String> result = new HashMap<String, String>();
        List<PropertyFilter> filters = new ArrayList<PropertyFilter>();
        logger.info("这里是特殊字段展示" + vid);
        //filters.add(new PropertyFilter("VISIT_ID", "STRING", MatchType.EQ.getOperation(), vid));
        filters.add(new PropertyFilter("FILE_NO", MatchType.EQ.getOperation(), fileNo));
        if(StringUtils.isNotBlank(mrClassCode) && !"-".equals(mrClassCode)){
            filters.add(new PropertyFilter("MR_CLASS_CODE", MatchType.EQ.getOperation(), mrClassCode));
        }
        //新增病历过滤配置 civ配置文件 EMR_VIEW_CONFIG
        Config.setEmrViewVonfig(oid, filters);
        List<Map<String, String>> list = new ArrayList<>();
        ResultVo<PageResultVo<Map<String, String>>> resultVo = hbaseQueryClient.getPageByCondition(
                PageRequestBuilder.init()
                        .tableName(HdrTableEnum.HDR_EMR_CONTENT.getCode())
                        .patientId(pid)
                        .oid(oid)
                        .visitId(vid)
                        .visitTypeCode("")
                        .filters(filters)
                        .pageNo(0)
                        .pageSize(0)
                        .orderBy("")
                        .desc()
                        .column( "MR_CONTENT_HTML", "FILE_UNIQUE_ID", "FILE_NAME", "FILE_NO", "IN_PATIENT_ID", "VISIT_ID", "VISIT_TYPE_CODE", "OUT_PATIENT_ID", "URL", "FILE_CLASS")
                        .build());
        if(resultVo.isSuccess()){
            list = resultVo.getContent().getResult();
        }
//        List<Map<String, String>> list = hbaseDao.findConditionByPatient(HdrTableEnum.HDR_EMR_CONTENT.getCode(), oid, pid, filters,
//                "MR_CONTENT_HTML", "FILE_UNIQUE_ID", "FILE_NAME", "FILE_NO", "IN_PATIENT_ID", "VISIT_ID", "VISIT_TYPE_CODE", "OUT_PATIENT_ID", "URL", "FILE_CLASS");
        //未找到，终止执行
        if (null == list || list.isEmpty()) {
            return result;
        }
        Map<String, String> dataMap = list.get(0);
        Map<String, String> config = powerService.getSysConfigByType("all", "StartUse_HidePatKeyM");
        String html = "";
        Map<String, String> replaceRule=new HashMap<>();
        if ("1".equals(config.get("result"))) {
            String encryptedMrUrl = ConfigCache.getCache(oid, "REMOTE_ENCRYPTED_MR_URL");
            //排除病历走url方式，MR_CONTENT_HTML为空的情况
            if (StringUtils.isNotBlank(dataMap.get("MR_CONTENT_HTML"))) {
                replaceRule = getRule(oid, pid, vid, visitTypeCode);
                logger.info("脱敏规则：" + replaceRule);
                //String html = DataMaskUtil.toBeMasked(dataMap.get("MR_CONTENT_HTML"), replaceRule);
                if (StringUtils.isNotBlank(dataMap.get("FILE_CLASS")) && dataMap.get("FILE_CLASS").equalsIgnoreCase("text")){
                    html = DataMaskUtil.toBeMasked(dataMap.get("MR_CONTENT_HTML"), replaceRule);
                }else{
                    html = DataMaskUtil.maskHtml(dataMap.get("MR_CONTENT_HTML"), replaceRule);
                }
                dataMap.put("MR_CONTENT_HTML", html);
            } else if (StringUtils.isNotBlank(encryptedMrUrl)) {
                Map<String, String> encryptedMrParams = new HashMap<>();
                try {
                    encryptedMrParams.putAll(getEncryptedMrParams(oid, pid, vid, visitTypeCode, fileNo));
                } catch (JsonProcessingException e) {
                    throw new RuntimeException("远程加密病历的json配置解析失败", e);
                }
                replaceRule = getRule(oid, pid, vid, visitTypeCode);
                for (Map.Entry<String, String> entry : replaceRule.entrySet()) {
                    String beMasked = "";
                    //String beMasked = DataMaskUtil.toBeMasked(entry.getKey(), entry.getValue());
                    if (StringUtils.isNotBlank(dataMap.get("FILE_CLASS")) && dataMap.get("FILE_CLASS").equalsIgnoreCase("text")){
                        beMasked = DataMaskUtil.toBeMasked(entry.getKey(), entry.getValue());
                    }else{
                        beMasked = DataMaskUtil.maskHtml(entry.getKey(),replaceRule);
                    }
                    encryptedMrParams.put(entry.getKey(), beMasked);
                }
                restTemplate.getMessageConverters().set(1, new StringHttpMessageConverter(StandardCharsets.UTF_8));
                String s = "";
                try {
                    s = objectMapper.writeValueAsString(encryptedMrParams);
                } catch (JsonProcessingException e) {
                    throw new RuntimeException("序列化加密参数时异常", e);
                }
                logger.info("请求参数序列化：" + s);
                HttpHeaders headers = new HttpHeaders();
                headers.setContentType(MediaType.APPLICATION_JSON);
                HttpEntity<String> request = new HttpEntity<>(s, headers);
                html = restTemplate.postForObject(encryptedMrUrl, request, String.class);


                if (StringUtils.isNotBlank(html)) {
                    result.put("status", "1");
                    result.put("linkType", "Html");
                    result.put("htmlValue", html);
                } else {
                    result.put("status", "0");
                }
                return result;

            }
        }

        String visit_code = StringUtils.isNotBlank(dataMap.get("VISIT_TYPE_CODE")) ? dataMap.get("VISIT_TYPE_CODE") : "02";
        boolean isCall = Config.getEMR_ISCALL(oid, visit_code);
        String pat = "";
        if ("02".equals(visit_code)) {
            pat = dataMap.get("IN_PATIENT_ID");
        } else {
            pat = dataMap.get("OUT_PATIENT_ID");
        }

        if (isCall) {//webservice外部调用
            logger.info("电子病历外部调用");
            String visit_id = dataMap.get("VISIT_ID");
            String type = Config.getEMR_ISCALL_TYPE(oid, visit_code);
            String url = Config.getEMR_ISCALL_URL(oid, visit_code);
            String value = getEmrValueByCall(type, url, oid, pat, visit_id, fileNo);
            result.put("status", "1");
            result.put("linkType", "Html");
            result.put("htmlValue", value);
        } else {//非外部调用
            //是否配置了第三方url
            if (StringUtils.isNotBlank(CommonConfig.getURL(oid, "RM"))) {//是否配置了外部电子病历系统
                logger.info("电子病历第三方url调用");
                String sysCodeUrl="url";
                if(oid.equals("12370181493120325G")){
                    sysCodeUrl="base64";
                }
                Map<String, String> url = commonURLService.getCommonUrl(oid, pid, vid, visitTypeCode, "RM", sysCodeUrl);
                return url;
            }
//            是否配置远程地址，以json传参
            String encryptedMrUrl = ConfigCache.getCache(oid, "REMOTE_ENCRYPTED_MR_URL");
            if (StringUtils.isNotBlank(encryptedMrUrl) ) {
                if(oid.equals(HdrConstantEnum.HOSPITAL_DPYY.getCode()) && ! "01".equals(visitTypeCode)){
                    Map<String, String> encryptedMrParams = new HashMap<>();
                    try {

                        encryptedMrParams.putAll(getEncryptedMrParams(oid, pid, vid, visitTypeCode, fileNo));
                    } catch (JsonProcessingException e) {
                        throw new RuntimeException("远程加密病历的json配置解析失败", e);
                    }
                    restTemplate.getMessageConverters().set(1, new StringHttpMessageConverter(StandardCharsets.UTF_8));
                    String s = "";
                    try {
                        s = objectMapper.writeValueAsString(encryptedMrParams);
                    } catch (JsonProcessingException e) {
                        throw new RuntimeException("序列化加密参数时异常", e);
                    }
                    logger.info("请求参数序列化：" + s);
                    HttpHeaders headers = new HttpHeaders();
                    headers.setContentType(MediaType.APPLICATION_JSON);
                    HttpEntity<String> request = new HttpEntity<>(s, headers);
                    html = restTemplate.postForObject(encryptedMrUrl, request, String.class);
                    if (StringUtils.isNotBlank(html)) {
                        result.put("status", "1");
                        result.put("linkType", "Html");
                        result.put("htmlValue", html);
                    } else {
                        result.put("status", "0");
                    }
                    return result;
                }
                if(!oid.equals(HdrConstantEnum.HOSPITAL_DPYY.getCode())){
                    Map<String, String> encryptedMrParams = new HashMap<>();
                    try {
                        encryptedMrParams.putAll(getEncryptedMrParams(oid, pid, vid, visitTypeCode, fileNo));
                        encryptedMrParams.putAll(replaceRule);
                    } catch (JsonProcessingException e) {
                        throw new RuntimeException("远程加密病历的json配置解析失败", e);
                    }
                    restTemplate.getMessageConverters().set(1, new StringHttpMessageConverter(StandardCharsets.UTF_8));
                    String s = "";
                    try {
                        s = objectMapper.writeValueAsString(encryptedMrParams);
                    } catch (JsonProcessingException e) {
                        throw new RuntimeException("序列化加密参数时异常", e);
                    }
                    logger.info("请求参数序列化：" + s);
                    HttpHeaders headers = new HttpHeaders();
                    headers.setContentType(MediaType.APPLICATION_JSON);
                    HttpEntity<String> request = new HttpEntity<>(s, headers);
                    html = restTemplate.postForObject(encryptedMrUrl, request, String.class);
                    if (StringUtils.isNotBlank(html)) {
                        result.put("status", "1");
                        result.put("linkType", "Html");
                        result.put("htmlValue", html);
                    } else {
                        result.put("status", "0");
                    }
                    return result;
                }

            }
            //未配置url，依次获取 url，html字段值
            String url = dataMap.get("URL");
            if (StringUtils.isNotBlank(url)) {
                if (HdrConstantEnum.HOSPITAL_BJXH.getCode().equals(ConfigCache.getCache(oid, "org_oid"))) {
                    String orgName = ConfigCache.getCache(oid, "org_name");
                    String usercode = SecurityContextHolder.getContext().getAuthentication().getName();
                    String userName = powerService.getUserName(oid, usercode);
                    String emrHiddenConfig = Config.getEMRHiddenConfig(oid);
                    url += "&watermark=" + orgName + "-" + userName + usercode + "&privacy=" + emrHiddenConfig;
                }
                logger.info("电子病历数据端url调用");
                result.put("status", "1");
                result.put("linkType", "Iframe");
                result.put("url", url);
            } else {
                String htmlValue = "";
                //获取配置先读取哪个数据源
                String dataSource = Config.getCIV_EMR_FIRST_DATA_SOURCE(oid);
                String path = Config.getCIV_EMR_PDF_PATH(oid);
                if ("HTML".equals(dataSource)) {
                    logger.info("电子病历HTML调用");
                    //获取病历文书HTML
                    htmlValue = dataMap.get("MR_CONTENT_HTML");
                    result.put("linkType", "Html");
                    if ("PDF".equals(dataMap.get("FILE_CLASS"))) {
                        htmlValue = getPdfData(htmlValue, oid, pid, vid, fileNo, result, path);
                    }
                    result.put("fileClass", dataMap.get("FILE_CLASS"));
                    result.put("status", "1");
                    result.put("htmlValue", htmlValue);
                }
                if ("DG".equals(dataSource)) {
                    logger.info("电子病历DG调用");
                    result = getDgHtmlText(oid, pid, vid, mrClassCode, fileNo);
                    if (StringUtils.isBlank(result.get("htmlValue"))) {
                        //获取病历文书HTML
                        htmlValue = dataMap.get("MR_CONTENT_HTML");
                        if ("PDF".equals(dataMap.get("FILE_CLASS"))) {
                            htmlValue = getPdfData(htmlValue, oid, pid, vid, fileNo, result, path);
                        }
                        result.put("fileClass", dataMap.get("FILE_CLASS"));
                        result.put("htmlValue", htmlValue);
                        result.put("status", "1");
                    }
                }
                //都没有数据时的处理
                if (StringUtils.isBlank(result.get("htmlValue"))) {
                    result.put("status", "0");
                    result.put("message", "病历文书无预览信息！");
                }
            }
        }

        return result;
    }

    private Map<String, String> getRule(String oid, String pid, String vid, String visitTypeCode) {
        List<PropertyFilter> DesensitizationFieldFilter = new ArrayList<PropertyFilter>();
        //DesensitizationFieldFilter.add(new PropertyFilter("VISIT_ID", "STRING", MatchType.EQ.getOperation(), vid));
        DesensitizationFieldFilter.add(new PropertyFilter("VISIT_TYPE_CODE",  MatchType.EQ.getOperation(), visitTypeCode));
        String tableName = "";
        //门诊，脱敏字段来源HDR_OUT_VISIT
        //住院，脱敏字段来源HDR_INP_SUMMARY
        if (visitTypeCode.equals(VisitTypeEnum.OUT_VISIT.getCode())) {
            tableName = HdrTableEnum.HDR_OUT_VISIT.getCode();
        } else if (visitTypeCode.equals(VisitTypeEnum.IN_VISIT.getCode())) {
            tableName = HdrTableEnum.HDR_INP_SUMMARY.getCode();
        }
        List<Map<String, Object>> infoHiddenField = powerService.getInfoHiddenField(null, "");
        Map<String, String> replaceRule = new HashMap<String, String>();
//        List<Map<String, String>> desensitizationFieldList = new ArrayList<>();
        ResultVo<PageResultVo<Map<String, String>>> resultVo = hbaseQueryClient.getPageByCondition(
                PageRequestBuilder.init()
                        .tableName(tableName)
                        .patientId(pid)
                        .oid(oid)
                        .visitId(vid)
                        .visitTypeCode(visitTypeCode)
                        .filters(DesensitizationFieldFilter)
                        .pageNo(0)
                        .pageSize(0)
                        .orderBy("")
                        .desc()
                        .column()
                        .build());
//        if(resultVo.isSuccess()){
//            desensitizationFieldList = resultVo.getContent().getResult();
//        }
        //List<Map<String, String>> desensitizationFieldList = hbaseDao.findConditionByPatient(tableName, oid, pid, DesensitizationFieldFilter);
        //List<Map<String, Object>> infoHiddenField = powerService.getInfoHiddenField(null, "");
        //Map<String, String> replaceRule = new HashMap<String, String>();
//        if(desensitizationFieldList.size() != 0 && !desensitizationFieldList.isEmpty()){
//            for (Map<String, Object> map : infoHiddenField) {
//                String ormFileds = (String) map.get("orm_fileds");
//                String[] split = ormFileds.split(",");
//                for (String s : split) {
//                    String fieldValue = desensitizationFieldList.get(0).get(s);
//                    if (StringUtils.isNotBlank(fieldValue) && !fieldValue.contains("*")) {
//                        replaceRule.put(fieldValue, (String) map.get("value"));
//                    }
//
//                }
//            }
//        }

        // 处理查询结果
        if (resultVo.isSuccess()) {
            List<Map<String, String>> desensitizationFieldList = resultVo.getContent().getResult();
            if (!desensitizationFieldList.isEmpty()) {
                /*for (Map<String, Object> map : infoHiddenField) {
                    String ormFileds = (String) map.get("orm_fileds");
                    String[] split = ormFileds.split(",");
                    for (String s : split) {
                        String fieldValue = desensitizationFieldList.get(0).get(s);
                        if (StringUtils.isNotBlank(fieldValue) && !fieldValue.contains("*")) {
                            replaceRule.put(fieldValue, (String) map.get("value"));
                        }
                    }
                }*/
                Map<String, Map<String, Object>> maskConfigForCurrentUser = powerService.getMaskConfigForCurrentUser();
                for (Map.Entry<String, Map<String, Object>> entrySet : maskConfigForCurrentUser.entrySet()) {
                    String filedCode = entrySet.getKey();
                    Map<String, Object> ruleValue = entrySet.getValue();

                    String fieldValue = desensitizationFieldList.get(0).get(filedCode);
                    String maskValue = powerService.maskFiledValue(fieldValue, ruleValue);
                    if (StringUtils.isNotBlank(fieldValue) && !fieldValue.contains("*")) {
                        replaceRule.put(fieldValue, maskValue);
                    }
                }
                logger.info("脱敏规则：" + replaceRule);
            }
        }

        return replaceRule;
    }

    private Map<String, String> getEncryptedMrParams(String oid, String patientId, String visitId, String visitTypeCode, String fileNo) throws JsonProcessingException {
        Map<String, String> params = new HashMap<>();
        String paramJson = ConfigCache.getCache(oid, "REMOTE_ENCRYPTED_MR_PARAM");
        if (StringUtils.isNotBlank(paramJson)) {
            List<Map<String, String>> list = objectMapper.readValue(paramJson, new TypeReference<List<Map<String, String>>>() {
            });
            logger.info("读取到的参数配置： " + list);
            for (Map<String, String> map : list) {
                String param = map.get("param");
                String field = map.get("field");
                String tableName = map.get("tableName");
                String fixedValue = map.get("fixedValue");
                String value = "";
                if (StringUtils.isBlank(fixedValue)) {
                    List<PropertyFilter> filters = new ArrayList<PropertyFilter>();
//                    if (StringUtils.isNotBlank(visitId)) {
//                        filters.add(new PropertyFilter("VISIT_ID", "STRING", MatchType.EQ.getOperation(), visitId));
//                    }
                    if (StringUtils.isNotBlank(visitTypeCode)) {
                        filters.add(new PropertyFilter("VISIT_TYPE_CODE", MatchType.EQ.getOperation(), visitTypeCode));
                    }
                    if (HdrTableEnum.HDR_EMR_CONTENT.getCode().equals(tableName) && StringUtils.isNotBlank(fileNo)) {
                        filters.add(new PropertyFilter("FILE_NO", MatchType.EQ.getOperation(), fileNo));
                    }
                    List<Map<String, String>> conditionByPatient = new ArrayList<>();
                    ResultVo<PageResultVo<Map<String, String>>> resultVo = hbaseQueryClient.getPageByCondition(
                            PageRequestBuilder.init()
                                    .tableName(tableName)
                                    .patientId(patientId)
                                    .oid(oid)
                                    .visitId(visitId)
                                    .visitTypeCode(visitTypeCode)
                                    .filters(filters)
                                    .pageNo(0)
                                    .pageSize(0)
                                    .orderBy("")
                                    .desc()
                                    .column(field)
                                    .build());



                    if(resultVo.isSuccess()){
                        conditionByPatient = resultVo.getContent().getResult();
                    }
                    //List<Map<String, String>> conditionByPatient = hbaseDao.findConditionByPatient(tableName, oid, patientId, filters, field);
                    logger.info("查询出的结果：" + conditionByPatient);
                    if (conditionByPatient != null && !conditionByPatient.isEmpty()) {
                        value = conditionByPatient.get(0).get(field);
                    }
                    params.put(param, value);
                } else {
                    params.put(param, fixedValue);
                }

            }
        }
        logger.info("远程加密病历参数：" + params.toString());
        return params;
    }

    /**
     * 通过病例章节显示病历内容数据
     *
     * @param pid
     * @param vid
     * @param mrClassCode
     * @param
     * @return
     */
    @Override
    public Map<String, String> getDgHtmlText(String oid, String pid, String vid, String mrClassCode, String fileUniqueId) {
        Map<String, String> result = new HashMap<String, String>();
        //获取病例章节配置
        List<Map<String, String>> dgConfig = emrDgDao.getEmrDgHtml(mrClassCode);
        String html = "";
        //查询hdr_emr_context_dg表
        List<PropertyFilter> filtersDg = new ArrayList<PropertyFilter>();
        //filtersDg.add(new PropertyFilter("VISIT_ID", "STRING", MatchType.EQ.getOperation(), vid));
        //TODO 发包前需要把注释放开  FILE_UNIQUE_ID FILE_NO
        filtersDg.add(new PropertyFilter("FILE_NO", MatchType.EQ.getOperation(), fileUniqueId));
        filtersDg.add(new PropertyFilter("MR_CLASS_CODE", MatchType.EQ.getOperation(), mrClassCode));
        List<Map<String, String>> listDg = new ArrayList<>();
        ResultVo<PageResultVo<Map<String, String>>> resultVo = hbaseQueryClient.getPageByCondition(
                PageRequestBuilder.init()
                        .tableName("HDR_EMR_CONTENT_DG")
                        .patientId(pid)
                        .oid(oid)
                        .visitId(vid)
                        .visitTypeCode("")
                        .filters(filtersDg)
                        .pageNo(0)
                        .pageSize(0)
                        .orderBy("")
                        .desc()
                        .column("DG_CODE", "DG_NAME", "FILE_NAME", "FILE_NO", "DG_PLAIN_CONTENT", "FILE_UNIQUE_ID")
                        .build());



        if(resultVo.isSuccess()){
            listDg = resultVo.getContent().getResult();
        }
//        List<Map<String, String>> listDg = hbaseDao.findConditionByPatient("HDR_EMR_CONTENT_DG", oid, pid, filtersDg,
//                new String[]{"DG_CODE", "DG_NAME", "FILE_NAME", "FILE_NO", "DG_PLAIN_CONTENT", "FILE_UNIQUE_ID"});
        //使用配置模板填充数据
        if (dgConfig != null && dgConfig.size() > 0) {
            logger.info("电子病历模板配置调用");
            List<String> dgList = new ArrayList<String>();
            for (Map<String, String> map : dgConfig) {
                if (StringUtils.isBlank(html)) {
                    html = map.get("html_text");
                }
                dgList.add(map.get("dg_code"));
            }
            // 查询入出转 和 门诊表 获取 患者信息
//        Map<String, String> patInfo = HbaseService.getPatInfo(pid, vid);
//        if (patInfo.isEmpty()) {
//            patInfo = HbaseService.getPatInfoMZ(pid, vid);
//        }
//        if (!patInfo.isEmpty()) {
//            html = html.replaceAll("\\{\\{name}}", null == patInfo.get("PERSON_NAME")?"":patInfo.get("PERSON_NAME"));
//            html = html.replaceAll("\\{\\{sex}}",  null == patInfo.get("SEX_NAME")?"":patInfo.get("SEX_NAME"));
//            html = html.replaceAll("\\{\\{age}}",  null == patInfo.get("AGE_VALUE")?"":patInfo.get("AGE_VALUE"));
//            html = html.replaceAll("\\{\\{admissiong_time}}",  null == patInfo.get("ADMISSION_TIME")?"":patInfo.get("ADMISSION_TIME"));
//            html = html.replaceAll("\\{\\{discharge_time}}",  null == patInfo.get("DISCHARGE_TIME")?"":patInfo.get("DISCHARGE_TIME"));
//            html = html.replaceAll("\\{\\{inp_no}}",  null == patInfo.get("INP_NO")?"":patInfo.get("INP_NO"));
//        } else {
//            html = html.replaceAll("\\{\\{name}}", "");
//            html = html.replaceAll("\\{\\{sex}}", "");
//            html = html.replaceAll("\\{\\{age}}", "");
//            html = html.replaceAll("\\{\\{admissiong_time}}", "");
//            html = html.replaceAll("\\{\\{discharge_time}}", "");
//            html = html.replaceAll("\\{\\{inp_no}}", "");
//        }

            if (null != listDg && listDg.size() > 0) {
                //拼接HTML
                for (String dg : dgList) {
                    for (Map<String, String> dgMap : listDg) {
                        if (StringUtils.isNotBlank(dg) && dg.equalsIgnoreCase(dgMap.get("DG_CODE"))) {
                            html = html.replaceAll("\\{\\{" + dg + "}}", null == dgMap.get("DG_PLAIN_CONTENT") ? "" : dgMap.get("DG_PLAIN_CONTENT"));
                        }
                    }
                }
                //可能存在配置的dg章节没有数据，需要把配置的HTML里面的过滤为空
                for (String dg : dgList) {
                    if (html.contains("{{" + dg + "}}")) {
                        html = html.replaceAll("\\{\\{" + dg + "}}", "");
                    }
                }
            } else {
                html = "";
            }
        } else {
            logger.info("电子病历通用模板配置调用");
            //使用通用配置模板
            String htmlCommonModel = Config.getHTMLCommonModel(oid);
            if (htmlCommonModel == null || "".equals(htmlCommonModel)) {
                result.put("status", "1");
                result.put("linkType", "Html");
                result.put("htmlValue", html);
                return result;
            } else {
                String[] split = htmlCommonModel.split("@");
                if (split.length > 0) {
                    if (listDg != null && listDg.size() > 0) {
                        html = split[0];
                        for (Map<String, String> map : listDg) {
                            String dg_name = map.get("DG_NAME");
                            html += "<p><span style=\"font-family: 宋体; font-size: 14px;font-weight: bold;\">" + dg_name + ":</span><span style=\"font-family: 宋体; font-size: 14px;\">" + map.get("DG_PLAIN_CONTENT") + "</span></p>";
                        }
                        html += split[1];
                    }
                }
            }
        }

        result.put("status", "1");
        result.put("linkType", "Html");
        result.put("htmlValue", html);
        return result;
    }

    private String getEmrValueByCall(String type, String url, String oid, String patient_id, String visit_id, String fileNo) {
        // TODO Auto-generated method stub
        String htmlValue = "";
        if ("ws".equals(type)) {
            if (HdrConstantEnum.HOSPITAL_BYSY.getCode().equals(ConfigCache.getCache(oid, "org_oid"))) {
                try {
                    String Request = "<soapenv:Envelope xmlns:soapenv='http://schemas.xmlsoap.org/soap/envelope/' xmlns:web='http://webservice.iih.pkuhit/'><soapenv:Header/><soapenv:Body><web:selectMrAreas><!--Optional:--><web:paId>"
                            + patient_id + "</web:paId><!--Optional:--><web:times>" + visit_id
                            + "</web:times><!--Optional:--><web:typeCodes>MRM16.18,MRM16.46,MRM16.47,MRM16.48</web:typeCodes><!--Optional:--><web:names></web:names></web:selectMrAreas></soapenv:Body></soapenv:Envelope>";
                    System.out.println("Request:" + Request);
                    htmlValue = WsUtil.callRemoteWebService(url, Request);
                    System.out.println("wsvalue:" + htmlValue);
                    htmlValue = htmlValue.substring(htmlValue.indexOf("content&gt;") + 11,
                            htmlValue.indexOf("&lt;/content&gt;"));
                } catch (Exception e) {
                    e.printStackTrace();
                    return htmlValue;
                }
            } else if (HdrConstantEnum.HOSPITAL_NNEY.getCode().equals(ConfigCache.getCache(oid, "org_oid"))) {
                try {
                    String Request = "<soap:Envelope xmlns:soap='http://www.w3.org/2003/05/soap-envelope' xmlns:tem='http://tempuri.org/'><soap:Header/><soap:Body><tem:GetMedicalRecordInfo><!--Optional:--><tem:jsonParams><![CDATA[{ \"Channel\": \"WS22\",\"RecordNo\": \"" + fileNo + "\"}]]></tem:jsonParams></tem:GetMedicalRecordInfo></soap:Body></soap:Envelope>";
                    System.out.println("Request:" + Request);
                    htmlValue = WsUtil.callRemoteWebService(url, Request);
                    System.out.println("wsvalue:" + htmlValue);
                    htmlValue = htmlValue.substring(htmlValue.indexOf("FileContent\":\"") + 14,
                            htmlValue.indexOf("\",\"ResultCode\""));
                    //解密base64
                    BASE64Decoder decoder = new BASE64Decoder();
                    htmlValue = new String(decoder.decodeBuffer(htmlValue));
                } catch (Exception e) {
                    e.printStackTrace();
                    return htmlValue;
                }
            }else if (HdrConstantEnum.HOSPITAL_XNYY.getCode().equals(ConfigCache.getCache(oid, "org_oid"))) {
                try {
                    String Request = "<soap:Envelope xmlns:xsi=\"http://www.w3.org/2001/XMLSchema-instance\" xmlns:xsd=\"http://www.w3.org/2001/XMLSchema\" xmlns:soap=\"http://schemas.xmlsoap.org/soap/envelope/\">\n" +
                            "  <soap:Body>\n" +
                            "    <ConvertToHTML xmlns=\"http://www.healthcare.supcon.com/wsmeddoc/\">\n" +
                            "      <szPatientID>"+patient_id+"</szPatientID>\n" +
                            "      <szVisitID>"+visit_id+"</szVisitID>\n" +
                            "      <arrDocIDs>\n" +
                            "        <string>"+fileNo+"</string>\n" +
                            "      </arrDocIDs>\n" +
                            "    </ConvertToHTML>\n" +
                            "  </soap:Body>\n" +
                            "</soap:Envelope>";
                    //System.out.println("Request:" + Request);
                    htmlValue = WsUtil.callRemoteWebService(url, Request);
                    htmlValue = htmlValue.substring(htmlValue.indexOf("<ConvertToHTMLResult>") + 21,
                            htmlValue.indexOf("</ConvertToHTMLResult>"));
                    htmlValue = htmlValue.replace("&lt;","<").replace("&gt;",">").replace("nbsp;","").replace("&amp;"," ");
                    //System.out.println("wsvalue:" + htmlValue);
                } catch (Exception e) {
                    e.printStackTrace();
                    return htmlValue;
                }
            }else if(HdrConstantEnum.HOSPITAL_XQYY.getCode().equals(ConfigCache.getCache(oid, "org_oid"))){
                try {
                    String Request = "<soap:Envelope xmlns:soap=\"http://www.w3.org/2003/05/soap-envelope\" xmlns:wsm=\"http://www.healthcare.supcon.com/wsmeddoc/\">\n" +
                            "   <soap:Header/>\n" +
                            "   <soap:Body>\n" +
                            "      <wsm:ConvertToHTML>\n" +
                            "         <!--Optional:-->\n" +
                            "         <wsm:szPatientID>"+patient_id+"</wsm:szPatientID>\n" +
                            "         <!--Optional:-->\n" +
                            "         <wsm:szVisitID>"+visit_id+"</wsm:szVisitID>\n" +
                            "         <!--Optional:-->\n" +
                            "         <wsm:arrDocIDs>\n" +
                            "            <!--Zero or more repetitions:-->\n" +
                            "            <wsm:string>"+fileNo+"</wsm:string>\n" +
                            "         </wsm:arrDocIDs>\n" +
                            "      </wsm:ConvertToHTML>\n" +
                            "   </soap:Body>\n" +
                            "</soap:Envelope>";
                    System.out.println("Request:" + Request);
                    htmlValue = WsUtil.callRemoteWebService(url, Request);
                    htmlValue = htmlValue.substring(htmlValue.indexOf("<ConvertToHTMLResult>") + 21,
                            htmlValue.indexOf("</ConvertToHTMLResult>") - 3);
                    System.out.println("转义前:" + htmlValue);
                    htmlValue = htmlValue.replace("&lt;","<")
                            .replace("&gt;",">").replace("nbsp;","")
                            .replace("&amp;"," ").replace("&gt",">")
                            .replace("&lt","<").replace("quot;","");
                    System.out.println("转义后:" + htmlValue);
                } catch (Exception e) {
                    e.printStackTrace();
                    return htmlValue;
                }
            }

        }
        return htmlValue;
    }

    @Override
    public List<Map<String, String>> getMedicalTypes(String this_oid, String oid, String patientId, String outPatientId, String visitType) {
        // TODO Auto-generated method stub
        List<Map<String, String>> list = new ArrayList<Map<String, String>>();
        Map<String, String> items = new HashMap<String, String>();

        List<PropertyFilter> filters = new ArrayList<PropertyFilter>();
		filters.add(new PropertyFilter("VISIT_TYPE_CODE",  MatchType.EQ.getOperation(), visitType));
        //新增病历过滤配置 civ配置文件 EMR_VIEW_CONFIG
        Config.setEmrViewVonfig(oid, filters);
        String userName = SecurityContextHolder.getContext().getAuthentication().getName(); //在线用户账号
        Map<String, Object> power = powerService.getPowerConfigByEMR(oid, userName);
        if ("false".equals(power.get("isAll"))) {
            List<String> classList = (List<String>) power.get("power");
            String filterStr = StringUtils.join(classList.toArray(), ",");
            filters.add(new PropertyFilter("MR_CLASS_CODE", MatchType.IN.getOperation(), filterStr));
        }
        //-----
        for (PropertyFilter f : filters) {
            System.out.println(f.getPropertyName() + ";" + f.getMatchType() + ";" + ";" + f.getPropertyName());
        }
        //----
//        if (StringUtils.isNotBlank(patientId) && StringUtils.isNotBlank(this_oid) && this_oid.equals(oid) || this_oid.equals("ALL")) {
//            getAllMRCountByPat(oid, patientId, filters, items);
//        }
        if (StringUtils.isNotBlank(outPatientId)) {
            String[] pats = outPatientId.split(",");
            for (int i = 0; i < pats.length; i++) {
                if (StringUtils.isNotBlank(pats[i])) {
                    String[] pat = pats[i].split("\\|");
                    getAllMRCountByPat(pat[2], pat[1], pat[3], filters, items);
                }
            }
        }
//        List<String> itemList = new ArrayList<String>();
//        for (String item : items.keySet()) {
//            itemList.add(item);
//        }

        int count = 1;
        for (String item : items.keySet()) {
            Map<String, String> map = new HashMap<String, String>();
            map.put("id", items.get(item));
            map.put("name", item.replace("(", "").replace(")", "").replace("（", "").replace("）", ""));
            count++;
            list.add(map);
        }
        return list;
    }


//    /**
//     * 病历文书列表的统计数
//     */
//    @Override
//    public int getCount(String oid, String patient_id, String visit_id, String mr_class_code) {
//        Page<Map<String, String>> page = new Page<Map<String, String>>();
//        List<PropertyFilter> filters = new ArrayList<PropertyFilter>();
//        PropertyFilter filter1 = new PropertyFilter();
//        filter1.setMatchType("=");
//        filter1.setPropertyName("VISIT_ID");
//        filter1.setPropertyValue(visit_id);
//        filters.add(filter1);
//
//        PropertyFilter filter2 = new PropertyFilter();
//        filter2.setMatchType("in");
//        filter2.setPropertyName("MR_CLASS_CODE");
//        filter2.setPropertyValue(mr_class_code);
//        filters.add(filter2);
//
//        int count = 0;
//        try {
//            page = hbaseDao.findPageConditionByPatient(
//                    HdrTableEnum.HDR_EMR_CONTENT.getCode(), oid, patient_id, page,
//                    filters);
//            count = (int) page.getTotalCount();
//        } catch (Exception e) {
//            logger.error("查询Hbase数据库表HDR_EMR_CONTENT 出错。 ", e);
//            throw new ApplicationException("查询病历文书表出错。" + e.getCause());
//        }
//        return count;
//    }
//

//    @Override
//    public List<Map<String, Object>> getEmrTypesByPidVid(String oid, String patientId, String visitId, String visitType) {
//        List<Map<String, String>> mrsList = new ArrayList<Map<String, String>>();
//        //默认查询住院的病历文书
//        String vtype = "";
//        if ("OUTPV".equals(visitType)) {
//            vtype = "01";
//        } else if ("INPV".equals(visitType)) {
//            vtype = "02";
//        }
//
//        //条件过滤
//        List<PropertyFilter> filters = new ArrayList<PropertyFilter>();
//        //病历权限筛选
//        String userName = SecurityContextHolder.getContext().getAuthentication().getName(); //在线用户账号
//        //String userName = "admin";
//        Map<String, Object> power = powerService.getPowerConfigByEMR(oid, userName);
//        if ("false".equals(power.get("isAll"))) {
//            List<String> classList = (List<String>) power.get("power");
//            String filterStr = StringUtils.join(classList.toArray(), ",");
//            filters.add(new PropertyFilter("MR_CLASS_CODE", "STRING", MatchType.IN.getOperation(), filterStr));
//        }
//        filters.add(new PropertyFilter("VISIT_ID", "STRING", MatchType.EQ.getOperation(), visitId));
//        filters.add(new PropertyFilter("VISIT_TYPE_CODE", "STRING", MatchType.EQ.getOperation(), vtype));
//        //新增病历过滤配置 civ配置文件 EMR_VIEW_CONFIG
//        Config.setEmrViewVonfig(oid, filters);
//        System.out.println(filters);
//        mrsList = hbaseDao.findConditionByPatient(HdrTableEnum.HDR_EMR_CONTENT.getCode(), oid, patientId, filters,
//                "MR_CLASS_CODE", "MR_CLASS_NAME");
//
//        //每种类型的统计结果
//        Map<String, Integer> numMap = new HashMap<>();
//        for (Map<String, String> mrsMap : mrsList) {
//            Integer num = MapUtils.getInteger(numMap, MapUtils.getString(mrsMap, "MR_CLASS_CODE") + ":" + MapUtils.getString(mrsMap, "MR_CLASS_NAME"));
//            if (num == null) {
//                num = 1;
//            } else {
//                num += 1;
//            }
//            numMap.put(MapUtils.getString(mrsMap, "MR_CLASS_CODE") + ":" + MapUtils.getString(mrsMap, "MR_CLASS_NAME"), num);
//        }
//
//        //返回最终结果
//        List<Map<String, Object>> resultList = new ArrayList<Map<String, Object>>();
//        Map<String, Object> map1 = new HashMap<String, Object>();
//        map1.put("code", "all");
//        map1.put("name", "全部类型");
//        map1.put("num", mrsList.size());
//        resultList.add(map1);
//
//        for (String key : numMap.keySet()) {
//            String[] str = key.split(":");
//            Map<String, Object> map = new HashMap<String, Object>();
//            map.put("code", str[0]);
//            map.put("name", str[1]);
//            map.put("num", numMap.get(key));
//
//            resultList.add(map);
//        }
//        return resultList;
//    }

    @Override
    public List<Map<String, Object>> getMedicalRecordListByMrClassCode(String oid, String patientId, String visitId, String visitType, String orderBy, String orderDir) {


        //条件过滤
        List<PropertyFilter> filters = new ArrayList<PropertyFilter>();
        String userName = SecurityContextHolder.getContext().getAuthentication().getName(); //

        Map<String, Object> power = powerService.getPowerConfigByEMR(oid, userName);
        //病历权限筛选
        if ("false".equals(power.get("isAll"))) {
            List<String> classList = (List<String>) power.get("power");
            String filterStr = StringUtils.join(classList.toArray(), ",");
            filters.add(new PropertyFilter("MR_CLASS_CODE", MatchType.IN.getOperation(), filterStr));
        }
        //filters.add(new PropertyFilter("VISIT_ID", "STRING", MatchType.EQ.getOperation(), visitId));
        filters.add(new PropertyFilter("VISIT_TYPE_CODE", MatchType.EQ.getOperation(), visitType));
        //新增病历过滤配置 civ配置文件 EMR_VIEW_CONFIG
        Config.setEmrViewVonfig(oid, filters);
//
        List<Map<String, String>> mrs = new ArrayList<>();
        ResultVo<PageResultVo<Map<String, String>>> resultVo = hbaseQueryClient.getPageByCondition(
                PageRequestBuilder.init()
                        .tableName(HdrTableEnum.HDR_EMR_CONTENT.getCode())
                        .patientId(patientId)
                        .oid(oid)
                        .visitId(visitId)
                        .visitTypeCode(visitType)
                        .filters(filters)
                        .pageNo(0)
                        .pageSize(0)
                        .orderBy("")
                        .desc()
                        .column("FIRST_MR_SIGN_DATE_TIME", "CREATOR_NAME", "TOPIC", "VISIT_TYPE_CODE", "FILE_FLAG",
                                "MR_CLASS_CODE", "MR_CLASS_NAME", "CREATE_DATE_TIME", "IN_PATIENT_ID", "OUT_PATIENT_ID", "VISIT_ID", "FILE_NO", "FILE_UNIQUE_ID", "FFILE_CLASS")
                        .build());

        if(resultVo.isSuccess()){
            mrs = resultVo.getContent().getResult();
        }
//        List<Map<String, String>> mrs = hbaseDao.findConditionByPatient(HdrTableEnum.HDR_EMR_CONTENT.getCode(), oid, patientId, filters,
//                "FIRST_MR_SIGN_DATE_TIME", "CREATOR_NAME", "TOPIC", "VISIT_TYPE_CODE", "FILE_FLAG",
//                "MR_CLASS_CODE", "MR_CLASS_NAME", "CREATE_DATE_TIME", "IN_PATIENT_ID", "OUT_PATIENT_ID", "VISIT_ID", "FILE_NO", "FILE_UNIQUE_ID", "FFILE_CLASS");
        Utils.sortListByDate(mrs, orderBy, orderDir);


        //未查到数据，终止执行
        if (mrs.size() <= 0) {
            return new ArrayList<>();
        }
        //字段映射 数据处理
        List<Map<String, String>> emrs = new ArrayList<Map<String, String>>();
        for (Map<String, String> map : mrs) {
            //获取病历标记  添加状态字段
            String flag = map.get("FILE_FLAG");
            String status = "";
            if (HdrConstantEnum.HOSPITAL_BYSY.getCode().equals(ConfigCache.getCache(oid, "org_oid"))) {
                if ("02".equals(map.get("VISIT_TYPE_CODE"))) {
                    if ("T".equals(flag)) {
                        status = "未提交";
                    } else if ("1".equals(flag)) {
                        status = "住院医";
                    } else if ("2".equals(flag)) {
                        status = "主治审签";
                    } else if ("3".equals(flag)) {
                        status = "主任已签";
                    } else if ("4".equals(flag)) {
                        status = "上报卡打回";
                    } else if ("5".equals(flag)) {
                        status = "上报卡作废";
                    } else {
                        status = "未知状态";
                    }
                } else {
                    if ("T".equals(flag)) {
                        status = "草稿";
                    } else if ("0".equals(flag)) {
                        status = "未提交";
                    } else if ("1".equals(flag)) {
                        status = "已提交";
                    } else {
                        status = "未知状态";
                    }
                }
            } else {
                status = map.get("FILE_FLAG");
            }
            //保存状态字段
            map.put("STATUS", status);
            //首次签名时间不存在特殊处理
            if (StringUtils.isBlank(map.get("FIRST_MR_SIGN_DATE_TIME"))) {
                //替换为创建时间
                map.put("FIRST_MR_SIGN_DATE_TIME", map.get("CREATE_DATE_TIME"));
            }
            //处理下in_patient_id和out_patient_id,主要解决未按规范抽数导致的住院只有in_patient_id,门诊只有out_patient_id
            String pid = StringUtils.isNotBlank(map.get("IN_PATIENT_ID")) ? map.get("IN_PATIENT_ID") : map.get("OUT_PATIENT_ID");
            map.put("IN_PATIENT_ID", pid);
            map.put("OUT_PATIENT_ID", pid);
            //字段映射
            Map<String, String> emr = new HashMap<String, String>();
            ColumnUtil.convertMapping(emr, map, new String[]{"FIRST_MR_SIGN_DATE_TIME", "CREATOR_NAME",
                    "STATUS", "TOPIC", "MR_CLASS_CODE", "MR_CLASS_NAME", "CREATE_DATE_TIME", "IN_PATIENT_ID", "OUT_PATIENT_ID", "VISIT_ID", "FILE_NO", "FILE_UNIQUE_ID"});
            //珠海妇幼特殊处理
            String ffileClass = map.get("FFILE_CLASS");
            if (StringUtils.isNotBlank(ffileClass)) {
                if ("PDF".equals(ffileClass)) {
                    //如果是PDF需要将加密后的base64转码
                    emr.put("linkType", "pdf");
                }
                if ("HTML".equals(ffileClass) || "NULL".equals(ffileClass)) {
                    emr.put("linkType", "html");
                }

            }
            emrs.add(emr);
        }
        String linkType = CommonConfig.getLinkType(oid, "RM");
        String url = CommonConfig.getURL(oid, "RM");
        if (StringUtils.isBlank(url)) {
            linkType = "html";
        }
        for (Map<String, String> map : emrs) {
            map.put("linkType", linkType);
            map.put("patient_id", map.get("inPatientId"));
            map.put("visit_id", map.get("visitId"));
        }
        //重置分页
        //分类病历文书
        List<Map<String, Object>> list = new ArrayList<>();
        Map<String, List<Map<String, String>>> maptemp = new HashMap<>();
        for (Map<String, String> map : emrs) {
            String mrClassName = map.get("mrClassName");
            ArrayList childrenList = null;
            if (!maptemp.isEmpty() && maptemp.containsKey(mrClassName)) {
                childrenList = (ArrayList) maptemp.get(mrClassName);
            } else {
                childrenList = new ArrayList<>();
            }
            childrenList.add(map);
            maptemp.put(mrClassName, childrenList);
        }

        for (String key : maptemp.keySet()) {
            Map<String, Object> map = new HashMap<>();
            map.put("type", key);
            map.put("children", maptemp.get(key));
            list.add(map);
        }
        return list;
    }


    public String getMrText(String oid, String patientId, String visitId, String visitType) {
        String mrText = "";
        if (StringUtils.isNotBlank(patientId)) {
            String tableName = "HDR_EMR_CONTENT";
            //拼接rowkey前缀
            //页面查询条件
            List<PropertyFilter> filters = new ArrayList<PropertyFilter>();
            filters.add(new PropertyFilter("DELETE_FLAG", MatchType.IN.getOperation(), "0"));
            //createPropertyFilter("DELETE_FLAG", "0", MatchType.EQ.getOperation(), filters);
            if (StringUtils.isNotBlank(visitType)) {
                filters.add(new PropertyFilter("VISIT_TYPE_CODE", MatchType.IN.getOperation(), visitType));
            }
//            if (StringUtils.isNotBlank(visitId)) {
//                HbaseService.createPropertyFilter("VISIT_ID", visitId, MatchType.EQ.getOperation(), filters);
//            }
            String mrclasscode = Config.getEMR_RAN_MRCLASSCODE(oid);
            if (StringUtils.isNotBlank(mrclasscode)) {
                filters.add(new PropertyFilter("MR_CLASS_CODE", MatchType.IN.getOperation(), mrclasscode));
                //HbaseService.createPropertyFilter("MR_CLASS_CODE", mrclasscode, MatchType.IN.getOperation(), filters);
            }
            String subClassCode = ConfigCache.getCache(oid, "CURRENT_VIEW_MR_SUB_CLASS_CODE");
            if (StringUtils.isNotBlank(subClassCode)) {
                filters.add(new PropertyFilter("MR_SUB_CLASS_CODE", MatchType.EQ.getOperation(), subClassCode));
                //HbaseService.createPropertyFilter("MR_SUB_CLASS_CODE", subClassCode, MatchType.EQ.getOperation(), filters);
            }
            List<Map<String, String>> list = new ArrayList<>();
            if (StringUtils.isNotBlank(oid)) {
                ResultVo<PageResultVo<Map<String, String>>> resultVo = hbaseQueryClient.getPageByCondition(
                        PageRequestBuilder.init()
                                .tableName(tableName)
                                .patientId(patientId)
                                .oid(oid)
                                .visitId(visitId)
                                .visitTypeCode(visitType)
                                .filters(filters)
                                .pageNo(0)
                                .pageSize(0)
                                .orderBy("")
                                .desc()
                                .column("FILE_NO", "TOPIC", "CREATE_DATE_TIME", "CAPTION_DATE_TIME", "MR_CLASS",
                                        "VISIT_TYPE_CODE", "MR_CONTENT_TEXT")
                                .build());

                if(resultVo.isSuccess()){
                    list = resultVo.getContent().getResult();
                }
//                list = hbaseDao.findConditionByPatient(tableName, oid, patientId, filters,
//                        "FILE_NO", "TOPIC", "CREATE_DATE_TIME", "CAPTION_DATE_TIME", "MR_CLASS",
//                        "VISIT_TYPE_CODE", "MR_CONTENT_TEXT");
            }


            if (list != null && !list.isEmpty()) {
                //按时间排序来查找出最后一条数据
                Date currentDate = Utils.strToDate(list.get(0).get("CAPTION_DATE_TIME"), "yyyy-MM-dd hh:mm:ss");
                Map<String, String> tmpMap = list.get(0);
                for (int i = 1; i < list.size(); i++) {
                    Date tmpDate = Utils.strToDate(list.get(i).get("CAPTION_DATE_TIME"), "yyyy-MM-dd hh:mm:ss");
                    if (Utils.dateCompare(tmpDate, currentDate)) {
                        tmpMap = list.get(i);
                    }
                }
                mrText = tmpMap.get("MR_CONTENT_TEXT");
            }
        }
        return mrText;
    }

    @Override
    public ResultVO<Set<Map.Entry<String, String>>> getMedicalRecordInfo(String oid, String patientId, String visitId, String visitType) {

        String mrText = getMrText(oid, patientId, visitId, visitType);
        //未找到病历内容，中断执行
        if (StringUtils.isBlank(mrText)) {
            ResultVO<Set<Map.Entry<String, String>>> resultVO = new ResultVO<>();
            resultVO.fail("未找到病历内容");
            return resultVO;
        }
        //解析病历内容
        ResultVO<Set<Map.Entry<String, String>>> resultVO = new ResultVO<>();
        Map<String, String> element = EMRUtils.EMRElement(oid, mrText);

        resultVO.success("查询成功", element.entrySet());
        return resultVO;
    }

    @Override
    public ResultVO<List<Map<String, Object>>> getTechnologiesMedicalRecordList(String oid, String oidLast, String lastPatientId, String lastVisitId, String lastVisitTypeCode, String outPatientId) {
        /* 查询就诊列表*/
        List<Map<String, Object>> resultList = new ArrayList<>();
        List<Map<String, String>> outVisitList = new ArrayList<>();
        List<Map<String, String>> inpList = new ArrayList<>();
        String[] vTypeAndPidAndOidArray = outPatientId.split(",");
        String oidTmp = "";
        for (String s : vTypeAndPidAndOidArray) {
            String[] strings = s.split("\\|");
            String visitType = strings[0];
            String patientId = strings[1];
            String oidInCoordinates = strings[2];
            String visitId = strings[3];
            /*在为ALL时查询所有，否则仅查询当前选中的oid*/

            if (oidInCoordinates.equals(oidTmp)) {
                if (visitType.equals("01")) {
                    List<PropertyFilter> filters = new ArrayList<PropertyFilter>();
                    List<Map<String, String>> list = new ArrayList<>();
                    filters.add(new PropertyFilter("VISIT_TYPE_CODE", MatchType.EQ.getOperation(), visitType));
                    ResultVo<PageResultVo<Map<String, String>>> resultVo = hbaseQueryClient.getPageByCondition(
                            PageRequestBuilder.init()
                                    .tableName(HdrTableEnum.HDR_OUT_VISIT.getCode())
                                    .patientId(patientId)
                                    .oid(oidInCoordinates)
                                    .visitId(visitId)
                                    .visitTypeCode(visitType)
                                    .filters(filters)
                                    .pageNo(0)
                                    .pageSize(0)
                                    .orderBy("")
                                    .desc()
                                    .column( "VISIT_TIME", "VISIT_ID", "ORG_NAME", "VISIT_TYPE_CODE", "OID", "OUT_PATIENT_ID")
                                    .build());
                    if(resultVo.isSuccess()){
                        list = resultVo.getContent().getResult();
                    }
//                    List<Map<String, String>> list = hbaseDao.findConditionByPatient(HdrTableEnum.HDR_OUT_VISIT.getCode(), oidInCoordinates, patientId, filters,
//                            "VISIT_TIME", "VISIT_ID", "ORG_NAME", "VISIT_TYPE_CODE", "OID", "OUT_PATIENT_ID");

                    outVisitList.addAll(list);


                } else if (visitType.equals("02")) {
                    List<PropertyFilter> filters = new ArrayList<PropertyFilter>();
                    List<Map<String, String>> list = new ArrayList<>();
                    filters.add(new PropertyFilter("VISIT_TYPE_CODE", MatchType.EQ.getOperation(), visitType));
                    ResultVo<PageResultVo<Map<String, String>>> resultVo = hbaseQueryClient.getPageByCondition(
                            PageRequestBuilder.init()
                                    .tableName(HdrTableEnum.HDR_PAT_ADT.getCode())
                                    .patientId(patientId)
                                    .oid(oidInCoordinates)
                                    .visitId(visitId)
                                    .visitTypeCode(visitType)
                                    .filters(filters)
                                    .pageNo(0)
                                    .pageSize(0)
                                    .orderBy("")
                                    .desc()
                                    .column( "ADMISSION_TIME", "VISIT_ID", "ORG_NAME", "VISIT_TYPE_CODE", "OID", "IN_PATIENT_ID", "VISIT_TYPE_CODE")
                                    .build());
                    if(resultVo.isSuccess()){
                        list = resultVo.getContent().getResult();
                    }
//                    List<Map<String, String>> list = hbaseDao.findConditionByPatient(HdrTableEnum.HDR_PAT_ADT.getCode(), oidInCoordinates,
//                            patientId, filters, "ADMISSION_TIME", "VISIT_ID", "ORG_NAME", "VISIT_TYPE_CODE", "OID", "IN_PATIENT_ID", "VISIT_TYPE_CODE");

                    inpList.addAll(list);

                }
            }

        }

        /*查询分类病历*/
        for (Map<String, String> map : outVisitList) {
            String visitType = map.get("VISIT_TYPE_CODE");
            String visitId = map.get("VISIT_ID");
            String patientId = map.get("OUT_PATIENT_ID");
            String oidInCoordinates = map.get("OID");
            String orgName = map.get("ORG_NAME");
            String time = map.get("VISIT_TIME");
            List<Map<String, Object>> listByMrClassCode = getMedicalRecordListByMrClassCode(oidInCoordinates, patientId, visitId, visitType, "CREATE_DATE_TIME", "desc");
            Map<String, Object> mapTmp = new HashMap<>();
            mapTmp.put("visitId", visitId);
            mapTmp.put("visitType", visitType);
            mapTmp.put("patientId", patientId);
            mapTmp.put("oid", oidInCoordinates);
            mapTmp.put("orgName", orgName);
            mapTmp.put("time", time);
            mapTmp.put("recordList", listByMrClassCode);
            mapTmp.put("currentVisit", oidInCoordinates.equals(oidLast) && patientId.equals(lastPatientId)
                    && visitId.equals(lastVisitId) && visitType.equals(lastVisitTypeCode));

            resultList.add(mapTmp);
        }
        for (Map<String, String> map : inpList) {
            String visitType = map.get("VISIT_TYPE_CODE");
            String visitId = map.get("VISIT_ID");
            String patientId = map.get("IN_PATIENT_ID");
            String oidInCoordinates = map.get("OID");
            String orgName = map.get("ORG_NAME");
            String time = map.get("ADMISSION_TIME");
            List<Map<String, Object>> listByMrClassCode = getMedicalRecordListByMrClassCode(oidInCoordinates, patientId, visitId, visitType, "CREATE_DATE_TIME", "desc");
            Map<String, Object> mapTmp = new HashMap<>();
            mapTmp.put("visitId", visitId);
            mapTmp.put("visitType", visitType);
            mapTmp.put("patientId", patientId);
            mapTmp.put("oid", oidInCoordinates);
            mapTmp.put("orgName", orgName);
            mapTmp.put("time", time);
            mapTmp.put("recordList", listByMrClassCode);

            mapTmp.put("currentVisit", oidInCoordinates.equals(oidLast) && patientId.equals(lastPatientId)
                    && visitId.equals(lastVisitId) && visitType.equals(lastVisitTypeCode));


            resultList.add(mapTmp);
        }

        resultList.sort((Map<String, Object> t1, Map<String, Object> t2) -> {
            String s2 = StringUtils.isBlank((String) t2.get("time")) ? "" : ((String) t2.get("time"));
            String s1 = StringUtils.isBlank((String) t1.get("time")) ? "" : ((String) t1.get("time"));
            return s2.compareTo(s1);

        });


        ResultVO<List<Map<String, Object>>> resultVO = new ResultVO<>();
        resultVO.success("", resultList);
        return resultVO;
    }
}
