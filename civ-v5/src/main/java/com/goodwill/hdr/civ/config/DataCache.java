package com.goodwill.hdr.civ.config;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * <AUTHOR>
 * @Description 类描述：
 * @modify 修改记录：
 */
public class DataCache {
    private static final ConcurrentHashMap<String, Object> cache = new ConcurrentHashMap<String, Object>();


    /**
     * 设置缓存
     *
     * @param key
     * @param obj
     */
    public static void setCache(String oid, String key, Object obj) {
        cache.put(oid + "_" + key, obj);
    }

    public static Map<String, Object> getCache() {
        return cache;
    }

    /**
     * 获取缓存
     *
     * @return
     */
    public static Object getCache(String oid, String key) {
        return getCache().get(oid + "_" + key);
    }
}
