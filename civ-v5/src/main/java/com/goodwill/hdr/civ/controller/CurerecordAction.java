package com.goodwill.hdr.civ.controller;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.goodwill.hdr.civ.service.CurerecordService;
import com.goodwill.hdr.core.orm.Page;
import io.swagger.annotations.Api;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.Map;

/**
 * 治疗记录Action
 *
 * <AUTHOR>
 */
@RequestMapping("/curerecord")
@RestController
@Api(tags = "治疗记录")
public class CurerecordAction {


    private static final Logger log = LoggerFactory.getLogger(CurerecordAction.class);
    private final CurerecordService curerecordService;
    private final ObjectMapper objectMapper;

    public CurerecordAction(CurerecordService curerecordService, ObjectMapper objectMapper) {
        this.curerecordService = curerecordService;
        this.objectMapper = objectMapper;
    }


    @RequestMapping(value = "/getView", method = RequestMethod.POST)
    public Page<Map<String, String>> getView(String oid, String patientId, String visitId, String orderDir, String orderBy, int pageNo, int pageSize) {
        return curerecordService.getPageView(
                oid, patientId, visitId, orderBy, orderDir, pageNo, pageSize);
    }

    @RequestMapping(value = "/getDetails", method = RequestMethod.POST)
    public Page<Map<String, String>> getDetails(String oid, String patientId, String visitId, String orderDir, String orderBy, int pageNo, int pageSize, String orderCode) {
        Page<Map<String, String>> result = curerecordService
                .getPageViewForDetail(oid, patientId, visitId, orderCode, orderBy, orderDir,
                        pageNo, pageSize);
        return result;
    }

    /**
     * 治疗记录-血透报告Action
     *
     * <AUTHOR>
     */
    @RequestMapping(value = "/getHDReportView", method = RequestMethod.POST)
    public Page<Map<String, String>> getHDReportView(String searchStr, String outPatientId, String orderBy, String orderDir, int pageNo, int pageSize) {
        Map<String, String> queryMap;
        try {
            queryMap = objectMapper.readValue(searchStr, new TypeReference<Map<String, String>>() {
            });
        } catch (JsonProcessingException e) {
            queryMap = new HashMap<>();
            log.error("searchStr is not json format");
        }

        return curerecordService
                .getHDReportPageView(queryMap, outPatientId, orderBy, orderDir, pageNo,
                        pageSize);
    }

    @RequestMapping(value = "/getHDReportDetails", method = RequestMethod.POST)
    public Map<String, String> getHDReportDetails(String code) {
        Map<String, String> resultMap = curerecordService
                .getHDReportDetails(code);
        return resultMap;

    }
}
