package com.goodwill.hdr.civ.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.goodwill.hdr.civ.entity.PowerConfigDept;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-03-03
 */
@Mapper
public interface PowerConfigDeptMapper extends BaseMapper<PowerConfigDept> {

    List<Map<String, String>> getPowerConfigByDeptCode(@Param("oid") String oid, @Param("deptCode") String deptCode);
    List<Map<String, String>> getVipConfigByDeptCode(@Param("oid") String oid, @Param("deptCode") String deptCode);
}
