package com.goodwill.hdr.civ.utils;

import com.goodwill.hdr.civ.config.Config;

/**
 * <AUTHOR>
 * @Description 类描述：
 * @modify 修改记录：
 */
public class SolrUtils {

    public static final String CONFIG_FILE_NAME = "civ.properties";

//    public static final String patInfoCollection = Config.getConfigValue("patInfoCollection");
//
//    public static final String civListCollection = Config.getConfigValue("civListCollection");

    public static String getPatInfoCollection(String oid) {
        return Config.getConfigValue(oid, "patInfoCollection");
    }
    public static String getCivListCollection(String oid) {
        return Config.getConfigValue(oid, "civListCollection");
    }
}
