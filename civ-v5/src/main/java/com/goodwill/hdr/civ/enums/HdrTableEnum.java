package com.goodwill.hdr.civ.enums;


import com.goodwill.hdr.core.enums.EnumType;

/**
 * <AUTHOR>
 * @Description 类描述：业务数据表名枚举
 * @Date 2016年1月19日
 * @modify 修改记录：
 */
public enum HdrTableEnum implements EnumType {
    /**
     * 检验报告明细表
     */
    HDR_LAB_REPORT_DETAIL {
        @Override
        public String getCode() {
            return "HDR_LAB_REPORT_DETAIL";
        }

        @Override
        public String getLabel() {
            return "检验报告明细表";
        }
    },
    /**
     * 检验报告主表
     */
    HDR_LAB_REPORT {
        @Override
        public String getCode() {
            return "HDR_LAB_REPORT";
        }

        @Override
        public String getLabel() {
            return "检验报告表";
        }
    },
    /**
     * 检查报告表
     */
    HDR_EXAM_REPORT {
        @Override
        public String getCode() {
            return "HDR_EXAM_REPORT";
        }

        @Override
        public String getLabel() {
            return "检查报告表";
        }
    },
    /**
     * 病案首页手术表
     */
    HDR_INP_SUMMARY_OPER {
        @Override
        public String getCode() {
            return "HDR_INP_SUMMARY_OPER";
        }

        @Override
        public String getLabel() {
            return "病案首页手术表";
        }
    },
    /**
     * 病历文书表
     */
    HDR_EMR_CONTENT {
        @Override
        public String getCode() {
            return "HDR_EMR_CONTENT";
        }

        @Override
        public String getLabel() {
            return "病历文书表";
        }
    },
    /**
     * 患者信息表
     */
    HDR_PATIENT {
        @Override
        public String getCode() {
            return "HDR_PATIENT";
        }

        @Override
        public String getLabel() {
            return "患者信息表";
        }

    },
    HDR_NURSE_CONTENT {
        @Override
        public String getCode() {
            return "HDR_NURSE_CONTENT";
        }

        @Override
        public String getLabel() {
            return "护理文书数据表";
        }

    },
    /**
     * 患者入出转信息表
     */
    HDR_PAT_ADT {
        @Override
        public String getCode() {
            return "HDR_PAT_ADT";
        }

        @Override
        public String getLabel() {
            return "入出转表";
        }

    },

    /**
     * 门诊访问表
     */
    HDR_OUT_VISIT {
        @Override
        public String getCode() {
            return "HDR_OUT_VISIT";
        }

        @Override
        public String getLabel() {
            return "门诊访问表";
        }
    },
    /**
     * 电子病历诊断表
     */
    HDR_EMR_CONTENT_DIAG {
        @Override
        public String getCode() {
            return "HDR_EMR_CONTENT_DIAG";
        }

        @Override
        public String getLabel() {
            return "电子病历诊断表";
        }
    },
    /**
     * 门诊诊断表
     */
    HDR_OUT_VISIT_DIAG {
        @Override
        public String getCode() {
            return "HDR_OUT_VISIT_DIAG";
        }

        @Override
        public String getLabel() {
            return "门诊诊断表";
        }
    },
    /**
     * 住院医嘱表
     */
    HDR_IN_ORDER {
        @Override
        public String getCode() {
            return "HDR_IN_ORDER";
        }

        @Override
        public String getLabel() {
            return "住院医嘱表";
        }
    },
    /**
     * 门诊计费表（门诊医嘱信息）
     */
    HDR_OUT_CHARGE {
        @Override
        public String getCode() {
            return "HDR_OUT_CHARGE";
        }

        @Override
        public String getLabel() {
            return "门诊计费表";
        }
    },
    /**
     * 住院医嘱执行表
     */
    HDR_ORDER_EXE {
        @Override
        public String getCode() {
            return "HDR_ORDER_EXE";
        }

        @Override
        public String getLabel() {
            return "住院医嘱执行表";
        }
    },
    /**
     * 病案首页表
     */
    HDR_INP_SUMMARY {
        @Override
        public String getCode() {
            return "HDR_INP_SUMMARY";
        }

        @Override
        public String getLabel() {
            return "病案首页表";
        }

    },
    /**
     * 病案首页诊断表
     */
    HDR_INP_SUMMARY_DIAG {
        @Override
        public String getCode() {
            return "HDR_INP_SUMMARY_DIAG";
        }

        @Override
        public String getLabel() {
            return "病案首页诊断表";
        }
    },
    /**
     * 门诊医嘱表
     */
    HDR_OUT_ORDER {
        @Override
        public String getCode() {
            return "HDR_OUT_ORDER";
        }

        @Override
        public String getLabel() {
            return "门诊医嘱表";
        }
    },
    /**
     * 字典项目表
     */
    HDR_DICT_ITEM {
        @Override
        public String getCode() {
            return "HDR_DICT_ITEM_NEW";
        }

        @Override
        public String getLabel() {
            return "字典项目表";
        }
    },
    /**
     * 术前访视表
     */
    HDR_OPER_VISIT {
        @Override
        public String getCode() {
            return "HDR_OPER_VISIT";
        }

        @Override
        public String getLabel() {
            return "术前访视表";
        }
    },
    /**
     * 手术过程表
     */
    HDR_OPER_ANAES {
        @Override
        public String getCode() {
            return "HDR_OPER_ANAES";
        }

        @Override
        public String getLabel() {
            return "手术过程表";
        }
    },

    /**
     * 手术恢复表
     */
    HDR_OPER_RECOVERY {
        @Override
        public String getCode() {
            return "HDR_OPER_RECOVERY";
        }

        @Override
        public String getLabel() {
            return "手术恢复表";
        }
    },
    /**
     * 术后访视表
     */
    HDR_OPER_AFTER {
        @Override
        public String getCode() {
            return "HDR_OPER_AFTER";
        }

        @Override
        public String getLabel() {
            return "术后访视表";
        }
    },
    /**
     * 术后镇痛表
     */
    HDR_OPER_ANALGESIC {
        @Override
        public String getCode() {
            return "HDR_OPER_ANALGESIC";
        }

        @Override
        public String getLabel() {
            return "术后镇痛表";
        }
    },
    /**
     * 手术申请表
     */
    HDR_OPER_APPLY {
        @Override
        public String getCode() {
            return "HDR_OPER_APPLY";
        }

        @Override
        public String getLabel() {
            return "手术申请表";
        }
    },
    /**
     * 手术用药
     */
    HDR_OPER_PHARMACY {
        @Override
        public String getCode() {
            return "HDR_OPER_PHARMACY";
        }

        @Override
        public String getLabel() {
            return "手术用药表";
        }
    },
    /**
     * 住院医技申请表
     */
    HDR_IN_APPLY {
        @Override
        public String getCode() {
            return "HDR_IN_APPLY";
        }

        @Override
        public String getLabel() {
            return "住院医技申请表";
        }
    },
    /**
     * 门诊医技申请表
     */
    HDR_OUT_APPLY {
        @Override
        public String getCode() {
            return "HDR_OUT_APPLY";
        }

        @Override
        public String getLabel() {
            return "门诊医技申请表";
        }
    },
    /**
     * 危急值表
     */
    HDR_CRITICAL_VALUES {
        @Override
        public String getCode() {
            return "HDR_CRITICAL_VALUES";
        }

        @Override
        public String getLabel() {
            return "危急值表";
        }
    },
    /**
     * 生命体征表
     */
    HDR_VITAL_MEASURE {
        @Override
        public String getCode() {
            return "HDR_VITAL_MEASURE";
        }

        @Override
        public String getLabel() {
            return "生命体征";
        }
    },
    /**
     * 出入量统计表
     */
    HDR_CARE_IN_OUTST {
        @Override
        public String getCode() {
            return "HDR_CARE_IN_OUTST";
        }

        @Override
        public String getLabel() {
            return "出入量统计";
        }
    },
    /**
     * 出入量统计表
     */
    HDR_IN_CHARGE {
        @Override
        public String getCode() {
            return "HDR_IN_CHARGE";
        }

        @Override
        public String getLabel() {
            return "费用明细";
        }
    },
    /**
     * 体检报告信息表
     */
    HDR_MEDICAL_REPORT_INFO {
        @Override
        public String getCode() {
            return "HDR_MEDICAL_REPORT_INFO";
        }

        @Override
        public String getLabel() {
            return "体检报告信息表";
        }
    },
    /**
     * 体检建议附加表
     */
    HDR_MEDICAL_REPORT_INFO_NEXT {
        @Override
        public String getCode() {
            return "HDR_MEDICAL_REPORT_INFO_NEXT";
        }

        @Override
        public String getLabel() {
            return "体检建议附加表";
        }
    },
    /**
     * 体检结果明细表
     */
    HDR_MEDICAL_RESULT_DETAIL {
        @Override
        public String getCode() {
            return "HDR_MEDICAL_RESULT_DETAIL";
        }

        @Override
        public String getLabel() {
            return "体检结果明细表";
        }
    },
    HDR_MEDICAL_APPLY_MASTER {
        @Override
        public String getCode() {
            return "HDR_MEDICAL_APPLY_MASTER";
        }

        @Override
        public String getLabel() {
            return "体检申请主表";
        }
    },

    /**
     * 出入量统计表
     */
    HDR_BLOU {
        @Override
        public String getCode() {
            return "HDR_BLOU";
        }

        @Override
        public String getLabel() {
            return "输血记录表";
        }
    },
    HDR_BLOU_PATROL {
        @Override
        public String getCode() {
            return "HDR_BLOU_PATROL";
        }

        @Override
        public String getLabel() {
            return "输血巡视记录";
        }
    },
    HDR_BLOU_MATCH {
        @Override
        public String getCode() {
            return "HDR_BLOU_MATCH";
        }

        @Override
        public String getLabel() {
            return "";
        }
    },
    HDR_BLOOD_APPLY {
        @Override
        public String getCode() {
            return "HDR_BLOOD_APPLY";
        }

        @Override
        public String getLabel() {
            return "用血申请";
        }
    },
    HDR_PREPARE_BLOOD {
        @Override
        public String getCode() {
            return "HDR_PREPARE_BLOOD";
        }

        @Override
        public String getLabel() {
            return "配血记录";
        }
    },
    HDR_TWFUN_CUREMST {
        @Override
        public String getCode() {
            return "HDR_TWFUN_CUREMST";
        }

        @Override
        public String getLabel() {
            return "";
        }
    },
    HDR_TWFUN_CURESUB {
        @Override
        public String getCode() {
            return "HDR_TWFUN_CURESUB";
        }

        @Override
        public String getLabel() {
            return "";
        }
    },
    HDR_HEMODIALYSIS {
        @Override
        public String getCode() {
            return "HDR_HEMODIALYSIS";
        }

        @Override
        public String getLabel() {
            return "血透报告表";
        }
    },
    HDR_UL_MEASURE {
        @Override
        public String getCode() {
            return "HDR_UL_MEASURE";
        }

        @Override
        public String getLabel() {
            return "超声测量值表";
        }
    },
    JH_PATIENT_BASIC_INFORMATION_VIEW {
        @Override
        public String getCode() {
            return "JH_PATIENT_BASIC_INFORMATION_VIEW";
        }

        @Override
        public String getLabel() {
            return "门诊就诊信息表";
        }
    },
    JH_DIAGNOSTIC_INFORMATION_VIEW {
        @Override
        public String getCode() {
            return "JH_DIAGNOSTIC_INFORMATION_VIEW";
        }

        @Override
        public String getLabel() {
            return "门诊诊断表";
        }
    },
    JH_SURGICAL_INFORMATION_VIEW {
        @Override
        public String getCode() {
            return "JH_SURGICAL_INFORMATION_VIEW";
        }

        @Override
        public String getLabel() {
            return "门诊手术表";
        }
    },
    JH_COST_INFORMATION_VIEW {
        @Override
        public String getCode() {
            return "JH_COST_INFORMATION_VIEW";
        }

        @Override
        public String getLabel() {
            return "门诊费用表";
        }
    }

}
