package com.goodwill.hdr.civ.dao.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.dynamic.datasource.toolkit.DynamicDataSourceContextHolder;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.goodwill.hdr.civ.dao.OrganizationDao;
import com.goodwill.hdr.civ.entity.SecurityUser;
import com.goodwill.hdr.civ.mapper.SecurityDeptMapper;
import com.goodwill.hdr.civ.mapper.SecurityUserMapper;
import com.goodwill.hdr.civ.vo.NameAndCodeVo;
import com.goodwill.hdr.civ.vo.TreeNodeVo;
import com.goodwill.hdr.security.priority.entity.OrgCommonInformation;
import com.goodwill.hdr.security.priority.entity.SecurityCommonDept;
import com.goodwill.hdr.security.priority.mapper.OrgCommonInformationMapper;
import com.goodwill.hdr.security.utils.SecurityCommonUtil;
import org.springframework.stereotype.Repository;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Repository
@DS("security")
public class OrganizationDaoImpl implements OrganizationDao {

    private final SecurityUserMapper securityUserMapper;
    private final SecurityDeptMapper securityDeptMapper;

    private final OrgCommonInformationMapper orgCommonInformationMapper;

    public OrganizationDaoImpl(SecurityUserMapper securityUserMapper, SecurityDeptMapper securityDeptMapper, OrgCommonInformationMapper orgCommonInformationMapper) {
        this.securityUserMapper = securityUserMapper;
        this.securityDeptMapper = securityDeptMapper;
        this.orgCommonInformationMapper = orgCommonInformationMapper;
    }








    @Override
    public Map<String, String> getLoginOrgInfo() {
        String userCode = SecurityCommonUtil.getLoginUserCode();

        QueryWrapper<SecurityUser> userQueryWrapper = new QueryWrapper<>();
        userQueryWrapper.select("pk_dept");
        userQueryWrapper.eq("usercode", userCode);
        List<Map<String, Object>> userMapList = securityUserMapper.selectMaps(userQueryWrapper);

        if (userMapList.isEmpty()) {
            throw new RuntimeException("查询不到用户：" + userCode);
        }
        String pkDept = (String) userMapList.get(0).get("pk_dept");

        QueryWrapper<SecurityCommonDept> deptQueryWrapper = new QueryWrapper<>();
        deptQueryWrapper.select("org_code");
        deptQueryWrapper.eq("pk_dept", pkDept);
        List<Map<String, Object>> deptMapList = securityDeptMapper.selectMaps(deptQueryWrapper);

        if(deptMapList.isEmpty()){
            throw new RuntimeException("查询不到用户"+userCode+"所对应的科室"+pkDept);
        }
        String orgCode =(String) deptMapList.get(0).get("org_code");

        QueryWrapper<OrgCommonInformation> orgCommonInformationQueryWrapper=new QueryWrapper<>();
        orgCommonInformationQueryWrapper.select("code","name");
        orgCommonInformationQueryWrapper.eq("code",orgCode);
        List<Map<String, Object>> orgMapList = orgCommonInformationMapper.selectMaps(orgCommonInformationQueryWrapper);
        if(orgMapList.isEmpty()){
            throw new RuntimeException("查询不到院区:"+orgCode);
        }

        Map<String,String> resultMap=new HashMap<>();
        resultMap.put("code",(String) orgMapList.get(0).get("code"));
        resultMap.put("nmae",(String) orgMapList.get(0).get("name"));
        return resultMap;
    }

    @Override
    @DS("security")
    public List<TreeNodeVo> getOrgInfoMap(List<NameAndCodeVo> parentOidList) {

        List<TreeNodeVo> result=new ArrayList<>();
        for (NameAndCodeVo cd : parentOidList) {
            TreeNodeVo node=new TreeNodeVo(cd.getName(),new ArrayList<>());
            QueryWrapper<OrgCommonInformation> orgCommonInformationQueryWrapper=new QueryWrapper<>();
            orgCommonInformationQueryWrapper.eq("parent_code", cd.getCode());
            List<OrgCommonInformation> orgList = orgCommonInformationMapper.selectList(orgCommonInformationQueryWrapper);
            List<NameAndCodeVo> list = orgList.stream().map(orgCommonInformation -> new NameAndCodeVo(orgCommonInformation.getName(), orgCommonInformation.getCode())).collect(Collectors.toList());
            node.setChildren(list);
            result.add(node);
        }

        return result;

    }
}
