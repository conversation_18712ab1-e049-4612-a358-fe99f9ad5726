package com.goodwill.hdr.civ.entity;

import com.baomidou.mybatisplus.annotation.TableName;

import java.io.Serializable;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2022-03-03
 */
@TableName("civ_config_type")
public class ConfigType implements Serializable {

    private static final long serialVersionUID = 1L;

    private Integer id;

    private String typeCode;

    private String typeName;

    private String typeDesc;

    private String inuse;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getTypeCode() {
        return typeCode;
    }

    public void setTypeCode(String typeCode) {
        this.typeCode = typeCode;
    }

    public String getTypeName() {
        return typeName;
    }

    public void setTypeName(String typeName) {
        this.typeName = typeName;
    }

    public String getTypeDesc() {
        return typeDesc;
    }

    public void setTypeDesc(String typeDesc) {
        this.typeDesc = typeDesc;
    }

    public String getInuse() {
        return inuse;
    }

    public void setInuse(String inuse) {
        this.inuse = inuse;
    }

    @Override
    public String toString() {
        return "ConfigType{" +
                "id=" + id +
                ", typeCode=" + typeCode +
                ", typeName=" + typeName +
                ", typeDesc=" + typeDesc +
                ", inuse=" + inuse +
                "}";
    }
}
