package com.goodwill.hdr.civ.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.goodwill.hdr.civ.entity.PowerConfig;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-03-03
 */
@Mapper
public interface PowerConfigMapper extends BaseMapper<PowerConfig> {

    List<Map<String, String>> getPowerConfigByUserCode(@Param("oid") String oid, @Param("userCode") String userCode);

    List<Map<String, String>> getVipConfigByUserCode(@Param("oid") String oid, @Param("userCode") String userCode);

    int confirmItemcodesNotNull(@Param("usercode") String usercode);

}

