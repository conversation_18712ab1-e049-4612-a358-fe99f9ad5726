server:
  port: 7003
  #  port: 8099
  servlet:
    context-path: /hdr-civ

project:
  version: V5.0
  name: hdr-civ

  #严格匹配数据源,默认false. true未匹配到指定数据源时抛异常,false使用默认数据源
spring:
  cache:
    type: none
  datasource:
    dynamic:
      strict: true
      primary: master
      datasource:
        master:
          url: ***************************************************************************************************************************
          username: root
          password: Hduap@001
          driver-class-name: com.mysql.cj.jdbc.Driver
        security:
          url: ************************************************************************************************************************************
          username: root
          password: Hduap@001
          driver-class-name: com.mysql.cj.jdbc.Driver
  main:
    allow-bean-definition-overriding: true
  redis:
    host: *************
    port: 6379
    password: Hduap@001
    database: 0
    jedis:
      pool:
        max-active: 300
        max-idle: 100
        max-wait: 1000
  # token存储方式
token:
  store-type: REDIS
  # token过期时间
  expire:
    seconds:
      access: 1200
      # accesstoken过期时间
      refresh: 3600
  ignore:
    urls: /doc.html,/loginpre/*,/config/showSuggestionImage,/technologies/*,/power/getSingleloginUser,/power/getSingleloginUserByOther,/power/loginOut,/singleLogin/getSingleloginUserByOther

artifactId:
  hdr-common-archetype

# 在此目录下的Controller都可以被扫描到
swagger:
  ctrlName: com.goodwill
  title: hdr-civ-v5
  description: hdr-civ-v5
  version: 5.0
  url: "http://www.goodwill.com/"

mybatis:
  configuration:
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl

logging:
  level:
    com.goodwill.hdr: debug

goodwill:
  hdr:
    hostAndPort: http://*************:7102/rest-server

hbaseServer:
  ipAndPort: *************:9101