<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.goodwill.hdr.civ.mapper.ConfigPublishedMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.goodwill.hdr.civ.entity.ConfigPublished">
        <result column="id" property="id"/>
        <result column="config_code" property="configCode"/>
        <result column="config_name" property="configName"/>
        <result column="config_value" property="configValue"/>
        <result column="config_desc" property="configDesc"/>
        <result column="config_type" property="configType"/>
        <result column="config_level" property="configLevel"/>
        <result column="config_index" property="configIndex"/>
        <result column="config_scope_page" property="configScopePage"/>
        <result column="group_id" property="groupId"/>
        <result column="group_name" property="groupName"/>
        <result column="group_index" property="groupIndex"/>
        <result column="create_time" property="createTime"/>
        <result column="last_update_time" property="lastUpdateTime"/>
        <result column="is_inuse" property="isInuse"/>
        <result column="must_be_configed" property="mustBeConfiged"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id
        , config_code, config_name, config_value, config_desc, config_type, config_level, config_index, config_scope_page, group_id, group_name, group_index, create_time, last_update_time, is_inuse, must_be_configed
    </sql>

</mapper>
