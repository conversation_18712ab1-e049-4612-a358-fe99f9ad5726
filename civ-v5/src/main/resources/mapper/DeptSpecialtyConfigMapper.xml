<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.goodwill.hdr.civ.mapper.DeptSpecialtyConfigMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.goodwill.hdr.civ.entity.DeptSpecialtyConfig">
        <id column="id" property="id"/>
        <result column="dept_code" property="deptCode"/>
        <result column="dept_name" property="deptName"/>
        <result column="item_code" property="itemCode"/>
        <result column="item_name" property="itemName"/>
        <result column="data_type" property="dataType"/>
        <result column="is_inuse" property="isInuse"/>
        <result column="array_index" property="arrayIndex"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id
        , dept_code, dept_name, item_code, item_name, data_type, is_inuse, array_index
    </sql>

</mapper>
