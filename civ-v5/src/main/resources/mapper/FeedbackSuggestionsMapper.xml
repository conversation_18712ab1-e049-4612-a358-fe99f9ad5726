<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.goodwill.hdr.civ.mapper.FeedbackSuggestionsMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.goodwill.hdr.civ.entity.FeedbackSuggestions">
        <id column="id" property="id"/>
        <result column="relevant_user" property="relevantUser"/>
        <result column="description" property="description"/>
        <result column="relevant_url" property="relevantUrl"/>
        <result column="relevant_url_name" property="relevantUrlName"/>
        <result column="relevant_email" property="relevantEmail"/>
        <result column="submit_time" property="submitTime"/>
        <result column="ip" property="ip"/>
        <result column="phone" property="phone"/>
        <result column="type" property="type"/>
        <result column="relevant_image" property="relevantImage"/>
        <result column="relevant_image_url" property="relevantImageUrl"/>
        <result column="file_name" property="fileName"/>
        <result column="oid" property="oid"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id
        , relevant_user, description, relevant_url, relevant_url_name, relevant_email, submit_time, ip, phone, type, relevant_image, relevant_image_url, file_name, oid
    </sql>

</mapper>
