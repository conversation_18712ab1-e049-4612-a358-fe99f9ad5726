<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.goodwill.hdr.civ.mapper.HdrEmrDgHtmlMapper">

    <resultMap id="BaseResultMap" type="com.goodwill.hdr.civ.entity.HdrEmrDgHtml">
        <id property="id" column="id" jdbcType="INTEGER"/>
        <result property="emrClassCode" column="emr_class_code" jdbcType="VARCHAR"/>
        <result property="emrClassName" column="emr_class_name" jdbcType="VARCHAR"/>
        <result property="htmlText" column="html_text" jdbcType="VARCHAR"/>
        <result property="isEnabled" column="is_enabled" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        id
        ,emr_class_code,emr_class_name,html_text,is_enabled
    </sql>
    <select id="getEmrDgHtml" resultType="java.util.Map">
        SELECT hedh.emr_class_code, hedh.emr_class_name, hedh.html_text, hed.dg_code, hed.dg_name
        FROM hdr_emr_dg_html hedh
                 left join hdr_emr_dg hed on hed.emr_class_code = hedh.emr_class_code
        WHERE hedh.emr_class_code = #{mrClassCode}
          AND hedh.is_enabled = 'Y'
    </select>
</mapper>
