<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.goodwill.hdr.civ.mapper.SpecialtyDeptMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.goodwill.hdr.civ.entity.SpecialtyDept">
        <id column="id" property="id"/>
        <result column="dept_code" property="deptCode"/>
        <result column="dept_name" property="deptName"/>
        <result column="is_inuse" property="isInuse"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id
        , dept_code, dept_name, is_inuse
    </sql>

    <!-- 查询总量 -->
    <select id="getDeptList" resultType="com.goodwill.hdr.civ.entity.SpecialtyDept">
        select * from civ_specialty_dept
        where is_inuse = 'Y'
        <if test="dept != null and dept !=''">
            and ( dept_name like "%" #{dept} "%" or dept_code like "%"#{dept}"%")
        </if>
    </select>
</mapper>
