<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.goodwill.hdr.civ.mapper.PowerSpecialtyMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.goodwill.hdr.civ.entity.PowerSpecialty">
        <id column="id" property="id"/>
        <result column="usercode" property="usercode"/>
        <result column="deptcode" property="deptcode"/>
        <result column="type" property="type"/>
        <result column="itemnames" property="itemnames"/>
        <result column="itemcodes" property="itemcodes"/>
        <result column="lastupdatetime" property="lastupdatetime"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id
        , usercode, deptcode, type, itemnames, itemcodes, lastupdatetime
    </sql>

</mapper>
