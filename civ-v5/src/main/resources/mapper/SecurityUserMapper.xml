<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.goodwill.hdr.civ.mapper.SecurityUserMapper">

    <resultMap id="BaseResultMap" type="com.goodwill.hdr.civ.entity.SecurityUser">
        <id property="pkUser" column="pk_user" jdbcType="VARCHAR"/>
        <result property="usercode" column="usercode" jdbcType="VARCHAR"/>
        <result property="username" column="username" jdbcType="VARCHAR"/>
        <result property="password" column="password" jdbcType="VARCHAR"/>
        <result property="name" column="name" jdbcType="VARCHAR"/>
        <result property="telephone" column="telephone" jdbcType="VARCHAR"/>
        <result property="mobile" column="mobile" jdbcType="VARCHAR"/>
        <result property="email" column="email" jdbcType="VARCHAR"/>
        <result property="address" column="address" jdbcType="VARCHAR"/>
        <result property="idType" column="id_type" jdbcType="VARCHAR"/>
        <result property="idNo" column="id_no" jdbcType="VARCHAR"/>
        <result property="enabled" column="enabled" jdbcType="CHAR"/>
        <result property="locked" column="locked" jdbcType="CHAR"/>
        <result property="credentialsNonExpired" column="credentials_non_expired" jdbcType="CHAR"/>
        <result property="ifAdmin" column="if_admin" jdbcType="CHAR"/>
        <result property="ifAllDept" column="if_all_dept" jdbcType="CHAR"/>
        <result property="ifAllUser" column="if_all_user" jdbcType="CHAR"/>
        <result property="validDate" column="valid_date" jdbcType="VARCHAR"/>
        <result property="expiryDate" column="expiry_date" jdbcType="VARCHAR"/>
        <result property="note" column="note" jdbcType="VARCHAR"/>
        <result property="hosOid" column="hos_oid" jdbcType="VARCHAR"/>
        <result property="wxuserid" column="wxuserid" jdbcType="VARCHAR"/>
        <result property="title" column="title" jdbcType="VARCHAR"/>
        <result property="sysOid" column="sys_oid" jdbcType="VARCHAR"/>
        <result property="vipSearch" column="vip_search" jdbcType="CHAR"/>
        <result property="firstLoginFailTime" column="first_login_fail_time" jdbcType="VARCHAR"/>
        <result property="loginFailTimes" column="login_fail_times" jdbcType="INTEGER"/>
        <result property="tokenOutTime" column="token_out_time" jdbcType="INTEGER"/>
        <result property="userpositioncode" column="userpositioncode" jdbcType="VARCHAR"/>
        <result property="userpositionname" column="userpositionname" jdbcType="VARCHAR"/>
        <result property="userTypeCode" column="user_type_code" jdbcType="VARCHAR"/>
        <result property="createTime" column="create_time" jdbcType="VARCHAR"/>
        <result property="createUser" column="create_user" jdbcType="VARCHAR"/>
        <result property="modifyTime" column="modify_time" jdbcType="VARCHAR"/>
        <result property="modifyUser" column="modify_user" jdbcType="VARCHAR"/>
        <result property="domainSystemCode" column="domain_system_code" jdbcType="VARCHAR"/>
        <result property="pkDept" column="pk_dept" jdbcType="VARCHAR"/>
        <result property="deptcode" column="deptcode" jdbcType="VARCHAR"/>
        <result property="deptname" column="deptname" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        pk_user
        ,usercode,username,
        password,name,telephone,
        mobile,email,address,
        id_type,id_no,enabled,
        locked,credentials_non_expired,if_admin,
        if_all_dept,if_all_user,valid_date,
        expiry_date,note,hos_oid,
        wxuserid,title,sys_oid,
        vip_search,first_login_fail_time,login_fail_times,
        token_out_time,userpositioncode,userpositionname,
        user_type_code,create_time,create_user,
        modify_time,modify_user,domain_system_code,
        pk_dept,deptcode,deptname
    </sql>
    <select id="selectDeptcode" resultType="java.lang.String">
        SELECT deptcode
        FROM security_user
        where usercode = #{userCode}
    </select>
    <select id="getUserList" resultType="java.util.Map">
        SELECT usercode,
        username,
        deptname,
        deptcode
        FROM security_user
        where usercode != ''
        <if test="user!=null and !''.equals(user)">
            AND (username like concat('%' ,#{user},'%') OR usercode like concat('%' ,#{user},'%'))
        </if>
        <if test="dept!=null  and !'allDept'.equals(dept) and !''.equals(dept)">
            AND deptcode=#{dept}
        </if>
    </select>
    <select id="getDeptList" resultType="java.util.Map">
        select distinct deptcode, deptname
        from security_user
        where deptcode is not null
          and deptcode!=''
    </select>
</mapper>
