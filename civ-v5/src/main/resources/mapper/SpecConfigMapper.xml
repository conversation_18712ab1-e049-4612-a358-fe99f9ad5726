<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.goodwill.hdr.civ.mapper.SpecConfigMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.goodwill.hdr.civ.entity.SpecConfig">
        <id column="id" property="id"/>
        <result column="usercode" property="usercode"/>
        <result column="deptcode" property="deptcode"/>
        <result column="type" property="type"/>
        <result column="itemcode" property="itemcode"/>
        <result column="itemname" property="itemname"/>
        <result column="lastupdatetime" property="lastupdatetime"/>
        <result column="create_by" property="createBy"/>
        <result column="create_time" property="createTime"/>
        <result column="modify_by" property="modifyBy"/>
        <result column="modify_time" property="modifyTime"/>
        <result column="modify_note" property="modifyNote"/>
        <result column="data_source" property="dataSource"/>
        <result column="data_status" property="dataStatus"/>
        <result column="name_one" property="nameOne"/>
        <result column="name_abrev" property="nameAbrev"/>
        <result column="name_full" property="nameFull"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id
        , usercode, deptcode, type, itemcode, itemname, lastupdatetime, create_by, create_time, modify_by, modify_time, modify_note, data_source, data_status, name_one, name_abrev, name_full
    </sql>

</mapper>
