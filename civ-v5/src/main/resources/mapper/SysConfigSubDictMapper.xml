<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.goodwill.hdr.civ.mapper.SysConfigSubDictMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.goodwill.hdr.civ.entity.SysConfigSubDict">
        <id column="id" property="id"/>
        <result column="filed_name" property="filedName"/>
        <result column="filed_code" property="filedCode"/>
        <result column="is_add" property="isAdd"/>
        <result column="oid" property="oid"/>
    </resultMap>


    <select id="getSysSubDictList" resultType="java.util.Map">
        SELECT filed_name,
        filed_code,
        is_add,
        oid
        FROM civ_sub_filed
        where is_add = '0'
        <if test="keyWord!=null and !''.equals(keyWord)">
            AND (filed_name like concat('%' ,#{keyWord},'%') OR filed_code like concat('%' ,#{keyWord},'%'))
        </if>
    </select>
</mapper>
