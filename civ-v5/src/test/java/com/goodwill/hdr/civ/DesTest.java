package com.goodwill.hdr.civ;


import cn.hutool.crypto.symmetric.DES;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.goodwill.hdr.civ.vo.YXPT_ConfigVo;
import org.apache.commons.codec.binary.Base64;
import org.apache.commons.lang3.StringUtils;
import org.junit.jupiter.api.Test;

import javax.crypto.Cipher;
import javax.crypto.SecretKey;
import javax.crypto.SecretKeyFactory;
import javax.crypto.spec.DESKeySpec;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.security.InvalidKeyException;
import java.security.Key;
import java.security.NoSuchAlgorithmException;
import java.security.SecureRandom;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class DesTest {

    public static final String KEY_ALGORITHM = "DES";

    /**
     * 算法名称/加密模式/填充方式//DES共有四种工作模式-->>ECB：电子密码本模式、CBC：加密分组链接模式、CFB：加密反馈模式、OFB：输出反馈模式
     * DES/ECB/NoPadding
     */
    public static final String CIPHER_ALGORITHM = "DES/ECB/NoPadding";

    /**
     * 编码格式
     */
    public static final String CODE_TYPE = "GBK";

    /**
     * NoPadding
     * API或算法本身不对数据进行处理，加密数据由加密双方约定填补算法。例如若对字符串数据进行加解密，可以补充\0或者空格，然后trim
     * PKCS5Padding
     * 加密前：数据字节长度对8取余，余数为m，若m>0,则补足8-m个字节，字节数值为8-m，即差几个字节就补几个字节，字节数值即为补充的字节数，若为0则补充8个字节的8
     * 生成密钥key对象
     *
     * @param keyStr 密钥字符串
     * @return 密钥对象
     * @throws InvalidKeyException
     * @throws NoSuchAlgorithmException
     * @throws Exception
     */
    private SecretKey keyGenerator(String keyStr) throws Exception {
        DESKeySpec desKey = new DESKeySpec(keyStr.getBytes());//创建一个密匙工厂，然后用它把DESKeySpec转换成
        SecretKeyFactory keyFactory = SecretKeyFactory.getInstance(KEY_ALGORITHM);
        SecretKey securekey = keyFactory.generateSecret(desKey);
        return securekey;
    }

    //字节数组转换十六进制字符串
    public String hexStringView(byte[] bytes) {
        StringBuffer sb = new StringBuffer(bytes.length);
        String sTmp;
        for (int i = 0; i < bytes.length; i++) {
            sTmp = Integer.toHexString(0xFF & bytes[i]);
            if (sTmp.length() < 2) {
                sb.append(0);
            }
            sb.append(sTmp.toUpperCase()).append(" ");
        }
        System.out.println("16进制:" + sb.toString());
        return sb.toString();
    }

    /**
     * 加密数据
     *
     * @param data 待加密数据
     * @param key  密钥
     * @return 加密后的数据
     */
    public String encrypt(String data, String key) throws Exception {
        Key deskey = keyGenerator(key);
        //实例化Cipher对象，它用于完成实际的加密操作
        Cipher cipher = Cipher.getInstance(CIPHER_ALGORITHM);
        SecureRandom random = new SecureRandom();
        //IvParameterSpec random = new IvParameterSpec(key.getBytes());
        //初始化Cipher对象，设置为加密模式
        cipher.init(Cipher.ENCRYPT_MODE, deskey, random);
        //加密字节
        byte[] bytes = data.getBytes(CODE_TYPE);
        int len = bytes.length;
        if (len % 8 != 0) {
            byte[] temp = new byte[len + (8 - len % 8)];
            for (int i = 0; i < len; i++) {
                temp[i] = bytes[i];
            }
            bytes = temp;
        }
        //加密字节16进制查看
        //hexStringView(bytes);
        byte[] results = cipher.doFinal(bytes);
        //执行加密操作。加密后的结果通常都会用Base64编码进行传输
        return org.apache.commons.codec.binary.Base64.encodeBase64String(results);

    }

    /**
     * 解密数据
     *
     * @param data 待解密数据
     * @param key  密钥
     * @return 解密后的数据
     */
    public String decrypt(String data, String key) throws Exception {
        Key deskey = keyGenerator(key);
        Cipher cipher = Cipher.getInstance(CIPHER_ALGORITHM);
        //初始化Cipher对象，设置为解密模式
        cipher.init(Cipher.DECRYPT_MODE, deskey);
        //解密字节-执行解密操作
        byte[] bytes = cipher.doFinal(org.apache.commons.codec.binary.Base64.decodeBase64(data));

        //解密字节16进制查看
        //hexStringView(bytes);
        String result = new String(bytes, CODE_TYPE);
        result = result.replaceAll("\0", "");
        return result;

    }

    @Test
    public void test1() throws Exception {

        String doctorId = "512922196204296469";

        String encryptDoctorId = encrypt(doctorId, "GMYY2305");

        System.out.println("encrypt~doctorId = " + encryptDoctorId);

        System.out.println("decrypt~doctorId = " + decrypt(encryptDoctorId, "GMYY2305"));

    }

    @Test
    public void XJYK_DesEncryptTest() throws Exception {
        String json = "{\"moduleName\":\"影像平台\",\"linkType\":\"iframe\",\"desKey\":\"GMYY2305\",\"url\":\"http://**************/holographic/connect/ssoLogin?DoctorID=#{userCode}&DoctorName=#{userName}&orgCode=#{oid}&idcardNo=#{idCardNo}\",\"paramMap\":{\"idCardNo\":\"ID_CARD_NO\"},\"queryList\":[{\"tableName\":\"HDR_PATIENT\",\"conditionList\":[],\"columnList\":[\"ID_CARD_NO\"]}]}";
        ObjectMapper objectMapper = new ObjectMapper();
        YXPT_ConfigVo yxptConfigVo = objectMapper.readValue(json, YXPT_ConfigVo.class);
        System.out.println("yxptConfigVo = " + yxptConfigVo);
        String keyStr = yxptConfigVo.getDesKey();
        String plaintext = "512922196204296469";
        DES des = new DES(keyStr.getBytes());
        DESKeySpec desKey = new DESKeySpec(keyStr.getBytes());//创建一个密匙工厂，然后用它把DESKeySpec转换成
        SecretKeyFactory keyFactory = SecretKeyFactory.getInstance("DES");
        Key deskey = keyFactory.generateSecret(desKey);
        //实例化Cipher对象，它用于完成实际的加密操作
        Cipher cipher = Cipher.getInstance("DES/ECB/NoPadding");
        SecureRandom random = new SecureRandom();
        //IvParameterSpec random = new IvParameterSpec(key.getBytes());
        //初始化Cipher对象，设置为加密模式
        cipher.init(Cipher.ENCRYPT_MODE, deskey, random);
        //加密字节
        byte[] bytes = plaintext.getBytes("GBK");
        int len = bytes.length;
        if (len % 8 != 0) {
            byte[] temp = new byte[len + (8 - len % 8)];
            for (int i = 0; i < len; i++) {
                temp[i] = bytes[i];
            }
            bytes = temp;
        }
        //加密字节16进制查看
        //hexStringView(bytes);
        byte[] results = cipher.doFinal(bytes);
        //执行加密操作。加密后的结果通常都会用Base64编码进行传输
        String base64String = Base64.encodeBase64String(results);
        System.out.println("base64String = " + base64String);

    }

    @Test
    public void XJYK_DesDecrypt() throws Exception {

        ObjectMapper objectMapper = new ObjectMapper();

        String configJson = "{\"moduleName\":\"影像平台\",\"linkType\":\"Iframe\",\"desKey\":\"GMYY2305\",\"doctorID\":\"test\",\"doctorName\":\"test\",\"url\":\"http://**************/holographic/connect/ssoLogin?DoctorID=#{userCode}&DoctorName=#{userName}&orgCode=#{oid}&idcardNo=#{idCardNo}\",\"paramMap\":{\"ID_CARD_NO\":\"idCardNo\"},\"queryList\":[{\"tableName\":\"HDR_EXAM_REPORT\",\"conditionList\":[],\"columnList\":[\"ID_CARD_NO\"]}]}";
        String oid = "123456";
        YXPT_ConfigVo yxptConfigVo = null;
        try {
            yxptConfigVo = objectMapper.readValue(configJson, YXPT_ConfigVo.class);
        } catch (JsonProcessingException e) {
            System.out.println("configJson转map异常:"+e);
        }
        if (yxptConfigVo != null) {
            String moduleName = yxptConfigVo.getModuleName();
            String desKey = yxptConfigVo.getDesKey();
            String url = yxptConfigVo.getUrl();
            List<YXPT_ConfigVo.queryHbaseConfig> queryList = yxptConfigVo.getQueryList();
            Map<String, String> paramMap = yxptConfigVo.getParamMap();
            Map<String, String> paramValueMap = new HashMap<>();

//                UserEntity loginUser = SecurityCommonUtil.getCurrentLoginUser();
//                String usercode = loginUser.getUsercode();
            String usercode = yxptConfigVo.getDoctorId();
            try {
                usercode = XJYK_DesEncrypt(desKey, usercode);
            } catch (Exception e) {
                System.out.println("usercode加密异常");
            }


//                String username = loginUser.getUsername();
            String username = yxptConfigVo.getDoctorName();
            try {
                username = XJYK_DesEncrypt(desKey, username);
            } catch (Exception e) {
                System.out.println("username加密异常");
            }

            String encryptOid = "";
            try {
                encryptOid = XJYK_DesEncrypt(desKey, oid);
            } catch (Exception e) {
                System.out.println("oid加密异常");
            }

            paramValueMap.put("oid", encryptOid);
            paramValueMap.put("userCode", usercode);
            paramValueMap.put("userName", username);


            Map<String, String> tmpResultMap = new HashMap<>();
            tmpResultMap.put("ID_CARD_NO","123456");
            for (Map.Entry<String, String> entry : tmpResultMap.entrySet()) {
                String key = entry.getKey();
                String paramName = paramMap.get(key);
                if (StringUtils.isBlank(paramName)) {
                    continue;
                }

                String value = entry.getValue();
                try {
                    value = XJYK_DesEncrypt(desKey, value);
                } catch (Exception e) {
                    continue;
                }

                paramValueMap.put(paramName, value);

            }


            for (Map.Entry<String, String> entry : paramValueMap.entrySet()) {
                String paramName = entry.getKey();
                String value = entry.getValue();
                url = url.replace("#{" + paramName + "}", value);
            }
        }

    }

    private String XJYK_DesEncrypt(String keyStr, String plaintext) throws Exception {

        DESKeySpec desKey = new DESKeySpec(keyStr.getBytes());//创建一个密匙工厂，然后用它把DESKeySpec转换成
        SecretKeyFactory keyFactory = SecretKeyFactory.getInstance("DES");
        Key deskey = keyFactory.generateSecret(desKey);
        //实例化Cipher对象，它用于完成实际的加密操作
        Cipher cipher = Cipher.getInstance("DES/ECB/NoPadding");
        SecureRandom random = new SecureRandom();
        //IvParameterSpec random = new IvParameterSpec(key.getBytes());
        //初始化Cipher对象，设置为加密模式
        cipher.init(Cipher.ENCRYPT_MODE, deskey, random);
        //加密字节
        byte[] bytes = plaintext.getBytes("GBK");
        int len = bytes.length;
        if (len % 8 != 0) {
            byte[] temp = new byte[len + (8 - len % 8)];
            for (int i = 0; i < len; i++) {
                temp[i] = bytes[i];
            }
            bytes = temp;
        }
        //加密字节16进制查看
        //hexStringView(bytes);
        byte[] results = cipher.doFinal(bytes);
        //执行加密操作。加密后的结果通常都会用Base64编码进行传输
        String base64String = Base64.encodeBase64String(results);
        return URLEncoder.encode(base64String, StandardCharsets.UTF_8.toString());

    }


}