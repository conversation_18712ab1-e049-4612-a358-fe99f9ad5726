<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AugmentWebviewStateStore">
    <option name="stateMap">
      <map>
        <entry key="CHAT_STATE" value="{&quot;currentConversationId&quot;:&quot;7d39e6fb-da83-4cb5-8568-3276309d18d9&quot;,&quot;conversations&quot;:{&quot;da6d1838-f9b7-45de-a381-b817bd6ed779&quot;:{&quot;id&quot;:&quot;da6d1838-f9b7-45de-a381-b817bd6ed779&quot;,&quot;createdAtIso&quot;:&quot;2025-07-28T08:52:18.686Z&quot;,&quot;lastInteractedAtIso&quot;:&quot;2025-07-28T08:52:18.686Z&quot;,&quot;chatHistory&quot;:[],&quot;feedbackStates&quot;:{},&quot;toolUseStates&quot;:{},&quot;draftExchange&quot;:{&quot;request_message&quot;:&quot;&quot;,&quot;rich_text_json_repr&quot;:{&quot;type&quot;:&quot;doc&quot;,&quot;content&quot;:[{&quot;type&quot;:&quot;paragraph&quot;}]},&quot;mentioned_items&quot;:[],&quot;status&quot;:&quot;draft&quot;},&quot;requestIds&quot;:[],&quot;isPinned&quot;:false,&quot;isShareable&quot;:false,&quot;extraData&quot;:{&quot;hasDirtyEdits&quot;:false},&quot;personaType&quot;:0,&quot;rootTaskUuid&quot;:&quot;983cf4cc-a4f5-4a7d-b0b6-a037ba72b101&quot;},&quot;7d39e6fb-da83-4cb5-8568-3276309d18d9&quot;:{&quot;id&quot;:&quot;7d39e6fb-da83-4cb5-8568-3276309d18d9&quot;,&quot;createdAtIso&quot;:&quot;2025-07-28T08:52:18.759Z&quot;,&quot;lastInteractedAtIso&quot;:&quot;2025-07-28T08:52:22.300Z&quot;,&quot;chatHistory&quot;:[{&quot;status&quot;:&quot;success&quot;,&quot;request_id&quot;:&quot;eccf9249-dbad-45d8-8576-f9d7faf4b4b4&quot;,&quot;request_message&quot;:&quot;/Users/<USER>/Library/Java/JavaVirtualMachines/corretto-1.8.0_432/Contents/Home/bin/java -Dmaven.multiModuleProjectDirectory=/Users/<USER>/jhProject/civ-5.2/civ-v5 -Djansi.passthrough=true -Dmaven.home=/usr/local/apache-maven-3.6.3 -Dclassworlds.conf=/usr/local/apache-maven-3.6.3/bin/m2.conf -Dmaven.ext.class.path=/Applications/IntelliJ IDEA.app/Contents/plugins/maven/lib/maven-event-listener.jar -javaagent:/Applications/IntelliJ IDEA.app/Contents/lib/idea_rt.jar=60443 -Dfile.encoding=UTF-8 -classpath /usr/local/apache-maven-3.6.3/boot/plexus-classworlds.license:/usr/local/apache-maven-3.6.3/boot/plexus-classworlds-2.6.0.jar org.codehaus.classworlds.Launcher -Didea.version=2025.1.3 -s /usr/local/apache-maven-3.6.3/conf/settings-ptyfzc2.xml -Dmaven.repo.local=/usr/local/apache-maven-3.6.3/repo clean\n[INFO] Scanning for projects...\n[ERROR] [ERROR] Some problems were encountered while processing the POMs:\n[FATAL] Non-resolvable parent POM for com.goodwill.hdr:civv5-rest:5.0.2-SNAPHSHOT: Failure to find com.goodwill.hdr:hdr-common:pom:1.0.45-SNAPSHOT in http://cloudnative-nexus.bjgoodwill.com:8081/repository/ptyfzc-maven/ was cached in the local repository, resolution will not be reattempted until the update interval of ptyfzc-maven has elapsed or updates are forced and 'parent.relativePath' points at wrong local POM @ com.goodwill.hdr:civv5-rest:5.0.2-SNAPHSHOT, /Users/<USER>/jhProject/civ-5.2/pom.xml, line 11, column 13\n @ \n[ERROR] The build could not read 1 project -&gt; [Help 1]\n[ERROR]   \n[ERROR]   The project com.goodwill.hdr:civ-v5:5.2.8.31-SNAPHSHOT (/Users/<USER>/jhProject/civ-5.2/civ-v5/pom.xml) has 1 error\n[ERROR]     Non-resolvable parent POM for com.goodwill.hdr:civv5-rest:5.0.2-SNAPHSHOT: Failure to find com.goodwill.hdr:hdr-common:pom:1.0.45-SNAPSHOT in http://cloudnative-nexus.bjgoodwill.com:8081/repository/ptyfzc-maven/ was cached in the local repository, resolution will not be reattempted until the update interval of ptyfzc-maven has elapsed or updates are forced and 'parent.relativePath' points at wrong local POM @ com.goodwill.hdr:civv5-rest:5.0.2-SNAPHSHOT, /Users/<USER>/jhProject/civ-5.2/pom.xml, line 11, column 13 -&gt; [Help 2]\n[ERROR] \n[ERROR] To see the full stack trace of the errors, re-run Maven with the -e switch.\n[ERROR] Re-run Maven using the -X switch to enable full debug logging.\n[ERROR] \n[ERROR] For more information about the errors and possible solutions, please read the following articles:\n[ERROR] [Help 1] http://cwiki.apache.org/confluence/display/MAVEN/ProjectBuildingException\n[ERROR] [Help 2] http://cwiki.apache.org/confluence/display/MAVEN/UnresolvableModelException\n\nProcess finished with exit code 1\n&quot;,&quot;rich_text_json_repr&quot;:{&quot;type&quot;:&quot;doc&quot;,&quot;content&quot;:[{&quot;type&quot;:&quot;paragraph&quot;,&quot;content&quot;:[{&quot;type&quot;:&quot;text&quot;,&quot;text&quot;:&quot;/Users/<USER>/Library/Java/JavaVirtualMachines/corretto-1.8.0_432/Contents/Home/bin/java -Dmaven.multiModuleProjectDirectory=/Users/<USER>/jhProject/civ-5.2/civ-v5 -Djansi.passthrough=true -Dmaven.home=/usr/local/apache-maven-3.6.3 -Dclassworlds.conf=/usr/local/apache-maven-3.6.3/bin/m2.conf -Dmaven.ext.class.path=/Applications/IntelliJ IDEA.app/Contents/plugins/maven/lib/maven-event-listener.jar -javaagent:/Applications/IntelliJ IDEA.app/Contents/lib/idea_rt.jar=60443 -Dfile.encoding=UTF-8 -classpath /usr/local/apache-maven-3.6.3/boot/plexus-classworlds.license:/usr/local/apache-maven-3.6.3/boot/plexus-classworlds-2.6.0.jar org.codehaus.classworlds.Launcher -Didea.version=2025.1.3 -s /usr/local/apache-maven-3.6.3/conf/settings-ptyfzc2.xml -Dmaven.repo.local=/usr/local/apache-maven-3.6.3/repo clean&quot;},{&quot;type&quot;:&quot;hardBreak&quot;},{&quot;type&quot;:&quot;text&quot;,&quot;text&quot;:&quot;[INFO] Scanning for projects...&quot;},{&quot;type&quot;:&quot;hardBreak&quot;},{&quot;type&quot;:&quot;text&quot;,&quot;text&quot;:&quot;[ERROR] [ERROR] Some problems were encountered while processing the POMs:&quot;},{&quot;type&quot;:&quot;hardBreak&quot;},{&quot;type&quot;:&quot;text&quot;,&quot;text&quot;:&quot;[FATAL] Non-resolvable parent POM for com.goodwill.hdr:civv5-rest:5.0.2-SNAPHSHOT: Failure to find com.goodwill.hdr:hdr-common:pom:1.0.45-SNAPSHOT in http://cloudnative-nexus.bjgoodwill.com:8081/repository/ptyfzc-maven/ was cached in the local repository, resolution will not be reattempted until the update interval of ptyfzc-maven has elapsed or updates are forced and 'parent.relativePath' points at wrong local POM @ com.goodwill.hdr:civv5-rest:5.0.2-SNAPHSHOT, /Users/<USER>/jhProject/civ-5.2/pom.xml, line 11, column 13&quot;},{&quot;type&quot;:&quot;hardBreak&quot;},{&quot;type&quot;:&quot;text&quot;,&quot;text&quot;:&quot; @ &quot;},{&quot;type&quot;:&quot;hardBreak&quot;},{&quot;type&quot;:&quot;text&quot;,&quot;text&quot;:&quot;[ERROR] The build could not read 1 project -&gt; [Help 1]&quot;},{&quot;type&quot;:&quot;hardBreak&quot;},{&quot;type&quot;:&quot;text&quot;,&quot;text&quot;:&quot;[ERROR]   &quot;},{&quot;type&quot;:&quot;hardBreak&quot;},{&quot;type&quot;:&quot;text&quot;,&quot;text&quot;:&quot;[ERROR]   The project com.goodwill.hdr:civ-v5:5.2.8.31-SNAPHSHOT (/Users/<USER>/jhProject/civ-5.2/civ-v5/pom.xml) has 1 error&quot;},{&quot;type&quot;:&quot;hardBreak&quot;},{&quot;type&quot;:&quot;text&quot;,&quot;text&quot;:&quot;[ERROR]     Non-resolvable parent POM for com.goodwill.hdr:civv5-rest:5.0.2-SNAPHSHOT: Failure to find com.goodwill.hdr:hdr-common:pom:1.0.45-SNAPSHOT in http://cloudnative-nexus.bjgoodwill.com:8081/repository/ptyfzc-maven/ was cached in the local repository, resolution will not be reattempted until the update interval of ptyfzc-maven has elapsed or updates are forced and 'parent.relativePath' points at wrong local POM @ com.goodwill.hdr:civv5-rest:5.0.2-SNAPHSHOT, /Users/<USER>/jhProject/civ-5.2/pom.xml, line 11, column 13 -&gt; [Help 2]&quot;},{&quot;type&quot;:&quot;hardBreak&quot;},{&quot;type&quot;:&quot;text&quot;,&quot;text&quot;:&quot;[ERROR] &quot;},{&quot;type&quot;:&quot;hardBreak&quot;},{&quot;type&quot;:&quot;text&quot;,&quot;text&quot;:&quot;[ERROR] To see the full stack trace of the errors, re-run Maven with the -e switch.&quot;},{&quot;type&quot;:&quot;hardBreak&quot;},{&quot;type&quot;:&quot;text&quot;,&quot;text&quot;:&quot;[ERROR] Re-run Maven using the -X switch to enable full debug logging.&quot;},{&quot;type&quot;:&quot;hardBreak&quot;},{&quot;type&quot;:&quot;text&quot;,&quot;text&quot;:&quot;[ERROR] &quot;},{&quot;type&quot;:&quot;hardBreak&quot;},{&quot;type&quot;:&quot;text&quot;,&quot;text&quot;:&quot;[ERROR] For more information about the errors and possible solutions, please read the following articles:&quot;},{&quot;type&quot;:&quot;hardBreak&quot;},{&quot;type&quot;:&quot;text&quot;,&quot;text&quot;:&quot;[ERROR] [Help 1] http://cwiki.apache.org/confluence/display/MAVEN/ProjectBuildingException&quot;},{&quot;type&quot;:&quot;hardBreak&quot;},{&quot;type&quot;:&quot;text&quot;,&quot;text&quot;:&quot;[ERROR] [Help 2] http://cwiki.apache.org/confluence/display/MAVEN/UnresolvableModelException&quot;},{&quot;type&quot;:&quot;hardBreak&quot;},{&quot;type&quot;:&quot;hardBreak&quot;},{&quot;type&quot;:&quot;text&quot;,&quot;text&quot;:&quot;Process finished with exit code 1&quot;},{&quot;type&quot;:&quot;hardBreak&quot;}]}]},&quot;mentioned_items&quot;:[],&quot;structured_output_nodes&quot;:[{&quot;id&quot;:1,&quot;type&quot;:0,&quot;content&quot;:&quot;The error shows that Maven cannot find the parent POM `com.goodwill.hdr:hdr-common:pom:1.0.45-SNAPSHOT` in the configured repository. Here are the solutions:\n\n## Quick Fix Options:\n\n### 1. Remove Parent Dependency (Fastest)\n&lt;augment_code_snippet path=\&quot;pom.xml\&quot; mode=\&quot;EDIT\&quot;&gt;\n```xml\n&lt;project xmlns=\&quot;http://maven.apache.org/POM/4.0.0\&quot; xmlns:xsi=\&quot;http://www.w3.org/2001/XMLSchema-instance\&quot;\n         xsi:schemaLocation=\&quot;http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd\&quot;&gt;\n    &lt;modelVersion&gt;4.0.0&lt;/modelVersion&gt;\n    &lt;groupId&gt;com.goodwill.hdr&lt;/groupId&gt;\n    &lt;artifactId&gt;civv5-rest&lt;/artifactId&gt;\n    &lt;version&gt;5.0.2-SNAPHSHOT&lt;/version&gt;\n    &lt;packaging&gt;pom&lt;/packaging&gt;\n    \n    &lt;modules&gt;\n        &lt;module&gt;civ-v5&lt;/module&gt;\n        &lt;module&gt;rest-client&lt;/module&gt;\n        &lt;module&gt;rest-server&lt;/module&gt;\n    &lt;/modules&gt;\n```\n&lt;/augment_code_snippet&gt;\n\n### 2. Update Repository Configuration\n&lt;augment_code_snippet path=\&quot;pom.xml\&quot; mode=\&quot;EDIT\&quot;&gt;\n```xml\n&lt;repositories&gt;\n    &lt;repository&gt;\n        &lt;id&gt;central&lt;/id&gt;\n        &lt;url&gt;https://repo1.maven.org/maven2&lt;/url&gt;\n    &lt;/repository&gt;\n    &lt;repository&gt;\n        &lt;id&gt;goodwill-nexus&lt;/id&gt;\n        &lt;url&gt;http://************:8081/nexus/repository/maven-public/&lt;/url&gt;\n    &lt;/repository&gt;\n&lt;/repositories&gt;\n```\n&lt;/augment_code_snippet&gt;\n\n### 3. Force Update Dependencies\n```bash\nmvn clean -U\n```\n\nThe `-U` flag forces Maven to update snapshots and check for newer versions.\n\n### 4. Clear Local Repository Cache\n```bash\nrm -rf ~/.m2/repository/com/goodwill/hdr/hdr-common\nmvn clean\n```\n\n**Recommendation**: Try option 1 first (removing parent dependency) as it's the quickest solution and the project appears to be self-contained.\n&quot;},{&quot;id&quot;:2,&quot;type&quot;:2,&quot;content&quot;:&quot;&quot;},{&quot;id&quot;:3,&quot;type&quot;:3,&quot;content&quot;:&quot;pom.xml:0-838\npom.xml:838-1268\npom.xml:1268-2108\npom.xml:2108-3044\npom.xml:3044-3747\npom.xml:3747-5338\npom.xml:0-768\npom.xml:768-1757\npom.xml:1757-1935\npom.xml:3003-3796\npom.xml:3796-4717\npom.xml:4717-5434\npom.xml:5434-6401\npom.xml:6401-7168\npom.xml:7168-8114\npom.xml:0-759\npom.xml:759-1807\npom.xml:2846-3682\npom.xml:3682-4613\npom.xml:5336-6303\npom.xml:6303-7070\npom.xml:7070-8017\npom.xml:0-687\npom.xml:687-1570\npom.xml:1570-2496\npom.xml:3611-4748\npom.xml:4748-6141\npom.xml:0-641\npom.xml:641-1336\npom.xml:1336-1969\npom.xml:2406-3422\npom.xml:3422-4407\nciv-v5/pom.xml:0-745\nciv-v5/pom.xml:745-1803\nciv-v5/pom.xml:1803-2883\nciv-v5/pom.xml:2883-3845\nciv-v5/pom.xml:3845-4692\ndocs/STARTUP_ISSUES_DIAGNOSIS.md:866-1686\ndocs/PROJECT_ISSUES_FIXED.md:0-764\ndocs/PROJECT_ISSUES_FIXED.md:764-1116\ndocs/PROJECT_ISSUES_FIXED.md:1726-2449\nciv-v5/src/main/resources/application.yml:0-117\nciv-v5/src/main/resources/application.yml:117-1071\nciv-v5/src/main/resources/application.yml:1071-1840\nciv-v5/src/main/java/com/goodwill/Application.java:0-755\nrest-server/pom.xml:0-501\nrest-server/pom.xml:2650-3700\nrest-client/pom.xml:0-879\nrest-client/pom.xml:1859-2638\nciv-v5/src/main/resources/licenseVerifyParam.properties:0-518\n.github/workflows/maven.yml:0-283\nmrr-api/pom.xml:3604-4753\nciv-v5/src/main/java/com/goodwill/hdr/civ/handle/GlobalExceptionHandler.java:333-976\nsrc/main/resources/application-dev.yml:1032-1868\nmrr-api/.gitignore:0-395\ndependencies.txt:0-722\n.gitignore:0-395\nmrr-api/src/main/resources/application-dev.yaml:3134-4023\nmrr-job/.gitignore:0-395\nsrc/main/resources/application-prod.yml:1022-1831\nxxl-job-admin/pom.xml:2433-3338\nmrr-es/.gitignore:0-395&quot;}],&quot;seen_state&quot;:&quot;seen&quot;,&quot;structured_request_nodes&quot;:[{&quot;id&quot;:0,&quot;type&quot;:0,&quot;text_node&quot;:{&quot;content&quot;:&quot;/Users/<USER>/Library/Java/JavaVirtualMachines/corretto-1.8.0_432/Contents/Home/bin/java -Dmaven.multiModuleProjectDirectory=/Users/<USER>/jhProject/civ-5.2/civ-v5 -Djansi.passthrough=true -Dmaven.home=/usr/local/apache-maven-3.6.3 -Dclassworlds.conf=/usr/local/apache-maven-3.6.3/bin/m2.conf -Dmaven.ext.class.path=/Applications/IntelliJ IDEA.app/Contents/plugins/maven/lib/maven-event-listener.jar -javaagent:/Applications/IntelliJ IDEA.app/Contents/lib/idea_rt.jar=60443 -Dfile.encoding=UTF-8 -classpath /usr/local/apache-maven-3.6.3/boot/plexus-classworlds.license:/usr/local/apache-maven-3.6.3/boot/plexus-classworlds-2.6.0.jar org.codehaus.classworlds.Launcher -Didea.version=2025.1.3 -s /usr/local/apache-maven-3.6.3/conf/settings-ptyfzc2.xml -Dmaven.repo.local=/usr/local/apache-maven-3.6.3/repo clean\n[INFO] Scanning for projects...\n[ERROR] [ERROR] Some problems were encountered while processing the POMs:\n[FATAL] Non-resolvable parent POM for com.goodwill.hdr:civv5-rest:5.0.2-SNAPHSHOT: Failure to find com.goodwill.hdr:hdr-common:pom:1.0.45-SNAPSHOT in http://cloudnative-nexus.bjgoodwill.com:8081/repository/ptyfzc-maven/ was cached in the local repository, resolution will not be reattempted until the update interval of ptyfzc-maven has elapsed or updates are forced and 'parent.relativePath' points at wrong local POM @ com.goodwill.hdr:civv5-rest:5.0.2-SNAPHSHOT, /Users/<USER>/jhProject/civ-5.2/pom.xml, line 11, column 13\n @ \n[ERROR] The build could not read 1 project -&gt; [Help 1]\n[ERROR]   \n[ERROR]   The project com.goodwill.hdr:civ-v5:5.2.8.31-SNAPHSHOT (/Users/<USER>/jhProject/civ-5.2/civ-v5/pom.xml) has 1 error\n[ERROR]     Non-resolvable parent POM for com.goodwill.hdr:civv5-rest:5.0.2-SNAPHSHOT: Failure to find com.goodwill.hdr:hdr-common:pom:1.0.45-SNAPSHOT in http://cloudnative-nexus.bjgoodwill.com:8081/repository/ptyfzc-maven/ was cached in the local repository, resolution will not be reattempted until the update interval of ptyfzc-maven has elapsed or updates are forced and 'parent.relativePath' points at wrong local POM @ com.goodwill.hdr:civv5-rest:5.0.2-SNAPHSHOT, /Users/<USER>/jhProject/civ-5.2/pom.xml, line 11, column 13 -&gt; [Help 2]\n[ERROR] \n[ERROR] To see the full stack trace of the errors, re-run Maven with the -e switch.\n[ERROR] Re-run Maven using the -X switch to enable full debug logging.\n[ERROR] \n[ERROR] For more information about the errors and possible solutions, please read the following articles:\n[ERROR] [Help 1] http://cwiki.apache.org/confluence/display/MAVEN/ProjectBuildingException\n[ERROR] [Help 2] http://cwiki.apache.org/confluence/display/MAVEN/UnresolvableModelException\n\nProcess finished with exit code 1\n&quot;}},{&quot;id&quot;:1,&quot;type&quot;:4,&quot;ide_state_node&quot;:{&quot;workspaceFolders&quot;:[{&quot;repositoryRoot&quot;:&quot;/Users/<USER>/jhProject/civ-5.2&quot;,&quot;folderRoot&quot;:&quot;/Users/<USER>/jhProject/civ-5.2&quot;}],&quot;workspaceFoldersUnchanged&quot;:false}}],&quot;timestamp&quot;:&quot;2025-07-28T08:52:22.307Z&quot;,&quot;response_text&quot;:&quot;The error shows that Maven cannot find the parent POM `com.goodwill.hdr:hdr-common:pom:1.0.45-SNAPSHOT` in the configured repository. Here are the solutions:\n\n## Quick Fix Options:\n\n### 1. Remove Parent Dependency (Fastest)\n````xml path=pom.xml mode=EDIT\n&lt;project xmlns=\&quot;http://maven.apache.org/POM/4.0.0\&quot; xmlns:xsi=\&quot;http://www.w3.org/2001/XMLSchema-instance\&quot;\n         xsi:schemaLocation=\&quot;http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd\&quot;&gt;\n    &lt;modelVersion&gt;4.0.0&lt;/modelVersion&gt;\n    &lt;groupId&gt;com.goodwill.hdr&lt;/groupId&gt;\n    &lt;artifactId&gt;civv5-rest&lt;/artifactId&gt;\n    &lt;version&gt;5.0.2-SNAPHSHOT&lt;/version&gt;\n    &lt;packaging&gt;pom&lt;/packaging&gt;\n    \n    &lt;modules&gt;\n        &lt;module&gt;civ-v5&lt;/module&gt;\n        &lt;module&gt;rest-client&lt;/module&gt;\n        &lt;module&gt;rest-server&lt;/module&gt;\n    &lt;/modules&gt;\n````\n\n### 2. Update Repository Configuration\n````xml path=pom.xml mode=EDIT\n&lt;repositories&gt;\n    &lt;repository&gt;\n        &lt;id&gt;central&lt;/id&gt;\n        &lt;url&gt;https://repo1.maven.org/maven2&lt;/url&gt;\n    &lt;/repository&gt;\n    &lt;repository&gt;\n        &lt;id&gt;goodwill-nexus&lt;/id&gt;\n        &lt;url&gt;http://************:8081/nexus/repository/maven-public/&lt;/url&gt;\n    &lt;/repository&gt;\n&lt;/repositories&gt;\n````\n\n### 3. Force Update Dependencies\n```bash\nmvn clean -U\n```\n\nThe `-U` flag forces Maven to update snapshots and check for newer versions.\n\n### 4. Clear Local Repository Cache\n```bash\nrm -rf ~/.m2/repository/com/goodwill/hdr/hdr-common\nmvn clean\n```\n\n**Recommendation**: Try option 1 first (removing parent dependency) as it's the quickest solution and the project appears to be self-contained.\n&quot;,&quot;workspace_file_chunks&quot;:[{&quot;char_start&quot;:&quot;0&quot;,&quot;char_end&quot;:&quot;838&quot;,&quot;blob_name&quot;:&quot;c653c2fb74fa2fb025f5f62dc150596404f53857e78e10b9dea6f011eb3995fd&quot;,&quot;file&quot;:{&quot;repoRoot&quot;:&quot;&quot;,&quot;pathName&quot;:&quot;pom.xml&quot;}},{&quot;char_start&quot;:&quot;838&quot;,&quot;char_end&quot;:&quot;1268&quot;,&quot;blob_name&quot;:&quot;c653c2fb74fa2fb025f5f62dc150596404f53857e78e10b9dea6f011eb3995fd&quot;,&quot;file&quot;:{&quot;repoRoot&quot;:&quot;&quot;,&quot;pathName&quot;:&quot;pom.xml&quot;}},{&quot;char_start&quot;:&quot;1268&quot;,&quot;char_end&quot;:&quot;2108&quot;,&quot;blob_name&quot;:&quot;c653c2fb74fa2fb025f5f62dc150596404f53857e78e10b9dea6f011eb3995fd&quot;,&quot;file&quot;:{&quot;repoRoot&quot;:&quot;&quot;,&quot;pathName&quot;:&quot;pom.xml&quot;}},{&quot;char_start&quot;:&quot;2108&quot;,&quot;char_end&quot;:&quot;3044&quot;,&quot;blob_name&quot;:&quot;c653c2fb74fa2fb025f5f62dc150596404f53857e78e10b9dea6f011eb3995fd&quot;,&quot;file&quot;:{&quot;repoRoot&quot;:&quot;&quot;,&quot;pathName&quot;:&quot;pom.xml&quot;}},{&quot;char_start&quot;:&quot;3044&quot;,&quot;char_end&quot;:&quot;3747&quot;,&quot;blob_name&quot;:&quot;c653c2fb74fa2fb025f5f62dc150596404f53857e78e10b9dea6f011eb3995fd&quot;,&quot;file&quot;:{&quot;repoRoot&quot;:&quot;&quot;,&quot;pathName&quot;:&quot;pom.xml&quot;}},{&quot;char_start&quot;:&quot;3747&quot;,&quot;char_end&quot;:&quot;5338&quot;,&quot;blob_name&quot;:&quot;c653c2fb74fa2fb025f5f62dc150596404f53857e78e10b9dea6f011eb3995fd&quot;,&quot;file&quot;:{&quot;repoRoot&quot;:&quot;&quot;,&quot;pathName&quot;:&quot;pom.xml&quot;}},{&quot;char_start&quot;:&quot;0&quot;,&quot;char_end&quot;:&quot;768&quot;,&quot;blob_name&quot;:&quot;47a79f332bd15dcc18b91dc35118d6804f7a860babee03f963fe4ed743879944&quot;,&quot;file&quot;:{&quot;repoRoot&quot;:&quot;&quot;,&quot;pathName&quot;:&quot;pom.xml&quot;}},{&quot;char_start&quot;:&quot;768&quot;,&quot;char_end&quot;:&quot;1757&quot;,&quot;blob_name&quot;:&quot;47a79f332bd15dcc18b91dc35118d6804f7a860babee03f963fe4ed743879944&quot;,&quot;file&quot;:{&quot;repoRoot&quot;:&quot;&quot;,&quot;pathName&quot;:&quot;pom.xml&quot;}},{&quot;char_start&quot;:&quot;1757&quot;,&quot;char_end&quot;:&quot;1935&quot;,&quot;blob_name&quot;:&quot;47a79f332bd15dcc18b91dc35118d6804f7a860babee03f963fe4ed743879944&quot;,&quot;file&quot;:{&quot;repoRoot&quot;:&quot;&quot;,&quot;pathName&quot;:&quot;pom.xml&quot;}},{&quot;char_start&quot;:&quot;3003&quot;,&quot;char_end&quot;:&quot;3796&quot;,&quot;blob_name&quot;:&quot;47a79f332bd15dcc18b91dc35118d6804f7a860babee03f963fe4ed743879944&quot;,&quot;file&quot;:{&quot;repoRoot&quot;:&quot;&quot;,&quot;pathName&quot;:&quot;pom.xml&quot;}},{&quot;char_start&quot;:&quot;3796&quot;,&quot;char_end&quot;:&quot;4717&quot;,&quot;blob_name&quot;:&quot;47a79f332bd15dcc18b91dc35118d6804f7a860babee03f963fe4ed743879944&quot;,&quot;file&quot;:{&quot;repoRoot&quot;:&quot;&quot;,&quot;pathName&quot;:&quot;pom.xml&quot;}},{&quot;char_start&quot;:&quot;4717&quot;,&quot;char_end&quot;:&quot;5434&quot;,&quot;blob_name&quot;:&quot;47a79f332bd15dcc18b91dc35118d6804f7a860babee03f963fe4ed743879944&quot;,&quot;file&quot;:{&quot;repoRoot&quot;:&quot;&quot;,&quot;pathName&quot;:&quot;pom.xml&quot;}},{&quot;char_start&quot;:&quot;5434&quot;,&quot;char_end&quot;:&quot;6401&quot;,&quot;blob_name&quot;:&quot;47a79f332bd15dcc18b91dc35118d6804f7a860babee03f963fe4ed743879944&quot;,&quot;file&quot;:{&quot;repoRoot&quot;:&quot;&quot;,&quot;pathName&quot;:&quot;pom.xml&quot;}},{&quot;char_start&quot;:&quot;6401&quot;,&quot;char_end&quot;:&quot;7168&quot;,&quot;blob_name&quot;:&quot;47a79f332bd15dcc18b91dc35118d6804f7a860babee03f963fe4ed743879944&quot;,&quot;file&quot;:{&quot;repoRoot&quot;:&quot;&quot;,&quot;pathName&quot;:&quot;pom.xml&quot;}},{&quot;char_start&quot;:&quot;7168&quot;,&quot;char_end&quot;:&quot;8114&quot;,&quot;blob_name&quot;:&quot;47a79f332bd15dcc18b91dc35118d6804f7a860babee03f963fe4ed743879944&quot;,&quot;file&quot;:{&quot;repoRoot&quot;:&quot;&quot;,&quot;pathName&quot;:&quot;pom.xml&quot;}},{&quot;char_start&quot;:&quot;0&quot;,&quot;char_end&quot;:&quot;759&quot;,&quot;blob_name&quot;:&quot;b5450a3c0f2cf2375aa12b17c23b6f0b440268bea0de45af52b6ae3bddc58f82&quot;,&quot;file&quot;:{&quot;repoRoot&quot;:&quot;&quot;,&quot;pathName&quot;:&quot;pom.xml&quot;}},{&quot;char_start&quot;:&quot;759&quot;,&quot;char_end&quot;:&quot;1807&quot;,&quot;blob_name&quot;:&quot;b5450a3c0f2cf2375aa12b17c23b6f0b440268bea0de45af52b6ae3bddc58f82&quot;,&quot;file&quot;:{&quot;repoRoot&quot;:&quot;&quot;,&quot;pathName&quot;:&quot;pom.xml&quot;}},{&quot;char_start&quot;:&quot;2846&quot;,&quot;char_end&quot;:&quot;3682&quot;,&quot;blob_name&quot;:&quot;b5450a3c0f2cf2375aa12b17c23b6f0b440268bea0de45af52b6ae3bddc58f82&quot;,&quot;file&quot;:{&quot;repoRoot&quot;:&quot;&quot;,&quot;pathName&quot;:&quot;pom.xml&quot;}},{&quot;char_start&quot;:&quot;3682&quot;,&quot;char_end&quot;:&quot;4613&quot;,&quot;blob_name&quot;:&quot;b5450a3c0f2cf2375aa12b17c23b6f0b440268bea0de45af52b6ae3bddc58f82&quot;,&quot;file&quot;:{&quot;repoRoot&quot;:&quot;&quot;,&quot;pathName&quot;:&quot;pom.xml&quot;}},{&quot;char_start&quot;:&quot;5336&quot;,&quot;char_end&quot;:&quot;6303&quot;,&quot;blob_name&quot;:&quot;b5450a3c0f2cf2375aa12b17c23b6f0b440268bea0de45af52b6ae3bddc58f82&quot;,&quot;file&quot;:{&quot;repoRoot&quot;:&quot;&quot;,&quot;pathName&quot;:&quot;pom.xml&quot;}},{&quot;char_start&quot;:&quot;6303&quot;,&quot;char_end&quot;:&quot;7070&quot;,&quot;blob_name&quot;:&quot;b5450a3c0f2cf2375aa12b17c23b6f0b440268bea0de45af52b6ae3bddc58f82&quot;,&quot;file&quot;:{&quot;repoRoot&quot;:&quot;&quot;,&quot;pathName&quot;:&quot;pom.xml&quot;}},{&quot;char_start&quot;:&quot;7070&quot;,&quot;char_end&quot;:&quot;8017&quot;,&quot;blob_name&quot;:&quot;b5450a3c0f2cf2375aa12b17c23b6f0b440268bea0de45af52b6ae3bddc58f82&quot;,&quot;file&quot;:{&quot;repoRoot&quot;:&quot;&quot;,&quot;pathName&quot;:&quot;pom.xml&quot;}},{&quot;char_start&quot;:&quot;0&quot;,&quot;char_end&quot;:&quot;687&quot;,&quot;blob_name&quot;:&quot;8c5f9501bc25941c5237045d1e1620c0ca6b63dc89a2a6d1318719ee04ed7cdd&quot;,&quot;file&quot;:{&quot;repoRoot&quot;:&quot;&quot;,&quot;pathName&quot;:&quot;pom.xml&quot;}},{&quot;char_start&quot;:&quot;687&quot;,&quot;char_end&quot;:&quot;1570&quot;,&quot;blob_name&quot;:&quot;8c5f9501bc25941c5237045d1e1620c0ca6b63dc89a2a6d1318719ee04ed7cdd&quot;,&quot;file&quot;:{&quot;repoRoot&quot;:&quot;&quot;,&quot;pathName&quot;:&quot;pom.xml&quot;}},{&quot;char_start&quot;:&quot;1570&quot;,&quot;char_end&quot;:&quot;2496&quot;,&quot;blob_name&quot;:&quot;8c5f9501bc25941c5237045d1e1620c0ca6b63dc89a2a6d1318719ee04ed7cdd&quot;,&quot;file&quot;:{&quot;repoRoot&quot;:&quot;&quot;,&quot;pathName&quot;:&quot;pom.xml&quot;}},{&quot;char_start&quot;:&quot;3611&quot;,&quot;char_end&quot;:&quot;4748&quot;,&quot;blob_name&quot;:&quot;8c5f9501bc25941c5237045d1e1620c0ca6b63dc89a2a6d1318719ee04ed7cdd&quot;,&quot;file&quot;:{&quot;repoRoot&quot;:&quot;&quot;,&quot;pathName&quot;:&quot;pom.xml&quot;}},{&quot;char_start&quot;:&quot;4748&quot;,&quot;char_end&quot;:&quot;6141&quot;,&quot;blob_name&quot;:&quot;8c5f9501bc25941c5237045d1e1620c0ca6b63dc89a2a6d1318719ee04ed7cdd&quot;,&quot;file&quot;:{&quot;repoRoot&quot;:&quot;&quot;,&quot;pathName&quot;:&quot;pom.xml&quot;}},{&quot;char_start&quot;:&quot;0&quot;,&quot;char_end&quot;:&quot;641&quot;,&quot;blob_name&quot;:&quot;962da7b4a8d2063e433a288960f56b9055254555725520bb02708a1cb9225582&quot;,&quot;file&quot;:{&quot;repoRoot&quot;:&quot;&quot;,&quot;pathName&quot;:&quot;pom.xml&quot;}},{&quot;char_start&quot;:&quot;641&quot;,&quot;char_end&quot;:&quot;1336&quot;,&quot;blob_name&quot;:&quot;962da7b4a8d2063e433a288960f56b9055254555725520bb02708a1cb9225582&quot;,&quot;file&quot;:{&quot;repoRoot&quot;:&quot;&quot;,&quot;pathName&quot;:&quot;pom.xml&quot;}},{&quot;char_start&quot;:&quot;1336&quot;,&quot;char_end&quot;:&quot;1969&quot;,&quot;blob_name&quot;:&quot;962da7b4a8d2063e433a288960f56b9055254555725520bb02708a1cb9225582&quot;,&quot;file&quot;:{&quot;repoRoot&quot;:&quot;&quot;,&quot;pathName&quot;:&quot;pom.xml&quot;}},{&quot;char_start&quot;:&quot;2406&quot;,&quot;char_end&quot;:&quot;3422&quot;,&quot;blob_name&quot;:&quot;962da7b4a8d2063e433a288960f56b9055254555725520bb02708a1cb9225582&quot;,&quot;file&quot;:{&quot;repoRoot&quot;:&quot;&quot;,&quot;pathName&quot;:&quot;pom.xml&quot;}},{&quot;char_start&quot;:&quot;3422&quot;,&quot;char_end&quot;:&quot;4407&quot;,&quot;blob_name&quot;:&quot;962da7b4a8d2063e433a288960f56b9055254555725520bb02708a1cb9225582&quot;,&quot;file&quot;:{&quot;repoRoot&quot;:&quot;&quot;,&quot;pathName&quot;:&quot;pom.xml&quot;}},{&quot;char_start&quot;:&quot;0&quot;,&quot;char_end&quot;:&quot;745&quot;,&quot;blob_name&quot;:&quot;27d1fc2c490e48c075682d4d109bab6a555c090390573bee0733e9f11e207ab1&quot;,&quot;file&quot;:{&quot;repoRoot&quot;:&quot;&quot;,&quot;pathName&quot;:&quot;civ-v5/pom.xml&quot;}},{&quot;char_start&quot;:&quot;745&quot;,&quot;char_end&quot;:&quot;1803&quot;,&quot;blob_name&quot;:&quot;27d1fc2c490e48c075682d4d109bab6a555c090390573bee0733e9f11e207ab1&quot;,&quot;file&quot;:{&quot;repoRoot&quot;:&quot;&quot;,&quot;pathName&quot;:&quot;civ-v5/pom.xml&quot;}},{&quot;char_start&quot;:&quot;1803&quot;,&quot;char_end&quot;:&quot;2883&quot;,&quot;blob_name&quot;:&quot;27d1fc2c490e48c075682d4d109bab6a555c090390573bee0733e9f11e207ab1&quot;,&quot;file&quot;:{&quot;repoRoot&quot;:&quot;&quot;,&quot;pathName&quot;:&quot;civ-v5/pom.xml&quot;}},{&quot;char_start&quot;:&quot;2883&quot;,&quot;char_end&quot;:&quot;3845&quot;,&quot;blob_name&quot;:&quot;27d1fc2c490e48c075682d4d109bab6a555c090390573bee0733e9f11e207ab1&quot;,&quot;file&quot;:{&quot;repoRoot&quot;:&quot;&quot;,&quot;pathName&quot;:&quot;civ-v5/pom.xml&quot;}},{&quot;char_start&quot;:&quot;3845&quot;,&quot;char_end&quot;:&quot;4692&quot;,&quot;blob_name&quot;:&quot;27d1fc2c490e48c075682d4d109bab6a555c090390573bee0733e9f11e207ab1&quot;,&quot;file&quot;:{&quot;repoRoot&quot;:&quot;&quot;,&quot;pathName&quot;:&quot;civ-v5/pom.xml&quot;}},{&quot;char_start&quot;:&quot;866&quot;,&quot;char_end&quot;:&quot;1686&quot;,&quot;blob_name&quot;:&quot;31b421612babfd2c51ff77f3a23baeb4aabb58a47a7def52430c88c51c82bb2a&quot;,&quot;file&quot;:{&quot;repoRoot&quot;:&quot;&quot;,&quot;pathName&quot;:&quot;docs/STARTUP_ISSUES_DIAGNOSIS.md&quot;}},{&quot;char_start&quot;:&quot;0&quot;,&quot;char_end&quot;:&quot;764&quot;,&quot;blob_name&quot;:&quot;068b1b2c8ebc1421d374eaf703d72ff5430d41e91a9785cc4ae8982866130f98&quot;,&quot;file&quot;:{&quot;repoRoot&quot;:&quot;&quot;,&quot;pathName&quot;:&quot;docs/PROJECT_ISSUES_FIXED.md&quot;}},{&quot;char_start&quot;:&quot;764&quot;,&quot;char_end&quot;:&quot;1116&quot;,&quot;blob_name&quot;:&quot;068b1b2c8ebc1421d374eaf703d72ff5430d41e91a9785cc4ae8982866130f98&quot;,&quot;file&quot;:{&quot;repoRoot&quot;:&quot;&quot;,&quot;pathName&quot;:&quot;docs/PROJECT_ISSUES_FIXED.md&quot;}},{&quot;char_start&quot;:&quot;1726&quot;,&quot;char_end&quot;:&quot;2449&quot;,&quot;blob_name&quot;:&quot;068b1b2c8ebc1421d374eaf703d72ff5430d41e91a9785cc4ae8982866130f98&quot;,&quot;file&quot;:{&quot;repoRoot&quot;:&quot;&quot;,&quot;pathName&quot;:&quot;docs/PROJECT_ISSUES_FIXED.md&quot;}},{&quot;char_start&quot;:&quot;0&quot;,&quot;char_end&quot;:&quot;117&quot;,&quot;blob_name&quot;:&quot;5e44ef46a95f98109894d15e11f951e5fea8a9a8fd7be574d57457e40f736640&quot;,&quot;file&quot;:{&quot;repoRoot&quot;:&quot;&quot;,&quot;pathName&quot;:&quot;civ-v5/src/main/resources/application.yml&quot;}},{&quot;char_start&quot;:&quot;117&quot;,&quot;char_end&quot;:&quot;1071&quot;,&quot;blob_name&quot;:&quot;5e44ef46a95f98109894d15e11f951e5fea8a9a8fd7be574d57457e40f736640&quot;,&quot;file&quot;:{&quot;repoRoot&quot;:&quot;&quot;,&quot;pathName&quot;:&quot;civ-v5/src/main/resources/application.yml&quot;}},{&quot;char_start&quot;:&quot;1071&quot;,&quot;char_end&quot;:&quot;1840&quot;,&quot;blob_name&quot;:&quot;5e44ef46a95f98109894d15e11f951e5fea8a9a8fd7be574d57457e40f736640&quot;,&quot;file&quot;:{&quot;repoRoot&quot;:&quot;&quot;,&quot;pathName&quot;:&quot;civ-v5/src/main/resources/application.yml&quot;}},{&quot;char_start&quot;:&quot;0&quot;,&quot;char_end&quot;:&quot;755&quot;,&quot;blob_name&quot;:&quot;0972d4b8961d462bf361fb93229bada6a4201aa59f01a66a8a3131e2837fd351&quot;,&quot;file&quot;:{&quot;repoRoot&quot;:&quot;&quot;,&quot;pathName&quot;:&quot;civ-v5/src/main/java/com/goodwill/Application.java&quot;}},{&quot;char_start&quot;:&quot;0&quot;,&quot;char_end&quot;:&quot;501&quot;,&quot;blob_name&quot;:&quot;0603c58dee0b0ab8b286bea90fe660195b027de601cd9c911566bb21ce47fd2c&quot;,&quot;file&quot;:{&quot;repoRoot&quot;:&quot;&quot;,&quot;pathName&quot;:&quot;rest-server/pom.xml&quot;}},{&quot;char_start&quot;:&quot;2650&quot;,&quot;char_end&quot;:&quot;3700&quot;,&quot;blob_name&quot;:&quot;0603c58dee0b0ab8b286bea90fe660195b027de601cd9c911566bb21ce47fd2c&quot;,&quot;file&quot;:{&quot;repoRoot&quot;:&quot;&quot;,&quot;pathName&quot;:&quot;rest-server/pom.xml&quot;}},{&quot;char_start&quot;:&quot;0&quot;,&quot;char_end&quot;:&quot;879&quot;,&quot;blob_name&quot;:&quot;749ff3f1910b14e7003ba590834daf143ce7f59ee12d25daeadcc632288e3083&quot;,&quot;file&quot;:{&quot;repoRoot&quot;:&quot;&quot;,&quot;pathName&quot;:&quot;rest-client/pom.xml&quot;}},{&quot;char_start&quot;:&quot;1859&quot;,&quot;char_end&quot;:&quot;2638&quot;,&quot;blob_name&quot;:&quot;749ff3f1910b14e7003ba590834daf143ce7f59ee12d25daeadcc632288e3083&quot;,&quot;file&quot;:{&quot;repoRoot&quot;:&quot;&quot;,&quot;pathName&quot;:&quot;rest-client/pom.xml&quot;}},{&quot;char_start&quot;:&quot;0&quot;,&quot;char_end&quot;:&quot;518&quot;,&quot;blob_name&quot;:&quot;c4a879f8dc65dcde5673200d83463e9372b5f4d53a48b877cb6c062ecbc35ed2&quot;,&quot;file&quot;:{&quot;repoRoot&quot;:&quot;&quot;,&quot;pathName&quot;:&quot;civ-v5/src/main/resources/licenseVerifyParam.properties&quot;}},{&quot;char_start&quot;:&quot;0&quot;,&quot;char_end&quot;:&quot;283&quot;,&quot;blob_name&quot;:&quot;c7b8ecaa29f63595800aaf5841dcd1c6a4c70af0b7a0d0b2630605c1c647bbbf&quot;,&quot;file&quot;:{&quot;repoRoot&quot;:&quot;&quot;,&quot;pathName&quot;:&quot;.github/workflows/maven.yml&quot;}},{&quot;char_start&quot;:&quot;3604&quot;,&quot;char_end&quot;:&quot;4753&quot;,&quot;blob_name&quot;:&quot;2883e873425044c11e5744094c213d39e001cb6dc387714f61f2ef04f09beb2a&quot;,&quot;file&quot;:{&quot;repoRoot&quot;:&quot;&quot;,&quot;pathName&quot;:&quot;mrr-api/pom.xml&quot;}},{&quot;char_start&quot;:&quot;333&quot;,&quot;char_end&quot;:&quot;976&quot;,&quot;blob_name&quot;:&quot;14af312c3694b1ef45cbab744daacaaa841dfe6b3b9a4d5aa4a712baca04891f&quot;,&quot;file&quot;:{&quot;repoRoot&quot;:&quot;&quot;,&quot;pathName&quot;:&quot;civ-v5/src/main/java/com/goodwill/hdr/civ/handle/GlobalExceptionHandler.java&quot;}},{&quot;char_start&quot;:&quot;1032&quot;,&quot;char_end&quot;:&quot;1868&quot;,&quot;blob_name&quot;:&quot;ff01b3ad0f574ae20511c26b166559f8e31a993eef6f49edb2cf5d2d506b4696&quot;,&quot;file&quot;:{&quot;repoRoot&quot;:&quot;&quot;,&quot;pathName&quot;:&quot;src/main/resources/application-dev.yml&quot;}},{&quot;char_start&quot;:&quot;0&quot;,&quot;char_end&quot;:&quot;395&quot;,&quot;blob_name&quot;:&quot;aa84d75397d666ef22bf071548cc923a3f901aa5f698444a760aeeb2808b15c8&quot;,&quot;file&quot;:{&quot;repoRoot&quot;:&quot;&quot;,&quot;pathName&quot;:&quot;mrr-api/.gitignore&quot;}},{&quot;char_start&quot;:&quot;0&quot;,&quot;char_end&quot;:&quot;722&quot;,&quot;blob_name&quot;:&quot;68076457387b266cf9db95a348ac10c74246b199e129b5c53578b00f5825f530&quot;,&quot;file&quot;:{&quot;repoRoot&quot;:&quot;&quot;,&quot;pathName&quot;:&quot;dependencies.txt&quot;}},{&quot;char_start&quot;:&quot;0&quot;,&quot;char_end&quot;:&quot;395&quot;,&quot;blob_name&quot;:&quot;f9669707415bd5bf8a4c5815a13f2420a54c9ef3d9445438e09619759adc9fa7&quot;,&quot;file&quot;:{&quot;repoRoot&quot;:&quot;&quot;,&quot;pathName&quot;:&quot;.gitignore&quot;}},{&quot;char_start&quot;:&quot;3134&quot;,&quot;char_end&quot;:&quot;4023&quot;,&quot;blob_name&quot;:&quot;24add679c33f6952c9acc7ee2c4d4fc5998839e97bb4f15558799ed2ae642889&quot;,&quot;file&quot;:{&quot;repoRoot&quot;:&quot;&quot;,&quot;pathName&quot;:&quot;mrr-api/src/main/resources/application-dev.yaml&quot;}},{&quot;char_start&quot;:&quot;0&quot;,&quot;char_end&quot;:&quot;395&quot;,&quot;blob_name&quot;:&quot;bfaa601bde61fa673801ba5ec0f6042d55fd7c200be856cd9e067ab8dfe0268e&quot;,&quot;file&quot;:{&quot;repoRoot&quot;:&quot;&quot;,&quot;pathName&quot;:&quot;mrr-job/.gitignore&quot;}},{&quot;char_start&quot;:&quot;1022&quot;,&quot;char_end&quot;:&quot;1831&quot;,&quot;blob_name&quot;:&quot;83f89d2a1eeaf58fc2fc1b7fe856ca518c94a58d0f3fd190a9280e65fbe791f8&quot;,&quot;file&quot;:{&quot;repoRoot&quot;:&quot;&quot;,&quot;pathName&quot;:&quot;src/main/resources/application-prod.yml&quot;}},{&quot;char_start&quot;:&quot;2433&quot;,&quot;char_end&quot;:&quot;3338&quot;,&quot;blob_name&quot;:&quot;a006a00314e86f53d968a715814042b98b90ea4c0cfea72a0bf78e7dd82f3eda&quot;,&quot;file&quot;:{&quot;repoRoot&quot;:&quot;&quot;,&quot;pathName&quot;:&quot;xxl-job-admin/pom.xml&quot;}},{&quot;char_start&quot;:&quot;0&quot;,&quot;char_end&quot;:&quot;395&quot;,&quot;blob_name&quot;:&quot;8f0face53824e6c4f831144e68b44c50201b7b23ba3d215d4af359a6f537df9f&quot;,&quot;file&quot;:{&quot;repoRoot&quot;:&quot;&quot;,&quot;pathName&quot;:&quot;mrr-es/.gitignore&quot;}}]}],&quot;feedbackStates&quot;:{&quot;temp-fe-39cdf42b-6212-4530-a0b7-859fe9057e3d&quot;:{&quot;selectedRating&quot;:0,&quot;feedbackNote&quot;:&quot;&quot;}},&quot;toolUseStates&quot;:{},&quot;draftExchange&quot;:{&quot;request_message&quot;:&quot;&quot;,&quot;rich_text_json_repr&quot;:{&quot;type&quot;:&quot;doc&quot;,&quot;content&quot;:[{&quot;type&quot;:&quot;paragraph&quot;}]},&quot;status&quot;:&quot;draft&quot;},&quot;requestIds&quot;:[],&quot;isPinned&quot;:false,&quot;isShareable&quot;:true,&quot;extraData&quot;:{&quot;hasDirtyEdits&quot;:false},&quot;personaType&quot;:0,&quot;rootTaskUuid&quot;:&quot;7f2e8fb1-1ffd-4b0a-8f43-fda95b3eb6d0&quot;}},&quot;agentExecutionMode&quot;:&quot;manual&quot;,&quot;isPanelCollapsed&quot;:true,&quot;displayedAnnouncements&quot;:[],&quot;sortConversationsBy&quot;:&quot;lastMessageTimestamp&quot;,&quot;sendMode&quot;:&quot;send&quot;}" />
      </map>
    </option>
  </component>
</project>