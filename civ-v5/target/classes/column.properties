#
#Fri Sep 28 16:54:13 CST 2018
OPERATION_END_DATE=operationEndDate
REQ_TYPE_CODE=reqTypeCode
ANESTHESIA_RECOVERY_NURSE_NAME=anesthesiaRecoveryNurseName
EXAM_CLASS_CODE=examClassCode
MSR_NO=msrNo
GROUP_NO=groupNo
SOURCE_PK=sourcePk
FOLLOW_INDICATOR=followIndicator
CREATOR_NAME=creatorName
ANESTHESIA_END_TIME=anesthesiaEndTime
PRACTICAL_DOCTOR_NAME=practicalDoctorName
PDF_URL_BAK=pdfUrlBak
LAB_SECTION=labSection
LAB_RESULT_VALUE=labResultValue
STAY_DISTRICT_CODE=stayDistrictCode
PHONE_NUMBER=phoneNumber
SPECIFICATION=specification
EXAM_SECTION=examSection
DOSAGE_UNIT=dosageUnit
FILE_NAME=fileName
DRUG_NAME=drugName
ORDER_CONFIRMER_NAME=orderConfirmerName
ORDER_DOCTOR_NAME=orderDoctorName
PERSON_NAME=personName
MAILING_ADDR_STREET=mailingAddrStreet
INP_DOCTOR_NAME=inpDoctorName
BEFORE_COMA_TIME=beforeComaTime
ZGNMA_MMYG=zgnmaMmyg
EXAM_FEATURE_K=examFeatureK
AMOUNT_VALUE=amountValue
ZGNMA_JWL=zgnmaJwl
CIRCULATE_GRADE=circulateGrade
ZXJM_DG=zxjmDg
OPER_END_TIME=operEndTime
DELETE_FLAG=deleteFlag
FILE_NO=fileNo
EXAM_METHOD_CODE=examMethodCode
NATIVE_PROVINCE_NAME=nativeProvinceName
MR_CODE=mrCode
ENHANCE_FLAG=enhanceFlag
PLAN_INSTRUMENT_NURSE_NAME=planInstrumentNurseName
DEPT_ADMISSION_TO_CODE=deptAdmissionToCode
SENIOR_DOCTOR_NAME=seniorDoctorName
HOUSEMAN_ID=housemanId
WARD_DISCHARGE_FROM_NAME=wardDischargeFromName
IN_CP_DATE=inCpDate
EXAM_SUB_CLASS_NAME=examSubClassName
SURGICAL_POSITION_NAME=surgicalPositionName
MSG=msg
MR_CLASS=mrClass
SURGERY_SITE_NAME=surgerySiteName
BLOOD_TYPE_NAME=bloodTypeName
RIS_CHECK_OPER_NAME=risCheckOperName
PLAN_EXAM_TIME=planExamTime
FILE_FLAG_CODE=fileFlagCode
CHARGE_STATUS_NAME=chargeStatusName
FOLLOW_INTERVAL_UNITS=followIntervalUnits
REPORT_DOCTOR_NAME=reportDoctorName
PRE_OPER_ELEC_NA=preOperElecNa
BABY_ADMIN_WEIGHT=babyAdminWeight
ANES_IMG_3=anesImg3
ANES_IMG_2=anesImg2
OUT_PATIENT_ID=outPatientId
PRESC_DEPT_NAME=prescDeptName
ANESTHESIA_DOCTOR=anesthesiaDoctor
ANES_IMG_1=anesImg1
EXCLUDE_TYPE_NAME=excludeTypeName
PLAN_SECOND_ASSISTANT_NAME=planSecondAssistantName
REQ_TYPE_NAME=reqTypeName
ALLERGEN=allergen
REG_VISIT_SECTION_CODE=regVisitSectionCode
PRE_OPER_OTHER_EXAM=preOperOtherExam
REPORT_CONFIRMER_CODE=reportConfirmerCode
RIS_CHECK_TIME=risCheckTime
PRE_OPER_ELEC_MG=preOperElecMg
EXAM_CLASS_NAME=examClassName
EXECSQN=execsqn
EXAM_DEVICE_NAME=examDeviceName
TOTAL_DOSAGE_UNIT=totalDosageUnit
BABY_AGE=babyAge
BILL_ITEM_CODE=billItemCode
APPROVE_TIME=approveTime
ORDER_END_TIME=orderEndTime
SAMPLE_NURSE_CODE=sampleNurseCode
PAIN_ACCESS_DEGREE=painAccessDegree
STAY_DISTRICT_NAME=stayDistrictName
MR_CONTENT_HTML_K=mrContentHtmlK
REGISTER_BY_CODE=registerByCode
CREATOR_DEPT_CODE=creatorDeptCode
APPLY_STATUS=applyStatus
TIME_EXTUBATION=timeExtubation
CP_STATUS_CODE=cpStatusCode
PERFORMED_DOCTOR_NAME=performedDoctorName
PLAN_OPER_ROOM_NAME=planOperRoomName
BASEINFO_MAZUISHI=baseinfoMazuishi
MR_CONTENT_TEXT=mrContentText
OUT_OPER_T_VALUE=outOperTValue
PACS_URL_ORI=pacsUrlOri
FAMILY_ADDR_PROVINCE_NAME=familyAddrProvinceName
INFECT_INDICATOR=infectIndicator
DIAGNOSIS_DESC=diagnosisDesc
CAPTION_DATE_TIME=captionDateTime
EXAM_METHOD_NAME=examMethodName
FREQUENCY_DESC=frequencyDesc
TEMPLET_ID=templetId
RECORD_PERSON=recordPerson
MR_CLASS_CODE=mrClassCode
OPERATION_GRADE_CODE=operationGradeCode
EXAM_DIAG_K=examDiagK
DEPT_ADMISSION_TO_NAME=deptAdmissionToName
PHOTOCOPY=photocopy
RECOVERY_SCORE=recoveryScore
TIME_WAKE=timeWake
HEALING_GRADE_CODE=healingGradeCode
MARITAL_STATUS_CODE=maritalStatusCode
PHARMACY_START_TIME=pharmacyStartTime
LAB_DEVICESN=labDevicesn
IN_COMA_TIME=inComaTime
FILE_FLAG_NAME=fileFlagName
MAILING_ADDRESS=mailingAddress
FILE_VISIT_TYPE_CODE=fileVisitTypeCode
DURATION=duration
PHARMACY_PURPOSE_CODE=pharmacyPurposeCode
REG_VISIT_SECTION_NAME=regVisitSectionName
REPORT_CONFIRMER_NAME=reportConfirmerName
INFUSION_TRSFU_AMOUNT_VALUE=infusionTrsfuAmountValue
APPROVE_PERSON_NAME=approvePersonName
RELATIONSHIP_CODE=relationshipCode
APPLY_DOCTOR_CODE=applyDoctorCode
SXL=sxl
BILL_ITEM_NAME=billItemName
QT_XTZT=qtXtzt
SAMPLE_NURSE_NAME=sampleNurseName
OPER_DURATION=operDuration
PLAN_ANESTHESIOLOGIST_CODE=planAnesthesiologistCode
HEIGHT_UNIT=heightUnit
PATIENT_ID=patientId
NEXT_OF_KIN_PHONE=nextOfKinPhone
BIRTH_ADDR_PROVINCE_NAME=birthAddrProvinceName
DIAGNOSIS_NUM=diagnosisNum
REGISTER_BY_NAME=registerByName
PRE_OPER_SLID=preOperSlid
TOPIC=topic
PAGE_NO=pageNo
AGE_VALUE=ageValue
HEIGHT=height
CP_NO=cpNo
OUT_OPER_DEPT_TIME=outOperDeptTime
DRUG_AMOUNT_VALUE=drugAmountValue
EXAM_PART_CODE=examPartCode
IS_THIRTY_BACK_OPER=isThirtyBackOper
DIAGNOSIS_TIME=diagnosisTime
EDIT_DISABLE=editDisable
LAST_MODIFY_USER_CODE=lastModifyUserCode
VISIT_TYPE_CODE=visitTypeCode
PATIENT_GO=patientGo
TS=ts
LAB_DEVICENAME=labDevicename
OPERATION_GRADE_NAME=operationGradeName
MR_CLASS_NAME=mrClassName
EMPLOYER_ADDRESS=employerAddress
APPLY_PERSON_NAME=applyPersonName
AGE_UNIT=ageUnit
DRAINAGE_SITE_NAME=drainageSiteName
CHARGE_CLASS_CODE=chargeClassCode
ORDER_DEPT_CODE=orderDeptCode
HEALING_GRADE_NAME=healingGradeName
MARITAL_STATUS_NAME=maritalStatusName
RECORD_QUALITY=recordQuality
WAIT_SECTION=waitSection
TOTAL_ORDER=totalOrder
APPLY_TIME=applyTime
IN_OPER_HR_VALUE=inOperHrValue
WEIGHT_VALUE=weightValue
CHARGE_FEE39=chargeFee39
CHARGE_FEE38=chargeFee38
EID=eid
CHARGE_FEE37=chargeFee37
CHARGE_FEE36=chargeFee36
PAPER_MR_RECEIVE_OPERATOR_NAME=paperMrReceiveOperatorName
CHARGE_FEE35=chargeFee35
EXAM_ITEM_CODE=examItemCode
CHARGE_FEE34=chargeFee34
CHARGE_FEE33=chargeFee33
CHARGE_FEE32=chargeFee32
CHARGE_FEE31=chargeFee31
CHARGE_FEE30=chargeFee30
PHOTOCOPY_CODE=photocopyCode
PRES_STATUS_CODE=presStatusCode
PHARMACY_PURPOSE_NAME=pharmacyPurposeName
BLOOD_TRAN_REACT_TIMES=bloodTranReactTimes
LAST_MODIFY_USER_ID=lastModifyUserId
FAMILY_ADDR_COUNTY_NAME=familyAddrCountyName
OUT_OPER_HR_VALUE=outOperHrValue
RELATIONSHIP_NAME=relationshipName
CHARGE_FEE29=chargeFee29
CHARGE_FEE28=chargeFee28
CHARGE_FEE27=chargeFee27
PRE_OPER_ELEC_CL=preOperElecCl
CHARGE_FEE26=chargeFee26
CHARGE_FEE25=chargeFee25
CHARGE_FEE24=chargeFee24
CHARGE_FEE23=chargeFee23
APPLY_DOCTOR_NAME=applyDoctorName
CHARGE_FEE22=chargeFee22
CHARGE_FEE21=chargeFee21
CHARGE_FEE20=chargeFee20
PRE_OPER_ELEC_CA=preOperElecCa
PLAN_ANESTHESIOLOGIST_NAME=planAnesthesiologistName
AUTOPSY_INDICATOR=autopsyIndicator
CREATE_DATE_TIME=createDateTime
CHARGE_FEE19=chargeFee19
CHARGE_FEE18=chargeFee18
CHARGE_FEE17=chargeFee17
SAMPLE_METHOD_CODE=sampleMethodCode
PRE_OPER_LIVER=preOperLiver
CHARGE_FEE16=chargeFee16
PAIN_OCCUR_TIME=painOccurTime
CHARGE_FEE15=chargeFee15
DEPT_DISCHARGE_FROM_CODE=deptDischargeFromCode
CHARGE_FEE14=chargeFee14
CHARGE_FEE13=chargeFee13
CHARGE_FEE12=chargeFee12
CHARGE_FEE11=chargeFee11
CHARGE_FEE10=chargeFee10
LAB_PERFORMED_DEPT_CODE=labPerformedDeptCode
CANCEL_TIME=cancelTime
PRE_OPER_OTHER_LAB=preOperOtherLab
EXAM_PART_NAME=examPartName
PRESC_TIME=prescTime
FILE_EDIT_APPLY_PRIORITY_CODE=fileEditApplyPriorityCode
DIAG_BEFORE_OPERATION_NAME=diagBeforeOperationName
BUSINESS_PHONE_PHONE=businessPhonePhone
LAST_MODIFY_USER_NAME=lastModifyUserName
CP_ID=cpId
TIMES_NO=timesNo
ATTENDING_DOCTOR_CODE=attendingDoctorCode
ALLERGY_CATEGORY_CODE=allergyCategoryCode
QT_SHXFHM=qtShxfhm
VISIT_TYPE_NAME=visitTypeName
ANESTHESIOLOGIST_ASSISTANT=anesthesiologistAssistant
OCCUPATION_CODE=occupationCode
ACCNO=accno
OPERATOR_NURSE=operatorNurse
ZT_AFTEROP=ztAfterop
PRINT_FLAG=printFlag
WARD_DISCHARGE_FROM=wardDischargeFrom
OPER_ROOM_NAME=operRoomName
EMPLOYER_POSTCODE=employerPostcode
APPLYER_TEL=applyerTel
TOPIC_TITLE_CODE=topicTitleCode
SINGLE_DOSE_VALUE=singleDoseValue
ORDER_TIME=orderTime
RY_FLAG=ryFlag
ORDER_DEPT_NAME=orderDeptName
CHARGE_CLASS_NAME=chargeClassName
ORDER_CLASS_CODE=orderClassCode
SPEC_CONFIRM_TIME=specConfirmTime
IN_OPER_PB_VALUE=inOperPbValue
DISTRICT_DISCHARGE_FROM_CODE=districtDischargeFromCode
POSTCODE=postcode
SOURCE_OID=sourceOid
POISONING_DIAGNOSIS=poisoningDiagnosis
EMER_TREAT_TIMES=emerTreatTimes
CHARGE_CONFIRMER_NAME=chargeConfirmerName
PACS_URL=pacsUrl
EXAM_ITEM_NAME=examItemName
OUT_OPER_O2_VALUE=outOperO2Value
SPIRIT_GRADE=spiritGrade
MZ=mz
PRES_STATUS_NAME=presStatusName
TOPIC_DOCTOR_CODE=topicDoctorCode
LAB_RESULT_UNIT=labResultUnit
PACKAGE_NO=packageNo
APPLY_TYPE_CODE=applyTypeCode
PRE_OPER_HIV=preOperHiv
ALLERGY_REACTION=allergyReaction
HOUSEMAN=houseman
PRE_OPER_LUNGS=preOperLungs
CHARGE_AMOUNT_VALUE=chargeAmountValue
HERB_AMOUNT_VALUE=herbAmountValue
PRE_OPER_ECG=preOperEcg
EXAM=exam
MR_NURSE_PART_STATUS_CODE=mrNursePartStatusCode
REPORT_TIME=reportTime
SURGEN_CODE=surgenCode
ANESTHESIA_METHOD_CODE=anesthesiaMethodCode
PRE_OPER_HEART=preOperHeart
PRE_OPER_WT_VALUE=preOperWtValue
DISCHARGE_CLASS_CODE=dischargeClassCode
ADM_CONDITION_CODE=admConditionCode
OPERATION_CODE=operationCode
CHARGE_NO=chargeNo
IN_RECOVERY_ROOM_TIME=inRecoveryRoomTime
NEW_PAGE_FLAG=newPageFlag
SHARE_FILE=shareFile
PRINT_NEED_FINISH=printNeedFinish
EMPLOYER_COMPANY=employerCompany
ORDER_BEGIN_TIME=orderBeginTime
SAMPLE_METHOD_NAME=sampleMethodName
ORDER_ITEM_CODE=orderItemCode
IN_CP_DOC_CODE=inCpDocCode
DEPT_DISCHARGE_FROM_NAME=deptDischargeFromName
ANALGESIC_TREATMENT_TIME=analgesicTreatmentTime
OUTP_NO=outpNo
OPERATION_TYPE_CODE=operationTypeCode
LAB_TYPE_CODE=labTypeCode
LAB_PERFORMED_DEPT_NAME=labPerformedDeptName
ON_DUTY_FLAG=onDutyFlag
MR_CONTENT_CDA=mrContentCda
SAMPLE_TIME=sampleTime
PRE_OPER_ELEC_K=preOperElecK
CHARGE_TYPE_CODE=chargeTypeCode
IN_OPER_CV_VALUE=inOperCvValue
FILE_VISIT_TYPE=fileVisitType
VISIT_TYPE=visitType
MR_VALUE=mrValue
WARD_ADMISSION_TO=wardAdmissionTo
ESC_EMER_TIMES=escEmerTimes
CATALOGER_NAME=catalogerName
STAY_BED=stayBed
DRUG_ALLERGY_INDICATOR=drugAllergyIndicator
OPER_ROOM_NO=operRoomNo
CHARGE_TYPE=chargeType
ATTENDING_DOCTOR_NAME=attendingDoctorName
ALLERGY_CATEGORY_NAME=allergyCategoryName
BLOOD_TRANSFUSION_TYPE_NAME=bloodTransfusionTypeName
BLEEDING=bleeding
DETAIL_SN=detailSn
OPER_END_DATE=operEndDate
SW_IND=swInd
OCCUPATION_NAME=occupationName
MEDICAL_PAY_WAY_CODE=medicalPayWayCode
OPERATION_DATE=operationDate
ANESTHESIA_DOCTOR_CODE=anesthesiaDoctorCode
FILE_V5_FLAG=fileV5Flag
HIGH=high
MODIFIY_TOPIC=modifiyTopic
TOPIC_TITLE_NAME=topicTitleName
EXAM_NO=examNo
CHECKTYPENAME=checktypename
WRITE_USER_CODE=writeUserCode
PRE_OPER_R_VALUE=preOperRValue
MODIFIY_TOPIC_TITLE=modifiyTopicTitle
ORDER_CLASS_NAME=orderClassName
AGAIN_IN_PLAN=againInPlan
IN_OPER_T_VALUE=inOperTValue
EXAM_APPOINT_TIME=examAppointTime
BLEEDING_AMOUNT_VALUE=bleedingAmountValue
DISTRICT_DISCHARGE_FROM_NAME=districtDischargeFromName
ZGNMA_ZDTT=zgnmaZdtt
APPLY_OPER_TIME=applyOperTime
VISIT_DOCTOR_CODE=visitDoctorCode
OTHER=other
PRE_OPER_HBEAG=preOperHbeag
PRE_OPER_HBEAB=preOperHbeab
XX_NO=xxNo
TOPIC_DOCTOR_NAME=topicDoctorName
ANESTHESIA_RECOVERY_DOCTOR_NAME=anesthesiaRecoveryDoctorName
APPLY_TYPE_NAME=applyTypeName
OPDATE=opdate
BLOOD_CF=bloodCf
BED_LABEL=bedLabel
LAB_PERFORMED_TIME=labPerformedTime
MAILING_ADDR_STREET_NAME=mailingAddrStreetName
APPLY_DIAGNOSIS_CODE=applyDiagnosisCode
LAB_SUB_ITEM_CODE=labSubItemCode
PRIMARY_NURSE_NAME=primaryNurseName
ANALGESIC_PUMP_AMOUNT_VALUE=analgesicPumpAmountValue
PAIN_LOCATION_NAME=painLocationName
SURGEN_NAME=surgenName
LIVER_KIDNEY_LACK=liverKidneyLack
IN_OPER_STOMACH=inOperStomach
WARD_ADMISSION_TO_NAME=wardAdmissionToName
QT_SW=qtSw
ANESTHESIA_METHOD_NAME=anesthesiaMethodName
APPROVE_DATE=approveDate
DISCHARGE_CLASS_NAME=dischargeClassName
RANGE=range
ADM_CONDITION_NAME=admConditionName
IDENTITY_NAME=identityName
OPERATION_NAME=operationName
PHARMACY_WAY_CODE=pharmacyWayCode
VISIT_TIME=visitTime
PLAN_OPER_TIME=planOperTime
OCC_TIME=occTime
PRE_OPER_HCV=preOperHcv
NEXT_OF_KIN=nextOfKin
DISCHARGE_TIME=dischargeTime
ORDER_ITEM_NAME=orderItemName
CHARGE_TIME=chargeTime
AUTHOR_CODE=authorCode
WOUND_LEVEL_NAME=woundLevelName
OPERATION_TYPE_NAME=operationTypeName
LAB_TYPE_NAME=labTypeName
ANALGESIC_PUMP_LOCK_TIME=analgesicPumpLockTime
HOSPITAL_CODE=hospitalCode
CHARGE_TYPE_NAME=chargeTypeName
ANALGESIC_PUMP_TYPE_NAME=analgesicPumpTypeName
ANALGESIC_DOCTOR_NAME=analgesicDoctorName
IN_PATIENT_ID=inPatientId
PLAN_FIRST_ASSISTANT_CODE=planFirstAssistantCode
CATALOG_TIME=catalogTime
WRITE_DATE=writeDate
MEDICAL_PAY_WAY_NAME=medicalPayWayName
FURTHER_DOCTOR_NAME=furtherDoctorName
PLAN_OPER_ROOM_NO=planOperRoomNo
OPER_STATUS_NAME=operStatusName
CHARGE_FEE_VALUE=chargeFeeValue
ANESTHESIA_DOCTOR_NAME=anesthesiaDoctorName
PV_ID=pvId
VISIT_DEPT_CODE=visitDeptCode
WEIGHT_UNIT=weightUnit
OPER_OTHER_NOTE=operOtherNote
CALL_BACK_FLAG=callBackFlag
SEX_CODE=sexCode
DEPT_DIRECTOR_CODE=deptDirectorCode
EXAM_PERFORM_TIME=examPerformTime
ADMISSION_TIME=admissionTime
STATUS=status
PRE_OPER_SOLID=preOperSolid
VISIT_DOCTOR_NAME=visitDoctorName
BLOOD_TRANSFUSION_AMOUNT_VALUE=bloodTransfusionAmountValue
PRESC_NURSE_CODE=prescNurseCode
LAB_ITEM_CODE=labItemCode
OUT_OPER_R_VALUE=outOperRValue
VISIT_FLAG=visitFlag
ASA=asa
PATIENT_CUR_DEPT_CODE=patientCurDeptCode
BLOOD_AMOUNT_UNIT=bloodAmountUnit
WOUND_GRADE_CODE=woundGradeCode
CASE_NO=caseNo
APPLY_DIAGNOSIS_NAME=applyDiagnosisName
LAB_SUB_ITEM_NAME=labSubItemName
DIAGNOSIS_CODE=diagnosisCode
REG_CATEGORY_CODE=regCategoryCode
PRE_OPER_BP_VALUE=preOperBpValue
REGISTING_TIME=registingTime
ID_CARD_TYPE_CODE=idCardTypeCode
FREQUENCY_CODE=frequencyCode
PHARMACY_WAY_NAME=pharmacyWayName
STAY_DEPT_CODE=stayDeptCode
SPEC_CONFIRMER_NAME=specConfirmerName
AUTHOR_NAME=authorName
REPORT_STATUS=reportStatus
PATIENTS_TO=patientsTo
ALLERGY_SEVERITY=allergySeverity
FINISH_TIME=finishTime
MZ_FLAG=mzFlag
OUT_CP_DATE=outCpDate
ASA_GRADE_CODE=asaGradeCode
DEFAULT_TOPIC_VISIBLE=defaultTopicVisible
VISIT_ORDER=visitOrder
CIRCUIT_NURSE_CODE=circuitNurseCode
PLAN_FIRST_ASSISTANT_NAME=planFirstAssistantName
ZXJM_XX=zxjmXx
ORDER_TEXT=orderText
VISIT_ID=visitId
PX_TIME=pxTime
VISIT_DEPT_NAME=visitDeptName
BABY_NO=babyNo
FILE_FLAG=fileFlag
PACU_TRSFU_AMOUNT_VALUE=pacuTrsfuAmountValue
STOP_ORDER_DOCTOR_CODE=stopOrderDoctorCode
RECORD_QUALITY_NAME=recordQualityName
TREAT_RESULT_CODE=treatResultCode
PLAN_PRESC_TIME=planPrescTime
SEX_NAME=sexName
DEPT_DIRECTOR_NAME=deptDirectorName
TREAT_DAYS=treatDays
ALLERGY_REASON=allergyReason
DIAGNOSIS_TYPE_CODE=diagnosisTypeCode
REGISTING_CANCEL_TIME=registingCancelTime
QC_NURSE_NAME=qcNurseName
REPORT_NO=reportNo
EMR_CP_NAME=emrCpName
DRUG_AMOUNT_UNIT=drugAmountUnit
APPLY_DATE=applyDate
STOP_ORDER_TIME=stopOrderTime
DEFAULT_DATE_TYPE=defaultDateType
EXAM_FEATURE=examFeature
HEALTH_CARD_NO=healthCardNo
ROWKEY=rowkey
STAT_FLAG_CODE=statFlagCode
PRESC_NURSE_NAME=prescNurseName
LAB_ITEM_NAME=labItemName
REG_TYPE_CODE=regTypeCode
FIRST_MR_SIGN_DATE_TIME=firstMrSignDateTime
CONSULTING_DOCTOR_CODE=consultingDoctorCode
ANALGESIC_EVALUATE_NAME=analgesicEvaluateName
DOSAGE_TYPE_CODE=dosageTypeCode
NEXT_OF_KIN_ADDR=nextOfKinAddr
WOUND_GRADE_NAME=woundGradeName
SKIN_GRADE=skinGrade
INP_NO=inpNo
SPECIMAN_TYPE_CODE=specimanTypeCode
RESULT_STATUS_CODE=resultStatusCode
DIAGNOSIS_DOCTOR_NAME=diagnosisDoctorName
GH_DOCTOR_CODE=ghDoctorCode
PLAN_OPER_DOCTOR_CODE=planOperDoctorCode
APPLY_DEPT_CODE=applyDeptCode
BABY_BIRTH_WEIGHT=babyBirthWeight
FAMILY_ADDRESS=familyAddress
MAILING_ADDR_COUNTY_NAME=mailingAddrCountyName
DIAGNOSIS_NAME=diagnosisName
DURATION_UNIT=durationUnit
REG_CATEGORY_NAME=regCategoryName
ID_CARD_TYPE_NAME=idCardTypeName
ORDER_CONFIRM_TIME=orderConfirmTime
EXAM_DEVICE_SN=examDeviceSn
FREQUENCY_NAME=frequencyName
BREATH_GRADE=breathGrade
LAB_DIAGNOSIS_NAME=labDiagnosisName
STAY_DEPT_NAME=stayDeptName
IN_OPER_DISABILITY=inOperDisability
SHCZZT_FLAG=shczztFlag
RH_BLOOD_CODE=rhBloodCode
INFUSION_TRSFU_AMOUNT=infusionTrsfuAmount
PRE_OPER_BLOOD=preOperBlood
PRE_OPER_HBSAG=preOperHbsag
ZT_ATFEROP=ztAtferop
PRE_OPER_HBSAB=preOperHbsab
CHARGE_ITEM_PRICE_VALUE=chargeItemPriceValue
PAPER_MR_RECEIVE_NURSE_DATE=paperMrReceiveNurseDate
FILE_UNIQUE_ID=fileUniqueId
FILE_EDIT_APPLY_PRIORITY=fileEditApplyPriority
RECORDTIME=recordtime
ORDER_CODE=orderCode
EXAM_PART=examPart
ZGNMA_YMWXZ=zgnmaYmwxz
ASA_GRADE_NAME=asaGradeName
PRE_OPER_OTHERS_LAB=preOperOthersLab
ICU_DAYS=icuDays
CIRCUIT_NURSE_NAME=circuitNurseName
FILE_EDIT_APPLY_TIME_LIMIT=fileEditApplyTimeLimit
PAPER_MR_RECEIVE_OPER_DATE=paperMrReceiveOperDate
PRE_OPER_HBCAB=preOperHbcab
ADMISSION_CLASS_CODE=admissionClassCode
SQD_STATUS_CODE=sqdStatusCode
OPER_STOP_TIME=operStopTime
ZXJM_JBXZ=zxjmJbxz
STOP_ORDER_DOCTOR_NAME=stopOrderDoctorName
TREAT_RESULT_NAME=treatResultName
EXEC_NAME=execName
TEMPLET_STATE_NAME=templetStateName
VISIT_ID_T=visitIdT
MR_CONTENT=mrContent
VISIT_ID_R=visitIdR
DISTRICT_ADMISSION_TO_CODE=districtAdmissionToCode
WEEIGHT=weeight
DIAGNOSIS_TYPE_NAME=diagnosisTypeName
VISIT_ID_K=visitIdK
DURATION_VALUE=durationValue
OUT_OPER_SP02=outOperSp02
REPORT_CONFIRMER_ID=reportConfirmerId
FURTHER_DOCTOR=furtherDoctor
LOW=low
PARENT_ORDER_NO=parentOrderNo
PERFORM_DOCTOR_CODE=performDoctorCode
STAT_FLAG_NAME=statFlagName
NATION_NAME=nationName
SECOND_ASSISTANT_NAME=secondAssistantName
time_wake=timeWake
REG_TYPE_NAME=regTypeName
OPER_TRSFU_AMOUNT_VALUE=operTrsfuAmountValue
HEIGHT_VALUE=heightValue
STAY_WARD_CODE=stayWardCode
ZXJM_QX=zxjmQx
PATHOLOGY_NO=pathologyNo
DOSAGE_TYPE_NAME=dosageTypeName
SAMPLE_NO=sampleNo
FIRSTV_INDICATOR=firstvIndicator
PAPER_MR_RECEIVE_NURSE_NAME=paperMrReceiveNurseName
ANESTHETIST_DATE=anesthetistDate
ORDER_PROPERTIES_CODE=orderPropertiesCode
SPECIMAN_TYPE_NAME=specimanTypeName
GH_DOCTOR_NAME=ghDoctorName
APPLY_DEPT_NAME=applyDeptName
PLAN_OPER_DOCTOR_NAME=planOperDoctorName
FIRST_ASSISTANT_NAME=firstAssistantName
INSTRUMENT_NURSE_CODE=instrumentNurseCode
PRESC_WAY_CODE=prescWayCode
NATIONALITY_NAME=nationalityName
ORDER_NO=orderNo
ORDER_STATUS_CODE=orderStatusCode
FINISH_NURSE_NAME=finishNurseName
QC_DOCTOR_CODE=qcDoctorCode
RH_BLOOD_NAME=rhBloodName
HOSPITAL_NO=hospitalNo
OPER_NO=operNo
MODIFIY_TOPIC_DOCTOR=modifiyTopicDoctor
SING_NEED_DIAG=singNeedDiag
ARCHIVING_DATE=archivingDate
OPER_URINE_VOLUME_VALUE=operUrineVolumeValue
DIAGNOSIS_DEPT_CODE=diagnosisDeptCode
ANE_COMPLICATION=aneComplication
EXTUBATION=extubation
LAB_PERFORMED_GROUP_CODE=labPerformedGroupCode
ORDER_NAME=orderName
LAST_MODIFY_GUID=lastModifyGuid
ANESTHESIA_START_TIME=anesthesiaStartTime
PLAN_EXECUTE_TIME=planExecuteTime
ANALGESIC_PUMP_FORMULA=analgesicPumpFormula
STOP_ORDER_DEPT_CODE=stopOrderDeptCode
PRE_OPER_LUESRPR=preOperLuesrpr
AGE=age
ADD_TO_VISIT=addToVisit
ADMISSION_CLASS_NAME=admissionClassName
FX_PERSON_NAME=fxPersonName
PACU_URINE_VOLUME_VALUE=pacuUrineVolumeValue
PRE_OPER_URINE=preOperUrine
ARCHIVIST_NAME=archivistName
SQD_STATUS_NAME=sqdStatusName
DIS_PHARMACY_IND=disPharmacyInd
COUNT_CHARGE_FEE=countChargeFee
IN_OPER_POSITION=inOperPosition
DEPT_CODE=deptCode
URINEQ=urineq
EMPLET_ID=empletId
ANESTHETIST_NAME=anesthetistName
NEED_PARENT_SIGN_FLAG=needParentSignFlag
DISTRICT_ADMISSION_TO_NAME=districtAdmissionToName
REPORT_STATUS_CODE=reportStatusCode
MR_DOCTOR_PART_STATUS_CODE=mrDoctorPartStatusCode
MODIFIY_TOPIC_DOCTOR_CODE=modifiyTopicDoctorCode
FOLLOW_INTERVAL_VALUE=followIntervalValue
CREATOR_ID=creatorId
PRE_OPER_ASSESS=preOperAssess
IN_OPER_WOUND_OBVM=inOperWoundObvm
PAPER_MR_OFFSET_DAYS=paperMrOffsetDays
APPLY_NO=applyNo
PERFORM_DOCTOR_NAME=performDoctorName
CHARGE_AMOUNT_UNIT=chargeAmountUnit
TEST_NO=testNo
HERB_AMOUNT_UNIT=herbAmountUnit
IN_CP_USER_NAME=inCpUserName
CGQM_SYSY=cgqmSysy
CHARGE_OPER_CODE=chargeOperCode
MR_CONTENT_HTML=mrContentHtml
APPLY_MATTERS=applyMatters
CHARGE_CODE=chargeCode
PRE_OPER_T_VALUE=preOperTValue
TOPIC_TITLE_ID=topicTitleId
ORDER_PROPERTIES_NAME=orderPropertiesName
OPER_APPLY_NO=operApplyNo
GH_DEPT_CODE=ghDeptCode
TOPIC_UNDERLINE_TYPE=topicUnderlineType
CANCEL_DOCTOR_CODE=cancelDoctorCode
INSTRUMENT_NURSE_NAME=instrumentNurseName
PRESC_WAY_NAME=prescWayName
ORDER_STATUS_NAME=orderStatusName
CHARGE_FEE9=chargeFee9
CHARGE_FEE8=chargeFee8
QC_DOCTOR_NAME=qcDoctorName
RECEIVE_ORG=receiveOrg
CHARGE_FEE7=chargeFee7
CHARGE_FEE6=chargeFee6
CHARGE_CONFIRM_TIME=chargeConfirmTime
CHARGE_FEE5=chargeFee5
CHARGE_FEE4=chargeFee4
EFFECT=effect
CHARGE_FEE3=chargeFee3
BACKGROUND_INPUT_SPEED_VALUE=backgroundInputSpeedValue
IN_OPER_R_VALUE=inOperRValue
CHARGE_FEE2=chargeFee2
CHARGE_FEE1=chargeFee1
NARCOTIC_DRUG=narcoticDrug
BASEINFO_ZHILIAO=baseinfoZhiliao
QC_DATE=qcDate
IN_OPER_VT_VALUE=inOperVtValue
DIAGNOSIS_DEPT_NAME=diagnosisDeptName
ACTIVE_GRADE=activeGrade
OPERATE_NURSE_TIME=operateNurseTime
ORDER_NOTE=orderNote
LAB_PERFORMED_GROUP_NAME=labPerformedGroupName
VISIT_TYPE_CODE_NAME=visitTypeCodeName
DRUG_ALLERGY_NAME=drugAllergyName
MONITOR_CODE=monitorCode
PRE_OPER_ALLERGY=preOperAllergy
PRE_OPER_KIDNEY=preOperKidney
CREATOR_CODE=creatorCode
PRACTICAL_DOCTOR_CODE=practicalDoctorCode
PX_PERSON_NAME=pxPersonName
STOP_ORDER_DEPT_NAME=stopOrderDeptName
ZGNMA_YMWNZ=zgnmaYmwnz
IN_OPER_BP_VALUE=inOperBpValue
MODIFIY_TOPIC_TITLE_CODE=modifiyTopicTitleCode
EMERGENCY_VISIT_IND=emergencyVisitInd
PLAN_PRESC_NURSE_NAME=planPrescNurseName
OUT_OPER_VT_VALUE=outOperVtValue
IN_OPER_DEPT_TIME=inOperDeptTime
DRUG_ID=drugId
ZGNMA_JT=zgnmaJt
ORDER_CONFIRMER_CODE=orderConfirmerCode
CCU_DAYS=ccuDays
ANAESTHESIA_METHOD=anaesthesiaMethod
ORDER_DOCTOR_CODE=orderDoctorCode
TOPIC_UNDERLINE_TYPE_CODE=topicUnderlineTypeCode
REPORT_CONFIRM_TIME=reportConfirmTime
PRINT_NUM=printNum
INP_DOCTOR_CODE=inpDoctorCode
OPER_START_TIME=operStartTime
OUT_OPER_BP_VALUE=outOperBpValue
PRE_OPER_HR_VALUE=preOperHrValue
REPORT_STATUS_NAME=reportStatusName
ANALGESIC_TREATMENT_OPTIONS_NAME=analgesicTreatmentOptionsName
OUT_RECOVERY_ROOM_TIME=outRecoveryRoomTime
LAST_MODIFY_DATE_TIME=lastModifyDateTime
PATHOLOGY_DIAGNOSIS=pathologyDiagnosis
EXAM_DIAG=examDiag
BIRTH_ADDRESS=birthAddress
OPERATION_DESC=operationDesc
CHARGE_OPER_NAME=chargeOperName
PLAN_INSTRUMENT_NURSE_CODE=planInstrumentNurseCode
SENIOR_DOCTOR_CODE=seniorDoctorCode
EXAM_SUB_CLASS_CODE=examSubClassCode
LEDGER_SN=ledgerSn
CHARGE_NAME=chargeName
ANALGESIC_TREATMENT_RESULT_NAME=analgesicTreatmentResultName
BLOOD_TYPE_CODE=bloodTypeCode
DOSAGE_VALUE=dosageValue
GH_DEPT_NAME=ghDeptName
RIS_CHECK_OPER_CODE=risCheckOperCode
CGQM_FYQ=cgqmFyq
CANCEL_DOCTOR_NAME=cancelDoctorName
ID_CARD_NO=idCardNo
FX_TIME=fxTime
DATE_OF_BIRTH=dateOfBirth
CHARGE_STATUS_CODE=chargeStatusCode
BLOOD_AMOUNT_VALUE=bloodAmountValue
REPORT_DOCTOR_CODE=reportDoctorCode
PRESC_DEPT_CODE=prescDeptCode
GROUPSORTCOLUMN=groupSortColumn
TOPIC_DOCTOR_ID=topicDoctorId
LAB_QUAL_RESULT=labQualResult
ORIGIN_DEPT=originDept
EXCLUDE_TYPE_CODE=excludeTypeCode
PLAN_SECOND_ASSISTANT_CODE=planSecondAssistantCode
TOTAL_DOSAGE_VALUE=totalDosageValue
PACU_DRAINAGE_VOLUME_VALUE=pacuDrainageVolumeValue
PHARMACY_REASON_CODE=pharmacyReasonCode
LAB_YM_NAME=labYmName
LAB_YM_RESULT=labYmResult
MICRO_ITEM_NAME=microItemName
NR_NAME=nrName
NR_CONTENT_HTML=nrContentHtml
SPEED_RATE_UNIT=speedRateUnit
BRAND=brand
SPEED_RATE_VALUE=speedRateValue
FILE_CLASS=fileClass
URL=url
HH=HH
LL=LL
P=P
NOTE=note
OID=oid
ORG_NO=orgNo
ORG_NAME=orgName
LAB_DEVICE_NAME=labDeviceName
EXAM_DEVICE=examDevice
EXAM_GOAL=examGoal
EXAM_ATTENTION=examAttention
LAB_METHOD_NAME=labMethodName
RESULT_STATUS_NAME=resultStatusName
LAB_MICRO_GROWTH=labMicroGrowth
LAB_MICRO_COMMENT=labMicroComment
LAB_MICRO_TCT=labMicroTct
LAB_MICRO_CIR=labMicroCir
PANIC_VALUE_CODE=panicValueCode
WYHR_REPORT_NO=wyhrReportNo
LAB_MICRO_BACTERIA_CODE=labMicroBacteriacode
LAB_MICRO_BACTERIA_NAME=labMicroBacteriaName
ANESTHESIA_GRADE=anesthesiaGrade