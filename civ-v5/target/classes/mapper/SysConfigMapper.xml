<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.goodwill.hdr.civ.mapper.SysConfigMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.goodwill.hdr.civ.entity.SysConfig">
        <id column="id" property="id"/>
        <result column="configname" property="configName"/>
        <result column="configcode" property="configCode"/>
        <result column="configvalue" property="configValue"/>
        <result column="valuetype" property="valueType"/>
        <result column="valuerange" property="valueRange"/>
        <result column="configdesc" property="configDesc"/>
        <result column="sort" property="sort"/>
        <result column="lastupdatetime" property="lastUpdateTime"/>
        <result column="oid" property="oid"/>
    </resultMap>


    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id
        , configname, configcode, configvalue, valuetype, valuerange, configdesc, sort, lastupdatetime, oid
    </sql>
    <select id="selectSysConfigMap" resultType="java.util.Map">
        select configname, configcode, configvalue, configdesc, sort
        from civ_sys_config
        where oid = #{oid}
    </select>

</mapper>
