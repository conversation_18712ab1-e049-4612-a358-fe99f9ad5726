<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.goodwill.hdr.civ.mapper.SicknessMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.goodwill.hdr.civ.entity.Sickness">
        <id column="id" property="id"/>
        <result column="sickness_code" property="sicknessCode"/>
        <result column="sickness_name" property="sicknessName"/>
        <result column="is_inuse" property="isInuse"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id
        , sickness_code, sickness_name, is_inuse
    </sql>

</mapper>
