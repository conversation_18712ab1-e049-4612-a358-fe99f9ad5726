<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.goodwill.hdr.civ.mapper.SysConfigSubMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.goodwill.hdr.civ.entity.SysConfigSub">
        <id column="id" property="id"/>
        <result column="super_code" property="superCode"/>
        <result column="config_code" property="configCode"/>
        <result column="config_value" property="configValue"/>
        <result column="config_name" property="configName"/>
        <result column="orm_fileds" property="ormFileds"/>
        <result column="enabled" property="enabled"/>
        <result column="oid" property="oid"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id
        , super_code, config_code, config_value, config_name, orm_fileds, enabled, oid
    </sql>

</mapper>
