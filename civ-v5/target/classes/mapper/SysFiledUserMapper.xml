<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace=".dao.CivSysFiledUserDao">

    <resultMap type="com.goodwill.hdr.civ.entity.SysFiledUser" id="CivSysFiledUserMap">
        <result property="id" column="id" jdbcType="VARCHAR"/>
        <result property="filedCode" column="filed_code" jdbcType="VARCHAR"/>
        <result property="userCode" column="user_code" jdbcType="VARCHAR"/>
    </resultMap>

    <!--查询单个-->
    <select id="queryById" resultMap="CivSysFiledUserMap">
        select
          id, filed_code, user_code
        from civ_sys_filed_user
        where id = #{id}
    </select>

    <!--查询指定行数据-->
    <select id="queryAllByLimit" resultMap="CivSysFiledUserMap">
        select
          id, filed_code, user_code
        from civ_sys_filed_user
        <where>
            <if test="id != null and id != ''">
                and id = #{id}
            </if>
            <if test="filedCode != null and filedCode != ''">
                and filed_code = #{filedCode}
            </if>
            <if test="userCode != null and userCode != ''">
                and user_code = #{userCode}
            </if>
        </where>
        limit #{pageable.offset}, #{pageable.pageSize}
    </select>

    <!--统计总行数-->
    <select id="count" resultType="java.lang.Long">
        select count(1)
        from civ_sys_filed_user
        <where>
            <if test="id != null and id != ''">
                and id = #{id}
            </if>
            <if test="filedCode != null and filedCode != ''">
                and filed_code = #{filedCode}
            </if>
            <if test="userCode != null and userCode != ''">
                and user_code = #{userCode}
            </if>
        </where>
    </select>

    <!--新增所有列-->
    <insert id="insert" keyProperty="id" useGeneratedKeys="true">
        insert into civ_sys_filed_user(filed_code, user_code)
        values (#{filedCode}, #{userCode})
    </insert>

    <insert id="insertBatch" keyProperty="id" useGeneratedKeys="true">
        insert into civ_sys_filed_user(filed_code, user_code)
        values
        <foreach collection="entities" item="entity" separator=",">
        (#{entity.filedCode}, #{entity.userCode})
        </foreach>
    </insert>

    <insert id="insertOrUpdateBatch" keyProperty="id" useGeneratedKeys="true">
        insert into civ_sys_filed_user(filed_code, user_code)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.filedCode}, #{entity.userCode})
        </foreach>
        on duplicate key update
        filed_code = values(filed_code),
        user_code = values(user_code)
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update civ_sys_filed_user
        <set>
            <if test="filedCode != null and filedCode != ''">
                filed_code = #{filedCode},
            </if>
            <if test="userCode != null and userCode != ''">
                user_code = #{userCode},
            </if>
        </set>
        where id = #{id}
    </update>

    <!--通过主键删除-->
    <delete id="deleteById">
        delete from civ_sys_filed_user where id = #{id}
    </delete>

</mapper>

