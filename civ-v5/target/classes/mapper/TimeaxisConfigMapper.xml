<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.goodwill.hdr.civ.mapper.TimeaxisConfigMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.goodwill.hdr.civ.entity.TimeaxisConfig">
        <id column="id" property="id"/>
        <result column="usercode" property="usercode"/>
        <result column="patientid" property="patientid"/>
        <result column="visittype" property="visittype"/>
        <result column="visitid" property="visitid"/>
        <result column="itemcode" property="itemcode"/>
        <result column="itemname" property="itemname"/>
        <result column="subitemcode" property="subitemcode"/>
        <result column="subitemname" property="subitemname"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id
        , usercode, patientid, visittype, visitid, itemcode, itemname, subitemcode, subitemname
    </sql>

</mapper>
