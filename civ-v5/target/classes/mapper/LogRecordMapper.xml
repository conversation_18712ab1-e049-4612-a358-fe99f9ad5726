<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.goodwill.hdr.civ.mapper.LogRecordMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.goodwill.hdr.civ.entity.LogRecord">
        <id column="id_pk" property="idPk"/>
        <result column="deptname" property="deptname"/>
        <result column="deptcode" property="deptcode"/>
        <result column="username" property="username"/>
        <result column="usercode" property="usercode"/>
        <result column="pagename" property="pagename"/>
        <result column="pagecode" property="pagecode"/>
        <result column="accesstime" property="accesstime"/>
        <result column="ip" property="ip"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id_pk
        , deptname, deptcode, username, usercode, pagename, pagecode, accesstime, ip
    </sql>
    <select id="selectMonitorLogAccordingDept" resultType="java.util.Map">
        select sa.deptname as name, count(clr.id_pk) as num
        from (select distinct deptcode, deptname from civ_log_record where oid = #{oid}) sa
        left join (select * from civ_log_record where oid = #{oid}
        <if test="beginDate !=null and !beginDate.equals('')">
            and accesstime >= str_to_date(#{beginDate},'%Y-%m-%d')
        </if>
        <if test="endDate !=null and !endDate.equals('')">
            and accesstime &lt;= str_to_date(#{endDate},'%Y-%m-%d')
        </if>
        <if test="pageCode !=null and !pageCode.equals('')">
            and pagecode = #{pageCode}
        </if>
        ) clr on clr.deptcode = sa.deptcode
        group by sa.deptcode order by sa.deptcode desc
    </select>
    <select id="selectMonitorLogAccordingDoctor" resultType="java.util.Map">
        select su.username as name, count(clr.id_pk) as num
        from (select distinct usercode, username from civ_log_record where oid = #{oid}) su
        left join (select * from civ_log_record where oid = #{oid}
        <if test="beginDate !=null and !beginDate.equals('')">
            and accesstime >= str_to_date(#{beginDate},'%Y-%m-%d')
        </if>
        <if test="endDate !=null and !endDate.equals('')">
            and accesstime &lt;= str_to_date(#{endDate},'%Y-%m-%d')
        </if>
        <if test="value !=null and !value.equals('01') and !value.equals('')">
            and deptcode = #{value}
        </if>
        <if test="pageCode !=null and !pageCode.equals('')">
            and pagecode = #{pageCode}
        </if>
        ) clr on clr.usercode = su.usercode
        group by su.usercode order by su.usercode desc
    </select>
    <select id="selectTimeMonitorLogAccordingDeptTimeNearOneYear" resultType="java.util.Map">
        select td.tdate, ifnull(count(clr.accesstime), 0) as num
        from (select date_sub(date_add(#{dateNow}, interval -day(#{dateNow})+1 day),
        interval clrd.code_year month) AS tdate
        from civ_log_record_data clrd
        where clrd.code_year is not null and oid =#{oid}) td
        left join (select * from civ_log_record where 1 = 1 and pagecode = #{pageCode}
        <if test="!'01'.equals(deptCode)">
            and deptcode =#{deptCode}
        </if>
        ) clr on (clr.accesstime >= td.tdate and clr.accesstime &lt; date_add(td.tdate,interval 1 month) ) group by
        td.tdate
    </select>
    <select id="selectTimeMonitorLogAccordingDeptTimeNearOneMonth" resultType="java.util.Map">
        select td.tdate,ifnull(count(clr.accesstime), 0) as num from
        (select date_sub(#{dateNow}, interval clrd.code day) as tdate from civ_log_record_data clrd) td
        left join (select * from civ_log_record where 1=1 and pagecode =#{pageCode}
        <if test="!'01'.equals(deptCode)">
            and deptcode =#{deptCode}
        </if>
        ) clr on td.tdate = clr.accesstime group by td.tdate
    </select>
    <select id="selectTimeMonitorLogAccordingDoctorTimeNearOneYear" resultType="java.util.Map">
        select td.tdate, ifnull(count(clr.accesstime), 0) AS num
        from (select date_sub(#{dateNow}, interval clrd.code_year month) as tdate
        from civ_log_record_data clrd
        where clrd.code_year is not null and oid=#{oid}) td
        left join (select * from civ_log_record where oid = #{oid} and pagecode = #{pageCode}
        <if test="!'01'.equals(deptCode)">
            and deptcode =#{deptCode}
        </if>
        <if test="userCode!=null and !userCode.equals('')">
            and usercode =#{userCode}
        </if>
        ) clr on (clr.accesstime >= td.tdate and clr.accesstime &lt; date_add(tdate,interval 1 month) )
        group by td.tdate
    </select>
    <select id="selectTimeMonitorLogAccordingDoctorTimeNearOneMonth" resultType="java.util.Map">
        select td.tdate, ifnull(count(clr.accesstime), 0) as num
        from (select date_sub(#{dateNow}, interval clrd.code day) as tdate from civ_log_record_data clrd) td
        left join (select * from civ_log_record where 1 = 1 and pagecode = #{pageCode}
        <if test="!'01'.equals(deptCode)">
            and deptcode =#{deptCode}
        </if>
        <if test="userCode!=null and !userCode.equals('')">
            and usercode =#{userCode}
        </if>
        ) clr on td.tdate = clr.accesstime
        group by td.tdate
    </select>

</mapper>
