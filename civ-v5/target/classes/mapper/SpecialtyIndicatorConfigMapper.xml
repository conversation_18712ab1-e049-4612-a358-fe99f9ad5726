<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.goodwill.hdr.civ.mapper.SpecialtyIndicatorConfigMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.goodwill.hdr.civ.entity.SpecialtyIndicatorConfigEntity">
        <id column="id" property="id"/>
        <result column="doctor_code" property="doctorCode"/>
        <result column="doctor_name" property="doctorName"/>
        <result column="sickness_code" property="sicknessCode"/>
        <result column="sickness_name" property="sicknessName"/>
        <result column="item_code" property="itemCode"/>
        <result column="item_name" property="itemName"/>
        <result column="item_class_code" property="itemClassCode"/>
        <result column="item_class_name" property="itemClassName"/>
        <result column="item_indicator_code" property="itemIndicatorCode"/>
        <result column="item_indicator_name" property="itemIndicatorName"/>
        <result column="is_inuse" property="isInuse"/>
        <result column="array_index" property="arrayIndex"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id
        , doctor_code, doctor_name, sickness_code, sickness_name, item_code, item_name, item_class_code, item_class_name, item_indicator_code, item_indicator_name, is_inuse, array_index
    </sql>

</mapper>
