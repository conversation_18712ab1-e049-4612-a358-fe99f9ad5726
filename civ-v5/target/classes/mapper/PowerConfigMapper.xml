<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.goodwill.hdr.civ.mapper.PowerConfigMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.goodwill.hdr.civ.entity.PowerConfig">
        <id column="id" property="id"/>
        <result column="usercode" property="usercode"/>
        <result column="deptcode" property="deptcode"/>
        <result column="type" property="type"/>
        <result column="itemcodes" property="itemcodes"/>
        <result column="lastupdatetime" property="lastupdatetime"/>
        <result column="oid" property="oid"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id
        , usercode, deptcode, type, itemcodes, lastupdatetime, oid
    </sql>
    <select id="getPowerConfigByUserCode" resultType="java.util.Map">
        select usercode, deptcode, type, itemcodes
        from civ_power_config
        where oid = #{oid}
          and usercode = #{userCode}
    </select>
    <select id="getVipConfigByUserCode" resultType="java.util.Map">
        select usercode, deptcode, type, itemcodes
        from civ_power_config
        where oid = #{oid}
          and usercode = #{userCode}
          and type = 'VIP'
    </select>
    <select id="confirmItemcodesNotNull" resultType="int">
        select (CASE when count(itemcodes is not null) > 0 then 1 else 0 end)
        from civ_power_config
        where usercode = #{usercode}
    </select>

</mapper>
