<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.goodwill.hdr.civ.mapper.CmrReportTimeDurationMapper">

    <resultMap id="BaseResultMap" type="com.goodwill.hdr.civ.entity.CmrReportTimeDuration">
        <result property="orderItemCode" column="order_item_code" jdbcType="VARCHAR"/>
        <result property="orderItemName" column="order_item_name" jdbcType="VARCHAR"/>
        <result property="reportCount" column="report_count" jdbcType="INTEGER"/>
        <result property="hourCount" column="hour_count" jdbcType="INTEGER"/>
        <result property="reportType" column="report_type" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        order_item_code
        ,order_item_name,report_count,
        hour_count,report_type
    </sql>
</mapper>
