<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.goodwill.hdr.civ.mapper.ConfigTypeMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.goodwill.hdr.civ.entity.ConfigType">
        <id column="id" property="id"/>
        <result column="type_code" property="typeCode"/>
        <result column="type_name" property="typeName"/>
        <result column="type_desc" property="typeDesc"/>
        <result column="inuse" property="inuse"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id
        , type_code, type_name, type_desc, inuse
    </sql>

</mapper>
