<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace=".dao.CivSysMaskRuleDao">

    <resultMap type="com.goodwill.hdr.civ.entity.SysMaskRule" id="SysMaskRuleMap">
        <result property="id" column="id" jdbcType="VARCHAR"/>
        <result property="configcode" column="configcode" jdbcType="VARCHAR"/>
        <result property="configname" column="configname" jdbcType="VARCHAR"/>
        <result property="configvalue" column="configvalue" jdbcType="VARCHAR"/>
        <result property="configdesc" column="configdesc" jdbcType="VARCHAR"/>
        <result property="params" column="params" jdbcType="VARCHAR"/>
        <result property="enabled" column="enabled" jdbcType="VARCHAR"/>
        <result property="oid" column="oid" jdbcType="VARCHAR"/>
    </resultMap>

    <!--查询数据-->
    <select id="querySysMaskRuleMap" resultMap="SysMaskRuleMap">
        select id, configcode, configname, configvalue, configdesc, params, enabled, oid
        from civ_sys_mask_rule
        where oid = #{oid}
    </select>

</mapper>

