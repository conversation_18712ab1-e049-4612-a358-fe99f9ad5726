<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <modules>
        <module>civ-v5</module>
        <module>rest-client</module>
        <module>rest-server</module>
    </modules>


    <groupId>com.goodwill.hdr</groupId>
    <artifactId>civv5-rest</artifactId>
    <version>5.0.2-SNAPHSHOT</version>
    <packaging>pom</packaging>

    <name>civv5-rest</name>


    <properties>
        <maven.compiler.source>8</maven.compiler.source>
        <maven.compiler.target>8</maven.compiler.target>
    </properties>

    <dependencyManagement>
        <dependencies>
<!--            <dependency>-->
<!--                <groupId>com.goodwill.hdr</groupId>-->
<!--                <artifactId>hdr-common-security</artifactId>-->
<!--            </dependency>-->
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>druid</artifactId>
                <version>1.1.22</version>
            </dependency>

            <!--rest统一查询服务-->
            <dependency>
                <groupId>com.goodwill.hdr</groupId>
                <artifactId>common-query-api</artifactId>
                <version>0.0.1-SNAPHSHOT</version>
                <exclusions>
                    <exclusion>
                        <artifactId>slf4j-log4j12</artifactId>
                        <groupId>org.slf4j</groupId>
                    </exclusion>
                    <exclusion>
                        <groupId>jdk.tools</groupId>
                        <artifactId>jdk.tools</artifactId>
                    </exclusion>
                    <exclusion>
                        <artifactId>guava</artifactId>
                        <groupId>com.google.guava</groupId>
                    </exclusion>
                </exclusions>
            </dependency>

            <!--增加 jacoco覆盖率 依赖-->
            <dependency>
                <groupId>org.jacoco</groupId>
                <artifactId>jacoco-maven-plugin</artifactId>
                <version>0.8.7</version>
            </dependency>
            <dependency>
                <groupId>org.springframework.security</groupId>
                <artifactId>spring-security-test</artifactId>
                <version>5.3.4.RELEASE</version>
            </dependency>
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-test</artifactId>
                <version>2.3.4.RELEASE</version>
            </dependency>
            <dependency>
                <groupId>com.goodwill.hdr</groupId>
                <artifactId>rest-client</artifactId>
                <version>5.0.4</version>
            </dependency>
        </dependencies>
    </dependencyManagement>


    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-source-plugin</artifactId>
                <version>2.1.1</version>
                <!-- 绑定source插件到Maven的生命周期,并在生命周期后执行绑定的source的goal -->
                <executions>
                    <execution>
                        <id>attach-sources</id>
                        <phase>package</phase>
                        <!--在生命周期后执行绑定的source插件的goals -->
                        <goals>
                            <goal>jar-no-fork</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>

            <plugin>
                <groupId>org.jacoco</groupId>
                <artifactId>jacoco-maven-plugin</artifactId>
                <version>0.8.7</version>
                <executions>
                    <execution>
                        <id>pre-test</id>
                        <goals>
                            <goal>prepare-agent</goal>
                        </goals>
                    </execution>
                    <execution>
                        <id>report</id>
                        <phase>test</phase>
                        <goals>
                            <goal>report</goal>
                        </goals>
                    </execution>
                    <execution>
                        <configuration>
                            <!--定义输出的文件夹-->
                            <outputDirectory>target/jacoco-report</outputDirectory>
                            <!--执行数据的文件-->
                            <dataFile>${project.build.directory}/jacoco.exec</dataFile>
                            <!--生成报告的文件类型 HTML(默认)、XML、CSV-->
                            <formats>HTML</formats>
                            <!--生成报告的编码格式，默认UTF-8-->
                            <outputEncoding>UTF-8</outputEncoding>
                            <!--源文件编码-->
                            <sourceEncoding>UTF-8</sourceEncoding>
                            <!--HTML报告的标题-->
                            <title>${project.name}</title>
                        </configuration>
                    </execution>
                </executions>
            </plugin>


        </plugins>


    </build>
</project>
