package com.goodwill.hdr.rest.client.transmission;

import com.goodwill.hdr.rest.client.enums.JhdcpServerCode;
import com.goodwill.hdr.rest.client.wrapper.imp.JhdcpQueryWrapperImp;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit.jupiter.SpringExtension;
import org.springframework.util.Assert;

//@SpringBootTest
//@ExtendWith(SpringExtension.class)
public class JhdcpHttpSenderTest {

//    @Autowired
//    JhdcpHttpSender jhdcpHttpSender;
//
//    @Test
//    public void httpSenderTest() {
//        JhdcpQueryWrapperImp queryWrapper = new JhdcpQueryWrapperImp(JhdcpServerCode.IN_VISIT.getCode(), 1, 10);
//        queryWrapper.eq("HIS_PAT_ID", "1001710762");
//        String dataPageJson = jhdcpHttpSender.getDataPageJson(queryWrapper);
//        System.out.println(dataPageJson);
//        Assert.hasText(dataPageJson, "未获取到数据");
//

//    }
}
