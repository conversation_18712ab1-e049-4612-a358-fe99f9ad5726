package com.goodwill.hdr.rest.client.vo.server;

/**
 * 嘉和数据中台查询条件载体
 *
 * <AUTHOR>
 * @since 0.0.1
 */
public class JhdcpCondition {
    /**
     * 数据库字段
     */
    String column;
    /**
     * 操作类型
     */
    String type;
    /**
     * 操作值
     */
    String value;

    public JhdcpCondition() {
    }

    public JhdcpCondition(String column, String type, String value) {
        this.column = column;
        this.type = type;
        this.value = value;
    }

    public String getColumn() {
        return column;
    }

    public void setColumn(String column) {
        this.column = column;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }

    @Override
    public String toString() {
        return "JhdcpCondition{" +
                "column='" + column + '\'' +
                ", type='" + type + '\'' +
                ", value='" + value + '\'' +
                '}';
    }
}
