package com.goodwill.hdr.rest.client.wrapper;

/**
 * 数据源基础操作
 *
 * <AUTHOR>
 * @since 0.0.1
 */
public interface QueryWrapper {
    /**
     * 等于
     *
     * @param cloumn 数据库字段
     * @param value  操作值
     * @return 包装器
     */
    QueryWrapper eq(String cloumn, String value);

    /**
     * 不等于
     *
     * @param cloumn 数据库字段
     * @param value  操作值
     * @return 包装器
     */
    QueryWrapper notEq(String cloumn, String value);


    /**
     * 小于等于
     *
     * @param cloumn 数据库字段
     * @param value  操作值
     * @return 包装器
     */
    QueryWrapper le(String cloumn, String value);

    /**
     * 小于
     *
     * @param cloumn 数据库字段
     * @param value  操作值
     * @return 包装器
     */
    QueryWrapper lt(String cloumn, String value);

    /**
     * 大于等于
     *
     * @param cloumn 数据库字段
     * @param value  操作值
     * @return 包装器
     */
    QueryWrapper ge(String cloumn, String value);

    /**
     * 大于
     *
     * @param cloumn 数据库字段
     * @param value  操作值
     * @return 包装器
     */
    QueryWrapper gt(String cloumn, String value);




}
