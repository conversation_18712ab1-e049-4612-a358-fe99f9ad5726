package com.goodwill.hdr.rest.client.wrapper.imp;

import com.goodwill.hdr.rest.client.vo.server.JhdcpCondition;
import com.goodwill.hdr.rest.client.vo.server.JhdcpOrder;
import com.goodwill.hdr.rest.client.vo.server.JhdcpPageRequestVo;
import com.goodwill.hdr.rest.client.wrapper.JhdcpQueryWrapper;

import java.util.ArrayList;

/**
 * 嘉和数据中台查询
 *
 * <AUTHOR>
 * @since 0.0.1
 */
public class JhdcpQueryWrapperImp implements JhdcpQueryWrapper {

    private JhdcpPageRequestVo jhdcpPageRequestVo;


    public JhdcpQueryWrapperImp(String serverCode, Integer pageNo, Integer pageSize) {

        this.jhdcpPageRequestVo = new JhdcpPageRequestVo(serverCode, pageNo, pageSize, "hdr-console");
        jhdcpPageRequestVo.setCondition(new ArrayList<>());
        jhdcpPageRequestVo.setOrders(new ArrayList<>());

    }

    /**
     * @param cloumn 数据库字段
     * @param value  操作值
     * @return 包装器
     */
    @Override
    public JhdcpQueryWrapper isNull(String cloumn, String value) {
        jhdcpPageRequestVo.getCondition().add(new JhdcpCondition(cloumn, "isnull", value));
        return this;
    }

    /**
     * @param cloumn
     * @param value
     * @return
     */
    @Override
    public JhdcpQueryWrapper isNotNull(String cloumn, String value) {
        jhdcpPageRequestVo.getCondition().add(new JhdcpCondition(cloumn, "isnotnull", value));
        return this;
    }

    /**
     * @param cloumn
     * @param value
     * @return
     */
    @Override
    public JhdcpQueryWrapper likeRight(String cloumn, String value) {
        jhdcpPageRequestVo.getCondition().add(new JhdcpCondition(cloumn, "likeright", value));
        return this;
    }

    /**
     * @param cloumn
     * @param value
     * @return
     */
    @Override
    public JhdcpQueryWrapper eq(String cloumn, String value) {
        jhdcpPageRequestVo.getCondition().add(new JhdcpCondition(cloumn, "eq", value));
        return this;
    }

    /**
     * @param cloumn
     * @param value
     * @return
     */
    @Override
    public JhdcpQueryWrapper notEq(String cloumn, String value) {
        jhdcpPageRequestVo.getCondition().add(new JhdcpCondition(cloumn, "ne", value));
        return this;
    }

    /**
     * @param cloumn
     * @param value
     * @return
     */
    @Override
    public JhdcpQueryWrapper le(String cloumn, String value) {
        jhdcpPageRequestVo.getCondition().add(new JhdcpCondition(cloumn, "le", value));
        return this;
    }

    /**
     * @param cloumn
     * @param value
     * @return
     */
    @Override
    public JhdcpQueryWrapper lt(String cloumn, String value) {
        jhdcpPageRequestVo.getCondition().add(new JhdcpCondition(cloumn, "lt", value));
        return this;
    }

    /**
     * @param cloumn
     * @param value
     * @return
     */
    @Override
    public JhdcpQueryWrapper ge(String cloumn, String value) {
        jhdcpPageRequestVo.getCondition().add(new JhdcpCondition(cloumn, "ge", value));
        return this;
    }

    /**
     * @param cloumn
     * @param value
     * @return
     */
    @Override
    public JhdcpQueryWrapper gt(String cloumn, String value) {
        jhdcpPageRequestVo.getCondition().add(new JhdcpCondition(cloumn, "gt", value));
        return this;
    }



    /**
     * @param cloumn
     * @param value
     * @return
     */
    @Override
    public JhdcpQueryWrapper in(String cloumn, String... value) {
        jhdcpPageRequestVo.getCondition().add(new JhdcpCondition(cloumn, "in", String.join(",", value)+","));
        return this;
    }

    /**
     * @param cloumn
     * @param value
     * @return
     */
    @Override
    public JhdcpQueryWrapper notIn(String cloumn, String... value) {
        jhdcpPageRequestVo.getCondition().add(new JhdcpCondition(cloumn, "notin", String.join(",", value)));
        return this;
    }

    /**
     * 排序规则，正序ASC，逆序DESC
     *
     * @param cloumn
     */
    @Override
    public JhdcpQueryWrapper ascOrder(String cloumn) {
        jhdcpPageRequestVo.getOrders().add(new JhdcpOrder(cloumn, "ASC"));
        return this;
    }

    /**
     * @param cloumn
     * @return
     */
    @Override
    public JhdcpQueryWrapper descOrder(String cloumn) {
        jhdcpPageRequestVo.getOrders().add(new JhdcpOrder(cloumn, "DESC"));
        return this;
    }

    /**
     * @return
     */
    @Override
    public JhdcpPageRequestVo build() {
        return this.jhdcpPageRequestVo;
    }

    /**
     * @param cloumn
     * @param value
     */
    @Override
    public JhdcpQueryWrapper like(String cloumn, String value) {
        jhdcpPageRequestVo.getCondition().add(new JhdcpCondition(cloumn, "like", value));
        return this;
    }

}
