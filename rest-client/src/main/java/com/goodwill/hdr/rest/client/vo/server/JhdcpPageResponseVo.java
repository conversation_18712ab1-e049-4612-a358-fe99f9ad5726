package com.goodwill.hdr.rest.client.vo.server;

import java.util.List;
import java.util.Map;

/**
 * 数据中台响应结果的载体
 * <AUTHOR>
 * @since 0.0.1
 */
public class JhdcpPageResponseVo {

    private Integer total;
    private Integer pageSize;
    private Integer pageNo;
    private Integer pages;
    private List<Map<String,String>> data;

    public Integer getTotal() {
        return total;
    }

    public void setTotal(Integer total) {
        this.total = total;
    }

    public Integer getPageSize() {
        return pageSize;
    }

    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }

    public Integer getPageNo() {
        return pageNo;
    }

    public void setPageNo(Integer pageNo) {
        this.pageNo = pageNo;
    }

    public Integer getPages() {
        return pages;
    }

    public void setPages(Integer pages) {
        this.pages = pages;
    }

    public List<Map<String, String>> getData() {
        return data;
    }

    public void setData(List<Map<String, String>> data) {
        this.data = data;
    }
}
