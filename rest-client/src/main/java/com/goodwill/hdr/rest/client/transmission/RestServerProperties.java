package com.goodwill.hdr.rest.client.transmission;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

@ConfigurationProperties(prefix = "goodwill.hdr")
@Configuration
public class RestServerProperties {
    private String hostAndPort;

    public String getHostAndPort() {
        return hostAndPort;
    }

    public void setHostAndPort(String hostAndPort) {
        this.hostAndPort = hostAndPort;
    }
}
