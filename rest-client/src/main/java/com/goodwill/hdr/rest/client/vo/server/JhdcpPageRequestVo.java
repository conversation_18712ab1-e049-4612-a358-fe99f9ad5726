package com.goodwill.hdr.rest.client.vo.server;

import java.util.List;

/**
 * 从数据中台查询数据的请求模型
 *
 * <AUTHOR>
 * @since 0.0.1
 */
public class JhdcpPageRequestVo {
    /**
     * 查询条件集合
     */
    private List<JhdcpCondition> condition;
    /**
     * 排序条件集合
     */
    private List<JhdcpOrder> orders;
    /**
     * 分页条数
     */
    private Integer pageSize;
    private Integer pageNo;
    /**
     * 服务编码
     */
    private String serverCode;
    private String sysCode;


    public JhdcpPageRequestVo() {
    }

    public JhdcpPageRequestVo(String serverCode, Integer pageNo, Integer pageSize, String sysCode) {
        this.pageSize = pageSize;
        this.pageNo = pageNo;
        this.serverCode = serverCode;
        this.sysCode = sysCode;
    }

    public List<JhdcpCondition> getCondition() {
        return condition;
    }

    public void setCondition(List<JhdcpCondition> condition) {
        this.condition = condition;
    }


    public Integer getPageSize() {
        return pageSize;
    }

    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }

    public Integer getPageNo() {
        return pageNo;
    }

    public void setPageNo(Integer pageNo) {
        this.pageNo = pageNo;
    }

    public String getServerCode() {
        return serverCode;
    }

    public void setServerCode(String serverCode) {
        this.serverCode = serverCode;
    }


    public List<JhdcpOrder> getOrders() {
        return orders;
    }

    public void setOrders(List<JhdcpOrder> orders) {
        this.orders = orders;
    }

    public String getSysCode() {
        return sysCode;
    }

    public void setSysCode(String sysCode) {
        this.sysCode = sysCode;
    }

    @Override
    public String toString() {
        return "JhdcpPageRequestVo{" +
                "condition=" + condition +
                ", orders=" + orders +
                ", pageSize=" + pageSize +
                ", pageNo=" + pageNo +
                ", serverCode='" + serverCode + '\'' +
                '}';
    }
}
