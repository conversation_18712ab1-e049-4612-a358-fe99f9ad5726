package com.goodwill.hdr.rest.client.wrapper;

import com.goodwill.hdr.rest.client.vo.server.JhdcpPageRequestVo;

/**
 * 嘉和数据中台查询
 *
 * <AUTHOR>
 * @since 0.0.1
 */
public interface JhdcpQueryWrapper extends QueryWrapper {

    /**
     * 为空
     *
     * @param cloumn 数据库字段
     * @param value  操作值
     * @return 包装器
     */
    JhdcpQueryWrapper isNull(String cloumn, String value);

    /**
     * 不为空
     *
     * @param cloumn 数据库字段
     * @param value  操作值
     * @return 包装器
     */
    JhdcpQueryWrapper isNotNull(String cloumn, String value);

    /**
     * 开始于
     *
     * @param cloumn 数据库字段
     * @param value  操作值
     * @return 包装器
     */
    JhdcpQueryWrapper likeRight(String cloumn, String value);

    /**
     * 不在范围内
     *
     * @param cloumn 数据库字段
     * @param value  操作值的集合
     * @return 包装器
     */
    JhdcpQueryWrapper in(String cloumn, String... value);


    /**
     * 不在列表范围内
     *
     * @param cloumn 数据库字段
     * @param value  操作值的集合
     * @return 包装器
     */
    JhdcpQueryWrapper notIn(String cloumn, String... value);

    /**
     * 排序规则，正序ASC，逆序DESC
     */
    JhdcpQueryWrapper ascOrder(String cloumn);

    JhdcpQueryWrapper descOrder(String cloumn);

    JhdcpPageRequestVo build();

    /**
     * 包含
     *
     * @param cloumn 数据库字段
     * @param value  操作值
     * @return 包装器
     */
    JhdcpQueryWrapper like(String cloumn, String value);

}
