package com.goodwill.hdr.rest.client.transmission;

import com.goodwill.hdr.rest.client.vo.server.JhdcpPageRequestVo;
import com.goodwill.hdr.rest.client.wrapper.JhdcpQueryWrapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cache.annotation.CacheConfig;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.http.HttpEntity;
import org.springframework.http.ResponseEntity;
import org.springframework.http.converter.StringHttpMessageConverter;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestClientException;
import org.springframework.web.client.RestTemplate;

import java.nio.charset.StandardCharsets;

@Service

public class JhdcpHttpSender {
    private final RestServerProperties restServerProperties;

    private final RestTemplate restTemplate;

    private static final Logger log = LoggerFactory.getLogger(JhdcpHttpSender.class);

    public JhdcpHttpSender(RestServerProperties restServerProperties, RestTemplate restTemplate) {
        this.restServerProperties = restServerProperties;
        this.restTemplate = restTemplate;

    }



    public String getDataPageJson(JhdcpQueryWrapper jhdcpQueryWrapper) {
        String url = restServerProperties.getHostAndPort() + "/jhdcp/dataPageJson";

        restTemplate.getMessageConverters().set(1, new StringHttpMessageConverter(StandardCharsets.UTF_8));
        JhdcpPageRequestVo jhdcpPageRequestVo = jhdcpQueryWrapper.build();
        log.info(jhdcpPageRequestVo.toString());

        HttpEntity<JhdcpPageRequestVo> request = new HttpEntity<>(jhdcpPageRequestVo);
        try {
            ResponseEntity<String> stringResponseEntity = restTemplate.postForEntity(url, request, String.class);

            return stringResponseEntity.getBody();
        } catch (RestClientException e) {
            System.out.println(e);
        }

        return "";

    }

}
